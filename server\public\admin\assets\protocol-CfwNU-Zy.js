import{_ as V}from"./index-onOHNH0j.js";import{d as w,i as x,z as y,o as c,a as E,b as a,m as o,w as l,e as r,B as g,C as B,p as C,F as k,E as F,v as U}from"./index-B2xNDy79.js";import{E as D}from"./el-card-DpH4mUSc.js";import{_ as N}from"./index.vue_vue_type_style_index_0_lang-Dw4fui56.js";import{E as P,a as I}from"./el-form-item-DlU85AZK.js";import{c as h,d as z}from"./website-B_jWLJYu.js";import"./picker-Cd5l2hZ5.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-BuNto3DN.js";import"./index-DSiy6YVt.js";import"./_baseClone-CdezRMKA.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";const A={class:"xl:flex"},de=w({__name:"protocol",setup(G){const t=x({service_title:"",service_content:"",privacy_title:"",privacy_content:""}),m=async()=>{t.value=await h()},u=async()=>{await z({...t.value}),m()};return m(),(T,e)=>{const s=F,i=P,p=I,d=N,_=D,f=U,v=V,b=y("perms");return c(),E(k,null,[a("div",A,[o(_,{class:"!border-none flex-1 xl:mr-4 mb-4",shadow:"never"},{header:l(()=>e[4]||(e[4]=[a("span",{class:"font-medium"},"服务协议",-1)])),default:l(()=>[o(p,{model:r(t),"label-width":"80px"},{default:l(()=>[o(i,{label:"协议名称"},{default:l(()=>[o(s,{modelValue:r(t).service_title,"onUpdate:modelValue":e[0]||(e[0]=n=>r(t).service_title=n)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),o(d,{class:"mb-10",modelValue:r(t).service_content,"onUpdate:modelValue":e[1]||(e[1]=n=>r(t).service_content=n),height:"500"},null,8,["modelValue"])]),_:1}),o(_,{class:"!border-none flex-1 mb-4",shadow:"never"},{header:l(()=>e[5]||(e[5]=[a("span",{class:"font-medium"},"隐私协议",-1)])),default:l(()=>[o(p,{model:r(t),"label-width":"80px"},{default:l(()=>[o(i,{label:"协议名称"},{default:l(()=>[o(s,{modelValue:r(t).privacy_title,"onUpdate:modelValue":e[2]||(e[2]=n=>r(t).privacy_title=n)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),o(d,{class:"mb-10",modelValue:r(t).privacy_content,"onUpdate:modelValue":e[3]||(e[3]=n=>r(t).privacy_content=n),height:"500"},null,8,["modelValue"])]),_:1})]),g((c(),B(v,null,{default:l(()=>[o(f,{type:"primary",onClick:u},{default:l(()=>e[6]||(e[6]=[C("保存")])),_:1})]),_:1})),[[b,["setting.web.web_setting/setAgreement"]]])],64)}}});export{de as default};
