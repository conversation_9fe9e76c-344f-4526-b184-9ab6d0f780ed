import{d as k,s as b,j as v,o as w,a as B,m as a,w as l,p as m,b as f,e as t,t as i,C as N,G as q,I as g,v as D,E as S}from"./index-B2xNDy79.js";import{E as U,a as j}from"./el-form-item-DlU85AZK.js";/* empty css                       */import{E as I,a as O}from"./el-radio-CKcO4hVq.js";import{P as G}from"./index-DFOp_83R.js";const L={class:"export-data"},M={class:"flex"},J=k({__name:"index",props:{params:{type:Object,default:()=>({})},pageSize:{type:Number,default:25},fetchFun:{type:Function,required:!0}},setup(E){const c=b(),d=E,x=b(),o=v({page_type:0,page_start:1,page_end:200,file_name:""}),z={page_start:[{required:!0,message:"请输入起始页码"},{type:"number",message:"页码必须是整数"},{validator:(r,e,n)=>{if(e<=0)return n(new Error("页码必须大于0"));n()}}],page_end:[{required:!0,message:"请输入结束页码"},{type:"number",message:"页码必须是整数"},{validator:(r,e,n)=>{if(e<=0)return n(new Error("页码必须大于0"));n()}}]},u=v({count:0,sum_page:0,page_size:0,max_page:0,all_max_size:0}),y=async()=>{const r=await d.fetchFun({...d.params,page_size:d.pageSize,export:1});Object.assign(u,r),o.file_name=r.file_name,o.page_end=r.page_end,o.page_start=r.page_start},R=async()=>{var r,e;await((r=c.value)==null?void 0:r.validate()),g.loading("正在导出中...");try{await d.fetchFun({...d.params,...o,page_size:d.pageSize,export:2}),(e=x.value)==null||e.close(),g.closeLoading()}catch{g.closeLoading()}};return y(),(r,e)=>{const n=D,p=U,V=I,C=O,_=S,F=j;return w(),B("div",L,[a(G,{ref_key:"popupRef",ref:x,title:"导出设置",width:"500px","confirm-button-text":"确认导出",onConfirm:R,async:!0,onOpen:y},{trigger:l(()=>[a(n,null,{default:l(()=>e[4]||(e[4]=[m("导出")])),_:1})]),default:l(()=>[f("div",null,[a(F,{ref_key:"formRef",ref:c,model:t(o),"label-width":"120px",rules:z},{default:l(()=>[a(p,{label:"数据量："},{default:l(()=>[m(" 预计导出"+i(t(u).count)+"条数据， 共"+i(t(u).sum_page)+"页，每页"+i(t(u).page_size)+"条数据 ",1)]),_:1}),a(p,{label:"导出限制："},{default:l(()=>[m(" 每次导出最大允许"+i(t(u).max_page)+"页，共"+i(t(u).all_max_size)+"条数据 ",1)]),_:1}),a(p,{prop:"page_type",label:"导出范围：",required:""},{default:l(()=>[a(C,{modelValue:t(o).page_type,"onUpdate:modelValue":e[0]||(e[0]=s=>t(o).page_type=s)},{default:l(()=>[a(V,{value:0},{default:l(()=>e[5]||(e[5]=[m("全部导出")])),_:1}),a(V,{value:1},{default:l(()=>e[6]||(e[6]=[m("分页导出")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(o).page_type==1?(w(),N(p,{key:0,label:"分页范围："},{default:l(()=>[f("div",M,[a(p,{prop:"page_start"},{default:l(()=>[a(_,{style:{width:"140px"},modelValue:t(o).page_start,"onUpdate:modelValue":e[1]||(e[1]=s=>t(o).page_start=s),modelModifiers:{number:!0},placeholder:""},null,8,["modelValue"])]),_:1}),e[7]||(e[7]=f("span",{class:"flex-none ml-2 mr-2"},"页，至",-1)),a(p,{prop:"page_end"},{default:l(()=>[a(_,{style:{width:"140px"},modelValue:t(o).page_end,"onUpdate:modelValue":e[2]||(e[2]=s=>t(o).page_end=s),modelModifiers:{number:!0},placeholder:""},null,8,["modelValue"])]),_:1})])]),_:1})):q("",!0),a(p,{label:"导出文件名称：",prop:"file_name"},{default:l(()=>[a(_,{modelValue:t(o).file_name,"onUpdate:modelValue":e[3]||(e[3]=s=>t(o).file_name=s),placeholder:"请输入导出文件名称"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},512)])}}});export{J as _};
