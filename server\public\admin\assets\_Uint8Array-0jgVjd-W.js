import{aU as l,aV as i,aW as E,aX as L,aQ as D,aY as A,aZ as F,a_ as G,a$ as K,aS as U,b0 as m,b1 as y,b2 as W,aO as R,b3 as u}from"./index-B2xNDy79.js";var h=l(i,"WeakMap");function q(t){return t!=null&&E(t.length)&&!L(t)}var N=Object.prototype;function Y(t){var r=t&&t.constructor,e=typeof r=="function"&&r.prototype||N;return t===e}function Z(t,r){for(var e=-1,n=Array(t);++e<t;)n[e]=r(e);return n}function H(){return!1}var k=typeof exports=="object"&&exports&&!exports.nodeType&&exports,w=k&&typeof module=="object"&&module&&!module.nodeType&&module,Q=w&&w.exports===k,x=Q?i.Buffer:void 0,X=x?x.isBuffer:void 0,J=X||H,tt="[object Arguments]",et="[object Array]",rt="[object Boolean]",at="[object Date]",nt="[object Error]",ot="[object Function]",st="[object Map]",it="[object Number]",ut="[object Object]",ct="[object RegExp]",pt="[object Set]",ft="[object String]",gt="[object WeakMap]",bt="[object ArrayBuffer]",yt="[object DataView]",lt="[object Float32Array]",dt="[object Float64Array]",Tt="[object Int8Array]",ht="[object Int16Array]",jt="[object Int32Array]",vt="[object Uint8Array]",_t="[object Uint8ClampedArray]",At="[object Uint16Array]",mt="[object Uint32Array]",a={};a[lt]=a[dt]=a[Tt]=a[ht]=a[jt]=a[vt]=a[_t]=a[At]=a[mt]=!0;a[tt]=a[et]=a[bt]=a[rt]=a[yt]=a[at]=a[nt]=a[ot]=a[st]=a[it]=a[ut]=a[ct]=a[pt]=a[ft]=a[gt]=!1;function wt(t){return D(t)&&E(t.length)&&!!a[A(t)]}function xt(t){return function(r){return t(r)}}var z=typeof exports=="object"&&exports&&!exports.nodeType&&exports,g=z&&typeof module=="object"&&module&&!module.nodeType&&module,St=g&&g.exports===z,T=St&&F.process,S=function(){try{var t=g&&g.require&&g.require("util").types;return t||T&&T.binding&&T.binding("util")}catch{}}(),O=S&&S.isTypedArray,Ot=O?xt(O):wt,Pt=Object.prototype,$t=Pt.hasOwnProperty;function Mt(t,r){var e=U(t),n=!e&&G(t),c=!e&&!n&&J(t),p=!e&&!n&&!c&&Ot(t),f=e||n||c||p,d=f?Z(t.length,String):[],V=d.length;for(var o in t)(r||$t.call(t,o))&&!(f&&(o=="length"||c&&(o=="offset"||o=="parent")||p&&(o=="buffer"||o=="byteLength"||o=="byteOffset")||K(o,V)))&&d.push(o);return d}function Ct(t,r){return function(e){return t(r(e))}}var It=Ct(Object.keys,Object),Bt=Object.prototype,Et=Bt.hasOwnProperty;function Ut(t){if(!Y(t))return It(t);var r=[];for(var e in Object(t))Et.call(t,e)&&e!="constructor"&&r.push(e);return r}function kt(t){return q(t)?Mt(t):Ut(t)}function zt(){this.__data__=new m,this.size=0}function Vt(t){var r=this.__data__,e=r.delete(t);return this.size=r.size,e}function Lt(t){return this.__data__.get(t)}function Dt(t){return this.__data__.has(t)}var Ft=200;function Gt(t,r){var e=this.__data__;if(e instanceof m){var n=e.__data__;if(!y||n.length<Ft-1)return n.push([t,r]),this.size=++e.size,this;e=this.__data__=new W(n)}return e.set(t,r),this.size=e.size,this}function b(t){var r=this.__data__=new m(t);this.size=r.size}b.prototype.clear=zt;b.prototype.delete=Vt;b.prototype.get=Lt;b.prototype.has=Dt;b.prototype.set=Gt;function Kt(t,r){for(var e=-1,n=t==null?0:t.length,c=0,p=[];++e<n;){var f=t[e];r(f,e,t)&&(p[c++]=f)}return p}function Wt(){return[]}var Rt=Object.prototype,qt=Rt.propertyIsEnumerable,P=Object.getOwnPropertySymbols,Nt=P?function(t){return t==null?[]:(t=Object(t),Kt(P(t),function(r){return qt.call(t,r)}))}:Wt;function Yt(t,r,e){var n=r(t);return U(t)?n:R(n,e(t))}function re(t){return Yt(t,kt,Nt)}var j=l(i,"DataView"),v=l(i,"Promise"),_=l(i,"Set"),$="[object Map]",Zt="[object Object]",M="[object Promise]",C="[object Set]",I="[object WeakMap]",B="[object DataView]",Ht=u(j),Qt=u(y),Xt=u(v),Jt=u(_),te=u(h),s=A;(j&&s(new j(new ArrayBuffer(1)))!=B||y&&s(new y)!=$||v&&s(v.resolve())!=M||_&&s(new _)!=C||h&&s(new h)!=I)&&(s=function(t){var r=A(t),e=r==Zt?t.constructor:void 0,n=e?u(e):"";if(n)switch(n){case Ht:return B;case Qt:return $;case Xt:return M;case Jt:return C;case te:return I}return r});var ae=i.Uint8Array;export{b as S,ae as U,s as a,Yt as b,xt as c,re as d,Y as e,q as f,Nt as g,Mt as h,J as i,Ot as j,kt as k,S as n,Ct as o,Wt as s};
