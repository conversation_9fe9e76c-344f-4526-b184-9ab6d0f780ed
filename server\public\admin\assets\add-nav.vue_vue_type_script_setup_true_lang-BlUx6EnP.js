import{d as U,c as C,o as p,a as B,b as l,m as a,w as d,C as N,e as c,D as $,p as D,I as r,q as z,E as I,J as A,v as R}from"./index-B2xNDy79.js";import{_ as q}from"./index-BuNto3DN.js";import{E as F}from"./el-form-item-DlU85AZK.js";import{_ as J}from"./picker-qQ9YEtJl.js";import{D as L,_ as P}from"./picker-Cd5l2hZ5.js";const S={class:"bg-fill-light flex items-center w-full p-4 mb-4"},T={class:"upload-btn w-[60px] h-[60px]"},j={class:"ml-3 flex-1"},G={class:"flex items-center"},H={class:"flex items-center mt-[18px]"},K={class:"flex-1 flex items-center"},M={class:"drag-move cursor-move ml-auto"},Z=U({__name:"add-nav",props:{modelValue:{type:Array,default:()=>[]},max:{type:Number,default:100},min:{type:Number,default:1}},emits:["update:modelValue"],setup(_,{emit:f}){const t=_,V=f,m=C({get(){return t.modelValue},set(s){V("update:modelValue",s)}}),x=()=>{var s;((s=t.modelValue)==null?void 0:s.length)<t.max?m.value.push({image:"",name:"导航名称",link:{},is_show:"1"}):r.msgError(`最多添加${t.max}个`)},v=s=>{var e;if(((e=t.modelValue)==null?void 0:e.length)<=t.min)return r.msgError(`最少保留${t.min}个`);m.value.splice(s,1)};return(s,e)=>{const u=z,g=P,h=I,k=J,b=A,w=F,y=q,E=R;return p(),B("div",null,[l("div",null,[a(c(L),{class:"draggable",modelValue:c(m),"onUpdate:modelValue":e[0]||(e[0]=o=>$(m)?m.value=o:null),animation:"300",handle:".drag-move","item-key":"index"},{item:d(({element:o,index:i})=>[(p(),N(y,{class:"w-[467px]",key:i,onClose:n=>v(i)},{default:d(()=>[l("div",S,[a(g,{modelValue:o.image,"onUpdate:modelValue":n=>o.image=n,"upload-class":"bg-body",size:"60px","exclude-domain":""},{upload:d(()=>[l("div",T,[a(u,{name:"el-icon-Plus",size:20})])]),_:2},1032,["modelValue","onUpdate:modelValue"]),l("div",j,[l("div",G,[e[1]||(e[1]=l("span",{class:"text-tx-regular flex-none mr-3"},"名称",-1)),a(h,{modelValue:o.name,"onUpdate:modelValue":n=>o.name=n,placeholder:"请输入名称"},null,8,["modelValue","onUpdate:modelValue"])]),l("div",H,[e[2]||(e[2]=l("span",{class:"text-tx-regular flex-none mr-3"},"链接",-1)),a(k,{modelValue:o.link,"onUpdate:modelValue":n=>o.link=n},null,8,["modelValue","onUpdate:modelValue"])]),a(w,{label:"是否显示",class:"mt-[18px]"},{default:d(()=>[l("div",K,[a(b,{modelValue:o.is_show,"onUpdate:modelValue":n=>o.is_show=n,"active-value":"1","inactive-value":"0"},null,8,["modelValue","onUpdate:modelValue"]),l("div",M,[a(u,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"])]),l("div",null,[a(E,{type:"primary",onClick:x},{default:d(()=>e[3]||(e[3]=[D("添加")])),_:1})])])}}});export{Z as _};
