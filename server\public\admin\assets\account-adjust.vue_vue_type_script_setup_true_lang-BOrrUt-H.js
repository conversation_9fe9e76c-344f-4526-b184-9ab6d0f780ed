import{d as h,s as v,j as q,c as B,V as w,I as V,o as I,C as j,w as r,b as F,m as a,e as n,p as s,t as y,E as D}from"./index-B2xNDy79.js";import{E as P,a as S}from"./el-form-item-DlU85AZK.js";/* empty css                       */import{E as U,a as G}from"./el-radio-CKcO4hVq.js";import{P as M}from"./index-DFOp_83R.js";const T={class:"pr-8"},L=h({__name:"account-adjust",props:{show:{type:Boolean,required:!0},value:{type:[Number,String],required:!0}},emits:["update:show","confirm"],setup(d,{emit:E}){const u=v(),i=d,f=E,o=q({action:1,num:"",remark:""}),m=v(),c=B(()=>Number(i.value)+Number(o.num)*(o.action==1?1:-1)),k={num:[{required:!0,message:"请输入调整的金额"}]},R=e=>{if(e.includes("-"))return V.msgError("请输入正整数");o.num=e},x=async()=>{var e;await((e=u.value)==null?void 0:e.validate()),f("confirm",o)},C=()=>{var e;f("update:show",!1),(e=u.value)==null||e.resetFields()};return w(()=>i.show,e=>{var t,l;e?(t=m.value)==null||t.open():(l=m.value)==null||l.close()}),w(c,e=>{e<0&&(V.msgError("调整后余额需大于0"),o.num="")}),(e,t)=>{const l=P,_=U,g=G,b=D,N=S;return I(),j(M,{ref_key:"popupRef",ref:m,title:"余额调整",width:"500px",onConfirm:x,async:!0,onClose:C},{default:r(()=>[F("div",T,[a(N,{ref_key:"formRef",ref:u,model:n(o),"label-width":"120px",rules:k},{default:r(()=>[a(l,{label:"当前余额"},{default:r(()=>[s("¥ "+y(d.value),1)]),_:1}),a(l,{label:"余额增减",required:"",prop:"action"},{default:r(()=>[a(g,{modelValue:n(o).action,"onUpdate:modelValue":t[0]||(t[0]=p=>n(o).action=p)},{default:r(()=>[a(_,{value:1},{default:r(()=>t[2]||(t[2]=[s("增加余额")])),_:1}),a(_,{value:2},{default:r(()=>t[3]||(t[3]=[s("扣减余额")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(l,{label:"调整余额",prop:"num"},{default:r(()=>[a(b,{"model-value":n(o).num,placeholder:"请输入调整的金额",type:"number",onInput:R},null,8,["model-value"])]),_:1}),a(l,{label:"调整后余额"},{default:r(()=>[s(" ¥ "+y(n(c)),1)]),_:1}),a(l,{label:"备注",prop:"remark"},{default:r(()=>[a(b,{modelValue:n(o).remark,"onUpdate:modelValue":t[1]||(t[1]=p=>n(o).remark=p),type:"textarea",rows:4},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},512)}}});export{L as _};
