import{d as D,s as S,i as T,c as N,o as f,a as L,m as t,w as n,b as y,p as u,B as U,C as g,e as l,t as A,D as O,G as P,H as w,I as j,q,v as z,J as F,K as G}from"./index-B2xNDy79.js";import{_ as H}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as I,a as J}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as K}from"./el-card-DpH4mUSc.js";import{E as M}from"./el-alert-BUxHh72o.js";import{o as Q,d as W,e as X}from"./wx_oa-3-DCeMZg.js";import{u as Y}from"./usePaging-Dm2wALfy.js";import{_ as Z}from"./edit.vue_vue_type_script_setup_true_lang-D8ArQME0.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./isEqual-CLGO95LP.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./el-form-item-DlU85AZK.js";import"./_baseClone-CdezRMKA.js";/* empty css                       */import"./el-radio-CKcO4hVq.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";const ee={class:"flex justify-end mt-4"},Ee=D({__name:"follow_reply",setup(te){const m=S(),d=T(!1),h=N(()=>o=>{switch(o){case 1:return"文本"}}),{pager:r,getLists:s}=Y({fetchFun:X,params:{reply_type:1}}),b=async()=>{var o;d.value=!0,await w(),(o=m.value)==null||o.open("add",1)},C=async o=>{var e,p;d.value=!0,await w(),(e=m.value)==null||e.open("edit",1),(p=m.value)==null||p.getDetail(o)},k=async o=>{await j.confirm("确定要删除？"),await Q({id:o}),s()},E=async o=>{try{await W({id:o}),s()}catch{s()}};return s(),(o,e)=>{const p=M,v=K,V=q,_=z,i=I,$=F,x=J,R=H,B=G;return f(),L("div",null,[t(v,{class:"!border-none",shadow:"never"},{default:n(()=>[t(p,{type:"warning",title:"温馨提示：1.粉丝关注公众号时，会自动发送启用的关注回复；2.同时只能启用一个关注回复。",closable:!1,"show-icon":""})]),_:1}),t(v,{class:"!border-none mt-4",shadow:"never"},{default:n(()=>[y("div",null,[t(_,{class:"mb-4",type:"primary",onClick:e[0]||(e[0]=a=>b())},{icon:n(()=>[t(V,{name:"el-icon-Plus"})]),default:n(()=>[e[3]||(e[3]=u(" 新增 "))]),_:1})]),U((f(),g(x,{size:"large",data:l(r).lists},{default:n(()=>[t(i,{label:"规则名称",prop:"name","min-width":"120"}),t(i,{label:"回复类型","min-width":"120"},{default:n(({row:a})=>[u(A(l(h)(a.content_type)),1)]),_:1}),t(i,{label:"回复内容",prop:"content","min-width":"120"}),t(i,{label:"状态","min-width":"120"},{default:n(({row:a})=>[t($,{modelValue:a.status,"onUpdate:modelValue":c=>a.status=c,"active-value":1,"inactive-value":0,onChange:c=>E(a.id)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(i,{label:"排序",prop:"sort","min-width":"120"}),t(i,{label:"操作",width:"120",fixed:"right"},{default:n(({row:a})=>[t(_,{type:"primary",link:"",onClick:c=>C(a)},{default:n(()=>e[4]||(e[4]=[u(" 编辑 ")])),_:2},1032,["onClick"]),t(_,{type:"danger",link:"",onClick:c=>k(a.id)},{default:n(()=>e[5]||(e[5]=[u(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[B,l(r).loading]]),y("div",ee,[t(R,{modelValue:l(r),"onUpdate:modelValue":e[1]||(e[1]=a=>O(r)?r.value=a:null),onChange:l(s)},null,8,["modelValue","onChange"])])]),_:1}),l(d)?(f(),g(Z,{key:0,ref_key:"editRef",ref:m,onSuccess:l(s),onClose:e[2]||(e[2]=a=>d.value=!1)},null,8,["onSuccess"])):P("",!0)])}}});export{Ee as default};
