import{_ as S}from"./index-onOHNH0j.js";import{y as x,d as D,u as B,j as F,s as R,z as c,o as m,a as j,m as s,w as d,b as l,e as t,B as r,C as u,p as _,E as N,v as W}from"./index-B2xNDy79.js";import{E as z,a as M}from"./el-form-item-DlU85AZK.js";import{_ as T}from"./picker-Cd5l2hZ5.js";import{E as G}from"./el-card-DpH4mUSc.js";import{E as H}from"./el-alert-BUxHh72o.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./index-BuNto3DN.js";import"./index-DSiy6YVt.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";function J(w){return x.post({url:"/channel.mnp_settings/setConfig",params:w})}function K(){return x.get({url:"/channel.mnp_settings/getConfig"})}const L={class:"w-80"},O={class:"w-80"},P={class:"flex-1"},Q={class:"w-80"},X={class:"w-80"},Y={class:"flex-1 min-w-0"},Z={class:"sm:flex"},$={class:"mr-4 sm:w-80 flex"},h={class:"flex-1 min-w-0"},ee={class:"sm:flex"},le={class:"mr-4 sm:w-80 flex"},oe={class:"flex-1 min-w-0"},se={class:"sm:flex"},te={class:"mr-4 sm:w-80 flex"},de={class:"flex-1 min-w-0"},ie={class:"sm:flex"},ne={class:"mr-4 sm:w-80 flex"},ae={class:"flex-1 min-w-0"},me={class:"sm:flex"},pe={class:"mr-4 sm:w-80 flex"},re={class:"flex-1 min-w-0"},ue={class:"sm:flex"},_e={class:"mr-4 sm:w-80 flex"},fe=D({name:"weappConfig"}),Ge=D({...fe,setup(w){const k=B(),o=F({name:"",original_id:"",qr_code:"",app_id:"",app_secret:"",business_domain:"",download_file_domain:"",request_domain:"",socket_domain:"",tcpDomain:"",udp_domain:"",upload_file_domain:""}),V=R(),q={app_id:[{required:!0,message:"请输入AppID",trigger:["blur","change"]}],app_secret:[{required:!0,message:"请输入AppSecret",trigger:["blur","change"]}]},g=async()=>{const v=await K();for(const e in o)o[e]=v[e]},C=async()=>{var v;await((v=V.value)==null?void 0:v.validate()),await J(o),g()};return g(),(v,e)=>{const E=H,b=G,a=N,n=z,I=T,p=W,U=M,y=S,f=c("copy"),A=c("perms");return m(),j("div",null,[s(b,{class:"!border-none",shadow:"never"},{default:d(()=>[s(E,{type:"warning",title:"温馨提示：填写微信小程序开发配置，请前往微信公众平台申请小程序并完成认证",closable:!1,"show-icon":""})]),_:1}),s(U,{ref_key:"formRef",ref:V,model:t(o),rules:q,"label-width":t(k).isMobile?"80px":"160px"},{default:d(()=>[s(b,{class:"!border-none mt-4",shadow:"never"},{default:d(()=>[e[12]||(e[12]=l("div",{class:"font-medium mb-7"},"微信小程序",-1)),s(n,{label:"小程序名称",prop:"name"},{default:d(()=>[l("div",L,[s(a,{modelValue:t(o).name,"onUpdate:modelValue":e[0]||(e[0]=i=>t(o).name=i),placeholder:"请输入小程序名称"},null,8,["modelValue"])])]),_:1}),s(n,{label:"原始ID",prop:"original_id"},{default:d(()=>[l("div",O,[s(a,{modelValue:t(o).original_id,"onUpdate:modelValue":e[1]||(e[1]=i=>t(o).original_id=i),placeholder:"请输入原始ID"},null,8,["modelValue"])])]),_:1}),s(n,{label:"小程序码",prop:"qr_code"},{default:d(()=>[l("div",P,[l("div",null,[s(I,{modelValue:t(o).qr_code,"onUpdate:modelValue":e[2]||(e[2]=i=>t(o).qr_code=i),limit:1},null,8,["modelValue"])]),e[11]||(e[11]=l("div",{class:"form-tips"},"建议尺寸：宽400px*高400px。jpg，jpeg，png格式",-1))])]),_:1})]),_:1}),s(b,{class:"!border-none mt-4",shadow:"never"},{default:d(()=>[e[14]||(e[14]=l("div",{class:"font-medium mb-7"},"开发者ID",-1)),s(n,{label:"AppID",prop:"app_id"},{default:d(()=>[l("div",Q,[s(a,{modelValue:t(o).app_id,"onUpdate:modelValue":e[3]||(e[3]=i=>t(o).app_id=i),placeholder:"请输入AppID"},null,8,["modelValue"])])]),_:1}),s(n,{label:"AppSecret",prop:"app_secret"},{default:d(()=>[l("div",X,[s(a,{modelValue:t(o).app_secret,"onUpdate:modelValue":e[4]||(e[4]=i=>t(o).app_secret=i),placeholder:"请输入AppSecret"},null,8,["modelValue"])])]),_:1}),s(n,null,{default:d(()=>e[13]||(e[13]=[l("div",{class:"form-tips"}," 小程序账号登录微信公众平台，点击开发>开发设置->开发者ID，设置AppID和AppSecret ",-1)])),_:1})]),_:1}),s(b,{class:"!border-none mt-4",shadow:"never"},{default:d(()=>[e[25]||(e[25]=l("div",{class:"font-medium mb-7"},"服务器域名",-1)),s(n,{label:"request合法域名",prop:"appId"},{default:d(()=>[l("div",Y,[l("div",Z,[l("div",$,[s(a,{modelValue:t(o).request_domain,"onUpdate:modelValue":e[5]||(e[5]=i=>t(o).request_domain=i),disabled:""},null,8,["modelValue"])]),r((m(),u(p,null,{default:d(()=>e[15]||(e[15]=[_("复制")])),_:1})),[[f,t(o).request_domain]])]),e[16]||(e[16]=l("div",{class:"form-tips"}," 小程序账号登录微信公众平台，点击开发>开发设置->服务器域名，填写https协议域名 ",-1))])]),_:1}),s(n,{label:"socket合法域名"},{default:d(()=>[l("div",h,[l("div",ee,[l("div",le,[s(a,{modelValue:t(o).socket_domain,"onUpdate:modelValue":e[6]||(e[6]=i=>t(o).socket_domain=i),disabled:""},null,8,["modelValue"])]),r((m(),u(p,null,{default:d(()=>e[17]||(e[17]=[_("复制")])),_:1})),[[f,t(o).socket_domain]])]),e[18]||(e[18]=l("div",{class:"form-tips"}," 小程序账号登录微信公众平台，点击开发>开发设置->服务器域名，填写wss协议域名 ",-1))])]),_:1}),s(n,{label:"uploadFile合法域名"},{default:d(()=>[l("div",oe,[l("div",se,[l("div",te,[s(a,{modelValue:t(o).upload_file_domain,"onUpdate:modelValue":e[7]||(e[7]=i=>t(o).upload_file_domain=i),disabled:""},null,8,["modelValue"])]),r((m(),u(p,null,{default:d(()=>e[19]||(e[19]=[_("复制")])),_:1})),[[f,t(o).upload_file_domain]])]),e[20]||(e[20]=l("div",{class:"form-tips"}," 小程序账号登录微信公众平台，点击开发>开发设置->服务器域名，填写https协议域名 ",-1))])]),_:1}),s(n,{label:"downloadFile合法域名"},{default:d(()=>[l("div",de,[l("div",ie,[l("div",ne,[s(a,{modelValue:t(o).download_file_domain,"onUpdate:modelValue":e[8]||(e[8]=i=>t(o).download_file_domain=i),disabled:""},null,8,["modelValue"])]),r((m(),u(p,null,{default:d(()=>e[21]||(e[21]=[_("复制")])),_:1})),[[f,t(o).download_file_domain]])]),e[22]||(e[22]=l("div",{class:"form-tips"}," 小程序账号登录微信公众平台，点击开发>开发设置->服务器域名，填写https协议域名 ",-1))])]),_:1}),s(n,{label:"udp合法域名"},{default:d(()=>[l("div",ae,[l("div",me,[l("div",pe,[s(a,{modelValue:t(o).udp_domain,"onUpdate:modelValue":e[9]||(e[9]=i=>t(o).udp_domain=i),disabled:""},null,8,["modelValue"])]),r((m(),u(p,null,{default:d(()=>e[23]||(e[23]=[_("复制")])),_:1})),[[f,t(o).udp_domain]])]),e[24]||(e[24]=l("div",{class:"form-tips"}," 小程序账号登录微信公众平台，点击开发>开发设置->服务器域名，填写udp协议域名 ",-1))])]),_:1})]),_:1}),s(b,{class:"!border-none mt-4",shadow:"never"},{default:d(()=>[e[28]||(e[28]=l("div",{class:"font-medium mb-7"},"业务域名",-1)),s(n,{label:"业务域名"},{default:d(()=>[l("div",re,[l("div",ue,[l("div",_e,[s(a,{modelValue:t(o).business_domain,"onUpdate:modelValue":e[10]||(e[10]=i=>t(o).business_domain=i),disabled:""},null,8,["modelValue"])]),r((m(),u(p,null,{default:d(()=>e[26]||(e[26]=[_("复制")])),_:1})),[[f,t(o).business_domain]])]),e[27]||(e[27]=l("div",{class:"form-tips"}," 小程序账号登录微信公众平台，点击开发>开发设置->业务域名，填写业务域名 ",-1))])]),_:1})]),_:1})]),_:1},8,["model","label-width"]),r((m(),u(y,null,{default:d(()=>[s(p,{type:"primary",onClick:C},{default:d(()=>e[29]||(e[29]=[_("保存")])),_:1})]),_:1})),[[A,["channel.mnp_settings/setConfig"]]])])}}});export{Ge as default};
