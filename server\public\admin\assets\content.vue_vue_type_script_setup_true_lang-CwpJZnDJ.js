import p from"./decoration-img-2F0tdl1c.js";import{d,c as m,o as a,a as n,b as r,F as u,r as _,e as f,as as g,m as h,t as x}from"./index-B2xNDy79.js";const y={class:"nav bg-white pt-[15px] pb-[8px]"},w={class:"mt-[7px]"},k=d({__name:"content",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(o){const t=o,c=m(()=>{var s;return(((s=t.content.data)==null?void 0:s.filter(e=>e.is_show=="1"))||[]).slice(0,t.content.show_line*t.content.per_line)});return(l,s)=>(a(),n("div",y,[r("div",{class:"grid grid-rows-auto gap-y-3 w-full",style:g({"grid-template-columns":`repeat(${o.content.per_line}, 1fr)`})},[(a(!0),n(u,null,_(f(c),(e,i)=>(a(),n("div",{key:i,class:"flex flex-col items-center"},[h(p,{width:"41px",height:"41px",src:e.image,alt:""},null,8,["src"]),r("div",w,x(e.name),1)]))),128))],4)]))}});export{k as _};
