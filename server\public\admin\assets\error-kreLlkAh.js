import{d as u,i as m,h as v,bO as p,o as l,a as _,b as s,U as f,t as n,C as B,w as x,p as h,e as g,G as y,v as b,x as k}from"./index-B2xNDy79.js";const w={class:"error"},C={class:"error-code"},I={class:"text-lg text-tx-secondary mt-7 mb-7"},S=u({__name:"error",props:{code:String,title:String,showBtn:{type:Boolean,default:!0}},setup(e){const c=e;let t=null;const o=m(5),r=v();return c.showBtn&&(t=setInterval(()=>{o.value===0?(clearInterval(t),r.go(-1)):o.value--},1e3)),p(()=>{t&&clearInterval(t)}),(d,a)=>{const i=b;return l(),_("div",w,[s("div",null,[f(d.$slots,"content",{},()=>[s("div",C,n(e.code),1)],!0),s("div",I,n(e.title),1),e.showBtn?(l(),B(i,{key:0,type:"primary",onClick:a[0]||(a[0]=E=>g(r).go(-1))},{default:x(()=>[h(n(o.value)+" 秒后返回上一页 ",1)]),_:1})):y("",!0)])])}}}),V=k(S,[["__scopeId","data-v-58b95848"]]);export{V as default};
