import{d as y,c as v,o as e,a as t,b as c,t as r,G as l,F as m,r as x,e as u,m as i,q as b,x as g}from"./index-B2xNDy79.js";import _ from"./decoration-img-2F0tdl1c.js";const k={class:"my-service"},w={key:0,class:"title px-[15px] py-[10px]"},B={key:1,class:"flex flex-wrap pt-[20px] pb-[10px]"},N={class:"mt-[7px]"},V={key:2},j={class:"ml-[10px] flex-1"},C=y({__name:"content",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(s){const f=s,d=v(()=>{var o;return((o=f.content.data)==null?void 0:o.filter(p=>p.is_show=="1"))||[]});return(o,p)=>{const h=b;return e(),t("div",k,[s.content.title?(e(),t("div",w,[c("div",null,r(s.content.title),1)])):l("",!0),s.content.style==1?(e(),t("div",B,[(e(!0),t(m,null,x(u(d),(n,a)=>(e(),t("div",{key:a,class:"flex flex-col items-center w-1/4 mb-[15px]"},[i(_,{width:"26px",height:"26px",src:n.image,alt:""},null,8,["src"]),c("div",N,r(n.name),1)]))),128))])):l("",!0),s.content.style==2?(e(),t("div",V,[(e(!0),t(m,null,x(u(d),(n,a)=>(e(),t("div",{key:a,class:"flex items-center border-b border-[#e5e5e5] h-[50px] px-[12px]"},[i(_,{width:"24px",height:"24px",src:n.image,alt:""},null,8,["src"]),c("div",j,r(n.name),1),c("div",null,[i(h,{name:"el-icon-ArrowRight"})])]))),128))])):l("",!0)])}}}),I=g(C,[["__scopeId","data-v-e6ec8456"]]);export{I as default};
