import{d as m,o as s,a as o,m as l,w as r,b as e,F as c,r as i,Y as v,t as u,G as f,bJ as h,x}from"./index-B2xNDy79.js";import p from"./decoration-img-2F0tdl1c.js";const y={class:"pc-aside absolute top-0 bottom-0 left-0 w-[150px]"},b={class:"h-full px-[10px] flex flex-col"},k={class:"mb-auto"},w={class:"nav"},g=["to"],C={class:"ml-[10px]"},B={class:"menu"},E={class:"ml-[6px] line-clamp-1"},N=m({__name:"content",props:{nav:{type:Array,default:()=>[]},menu:{type:Array,default:()=>[]}},setup(d){return(S,n)=>{const _=h;return s(),o("div",y,[l(_,null,{default:r(()=>[e("div",b,[e("div",k,[e("div",w,[(s(!0),o(c,null,i(d.nav,(t,a)=>(s(),o(c,{key:a},[t.is_show=="1"?(s(),o("div",{key:0,to:t.link.path,class:v(["nav-item",{active:a==0}])},[l(p,{width:"18px",height:"18px",src:a==0?t.selected:t.unselected,fit:"cover"},{error:r(()=>n[0]||(n[0]=[e("span",null,null,-1)])),_:2},1032,["src"]),e("div",C,u(t.name),1)],10,g)):f("",!0)],64))),128))])]),e("div",null,[e("div",B,[(s(!0),o(c,null,i(d.menu,(t,a)=>(s(),o("div",{class:"menu-item",key:a},[l(p,{width:"16px",height:"16px",src:t.unselected,fit:"cover"},{error:r(()=>n[1]||(n[1]=[e("span",null,null,-1)])),_:2},1032,["src"]),e("span",E,u(t.name),1)]))),128))])])])]),_:1})])}}}),D=x(N,[["__scopeId","data-v-751ecec2"]]);export{D as default};
