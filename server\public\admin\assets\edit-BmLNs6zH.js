import{d as M,i as m,c as T,o as y,C as z,w as i,B as $,a as V,m as o,e as l,p as u,t as j,b as n,F as k,G as x,D as J,f4 as O,E as Q,L as W,v as X,f5 as Y,K as Z,x as h}from"./index-B2xNDy79.js";/* empty css                       */import{_ as ee}from"./picker-Cd5l2hZ5.js";import{a as le,E as oe}from"./el-form-item-DlU85AZK.js";import{E as te,a as ae}from"./el-radio-CKcO4hVq.js";import{g as ie,s as ne}from"./pay-Bumb48yC.js";import{u as re}from"./useLockFn-ceEG4yvt.js";import{u as se}from"./getExposeType-ctsD7yqi.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-BuNto3DN.js";import"./index-DSiy6YVt.js";import"./_baseClone-CdezRMKA.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";const de={class:"h-full flex flex-col","element-loading-text":"加载中...","element-loading-background":"var(--el-bg-color)"},pe={class:"flex-1"},me={class:"flex-1"},ue={class:"flex-1"},fe=M({__name:"edit",emits:["refresh"],setup(_e,{expose:b,emit:w}){const f=m(!1),v=se(),E=m(),U=m("profile"),C=m(!1),c=m(!0),_=T(()=>{switch(t.value.pay_way){case 1:return"余额支付";case 2:return"微信支付";case 3:return"支付宝支付";default:return""}}),t=m({id:"",pay_way:0,name:"",icon:"",sort:0,remark:"",domain:"",config:{interface_version:"",merchant_type:"",mch_id:"",pay_sign_key:"",apiclient_cert:"",apiclient_key:"",mode:"",app_id:"",private_key:"",ali_public_key:""}}),I={name:[{required:!0,message:"请输入显示名称"}],"config.mch_id":[{required:!0,message:"请输入微信支付商户号"}],"config.pay_sign_key":[{required:!0,message:"请输入微信支付商户API密钥"}],"config.apiclient_cert":[{required:!0,message:"请输入微信支付证书"}],"config.apiclient_key":[{required:!0,message:"请输入微信支付证书密钥"}],"config.app_id":[{required:!0,message:"请输入支付宝应用ID"}],"config.private_key":[{required:!0,message:"请输入支付宝应用私钥"}],"config.ali_public_key":[{required:!0,message:"请输入支付宝公钥"}]},D=w,P=(s,e,p)=>{c.value=!0,U.value=p||"profile",C.value=e||!1,q(s),E.value=s,f.value=!0},q=async s=>{const e=await ie({id:s});e.config||(e.config=t.value.config),c.value=!1,t.value=e},B=s=>{O.confirm("修改还未保存，确认退出编辑吗？").then(()=>{s()}).catch(()=>{})},F=()=>{var s;(s=v.value)==null||s.resetFields()},R=async()=>{var s;await((s=v.value)==null?void 0:s.validate()),await ne(t.value),f.value=!1,D("refresh")},{isLock:N,lockFn:A}=re(R);return b({openHandle:P}),(s,e)=>{const p=te,r=oe,d=Q,L=ee,g=ae,S=W,K=X,G=Y,H=Z;return y(),z(G,{modelValue:l(f),"onUpdate:modelValue":e[14]||(e[14]=a=>J(f)?f.value=a:null),"destroy-on-close":"",title:`${l(_)}配置`,direction:"rtl",size:"50%",onClose:F,"before-close":B},{default:i(()=>[$((y(),V("div",de,[o(l(le),{ref_key:"formRef",ref:v,model:l(t),"label-width":"9rem",rules:I},{default:i(()=>[o(r,{label:"支付方式"},{default:i(()=>[o(p,{value:l(_),"model-value":l(_)},{default:i(()=>[u(j(l(_)),1)]),_:1},8,["value","model-value"])]),_:1}),o(r,{label:"显示名称",prop:"name"},{default:i(()=>[o(d,{modelValue:l(t).name,"onUpdate:modelValue":e[0]||(e[0]=a=>l(t).name=a),placeholder:"请输入显示名称",style:{"max-width":"250px"}},null,8,["modelValue"])]),_:1}),o(r,{label:"图标",prop:"image"},{default:i(()=>[n("div",null,[o(L,{limit:1,disabled:!1,modelValue:l(t).icon,"onUpdate:modelValue":e[1]||(e[1]=a=>l(t).icon=a)},null,8,["modelValue"]),e[15]||(e[15]=n("div",{class:"form-tips"},"建议尺寸：200*200px",-1))])]),_:1}),l(t).pay_way==2?(y(),V(k,{key:0},[o(r,{prop:"config.interface_version",label:"微信支付接口版本"},{default:i(()=>[n("div",null,[o(g,{modelValue:l(t).config.interface_version,"onUpdate:modelValue":e[2]||(e[2]=a=>l(t).config.interface_version=a)},{default:i(()=>[o(p,{value:"v3"},{default:i(()=>e[16]||(e[16]=[u("V3")])),_:1})]),_:1},8,["modelValue"]),e[17]||(e[17]=n("div",{class:"form-tips"},"暂时只支持V3版本",-1))])]),_:1}),o(r,{label:"商户类型",prop:"config.merchant_type"},{default:i(()=>[n("div",null,[o(g,{modelValue:l(t).config.merchant_type,"onUpdate:modelValue":e[3]||(e[3]=a=>l(t).config.merchant_type=a)},{default:i(()=>[o(p,{value:"ordinary_merchant"},{default:i(()=>e[18]||(e[18]=[u("普通商户")])),_:1})]),_:1},8,["modelValue"]),e[19]||(e[19]=n("div",{class:"form-tips"}," 暂时只支持普通商户类型，服务商户类型模式暂不支持 ",-1))])]),_:1}),o(r,{label:"微信支付商户号",prop:"config.mch_id"},{default:i(()=>[n("div",null,[o(d,{modelValue:l(t).config.mch_id,"onUpdate:modelValue":e[4]||(e[4]=a=>l(t).config.mch_id=a),placeholder:"请输入微信支付商户号",style:{"max-width":"250px"}},null,8,["modelValue"]),e[20]||(e[20]=n("div",{class:"form-tips"},"微信支付商户号（MCHID）",-1))])]),_:1}),o(r,{label:"商户API密钥",prop:"config.pay_sign_key"},{default:i(()=>[n("div",null,[o(d,{modelValue:l(t).config.pay_sign_key,"onUpdate:modelValue":e[5]||(e[5]=a=>l(t).config.pay_sign_key=a),placeholder:"请输入微信支付商户API密钥",style:{"max-width":"250px"}},null,8,["modelValue"]),e[21]||(e[21]=n("div",{class:"form-tips"},"微信支付商户API密钥（paySignKey）",-1))])]),_:1}),o(r,{label:"微信支付证书",prop:"config.apiclient_cert"},{default:i(()=>[n("div",null,[o(d,{type:"textarea",rows:3,modelValue:l(t).config.apiclient_cert,"onUpdate:modelValue":e[6]||(e[6]=a=>l(t).config.apiclient_cert=a),placeholder:"请输入微信支付证书",style:{"max-width":"400px"}},null,8,["modelValue"]),e[22]||(e[22]=n("div",{class:"form-tips"}," 微信支付证书（apiclient_cert.pem），前往微信商家平台生成并黏贴至此处 ",-1))])]),_:1}),o(r,{label:"微信支付证书密钥",prop:"config.apiclient_key"},{default:i(()=>[n("div",null,[o(d,{type:"textarea",rows:3,modelValue:l(t).config.apiclient_key,"onUpdate:modelValue":e[7]||(e[7]=a=>l(t).config.apiclient_key=a),placeholder:"请输入微信支付证书密钥",style:{"max-width":"400px"}},null,8,["modelValue"]),e[23]||(e[23]=n("div",{class:"form-tips"}," 微信支付证书密钥（apiclient_key.pem），前往微信商家平台生成并黏贴至此处 ",-1))])]),_:1}),o(r,{label:"支付授权目录"},{default:i(()=>e[24]||(e[24]=[n("div",null,[n("div",null,[n("span",{class:"mr-[20px]"},"https://前台手机域名地址/")])],-1)])),_:1})],64)):x("",!0),l(t).pay_way==3?(y(),V(k,{key:1},[o(r,{label:"模式",prop:"config.mode"},{default:i(()=>[n("div",null,[o(g,{modelValue:l(t).config.mode,"onUpdate:modelValue":e[8]||(e[8]=a=>l(t).config.mode=a)},{default:i(()=>[o(p,{value:"normal_mode"},{default:i(()=>e[25]||(e[25]=[u("普通模式")])),_:1})]),_:1},8,["modelValue"]),e[26]||(e[26]=n("div",{class:"form-tips"},"暂时仅支持支付宝普通模式",-1))])]),_:1}),o(r,{label:"商户类型",prop:"config.merchant_type"},{default:i(()=>[n("div",null,[o(g,{modelValue:l(t).config.merchant_type,"onUpdate:modelValue":e[9]||(e[9]=a=>l(t).config.merchant_type=a)},{default:i(()=>[o(p,{value:"ordinary_merchant"},{default:i(()=>e[27]||(e[27]=[u("普通商户")])),_:1})]),_:1},8,["modelValue"]),e[28]||(e[28]=n("div",{class:"form-tips"}," 暂时只支持普通商户类型，服务商户类型模式暂不支持 ",-1))])]),_:1}),o(r,{label:"应用ID",prop:"config.app_id"},{default:i(()=>[n("div",pe,[o(d,{modelValue:l(t).config.app_id,"onUpdate:modelValue":e[10]||(e[10]=a=>l(t).config.app_id=a),placeholder:"请输入支付宝应用ID",style:{"max-width":"250px"}},null,8,["modelValue"]),e[29]||(e[29]=n("div",{class:"form-tips"},"支付宝应用APP_ID",-1))])]),_:1}),o(r,{label:"应用私钥",prop:"config.private_key"},{default:i(()=>[n("div",me,[o(d,{type:"textarea",rows:3,modelValue:l(t).config.private_key,"onUpdate:modelValue":e[11]||(e[11]=a=>l(t).config.private_key=a),placeholder:"请输入支付宝应用私钥",style:{"max-width":"400px"}},null,8,["modelValue"]),e[30]||(e[30]=n("div",{class:"form-tips"},"支付宝应用私钥（private_key）",-1))])]),_:1}),o(r,{label:"支付宝公钥",prop:"config.ali_public_key"},{default:i(()=>[n("div",ue,[o(d,{type:"textarea",rows:3,modelValue:l(t).config.ali_public_key,"onUpdate:modelValue":e[12]||(e[12]=a=>l(t).config.ali_public_key=a),placeholder:"请输入支付宝公钥",style:{"max-width":"400px"}},null,8,["modelValue"]),e[31]||(e[31]=n("div",{class:"form-tips"},"支付宝公钥（alipayCertPublicKey）",-1))])]),_:1})],64)):x("",!0),o(r,{label:"排序",prop:"sort"},{default:i(()=>[n("div",null,[o(S,{modelValue:l(t).sort,"onUpdate:modelValue":e[13]||(e[13]=a=>l(t).sort=a),min:0,max:9999},null,8,["modelValue"]),e[32]||(e[32]=n("div",{class:"form-tips"},"默认为0， 数值越大越排前",-1))])]),_:1}),o(r,null,{default:i(()=>[o(K,{type:"primary",loading:l(N),onClick:l(A)},{default:i(()=>e[33]||(e[33]=[u("保存")])),_:1},8,["loading","onClick"])]),_:1})]),_:1},8,["model"])])),[[H,l(c)]])]),_:1},8,["modelValue","title"])}}}),ze=h(fe,[["__scopeId","data-v-65ce6610"]]);export{ze as default};
