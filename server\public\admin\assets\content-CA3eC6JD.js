import r from"./decoration-img-2F0tdl1c.js";import{d as i,o,a as s,b as t,t as c,m,G as a,p as l,F as x,x as d}from"./index-B2xNDy79.js";const f={class:"customer-service bg-white flex flex-col justify-center items-center mx-[18px] mt-[15px] rounded-[10px] px-[10px] pb-[50px]"},u={class:"w-full border-solid border-0 border-b border-[#f5f5f5] p-[15px] text-center text-[#101010] text-base font-medium"},p={class:"mt-[30px]"},b={key:0,class:"text-sm mt-[20px] font-medium"},h={key:1,class:"text-sm mt-[12px] flex flex-wrap"},w=["href"],v={key:2,class:"text-muted text-sm mt-[15px]"},y=i({__name:"content",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(e){return(g,n)=>(o(),s(x,null,[n[0]||(n[0]=t("view",{class:"bg-white p-[15px] flex text-[#101010] font-medium text-lg"}," 联系我们 ",-1)),t("view",f,[t("view",u,c(e.content.title),1),t("view",p,[m(r,{width:"100px",height:"100px",src:e.content.qrcode,alt:""},null,8,["src"])]),e.content.remark?(o(),s("view",b,c(e.content.remark),1)):a("",!0),e.content.mobile?(o(),s("view",h,[t("a",{class:"ml-[5px] phone text-primary underline",href:"tel:"+e.content.mobile},c(e.content.mobile),9,w)])):a("",!0),e.content.time?(o(),s("view",v," 服务时间："+c(e.content.time),1)):a("",!0)]),n[1]||(n[1]=l(" Î "))],64))}}),V=d(y,[["__scopeId","data-v-7a551d8a"]]);export{V as default};
