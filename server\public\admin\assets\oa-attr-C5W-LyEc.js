import{_ as B}from"./index-BuNto3DN.js";import{P as M}from"./index-DFOp_83R.js";import{d as A,s as R,O as h,o as l,a as d,r as y,B as $,R as N,e as t,b as s,m as o,w as a,F as g,t as v,p as b,S as O,v as V,x as F}from"./index-B2xNDy79.js";import{_ as L}from"./oa-menu-form.vue_vue_type_script_setup_true_lang-uZC5lSoG.js";import{_ as U}from"./oa-menu-form-edit.vue_vue_type_script_setup_true_lang-Vx-dPtFc.js";import{u as j}from"./useMenuOa-CrPjRcCC.js";import"./index-C6Cr8aHe.js";import"./el-form-item-DlU85AZK.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";/* empty css                       */import"./el-radio-CKcO4hVq.js";import"./wx_oa-3-DCeMZg.js";const q={class:"flex items-center w-full p-4 mt-4 rounded bg-fill-light"},z={class:"flex-1"},G={class:"mr-auto"},H=A({__name:"oa-attr",setup(J){const _=R(),{menuList:x,menuIndex:i,handleAddSubMenu:T,handleEditSubMenu:k,handleDelMenu:w,handleDelSubMenu:E}=j(_);return(K,p)=>{const P=h("EditPen"),m=O,u=V,C=h("Delete"),D=M,S=B;return l(!0),d(g,null,y(t(x),(e,f)=>$((l(),d("div",{key:f,class:"flex-1 oa-attr min-w-0"},[p[2]||(p[2]=s("div",{class:"text-base oa-attr-title"},"菜单配置",-1)),o(S,{onClose:p[0]||(p[0]=n=>t(w)(t(i)))},{default:a(()=>[s("div",q,[o(L,{ref_for:!0,ref_key:"menuRef",ref:_,modular:"master",name:e.name,"onUpdate:name":n=>e.name=n,menuType:e.has_menu,"onUpdate:menuType":n=>e.has_menu=n,visitType:e.type,"onUpdate:visitType":n=>e.type=n,url:e.url,"onUpdate:url":n=>e.url=n,appId:e.appid,"onUpdate:appId":n=>e.appid=n,pagePath:e.pagepath,"onUpdate:pagePath":n=>e.pagepath=n},{default:a(()=>[s("div",z,[s("ul",null,[(l(!0),d(g,null,y(e.sub_button,(n,r)=>(l(),d("li",{class:"flex",key:r,style:{padding:"8px"}},[s("span",G,v(n.name),1),o(U,{modular:"edit",subItem:n,onEdit:c=>t(k)(c,r)},{default:a(()=>[o(u,{link:""},{default:a(()=>[o(m,null,{default:a(()=>[o(P)]),_:1})]),_:1})]),_:2},1032,["subItem","onEdit"]),o(D,{onConfirm:c=>t(E)(t(i),r)},{trigger:a(()=>[o(u,{link:""},{default:a(()=>[o(m,{class:"ml-5"},{default:a(()=>[o(C)]),_:1})]),_:1})]),default:a(()=>[p[1]||(p[1]=b(" 是否删除当前子菜单？ "))]),_:2},1032,["onConfirm"])]))),128))]),o(U,{modular:"add",onAdd:t(T)},{default:a(()=>[o(u,{type:"primary",link:"",disabled:e.sub_button.length>=5},{default:a(()=>[b(" 添加子菜单("+v(e.sub_button.length)+"/5) ",1)]),_:2},1032,["disabled"])]),_:2},1032,["onAdd"])])]),_:2},1032,["name","onUpdate:name","menuType","onUpdate:menuType","visitType","onUpdate:visitType","url","onUpdate:url","appId","onUpdate:appId","pagePath","onUpdate:pagePath"])])]),_:2},1024)],512)),[[N,f===t(i)]])),128)}}}),de=F(H,[["__scopeId","data-v-28c2cd0d"]]);export{de as default};
