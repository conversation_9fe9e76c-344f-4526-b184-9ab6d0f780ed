import{_ as F}from"./index-onOHNH0j.js";import{d as z,i as C,z as G,o as e,a as l,b as n,B as I,C as m,w as a,p as s,F as E,r as J,G as d,m as r,e as f,t as L,eW as P,v as R,ea as $,J as j}from"./index-B2xNDy79.js";import{E as q}from"./el-card-DpH4mUSc.js";import{E as K,a as M}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as O}from"./el-radio-CKcO4hVq.js";import{b as Q,c as X}from"./pay-Bumb48yC.js";import{E as Y}from"./index-CcX0CyWL.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./_initCloneObject-C-h6JGU9.js";const Z={key:0,class:"text-lg mb-[24px]"},tt={key:1,class:"text-lg mb-[24px]"},et={key:2,class:"text-lg mb-[24px]"},at={key:3,class:"text-lg mb-[24px]"},lt={key:4,class:"text-lg mb-[24px]"},st={key:1},ot={key:1},gt=z({__name:"index",setup(nt){const i=C({}),u=C(!1);let v={};const x=async()=>{i.value=await Q(),v=P.cloneDeep(i.value)},w=()=>{u.value=!0},V=(g,t)=>{i.value[t].forEach(_=>{_.is_default=0}),i.value[t][g].is_default=1},h=()=>{i.value=P.cloneDeep(v),u.value=!1},W=async()=>{await X(i.value),u.value=!1,x()};return x(),(g,t)=>{const _=R,B=$,y=K,D=O,N=Y,S=j,T=M,U=q,H=F,A=G("perms");return e(),l("div",null,[n("div",null,[I((e(),m(_,{type:"primary",onClick:w},{default:a(()=>t[0]||(t[0]=[s(" 设置支付方式 ")])),_:1})),[[A,["setting.pay.pay_way/setPayWay"]]])]),(e(!0),l(E,null,J(f(i),(b,p)=>(e(),m(U,{shadow:"never",class:"mt-4 !border-none",key:p},{default:a(()=>[n("div",null,[p==1?(e(),l("div",Z,t[1]||(t[1]=[s(" 微信小程序 "),n("span",{class:"form-tips ml-[10px]"},"在微信小程序中付款的场景",-1)]))):d("",!0),p==2?(e(),l("div",tt,t[2]||(t[2]=[s(" 微信公众号 "),n("span",{class:"form-tips ml-[10px]"}," 在微信公众号H5页面中付款的场景，公众号类型一般为服务号 ",-1)]))):d("",!0),p==3?(e(),l("div",et,t[3]||(t[3]=[s(" H5支付 "),n("span",{class:"form-tips ml-[10px]"},"在浏览器H5页面中付款的场景",-1)]))):d("",!0),p==4?(e(),l("div",at,t[4]||(t[4]=[s(" PC支付 "),n("span",{class:"form-tips ml-[10px]"},"在浏览器PC页面中付款的场景",-1)]))):d("",!0),p==5?(e(),l("div",lt,t[5]||(t[5]=[s(" APP支付 "),n("span",{class:"form-tips ml-[10px]"},"在APP付款的场景",-1)]))):d("",!0),b.length?(e(),m(T,{key:5,data:b,style:{width:"100%"}},{default:a(()=>[r(y,{label:"图标","min-width":"150"},{default:a(({row:o})=>[r(B,{src:o.icon,alt:"图标",style:{width:"34px",height:"34px"}},null,8,["src"])]),_:1}),r(y,{prop:"pay_way_name",label:"支付方式","min-width":"150"}),r(y,{label:"默认支付","min-width":"150"},{default:a(({row:o,$index:c})=>[n("div",null,[f(u)?(e(),m(D,{key:0,modelValue:o.is_default,"onUpdate:modelValue":k=>o.is_default=k,label:1,onChange:k=>V(c,p)},{default:a(()=>t[6]||(t[6]=[s(" 设为默认 ")])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])):(e(),l(E,{key:1},[o.is_default==1?(e(),m(N,{key:0},{default:a(()=>t[7]||(t[7]=[s("默认")])),_:1})):(e(),l("span",st,"-"))],64))])]),_:2},1024),r(y,{label:"开启状态","min-width":"150"},{default:a(({row:o})=>[f(u)?(e(),m(S,{key:0,modelValue:o.status,"onUpdate:modelValue":c=>o.status=c,"active-value":1,"inactive-value":0},null,8,["modelValue","onUpdate:modelValue"])):(e(),l("span",ot,L(o.status==1?"开启":"关闭"),1))]),_:1})]),_:2},1032,["data"])):d("",!0)])]),_:2},1024))),128)),f(u)?(e(),m(H,{key:0},{default:a(()=>[r(_,{onClick:h},{default:a(()=>t[8]||(t[8]=[s("取消")])),_:1}),r(_,{type:"primary",onClick:W},{default:a(()=>t[9]||(t[9]=[s("保存")])),_:1})]),_:1})):d("",!0)])}}});export{gt as default};
