import{at as N,b_ as $,b$ as g,d as S,am as D,an as I,i as V,c as p,o as a,C as l,w as d,B as A,b,Y as o,e,S as k,aq as F,G as n,a as i,U as C,p as h,t as f,F as M,m as P,R as j,bj as q,ar as z,c0 as G,ay as O}from"./index-B2xNDy79.js";const R=["light","dark"],U=N({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:$(g),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:Boolean,center:Boolean,effect:{type:String,values:R,default:"light"}}),Y={close:r=>r instanceof MouseEvent},_=S({name:"ElAlert"}),H=S({..._,props:U,emits:Y,setup(r,{emit:w}){const c=r,{Close:B}=G,u=D(),t=I("alert"),m=V(!0),y=p(()=>g[c.type]),E=p(()=>[t.e("icon"),{[t.is("big")]:!!c.description||!!u.default}]),T=p(()=>({"with-description":c.description||u.default})),v=s=>{m.value=!1,w("close",s)};return(s,K)=>(a(),l(q,{name:e(t).b("fade"),persisted:""},{default:d(()=>[A(b("div",{class:o([e(t).b(),e(t).m(s.type),e(t).is("center",s.center),e(t).is(s.effect)]),role:"alert"},[s.showIcon&&e(y)?(a(),l(e(k),{key:0,class:o(e(E))},{default:d(()=>[(a(),l(F(e(y))))]),_:1},8,["class"])):n("v-if",!0),b("div",{class:o(e(t).e("content"))},[s.title||s.$slots.title?(a(),i("span",{key:0,class:o([e(t).e("title"),e(T)])},[C(s.$slots,"title",{},()=>[h(f(s.title),1)])],2)):n("v-if",!0),s.$slots.default||s.description?(a(),i("p",{key:1,class:o(e(t).e("description"))},[C(s.$slots,"default",{},()=>[h(f(s.description),1)])],2)):n("v-if",!0),s.closable?(a(),i(M,{key:2},[s.closeText?(a(),i("div",{key:0,class:o([e(t).e("close-btn"),e(t).is("customed")]),onClick:v},f(s.closeText),3)):(a(),l(e(k),{key:1,class:o(e(t).e("close-btn")),onClick:v},{default:d(()=>[P(e(B))]),_:1},8,["class"]))],64)):n("v-if",!0)],2)],2),[[j,m.value]])]),_:3},8,["name"]))}});var J=z(H,[["__file","alert.vue"]]);const Q=O(J);export{Q as E};
