# LikeAdmin Docker 部署指南

## 项目结构

```
docker/
├── config/                 # 配置文件目录
│   ├── nginx/              # Nginx 配置
│   │   ├── nginx.conf      # Nginx 主配置
│   │   └── conf.d/         # 站点配置
│   │       └── default.conf
│   ├── php/                # PHP 配置
│   │   ├── php.ini         # PHP 主配置
│   │   ├── php-fpm.conf    # PHP-FPM 配置
│   │   └── www.conf        # PHP-FPM 池配置
│   ├── mysql/              # MySQL 配置
│   │   └── my.cnf          # MySQL 配置文件
│   └── redis/              # Redis 配置
│       └── redis.conf      # Redis 配置文件
├── data/                   # 数据持久化目录
│   ├── mysql/              # MySQL 数据目录
│   └── redis/              # Redis 数据目录
└── logs/                   # 日志目录
    ├── nginx/              # Nginx 日志
    ├── php/                # PHP 日志
    ├── mysql/              # MySQL 日志
    └── redis/              # Redis 日志
```

## 部署步骤

### 1. 准备工作

确保服务器已安装 Docker 和 Docker Compose：

```bash
# 安装 Docker
curl -fsSL https://get.docker.com | bash

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 上传项目文件

将整个项目目录上传到服务器，确保包含：
- `docker-compose.yml`
- `docker/` 目录及其所有配置文件
- `server/` 目录（ThinkPHP 后端代码）
- `admin/dist/` 目录（管理后台前端构建文件）
- `pc/dist/` 目录（PC端前端构建文件）

### 3. 配置环境变量

```bash
# 复制环境配置文件
cp .env.docker .env

# 根据需要编辑配置
vim .env
```

### 4. 设置目录权限

```bash
# 设置数据目录权限
sudo chown -R 999:999 docker/data/mysql
sudo chown -R 999:999 docker/data/redis

# 设置日志目录权限
sudo chown -R 999:999 docker/logs

# 设置 PHP 代码目录权限
sudo chown -R www-data:www-data server/runtime
sudo chmod -R 755 server/runtime
```

### 5. 启动服务

```bash
# 一键启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 6. 初始化数据库

首次部署时，需要导入数据库：

```bash
# 进入 MySQL 容器
docker exec -it likeadmin-mysql mysql -uroot -proot

# 在 MySQL 中执行
USE localhost_likeadmin;
SOURCE /docker-entrypoint-initdb.d/your_sql_file.sql;
```

## 服务访问

- **Web 应用**: http://your-server-ip
- **管理后台**: http://your-server-ip/admin
- **PC端**: http://your-server-ip/pc
- **MySQL**: your-server-ip:3306
- **Redis**: your-server-ip:6379

## 常用命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看日志
docker-compose logs -f [service_name]

# 进入容器
docker exec -it likeadmin-php sh
docker exec -it likeadmin-mysql bash
docker exec -it likeadmin-redis sh

# 备份数据库
docker exec likeadmin-mysql mysqldump -uroot -proot localhost_likeadmin > backup.sql

# 恢复数据库
docker exec -i likeadmin-mysql mysql -uroot -proot localhost_likeadmin < backup.sql
```

## 注意事项

1. **安全设置**: 生产环境请修改默认密码
2. **防火墙**: 确保开放必要端口（80, 443）
3. **SSL证书**: 生产环境建议配置 HTTPS
4. **备份**: 定期备份 `docker/data/` 目录
5. **监控**: 建议配置日志监控和告警

## 故障排除

### 常见问题

1. **权限问题**: 检查目录权限设置
2. **端口冲突**: 检查端口是否被占用
3. **内存不足**: 调整 PHP 和 MySQL 内存配置
4. **网络问题**: 检查 Docker 网络配置

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs nginx
docker-compose logs php
docker-compose logs mysql
docker-compose logs redis
```
