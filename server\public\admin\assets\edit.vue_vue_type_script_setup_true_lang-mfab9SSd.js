import{d as C,s as _,i as D,c as U,j as F,o as N,a as q,m as o,w as t,e as a,b as c,p as v,E as B,L as I}from"./index-B2xNDy79.js";import{E as P,a as T}from"./el-form-item-DlU85AZK.js";/* empty css                       */import{E as j,a as z}from"./el-radio-CKcO4hVq.js";import{a as A,b as G}from"./dict-DX85lXc6.js";import{P as L}from"./index-DFOp_83R.js";const S={class:"edit-popup"},Z=C({__name:"edit",emits:["success","close"],setup(H,{expose:b,emit:V}){const p=V,i=_(),n=_(),d=D("add"),w=U(()=>d.value=="edit"?"编辑字典数据":"新增字典数据"),l=F({id:"",type_value:"",name:"",value:"",sort:0,status:1,remark:"",type_id:0}),x={name:[{required:!0,message:"请输入数据名称",trigger:["blur"]}],value:[{required:!0,message:"请输入数据值",trigger:["blur"]}]},E=async()=>{var s,e;await((s=i.value)==null?void 0:s.validate()),d.value=="edit"?await A(l):await G(l),(e=n.value)==null||e.close(),p("success")},k=()=>{p("close")};return b({open:(s="add")=>{var e;d.value=s,(e=n.value)==null||e.open()},setFormData:s=>{for(const e in l)s[e]!=null&&s[e]!=null&&(l[e]=s[e])}}),(s,e)=>{const m=B,u=P,y=I,f=j,R=z,g=T;return N(),q("div",S,[o(L,{ref_key:"popupRef",ref:n,title:a(w),async:!0,width:"550px",onConfirm:E,onClose:k},{default:t(()=>[o(g,{class:"ls-form",ref_key:"formRef",ref:i,rules:x,model:a(l),"label-width":"84px"},{default:t(()=>[o(u,{label:"字典类型"},{default:t(()=>[o(m,{"model-value":a(l).type_value,placeholder:"请输入字典类型",disabled:""},null,8,["model-value"])]),_:1}),o(u,{label:"数据名称",prop:"name"},{default:t(()=>[o(m,{modelValue:a(l).name,"onUpdate:modelValue":e[0]||(e[0]=r=>a(l).name=r),placeholder:"请输入数据名称",clearable:""},null,8,["modelValue"])]),_:1}),o(u,{label:"数据值",prop:"value"},{default:t(()=>[o(m,{modelValue:a(l).value,"onUpdate:modelValue":e[1]||(e[1]=r=>a(l).value=r),placeholder:"请输入数据值",clearable:""},null,8,["modelValue"])]),_:1}),o(u,{label:"排序",prop:"sort"},{default:t(()=>[c("div",null,[o(y,{modelValue:a(l).sort,"onUpdate:modelValue":e[2]||(e[2]=r=>a(l).sort=r),min:0,max:9999},null,8,["modelValue"]),e[5]||(e[5]=c("div",{class:"form-tips"},"数值越大越排前",-1))])]),_:1}),o(u,{label:"状态",required:"",prop:"status"},{default:t(()=>[o(R,{modelValue:a(l).status,"onUpdate:modelValue":e[3]||(e[3]=r=>a(l).status=r)},{default:t(()=>[o(f,{value:1},{default:t(()=>e[6]||(e[6]=[v("正常")])),_:1}),o(f,{value:0},{default:t(()=>e[7]||(e[7]=[v("停用")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"备注",prop:"remark"},{default:t(()=>[o(m,{modelValue:a(l).remark,"onUpdate:modelValue":e[4]||(e[4]=r=>a(l).remark=r),type:"textarea",autosize:{minRows:4,maxRows:6},clearable:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{Z as _};
