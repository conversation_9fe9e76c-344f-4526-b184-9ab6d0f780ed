import{d as C,s as f,i as U,c as h,j as D,o as q,a as B,m as l,w as n,e as t,b as _,E as F,L as I,J as N}from"./index-B2xNDy79.js";import{E as P,a as S}from"./el-form-item-DlU85AZK.js";import{j as z,a as A,b as J}from"./post-zozQIMRx.js";import{P as L}from"./index-DFOp_83R.js";const T={class:"edit-popup"},Q=C({__name:"edit",emits:["success","close"],setup(G,{expose:b,emit:V}){const i=V,p=f(),m=f(),u=U("add"),v=h(()=>u.value=="edit"?"编辑岗位":"新增岗位"),o=D({id:"",name:"",code:"",sort:0,remark:"",status:1}),w={code:[{required:!0,message:"请输入岗位编码",trigger:["blur"]}],name:[{required:!0,message:"请输入岗位名称",trigger:["blur"]}]},x=async()=>{var a,e;await((a=p.value)==null?void 0:a.validate()),u.value=="edit"?await z(o):await A(o),(e=m.value)==null||e.close(),i("success")},g=(a="add")=>{var e;u.value=a,(e=m.value)==null||e.open()},c=a=>{for(const e in o)a[e]!=null&&a[e]!=null&&(o[e]=a[e])},k=async a=>{const e=await J({id:a.id});c(e)},E=()=>{i("close")};return b({open:g,setFormData:c,getDetail:k}),(a,e)=>{const d=F,r=P,R=I,y=N,j=S;return q(),B("div",T,[l(L,{ref_key:"popupRef",ref:m,title:t(v),async:!0,width:"550px",onConfirm:x,onClose:E},{default:n(()=>[l(j,{ref_key:"formRef",ref:p,model:t(o),"label-width":"84px",rules:w},{default:n(()=>[l(r,{label:"岗位名称",prop:"name"},{default:n(()=>[l(d,{modelValue:t(o).name,"onUpdate:modelValue":e[0]||(e[0]=s=>t(o).name=s),placeholder:"请输入岗位名称",clearable:"",maxlength:100},null,8,["modelValue"])]),_:1}),l(r,{label:"岗位编码",prop:"code"},{default:n(()=>[l(d,{modelValue:t(o).code,"onUpdate:modelValue":e[1]||(e[1]=s=>t(o).code=s),placeholder:"请输入岗位编码",clearable:""},null,8,["modelValue"])]),_:1}),l(r,{label:"排序",prop:"sort"},{default:n(()=>[_("div",null,[l(R,{modelValue:t(o).sort,"onUpdate:modelValue":e[2]||(e[2]=s=>t(o).sort=s),min:0,max:9999},null,8,["modelValue"]),e[5]||(e[5]=_("div",{class:"form-tips"},"默认为0， 数值越大越排前",-1))])]),_:1}),l(r,{label:"备注",prop:"remark"},{default:n(()=>[l(d,{modelValue:t(o).remark,"onUpdate:modelValue":e[3]||(e[3]=s=>t(o).remark=s),placeholder:"请输入备注",type:"textarea",autosize:{minRows:4,maxRows:6},maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(r,{label:"岗位状态",required:"",prop:"status"},{default:n(()=>[l(y,{modelValue:t(o).status,"onUpdate:modelValue":e[4]||(e[4]=s=>t(o).status=s),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{Q as _};
