import{a as j,E as I}from"./el-select-BRdnbwTl.js";import{E as w}from"./el-tree-8o9N7gsQ.js";import{an as B,V as S,H as x,aw as M,ax as q,a5 as E,c as g,d as P,ae as D,c1 as F,aD as U,ek as $,eA as G,a9 as J,bD as Q,i as R,j as T,k as W,bx as L,ar as X,ay as Y}from"./index-B2xNDy79.js";import{i as Z}from"./isEqual-CLGO95LP.js";import{s as ee}from"./token-DI9FKtlJ.js";const te=(e,{attrs:n,emit:o},{select:r,tree:s,key:c})=>{const h=B("tree-select");return S(()=>e.data,()=>{e.filterable&&x(()=>{var t,v;(v=s.value)==null||v.filter((t=r.value)==null?void 0:t.states.inputValue)})},{flush:"post"}),{...M(q(e),Object.keys(j.props)),...n,"onUpdate:modelValue":t=>o(E,t),valueKey:c,popperClass:g(()=>{const t=[h.e("popper")];return e.popperClass&&t.push(e.popperClass),t.join(" ")}),filterMethod:(t="")=>{var v;e.filterMethod?e.filterMethod(t):e.remoteMethod?e.remoteMethod(t):(v=s.value)==null||v.filter(t)}}},le=P({extends:I,setup(e,n){const o=I.setup(e,n);delete o.selectOptionClick;const r=D().proxy;return x(()=>{o.select.states.cachedOptions.get(r.value)||o.select.onOptionCreate(r)}),S(()=>n.attrs.visible,s=>{o.states.visible=s},{immediate:!0}),o},methods:{selectOptionClick(){this.$el.parentElement.click()}}});function H(e){return e||e===0}function p(e){return Array.isArray(e)&&e.length}function b(e){return Array.isArray(e)?e:H(e)?[e]:[]}function O(e,n,o,r,s){for(let c=0;c<e.length;c++){const h=e[c];if(n(h,c,e,s))return r?r(h,c,e,s):h;{const k=o(h);if(p(k)){const t=O(k,n,o,r,h);if(t)return t}}}}function A(e,n,o,r){for(let s=0;s<e.length;s++){const c=e[s];n(c,s,e,r);const h=o(c);p(h)&&A(h,n,o,c)}}const ae=(e,{attrs:n,slots:o,emit:r},{select:s,tree:c,key:h})=>{S(()=>e.modelValue,()=>{e.showCheckbox&&x(()=>{const l=c.value;l&&!Z(l.getCheckedKeys(),b(e.modelValue))&&l.setCheckedKeys(b(e.modelValue))})},{immediate:!0,deep:!0});const k=g(()=>({value:h.value,label:"label",children:"children",disabled:"disabled",isLeaf:"isLeaf",...e.props})),t=(l,a)=>{var i;const u=k.value[l];return U(u)?u(a,(i=c.value)==null?void 0:i.getNode(t("value",a))):a[u]},v=b(e.modelValue).map(l=>O(e.data||[],a=>t("value",a)===l,a=>t("children",a),(a,i,u,f)=>f&&t("value",f))).filter(l=>H(l)),_=g(()=>{if(!e.renderAfterExpand&&!e.lazy)return[];const l=[];return A(e.data.concat(e.cacheData),a=>{const i=t("value",a);l.push({value:i,currentLabel:t("label",a),isDisabled:t("disabled",a)})},a=>t("children",a)),l}),K=()=>{var l;return(l=c.value)==null?void 0:l.getCheckedKeys().filter(a=>{var i;const u=(i=c.value)==null?void 0:i.getNode(a);return!$(u)&&G(u.childNodes)})};return{...M(q(e),Object.keys(w.props)),...n,nodeKey:h,expandOnClickNode:g(()=>!e.checkStrictly&&e.expandOnClickNode),defaultExpandedKeys:g(()=>e.defaultExpandedKeys?e.defaultExpandedKeys.concat(v):v),renderContent:(l,{node:a,data:i,store:u})=>l(le,{value:t("value",i),label:t("label",i),disabled:t("disabled",i),visible:a.visible},e.renderContent?()=>e.renderContent(l,{node:a,data:i,store:u}):o.default?()=>o.default({node:a,data:i,store:u}):void 0),filterNodeMethod:(l,a,i)=>e.filterNodeMethod?e.filterNodeMethod(l,a,i):l?new RegExp(F(l),"i").test(t("label",a)||""):!0,onNodeClick:(l,a,i)=>{var u,f,C,m;if((u=n.onNodeClick)==null||u.call(n,l,a,i),!(e.showCheckbox&&e.checkOnClickNode)){if(!e.showCheckbox&&(e.checkStrictly||a.isLeaf)){if(!t("disabled",l)){const N=(f=s.value)==null?void 0:f.states.options.get(t("value",l));(C=s.value)==null||C.handleOptionSelect(N)}}else e.expandOnClickNode&&i.proxy.handleExpandIconClick();(m=s.value)==null||m.focus()}},onCheck:(l,a)=>{var i;if(!e.showCheckbox)return;const u=t("value",l),f={};A([c.value.store.root],d=>f[d.key]=d,d=>d.childNodes);const C=a.checkedKeys,m=e.multiple?b(e.modelValue).filter(d=>!(d in f)&&!C.includes(d)):[],N=m.concat(C);if(e.checkStrictly)r(E,e.multiple?N:N.includes(u)?u:void 0);else if(e.multiple){const d=K();r(E,m.concat(d))}else{const d=O([l],y=>!p(t("children",y))&&!t("disabled",y),y=>t("children",y)),V=d?t("value",d):void 0,z=H(e.modelValue)&&!!O([l],y=>t("value",y)===e.modelValue,y=>t("children",y));r(E,V===e.modelValue||z?void 0:V)}x(()=>{var d;const V=b(e.modelValue);c.value.setCheckedKeys(V),(d=n.onCheck)==null||d.call(n,l,{checkedKeys:c.value.getCheckedKeys(),checkedNodes:c.value.getCheckedNodes(),halfCheckedKeys:c.value.getHalfCheckedKeys(),halfCheckedNodes:c.value.getHalfCheckedNodes()})}),(i=s.value)==null||i.focus()},onNodeExpand:(l,a,i)=>{var u;(u=n.onNodeExpand)==null||u.call(n,l,a,i),x(()=>{if(!e.checkStrictly&&e.lazy&&e.multiple&&a.checked){const f={},C=c.value.getCheckedKeys();A([c.value.store.root],d=>f[d.key]=d,d=>d.childNodes);const m=b(e.modelValue).filter(d=>!(d in f)&&!C.includes(d)),N=K();r(E,m.concat(N))}})},cacheOptions:_}};var ce=P({props:{data:{type:Array,default:()=>[]}},setup(e){const n=J(ee);return S(()=>e.data,()=>{var o;e.data.forEach(s=>{n.states.cachedOptions.has(s.value)||n.states.cachedOptions.set(s.value,s)});const r=((o=n.selectRef)==null?void 0:o.querySelectorAll("input"))||[];Q&&!Array.from(r).includes(document.activeElement)&&n.setSelected()},{flush:"post",immediate:!0}),()=>{}}});const ne=P({name:"ElTreeSelect",inheritAttrs:!1,props:{...j.props,...w.props,cacheData:{type:Array,default:()=>[]}},setup(e,n){const{slots:o,expose:r}=n,s=R(),c=R(),h=g(()=>e.nodeKey||e.valueKey||"value"),k=te(e,n,{select:s,tree:c,key:h}),{cacheOptions:t,...v}=ae(e,n,{select:s,tree:c,key:h}),_=T({});return r(_),W(()=>{Object.assign(_,{...M(c.value,["filter","updateKeyChildren","getCheckedNodes","setCheckedNodes","getCheckedKeys","setCheckedKeys","setChecked","getHalfCheckedNodes","getHalfCheckedKeys","getCurrentKey","getCurrentNode","setCurrentKey","setCurrentNode","getNode","remove","append","insertBefore","insertAfter"]),...M(s.value,["focus","blur"])})}),()=>L(j,T({...k,ref:K=>s.value=K}),{...o,default:()=>[L(ce,{data:t.value}),L(w,T({...v,ref:K=>c.value=K}))]})}});var se=X(ne,[["__file","tree-select.vue"]]);const he=Y(se);export{he as E};
