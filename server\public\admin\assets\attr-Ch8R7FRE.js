import{d as b,c,o as V,a as v,b as x,m as t,w as m,e as a,F as w,a1 as E,a2 as g}from"./index-B2xNDy79.js";import{a as k}from"./el-form-item-DlU85AZK.js";import{_ as n}from"./menu-set.vue_vue_type_script_setup_true_lang-BpSpHdih.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-BuNto3DN.js";import"./picker-qQ9YEtJl.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./article-Dwgm3r-g.js";import"./usePaging-Dm2wALfy.js";/* empty css                       */import"./el-radio-CKcO4hVq.js";import"./picker-Cd5l2hZ5.js";import"./index-BhVAe0P7.js";import"./index-DSiy6YVt.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";const Z=b({__name:"attr",props:{modelValue:{type:Object,default:()=>({nav:[],menu:{}})}},emits:["update:modelValue"],setup(i,{emit:s}){const u=i,d=s,o=c({get(){return u.modelValue},set(l){d("update:modelValue",l)}});return(l,e)=>{const p=E,f=g,_=k;return V(),v(w,null,[e[2]||(e[2]=x("div",{class:"title flex items-center before:w-[3px] before:h-[14px] before:block before:bg-primary before:mr-2"}," pc导航设置 ",-1)),t(_,{class:"mt-4","label-width":"70px"},{default:m(()=>[t(f,{"model-value":"nav"},{default:m(()=>[t(p,{label:"主导航设置",name:"nav"},{default:m(()=>[t(n,{modelValue:a(o).nav,"onUpdate:modelValue":e[0]||(e[0]=r=>a(o).nav=r)},null,8,["modelValue"])]),_:1}),t(p,{label:"菜单设置",name:"menu"},{default:m(()=>[t(n,{modelValue:a(o).menu,"onUpdate:modelValue":e[1]||(e[1]=r=>a(o).menu=r)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})],64)}}});export{Z as default};
