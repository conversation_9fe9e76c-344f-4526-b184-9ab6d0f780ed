FROM php:8.0-fpm-alpine

# 安装系统依赖
RUN apk add --no-cache \
    freetype-dev \
    libjpeg-turbo-dev \
    libpng-dev \
    libzip-dev \
    icu-dev \
    oniguruma-dev \
    autoconf \
    g++ \
    make

# 配置和安装 PHP 扩展
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        gd \
        pdo_mysql \
        mysqli \
        zip \
        intl \
        mbstring \
        opcache \
        bcmath

# 安装 Redis 扩展
RUN pecl install redis \
    && docker-php-ext-enable redis

# 创建日志目录
RUN mkdir -p /var/log/php \
    && chown -R www-data:www-data /var/log/php

# 创建会话目录
RUN mkdir -p /var/lib/php/sessions \
    && chown -R www-data:www-data /var/lib/php/sessions

# 创建 wsdl 缓存目录
RUN mkdir -p /var/lib/php/wsdlcache \
    && chown -R www-data:www-data /var/lib/php/wsdlcache

# 复制启动脚本
COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# 设置工作目录
WORKDIR /var/www/html

# 暴露端口
EXPOSE 9000

# 设置启动脚本
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]

# 启动 PHP-FPM
CMD ["php-fpm"]
