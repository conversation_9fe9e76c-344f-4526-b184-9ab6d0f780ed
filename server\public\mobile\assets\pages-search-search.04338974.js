import{d as e,c as a,o as s,b as t,w as l,f as r,x as c,e as o,l as h,g as u,h as n,F as i,p,t as d,G as _,z as m,V as f,ad as g,aH as x,aI as y,aJ as v,aK as w,aL as k,r as b,a as j}from"./index-561dd99e.js";import{_ as C}from"./page-meta.438f2c32.js";import{_ as V}from"./u-search.1f3b26b2.js";import{_ as z}from"./news-card.1749442f.js";import{_ as I}from"./z-paging.9764c1e2.js";import{_ as S}from"./_plugin-vue_export-helper.1b428a4d.js";import{g as U}from"./news.a3153aee.js";import"./u-icon.f1b72599.js";import"./u-image.e9ed38ca.js";import"./icon_visit.713e13e8.js";const B=S(e({__name:"suggest",props:{hot_search:{default:()=>({data:[],status:0})},his_search:{default:()=>[]}},emits:["search","clear"],setup(e,{emit:_}){const m=e,f=a((()=>m.hot_search.data.filter((e=>e.name)))),g=e=>{_("search",e)};return(a,m)=>{const x=p;return s(),t(x,{class:"suggest bg-white"},{default:l((()=>[r(" 热门搜索 "),1==e.hot_search.status&&c(f).length?(s(),t(x,{key:0,class:"hot"},{default:l((()=>[o(x,{class:"font-medium pl-[24rpx] pt-[26rpx] pb-[6rpx] text-lg"},{default:l((()=>[h("热门搜索")])),_:1}),o(x,{class:"w-full px-[24rpx]"},{default:l((()=>[(s(!0),u(i,null,n(c(f),((e,a)=>(s(),t(x,{key:a,class:"keyword truncate max-w-full",onClick:a=>g(e.name)},{default:l((()=>[h(d(e.name),1)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):r("v-if",!0),1==e.hot_search.status&&c(f).length&&e.his_search.length?(s(),t(x,{key:1,class:"mx-[24rpx] my-[40rpx] border-b border-solid border-0 border-light"})):r("v-if",!0),r(" 历史搜索 "),e.his_search.length?(s(),t(x,{key:2,class:"history"},{default:l((()=>[o(x,{class:"flex justify-between px-[24rpx] pb-[6rpx] pt-[26rpx]"},{default:l((()=>[o(x,{class:"text-lg font-medium"},{default:l((()=>[h("历史搜索")])),_:1}),o(x,{class:"text-xs text-muted",onClick:m[0]||(m[0]=()=>_("clear"))},{default:l((()=>[h("清空")])),_:1})])),_:1}),o(x,{class:"w-full px-[24rpx]"},{default:l((()=>[(s(!0),u(i,null,n(e.his_search,((e,a)=>(s(),t(x,{key:a,class:"keyword truncate",onClick:a=>g(e)},{default:l((()=>[h(d(e),1)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):r("v-if",!0)])),_:1})}}}),[["__scopeId","data-v-75bd7f36"]]),F=S(e({__name:"search",setup(e){const a=_({hot_search:{data:[],status:1},his_search:[],result:[],searching:!1}),c=m(""),h=f(),d=e=>{c.value=e,c.value&&(a.his_search.includes(c.value)||(a.his_search.unshift(c.value),g.set(x,a.his_search))),h.value.reload(),a.searching=!0},S=async()=>{(await k({title:"温馨提示",content:"是否清空历史记录？"})).confirm&&(g.set(x,""),a.his_search=[])},F=async(e,a)=>{try{const{lists:s}=await U({keyword:c.value,page_no:e,page_size:a});h.value.complete(s)}catch(s){console.log("报错=>",s),h.value.complete(!1)}};return(async()=>{try{a.hot_search=await w()}catch(e){console.log("获取热门搜索失败=>",e)}})(),a.his_search=g.get(x)||[],(e,_)=>{const m=b(j("page-meta"),C),f=b(j("u-search"),V),g=p,x=b(j("news-card"),z),w=b(j("z-paging"),I);return s(),u(i,null,[o(m,{"page-style":e.$theme.pageStyle},null,8,["page-style"]),o(g,{class:"search"},{default:l((()=>[r(" 搜索框 "),o(g,{class:"px-[24rpx] py-[14rpx] bg-white"},{default:l((()=>[o(f,{modelValue:c.value,"onUpdate:modelValue":_[0]||(_[0]=e=>c.value=e),placeholder:"请输入关键词搜索",height:"72",onSearch:d,onCustom:d,onClear:_[1]||(_[1]=e=>a.searching=!1)},null,8,["modelValue"])])),_:1}),r(" 搜索 "),o(g,{class:"search-content"},{default:l((()=>[r("  "),y(o(B,{onSearch:d,onClear:S,hot_search:a.hot_search,his_search:a.his_search},null,8,["hot_search","his_search"]),[[v,!a.searching]]),r("  "),y(o(g,{class:"search-content-s pt-[20rpx]"},{default:l((()=>[o(w,{ref_key:"paging",ref:h,modelValue:a.result,"onUpdate:modelValue":_[2]||(_[2]=e=>a.result=e),onQuery:F,fixed:!1,height:"100%"},{default:l((()=>[(s(!0),u(i,null,n(a.result,(e=>(s(),t(x,{key:e.id,item:e,newsId:e.id},null,8,["item","newsId"])))),128))])),_:1},8,["modelValue"])])),_:1},512),[[v,a.searching]])])),_:1})])),_:1})],64)}}}),[["__scopeId","data-v-6f0f2122"]]);export{F as default};
