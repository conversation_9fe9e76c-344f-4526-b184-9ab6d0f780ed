import{d as S,j as B,i as O,O as Q,z as H,o as s,a as w,m as t,w as o,e as n,n as K,p as i,B as d,C as p,b as y,Q as J,D as W,G as X,I as V,E as Y,v as Z,q as ee,ee as te,ef as oe,eg as ne,K as ae}from"./index-B2xNDy79.js";import{_ as le}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as se,a as re}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as ie}from"./el-card-DpH4mUSc.js";import{E as de,a as me}from"./el-form-item-DlU85AZK.js";import{b as pe,c as ue,d as _e,e as ce,s as fe}from"./code-BFrZRRfL.js";import{u as ge}from"./usePaging-Dm2wALfy.js";import{_ as be}from"./code-preview.vue_vue_type_script_setup_true_lang-C218Naz1.js";import{_ as we}from"./data-table.vue_vue_type_script_setup_true_lang-CCQ6Ztei.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./isEqual-CLGO95LP.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./_baseClone-CdezRMKA.js";import"./index-C6Cr8aHe.js";import"./index-DFOp_83R.js";const ye={class:"code-generation"},ve={class:"flex"},Ce={class:"mt-4"},ke={class:"flex items-center"},Ee={class:"flex justify-end mt-4"},Ve=S({name:"codeGenerate"}),Oe=S({...Ve,setup(xe){const u=B({table_name:"",table_comment:""}),_=B({show:!1,loading:!1,code:[]}),{pager:f,getLists:b,resetParams:T,resetPage:v}=ge({fetchFun:ce,params:u}),g=O([]),I=a=>{g.value=a.map(({id:e})=>e)},G=async a=>{await V.confirm("确定要同步表结构？"),await fe({id:a})},x=async a=>{await V.confirm("确定要删除？"),await pe({id:a}),b()},N=async a=>{const e=await ue({id:a});_.code=e,_.show=!0},U=a=>a.some(e=>e.generate_type==1),D=async a=>{if(U(a))return V.msgError("生成方式为生成到模块，请在前端开发模式下使用，详细参考文档");const e=await _e({id:a});e.file&&window.open(e.file,"_blank")},z=(a,e)=>{switch(a){case"generate":D([e.id]);break;case"sync":G(e.id);break;case"delete":x(e.id)}};return b(),(a,e)=>{const P=Y,C=de,r=Z,F=me,$=ie,k=ee,c=se,j=Q("router-link"),E=te,q=oe,L=ne,M=re,R=le,m=H("perms"),A=ae;return s(),w("div",ye,[t($,{class:"!border-none",shadow:"never"},{default:o(()=>[t(F,{class:"mb-[-16px]",model:n(u),inline:""},{default:o(()=>[t(C,{class:"w-[280px]",label:"表名称"},{default:o(()=>[t(P,{modelValue:n(u).table_name,"onUpdate:modelValue":e[0]||(e[0]=l=>n(u).table_name=l),clearable:"",onKeyup:K(n(v),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(C,{class:"w-[280px]",label:"表描述"},{default:o(()=>[t(P,{modelValue:n(u).table_comment,"onUpdate:modelValue":e[1]||(e[1]=l=>n(u).table_comment=l),clearable:"",onKeyup:K(n(v),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),t(C,null,{default:o(()=>[t(r,{type:"primary",onClick:n(v)},{default:o(()=>e[6]||(e[6]=[i("查询")])),_:1},8,["onClick"]),t(r,{onClick:n(T)},{default:o(()=>e[7]||(e[7]=[i("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),d((s(),p($,{class:"!border-none mt-4",shadow:"never"},{default:o(()=>[y("div",ve,[d((s(),p(we,{class:"inline-block mr-[10px]",onSuccess:n(b)},{default:o(()=>[t(r,{type:"primary"},{icon:o(()=>[t(k,{name:"el-icon-Plus"})]),default:o(()=>[e[8]||(e[8]=i(" 导入数据表 "))]),_:1})]),_:1},8,["onSuccess"])),[[m,["tools.generator/selectTable"]]]),d((s(),p(r,{disabled:!n(g).length,onClick:e[2]||(e[2]=l=>x(n(g))),type:"danger"},{icon:o(()=>[t(k,{name:"el-icon-Delete"})]),default:o(()=>[e[9]||(e[9]=i(" 删除 "))]),_:1},8,["disabled"])),[[m,["tools.generator/delete"]]]),d((s(),p(r,{disabled:!n(g).length,onClick:e[3]||(e[3]=l=>D(n(g)))},{default:o(()=>e[10]||(e[10]=[i(" 生成代码 ")])),_:1},8,["disabled"])),[[m,["tools.generator/generate"]]])]),y("div",Ce,[t(M,{data:n(f).lists,size:"large",onSelectionChange:I},{default:o(()=>[t(c,{type:"selection",width:"55"}),t(c,{label:"表名称",prop:"table_name","min-width":"180"}),t(c,{label:"表描述",prop:"table_comment","min-width":"180"}),t(c,{label:"创建时间",prop:"create_time","min-width":"180"}),t(c,{label:"更新时间",prop:"update_time","min-width":"180"}),t(c,{label:"操作",width:"160",fixed:"right"},{default:o(({row:l})=>[y("div",ke,[d((s(),p(r,{type:"primary",link:"",onClick:h=>N(l.id)},{default:o(()=>e[11]||(e[11]=[i(" 预览 ")])),_:2},1032,["onClick"])),[[m,["tools.generator/preview"]]]),t(r,{type:"primary",link:""},{default:o(()=>[d((s(),p(j,{to:{path:n(J)("tools.generator/edit"),query:{id:l.id}}},{default:o(()=>e[12]||(e[12]=[i(" 编辑 ")])),_:2},1032,["to"])),[[m,["tools.generator/edit"]]])]),_:2},1024),d((s(),p(L,{class:"ml-2",onCommand:h=>z(h,l)},{dropdown:o(()=>[t(q,null,{default:o(()=>[d((s(),w("div",null,[t(E,{command:"generate"},{default:o(()=>[t(r,{type:"primary",link:""},{default:o(()=>e[14]||(e[14]=[i(" 生成代码 ")])),_:1})]),_:1})])),[[m,["tools.generator/generate"]]]),d((s(),w("div",null,[t(E,{command:"sync"},{default:o(()=>[t(r,{type:"primary",link:""},{default:o(()=>e[15]||(e[15]=[i(" 同步 ")])),_:1})]),_:1})])),[[m,["tools.generator/syncColumn"]]]),d((s(),w("div",null,[t(E,{command:"delete"},{default:o(()=>[t(r,{type:"danger",link:""},{default:o(()=>e[16]||(e[16]=[i(" 删除 ")])),_:1})]),_:1})])),[[m,["tools.generator/delete"]]])]),_:1})]),default:o(()=>[t(r,{type:"primary",link:""},{default:o(()=>[e[13]||(e[13]=i(" 更多 ")),t(k,{name:"el-icon-ArrowDown",size:14})]),_:1})]),_:2},1032,["onCommand"])),[[m,["tools.generator/generate","tools.generator/syncColumn","tools.generator/delete"]]])])]),_:1})]),_:1},8,["data"])]),y("div",Ee,[t(R,{modelValue:n(f),"onUpdate:modelValue":e[4]||(e[4]=l=>W(f)?f.value=l:null),onChange:n(b)},null,8,["modelValue","onChange"])])]),_:1})),[[A,n(f).loading]]),n(_).show?(s(),p(be,{key:0,modelValue:n(_).show,"onUpdate:modelValue":e[5]||(e[5]=l=>n(_).show=l),code:n(_).code},null,8,["modelValue","code"])):X("",!0)])}}});export{Oe as default};
