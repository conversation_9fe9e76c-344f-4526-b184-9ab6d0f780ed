[mysqld]
# 基本设置
port = 3306
bind-address = 0.0.0.0
datadir = /var/lib/mysql

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接设置
max_connections = 200
max_allowed_packet = 100M

# InnoDB 设置
default_storage_engine = InnoDB
innodb_buffer_pool_size = 128M

# 安全设置
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
