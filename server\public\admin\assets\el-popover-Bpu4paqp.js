import{at as R,ex as d,ey as f,ca as s,a8 as U,d as w,c as n,an as T,i as $,e as p,aL as D,o as u,C as V,w as c,a as L,Y as O,t as v,G as b,U as h,p as z,bm as F,bF as H,ar as I,ez as G,ay as K}from"./index-B2xNDy79.js";const Y=R({trigger:d.trigger,placement:f.placement,disabled:d.disabled,visible:s.visible,transition:s.transition,popperOptions:f.popperOptions,tabindex:f.tabindex,content:s.content,popperStyle:s.popperStyle,popperClass:s.popperClass,enterable:{...s.enterable,default:!0},effect:{...s.effect,default:"light"},teleported:s.teleported,title:String,width:{type:[String,Number],default:150},offset:{type:Number,default:void 0},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},showArrow:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},"onUpdate:visible":{type:Function}}),j={"update:visible":t=>U(t),"before-enter":()=>!0,"before-leave":()=>!0,"after-enter":()=>!0,"after-leave":()=>!0},q="onUpdate:visible",J=w({name:"ElPopover"}),M=w({...J,props:Y,emits:j,setup(t,{expose:r,emit:a}){const o=t,g=n(()=>o[q]),i=T("popover"),l=$(),y=n(()=>{var e;return(e=p(l))==null?void 0:e.popperRef}),C=n(()=>[{width:D(o.width)},o.popperStyle]),P=n(()=>[i.b(),o.popperClass,{[i.m("plain")]:!!o.content}]),E=n(()=>o.transition===`${i.namespace.value}-fade-in-linear`),S=()=>{var e;(e=l.value)==null||e.hide()},B=()=>{a("before-enter")},N=()=>{a("before-leave")},k=()=>{a("after-enter")},A=()=>{a("update:visible",!1),a("after-leave")};return r({popperRef:y,hide:S}),(e,_)=>(u(),V(p(H),F({ref_key:"tooltipRef",ref:l},e.$attrs,{trigger:e.trigger,placement:e.placement,disabled:e.disabled,visible:e.visible,transition:e.transition,"popper-options":e.popperOptions,tabindex:e.tabindex,content:e.content,offset:e.offset,"show-after":e.showAfter,"hide-after":e.hideAfter,"auto-close":e.autoClose,"show-arrow":e.showArrow,"aria-label":e.title,effect:e.effect,enterable:e.enterable,"popper-class":p(P),"popper-style":p(C),teleported:e.teleported,persistent:e.persistent,"gpu-acceleration":p(E),"onUpdate:visible":p(g),onBeforeShow:B,onBeforeHide:N,onShow:k,onHide:A}),{content:c(()=>[e.title?(u(),L("div",{key:0,class:O(p(i).e("title")),role:"title"},v(e.title),3)):b("v-if",!0),h(e.$slots,"default",{},()=>[z(v(e.content),1)])]),default:c(()=>[e.$slots.reference?h(e.$slots,"reference",{key:0}):b("v-if",!0)]),_:3},16,["trigger","placement","disabled","visible","transition","popper-options","tabindex","content","offset","show-after","hide-after","auto-close","show-arrow","aria-label","effect","enterable","popper-class","popper-style","teleported","persistent","gpu-acceleration","onUpdate:visible"]))}});var Q=I(M,[["__file","popover.vue"]]);const m=(t,r)=>{const a=r.arg||r.value,o=a==null?void 0:a.popperRef;o&&(o.triggerRef=t)};var W={mounted(t,r){m(t,r)},updated(t,r){m(t,r)}};const X="popover",Z=G(W,X),ee=K(Q,{directive:Z});export{ee as E};
