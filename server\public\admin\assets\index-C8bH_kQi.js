import{d as D,j as R,N as T,O as $,z as j,o as s,a as C,m as e,w as a,e as o,n as z,F as A,r as G,C as d,p as u,b as E,B as _,Q as x,G as J,D as Q,I as H,E as M,v as W,q as X,_ as Y,J as Z,K as ee}from"./index-B2xNDy79.js";import{_ as te}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as ae,a as le}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as oe}from"./el-card-DpH4mUSc.js";import{E as ie,a as ne}from"./el-form-item-DlU85AZK.js";import{E as se,a as re}from"./el-select-BRdnbwTl.js";import{h as de,k as me,l as ce,m as pe}from"./article-Dwgm3r-g.js";import{u as ue}from"./useDictOptions-D0QsC3Dl.js";import{u as _e}from"./usePaging-Dm2wALfy.js";import"./isEqual-CLGO95LP.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./_baseClone-CdezRMKA.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";const fe={class:"article-lists"},be={class:"flex justify-end mt-4"},ve=D({name:"articleLists"}),Ke=D({...ve,setup(we){const n=R({title:"",cid:"",is_show:""}),{pager:m,getLists:r,resetPage:h,resetParams:B}=_e({fetchFun:pe,params:n}),{optionsData:P}=ue({article_cate:{api:de}}),U=async(f,l)=>{try{await me({id:l,is_show:f}),r()}catch{r()}},F=async f=>{await H.confirm("确定要删除？"),await ce({id:f}),r()};return T(()=>{r()}),r(),(f,l)=>{const g=M,b=ie,c=se,V=re,p=W,L=ne,k=oe,N=X,y=$("router-link"),i=ae,S=Y,I=Z,K=le,q=te,v=j("perms"),O=ee;return s(),C("div",fe,[e(k,{class:"!border-none",shadow:"never"},{default:a(()=>[e(L,{ref:"formRef",class:"mb-[-16px]",model:o(n),inline:!0},{default:a(()=>[e(b,{class:"w-[280px]",label:"文章标题"},{default:a(()=>[e(g,{modelValue:o(n).title,"onUpdate:modelValue":l[0]||(l[0]=t=>o(n).title=t),placeholder:"输入文章标题",clearable:"",onKeyup:z(o(h),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(b,{class:"w-[280px]",label:"栏目名称"},{default:a(()=>[e(V,{modelValue:o(n).cid,"onUpdate:modelValue":l[1]||(l[1]=t=>o(n).cid=t)},{default:a(()=>[e(c,{label:"全部",value:""}),(s(!0),C(A,null,G(o(P).article_cate,t=>(s(),d(c,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(b,{class:"w-[280px]",label:"文章状态"},{default:a(()=>[e(V,{modelValue:o(n).is_show,"onUpdate:modelValue":l[2]||(l[2]=t=>o(n).is_show=t)},{default:a(()=>[e(c,{label:"全部",value:""}),e(c,{label:"显示",value:1}),e(c,{label:"隐藏",value:0})]),_:1},8,["modelValue"])]),_:1}),e(b,null,{default:a(()=>[e(p,{type:"primary",onClick:o(h)},{default:a(()=>l[4]||(l[4]=[u("查询")])),_:1},8,["onClick"]),e(p,{onClick:o(B)},{default:a(()=>l[5]||(l[5]=[u("重置")])),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"])]),_:1}),e(k,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[E("div",null,[_((s(),d(y,{to:{path:o(x)("article.article/add:edit")}},{default:a(()=>[e(p,{type:"primary",class:"mb-4"},{icon:a(()=>[e(N,{name:"el-icon-Plus"})]),default:a(()=>[l[6]||(l[6]=u(" 发布文章 "))]),_:1})]),_:1},8,["to"])),[[v,["article.article/add","article.article/add:edit"]]])]),_((s(),d(K,{size:"large",data:o(m).lists},{default:a(()=>[e(i,{label:"ID",prop:"id","min-width":"80"}),e(i,{label:"封面","min-width":"100"},{default:a(({row:t})=>[t.image?(s(),d(S,{key:0,src:t.image,width:60,height:45,"preview-src-list":[t.image],"preview-teleported":"",fit:"contain"},null,8,["src","preview-src-list"])):J("",!0)]),_:1}),e(i,{label:"标题",prop:"title","min-width":"160","show-tooltip-when-overflow":""}),e(i,{label:"栏目",prop:"cate_name","min-width":"100"}),e(i,{label:"作者",prop:"author","min-width":"120"}),e(i,{label:"浏览量",prop:"click","min-width":"100"}),e(i,{label:"状态","min-width":"100"},{default:a(({row:t})=>[_(e(I,{modelValue:t.is_show,"onUpdate:modelValue":w=>t.is_show=w,"active-value":1,"inactive-value":0,onChange:w=>U(w,t.id)},null,8,["modelValue","onUpdate:modelValue","onChange"]),[[v,["article.article/updateStatus"]]])]),_:1}),e(i,{label:"排序",prop:"sort","min-width":"100"}),e(i,{label:"发布时间",prop:"create_time","min-width":"120"}),e(i,{label:"操作",width:"120",fixed:"right"},{default:a(({row:t})=>[_((s(),d(p,{type:"primary",link:""},{default:a(()=>[e(y,{to:{path:o(x)("article.article/add:edit"),query:{id:t.id}}},{default:a(()=>l[7]||(l[7]=[u(" 编辑 ")])),_:2},1032,["to"])]),_:2},1024)),[[v,["article.article/edit","article.article/add:edit"]]]),_((s(),d(p,{type:"danger",link:"",onClick:w=>F(t.id)},{default:a(()=>l[8]||(l[8]=[u(" 删除 ")])),_:2},1032,["onClick"])),[[v,["article.article/delete"]]])]),_:1})]),_:1},8,["data"])),[[O,o(m).loading]]),E("div",be,[e(q,{modelValue:o(m),"onUpdate:modelValue":l[3]||(l[3]=t=>Q(m)?m.value=t:null),onChange:o(r)},null,8,["modelValue","onChange"])])]),_:1})])}}});export{Ke as default};
