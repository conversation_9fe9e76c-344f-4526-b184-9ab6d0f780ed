#!/bin/bash

# LikeAdmin Docker 部署脚本
# 使用方法: ./deploy.sh [start|stop|restart|status|logs]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 设置目录权限
setup_permissions() {
    log_info "设置目录权限..."
    
    # 创建必要的目录
    mkdir -p docker/data/mysql docker/data/redis
    mkdir -p docker/logs/nginx docker/logs/php docker/logs/mysql docker/logs/redis
    mkdir -p server/runtime
    
    # 设置权限
    sudo chown -R 999:999 docker/data/mysql 2>/dev/null || true
    sudo chown -R 999:999 docker/data/redis 2>/dev/null || true
    sudo chown -R 999:999 docker/logs 2>/dev/null || true
    sudo chown -R www-data:www-data server/runtime 2>/dev/null || true
    sudo chmod -R 755 server/runtime 2>/dev/null || true
    
    log_success "目录权限设置完成"
}

# 检查环境配置
check_env() {
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，从 .env.docker 复制..."
        cp .env.docker .env
        log_info "请编辑 .env 文件配置数据库等信息"
    fi
}

# 启动服务
start_services() {
    log_info "启动 LikeAdmin 服务..."
    
    check_requirements
    setup_permissions
    check_env
    
    # 启动服务
    docker compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker compose ps | grep -q "Up"; then
        log_success "服务启动成功！"
        echo ""
        log_info "访问地址："
        echo "  Web 应用: http://localhost:8190"
        echo "  管理后台: http://localhost:8190/admin"
        echo "  PC端: http://localhost:8190/pc"
        echo ""
        log_info "数据库连接："
        echo "  MySQL: localhost:3306"
        echo "  Redis: localhost:6379"
    else
        log_error "服务启动失败，请检查日志"
        docker compose logs
    fi
}

# 停止服务
stop_services() {
    log_info "停止 LikeAdmin 服务..."
    docker compose down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启 LikeAdmin 服务..."
    docker compose restart
    log_success "服务已重启"
}

# 查看服务状态
show_status() {
    log_info "LikeAdmin 服务状态："
    docker compose ps
}

# 查看日志
show_logs() {
    log_info "查看服务日志..."
    docker compose logs -f
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        *)
            echo "使用方法: $0 [start|stop|restart|status|logs]"
            echo ""
            echo "命令说明："
            echo "  start   - 启动所有服务（默认）"
            echo "  stop    - 停止所有服务"
            echo "  restart - 重启所有服务"
            echo "  status  - 查看服务状态"
            echo "  logs    - 查看服务日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
