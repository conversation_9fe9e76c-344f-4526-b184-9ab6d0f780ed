import{x as n,o as c,a,m as e,w as s,ed as r}from"./index-B2xNDy79.js";import{E as _}from"./el-card-DpH4mUSc.js";const l={};function d(m,f){const o=r,t=_;return c(),a("div",null,[e(t,{header:"基础使用",shadow:"never",class:"!border-none"},{default:s(()=>[e(o,{class:"w-20 m-4",content:"超出自动打点，悬浮弹窗显示全部内容"}),e(o,{class:"w-60 m-4",content:"超出自动打点，悬浮弹窗显示全部内容"})]),_:1})])}const w=n(l,[["render",d]]);export{w as default};
