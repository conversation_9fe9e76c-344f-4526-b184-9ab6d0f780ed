import{d as M,i as W,s as $,j as I,c as L,ew as z,V as T,H as _,k as R,o as u,a as r,m as n,w as a,e as b,E as j,C as P,F as v,p as U,b as i,ao as h,r as k,Y as q,t as D,f2 as H,f3 as J,q as O,v as Y,bJ as A,bF as G}from"./index-B2xNDy79.js";import{E as K}from"./el-popover-Bpu4paqp.js";const Q={class:"icon-select"},X={class:"flex justify-between"},Z=["onClick"],ee={class:"h-[280px]"},oe={class:"flex flex-wrap"},se={key:0,class:"flex items-center"},ie=M({__name:"picker",props:{modelValue:{default:""},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(te,{emit:w}){const p=w,f=W(0),V=[{name:"element图标",icons:H()},{name:"本地图标",icons:J()}],d=$(),e=I({inputValue:"",popoverVisible:!1,popoverWidth:0,mouseoverSelect:!1,inputFocus:!1}),C=()=>{e.inputFocus=e.popoverVisible=!0},y=()=>{e.inputFocus=!1,e.popoverVisible=e.mouseoverSelect},g=s=>{e.mouseoverSelect=e.popoverVisible=!1,p("update:modelValue",s),p("change",s)},E=()=>{p("update:modelValue",""),p("change","")},F=L(()=>{var l;const s=((l=V[f.value])==null?void 0:l.icons)??[];if(!e.inputValue)return s;const o=e.inputValue.toLowerCase();return s.filter(c=>{if(c.toLowerCase().indexOf(o)!==-1)return c})}),x=()=>{_(()=>{var o;const s=(o=d.value)==null?void 0:o.$el.offsetWidth;e.popoverWidth=s<300?300:s})};return z(document.body,"click",()=>{e.popoverVisible=!!(e.inputFocus||e.mouseoverSelect)}),T(()=>e.popoverVisible,async s=>{var o,l;await _(),s?(o=d.value)==null||o.focus():(l=d.value)==null||l.blur()}),R(()=>{x()}),(s,o)=>{const l=O,c=Y,S=A,B=G,N=K;return u(),r("div",Q,[n(N,{trigger:"contextmenu",visible:e.popoverVisible,"onUpdate:visible":o[3]||(o[3]=t=>e.popoverVisible=t),width:e.popoverWidth},{reference:a(()=>[n(b(j),{ref_key:"inputRef",ref:d,modelValue:e.inputValue,"onUpdate:modelValue":o[2]||(o[2]=t=>e.inputValue=t),modelModifiers:{trim:!0},placeholder:"搜索图标",autofocus:!1,disabled:s.disabled,onFocus:C,onBlur:y,clearable:""},{prepend:a(()=>[s.modelValue?(u(),r("div",se,[n(B,{class:"flex-1 w-20",content:s.modelValue,placement:"top"},{default:a(()=>[(u(),P(l,{class:"mr-1",key:s.modelValue,name:s.modelValue,size:16},null,8,["name"]))]),_:1},8,["content"])])):(u(),r(v,{key:1},[U("无")],64))]),append:a(()=>[n(c,null,{default:a(()=>[n(l,{name:"el-icon-Close",size:18,onClick:E})]),_:1})]),_:1},8,["modelValue","disabled"])]),default:a(()=>[i("div",{onMouseover:o[0]||(o[0]=h(t=>e.mouseoverSelect=!0,["stop"])),onMouseout:o[1]||(o[1]=h(t=>e.mouseoverSelect=!1,["stop"]))},[i("div",null,[i("div",X,[o[4]||(o[4]=i("div",{class:"mb-3"},"请选择图标",-1)),i("div",null,[(u(),r(v,null,k(V,(t,m)=>i("span",{key:m,class:q(["cursor-pointer text-sm ml-2",{"text-primary":m==b(f)}]),onClick:le=>f.value=m},D(t.name),11,Z)),64))])]),i("div",ee,[n(S,null,{default:a(()=>[i("div",oe,[(u(!0),r(v,null,k(F.value,t=>(u(),r("div",{key:t,class:"m-1"},[n(c,{onClick:m=>g(t)},{default:a(()=>[n(l,{name:t,size:18},null,8,["name"])]),_:2},1032,["onClick"])]))),128))])]),_:1})])])],32)]),_:1},8,["visible","width"])])}}});export{ie as _};
