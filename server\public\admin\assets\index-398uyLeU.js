import{_ as B}from"./index-onOHNH0j.js";import{y as f,d as c,j as y,z as k,o as _,a as R,m as o,w as t,b as n,e as l,p as s,B as D,C as N,E as j,v as F}from"./index-B2xNDy79.js";import{E as I}from"./el-card-DpH4mUSc.js";import{E as U,a as h}from"./el-form-item-DlU85AZK.js";/* empty css                       */import{E as q,a as z}from"./el-radio-CKcO4hVq.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";function G(){return f.get({url:"/recharge.recharge/getConfig"})}function O(m){return f.post({url:"/recharge.recharge/setConfig",params:m})}const S=c({name:"rechargeConfig"}),W=c({...S,setup(m){const a=y({status:1,min_amount:""}),i=async()=>{const u=await G();Object.assign(a,u)},g=async()=>{await O(a),i()};return i(),(u,e)=>{const d=q,v=z,p=U,C=j,E=h,b=I,V=F,w=B,x=k("perms");return _(),R("div",null,[o(b,{shadow:"never",class:"!border-none"},{header:t(()=>e[2]||(e[2]=[n("span",{class:"font-extrabold text-lg"},"充值设置",-1)])),default:t(()=>[o(E,{model:l(a),"label-width":"120px"},{default:t(()=>[o(p,{label:"状态"},{default:t(()=>[n("div",null,[o(v,{modelValue:l(a).status,"onUpdate:modelValue":e[0]||(e[0]=r=>l(a).status=r),class:"ml-4"},{default:t(()=>[o(d,{value:1},{default:t(()=>e[3]||(e[3]=[s("开启")])),_:1}),o(d,{value:0},{default:t(()=>e[4]||(e[4]=[s("关闭")])),_:1})]),_:1},8,["modelValue"]),e[5]||(e[5]=n("div",{class:"form-tips"},"关闭或开启充值功能，关闭后将不显示充值入口",-1))])]),_:1}),o(p,{label:"最低充值金额"},{default:t(()=>[n("div",null,[o(C,{modelValue:l(a).min_amount,"onUpdate:modelValue":e[1]||(e[1]=r=>l(a).min_amount=r),placeholder:"请输入最低充值金额",clearable:""},null,8,["modelValue"]),e[6]||(e[6]=n("div",{class:"form-tips"}," 最低充值金额要求，不填或填0表示不限制最低充值金额 ",-1))])]),_:1})]),_:1},8,["model"])]),_:1}),D((_(),N(w,null,{default:t(()=>[o(V,{type:"primary",onClick:g},{default:t(()=>e[7]||(e[7]=[s("保存")])),_:1})]),_:1})),[[x,["recharge.recharge/setConfig"]]])])}}});export{W as default};
