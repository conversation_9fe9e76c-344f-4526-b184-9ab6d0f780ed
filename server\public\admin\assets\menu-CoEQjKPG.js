import{y as t}from"./index-B2xNDy79.js";function n(u){return t.get({url:"/auth.menu/lists",params:u})}function r(u){return t.get({url:"/auth.menu/all",params:u})}function a(u){return t.post({url:"/auth.menu/add",params:u})}function l(u){return t.post({url:"/auth.menu/edit",params:u})}function m(u){return t.post({url:"/auth.menu/delete",params:u})}function s(u){return t.get({url:"/auth.menu/detail",params:u})}export{n as a,l as b,a as c,s as d,m as e,r as m};
