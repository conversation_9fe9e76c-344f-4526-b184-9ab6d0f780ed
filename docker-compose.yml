version: '3.8'

services:
  # Nginx 服务
  nginx:
    image: nginx:1.24-alpine
    container_name: likeadmin-nginx
    ports:
      - "8190:80"
      - "8443:443"
    volumes:
      - ./docker/config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/config/nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf
      - ./server:/var/www/html
      - ./admin/dist:/var/www/html/public/admin
      - ./pc/dist:/var/www/html/public/pc
      - ./docker/logs/nginx:/var/log/nginx
    depends_on:
      - php
    networks:
      - likeadmin-network
    restart: unless-stopped

  # PHP 8.0 服务
  php:
    build:
      context: .
      dockerfile: docker/Dockerfile.php
    container_name: likeadmin-php
    volumes:
      - ./server:/var/www/html
      - ./docker/config/php/php.ini:/usr/local/etc/php/php.ini
      - ./docker/config/php/php-fpm.conf:/usr/local/etc/php-fpm.conf
      - ./docker/config/php/www.conf:/usr/local/etc/php-fpm.d/www.conf
      - ./docker/logs/php:/var/log/php
    environment:
      - PHP_INI_SCAN_DIR=/usr/local/etc/php/conf.d
    depends_on:
      - mysql
      - redis
    networks:
      - likeadmin-network
    restart: unless-stopped

  # MySQL 8.0 服务
  mysql:
    image: mysql:8.0
    container_name: likeadmin-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: localhost_likeadmin
      MYSQL_USER: likeadmin
      MYSQL_PASSWORD: likeadmin123
      MYSQL_ROOT_HOST: '%'
    volumes:
      - ./docker/data/mysql:/var/lib/mysql
      - ./docker/config/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - likeadmin-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis 服务
  redis:
    image: redis:7.0-alpine
    container_name: likeadmin-redis
    ports:
      - "6379:6379"
    volumes:
      - ./docker/config/redis/redis.conf:/etc/redis/redis.conf
      - ./docker/data/redis:/data
      - ./docker/logs/redis:/var/log/redis
    networks:
      - likeadmin-network
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf

networks:
  likeadmin-network:
    driver: bridge

volumes:
  mysql-data:
  redis-data:
