#!/bin/sh

# 创建必要的目录
mkdir -p /var/www/html/runtime
mkdir -p /var/www/html/runtime/cache
mkdir -p /var/www/html/runtime/log
mkdir -p /var/www/html/runtime/temp
mkdir -p /var/www/html/runtime/session

# 设置目录权限
chown -R www-data:www-data /var/www/html/runtime
chmod -R 755 /var/www/html/runtime

# 设置上传目录权限（如果存在）
if [ -d "/var/www/html/public/uploads" ]; then
    chown -R www-data:www-data /var/www/html/public/uploads
    chmod -R 755 /var/www/html/public/uploads
fi

# 启动 PHP-FPM
exec "$@"
