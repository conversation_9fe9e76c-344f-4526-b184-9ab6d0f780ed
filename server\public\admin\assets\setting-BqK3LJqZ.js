import{_ as y}from"./index-onOHNH0j.js";import{d as f,i as k,f as I,j as _,o as x,a as B,m as r,w as a,e as s,b as m,p as C,E as S,v as M,I as l,ff as N}from"./index-B2xNDy79.js";import{E as q}from"./el-card-DpH4mUSc.js";import{E as F,a as R}from"./el-form-item-DlU85AZK.js";import{_ as j}from"./picker-Cd5l2hZ5.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./index-BuNto3DN.js";import"./index-DSiy6YVt.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";const D={class:"user-setting"},T={class:"w-80"},z={class:"w-80"},A={class:"w-80"},G={class:"w-80"},H={class:"w-80"},J=f({name:"userSetting"}),go=f({...J,setup(K){const i=k(),u=I(),o=_({avatar:"",account:"",name:"",password_old:"",password:"",password_confirm:""}),c=_({avatar:[{required:!0,message:"头像不能为空",trigger:["change"]}],name:[{required:!0,message:"请输入名称",trigger:["blur"]}]}),w=async()=>{const d=u.userInfo;for(const e in o)o[e]=d[e]},g=async()=>{if(o.password_old||o.password||o.password_confirm){if(!o.password_old)return l.msgError("请输入当前密码");if(!o.password)return l.msgError("请输入新的密码");if(!o.password_confirm)return l.msgError("请输入确定密码");if(o.password_confirm!=o.password)return l.msgError("两次输入的密码不一样")}if(o.password_old&&o.password&&o.password_confirm){if(o.password_old.length<6||o.password_old.length>32)return l.msgError("密码长度在6到32之间");if(o.password.length<6||o.password.length>32)return l.msgError("密码长度在6到32之间");if(o.password_confirm.length<6||o.password_confirm.length>32)return l.msgError("密码长度在6到32之间")}await N(o),u.getUserInfo()},V=async()=>{var d;await((d=i.value)==null?void 0:d.validate()),g()};return w(),(d,e)=>{const v=j,n=F,p=S,E=R,b=q,h=M,U=y;return x(),B("div",D,[r(b,{class:"!border-none",shadow:"never"},{default:a(()=>[r(E,{ref_key:"formRef",ref:i,class:"ls-form",model:s(o),rules:s(c),"label-width":"100px"},{default:a(()=>[r(n,{label:"头像：",prop:"avatar"},{default:a(()=>[r(v,{modelValue:s(o).avatar,"onUpdate:modelValue":e[0]||(e[0]=t=>s(o).avatar=t),limit:1},null,8,["modelValue"])]),_:1}),r(n,{label:"账号：",prop:"account"},{default:a(()=>[m("div",T,[r(p,{modelValue:s(o).account,"onUpdate:modelValue":e[1]||(e[1]=t=>s(o).account=t),disabled:""},null,8,["modelValue"])])]),_:1}),r(n,{label:"名称：",prop:"name"},{default:a(()=>[m("div",z,[r(p,{modelValue:s(o).name,"onUpdate:modelValue":e[2]||(e[2]=t=>s(o).name=t),placeholder:"请输入名称"},null,8,["modelValue"])])]),_:1}),r(n,{label:"当前密码：",prop:"password_old"},{default:a(()=>[m("div",A,[r(p,{modelValue:s(o).password_old,"onUpdate:modelValue":e[3]||(e[3]=t=>s(o).password_old=t),modelModifiers:{trim:!0},placeholder:"修改密码时必填, 不修改密码时留空",type:"password","show-password":""},null,8,["modelValue"])])]),_:1}),r(n,{label:"新的密码：",prop:"password"},{default:a(()=>[m("div",G,[r(p,{modelValue:s(o).password,"onUpdate:modelValue":e[4]||(e[4]=t=>s(o).password=t),modelModifiers:{trim:!0},placeholder:"修改密码时必填, 不修改密码时留空",type:"password","show-password":""},null,8,["modelValue"])])]),_:1}),r(n,{label:"确定密码：",prop:"password_confirm"},{default:a(()=>[m("div",H,[r(p,{modelValue:s(o).password_confirm,"onUpdate:modelValue":e[5]||(e[5]=t=>s(o).password_confirm=t),modelModifiers:{trim:!0},placeholder:"修改密码时必填, 不修改密码时留空",type:"password","show-password":""},null,8,["modelValue"])])]),_:1})]),_:1},8,["model","rules"])]),_:1}),r(U,null,{default:a(()=>[r(h,{type:"primary",onClick:V},{default:a(()=>e[6]||(e[6]=[C("保存")])),_:1})]),_:1})])}}});export{go as default};
