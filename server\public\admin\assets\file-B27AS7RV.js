import{d as r,j as p,o as v,a as f,m as a,w as m,b as e,e as s,p as x,v as V,E as _}from"./index-B2xNDy79.js";import{E as w}from"./el-card-DpH4mUSc.js";import{_ as E}from"./picker-Cd5l2hZ5.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-BuNto3DN.js";import"./index-DSiy6YVt.js";import"./_baseClone-CdezRMKA.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";const U={class:"flex flex-wrap"},b={class:"flex m-4"},B={class:"flex m-4"},k={class:"flex flex-1 m-4"},C={class:"flex-1"},N={class:"flex flex-wrap"},c={class:"flex m-4"},g={class:"flex m-4"},j={class:"flex m-4"},y={class:"flex m-4 items-center"},z={class:"flex m-4 items-center"},ol=r({__name:"file",setup(I){const o=p({value1:"",value2:[],value3:"",value4:"",value5:"",value6:""});return(T,l)=>{const d=E,n=w,u=V,i=_;return v(),f("div",null,[a(n,{header:"基础使用",shadow:"never",class:"!border-none"},{default:m(()=>[e("div",U,[e("div",b,[l[6]||(l[6]=e("div",{class:"mr-4"},"选择图片：",-1)),a(d,{modelValue:s(o).value1,"onUpdate:modelValue":l[0]||(l[0]=t=>s(o).value1=t)},null,8,["modelValue"])]),e("div",B,[l[7]||(l[7]=e("div",{class:"mr-4"},"选择视频：",-1)),a(d,{type:"video",modelValue:s(o).value3,"onUpdate:modelValue":l[1]||(l[1]=t=>s(o).value3=t)},null,8,["modelValue"])]),e("div",k,[l[8]||(l[8]=e("div",{class:"mr-4"},"多张图片：",-1)),e("div",C,[a(d,{limit:4,modelValue:s(o).value2,"onUpdate:modelValue":l[2]||(l[2]=t=>s(o).value2=t)},null,8,["modelValue"])])])])]),_:1}),a(n,{header:"进阶用法",shadow:"never",class:"!border-none mt-4"},{default:m(()=>[e("div",N,[e("div",c,[l[9]||(l[9]=e("div",{class:"mr-4"},"自定义选择器大小：",-1)),a(d,{size:"60px",modelValue:s(o).value4,"onUpdate:modelValue":l[3]||(l[3]=t=>s(o).value4=t)},null,8,["modelValue"])]),e("div",g,[l[11]||(l[11]=e("div",{class:"mr-4"},"使用插槽：",-1)),a(d,{modelValue:s(o).value5,"onUpdate:modelValue":l[4]||(l[4]=t=>s(o).value5=t)},{upload:m(()=>[a(u,null,{default:m(()=>l[10]||(l[10]=[x("选择文件")])),_:1})]),_:1},8,["modelValue"])]),e("div",j,[l[12]||(l[12]=e("div",{class:"mr-4"},"选出地址不带域名：",-1)),a(d,{"exclude-domain":!0,modelValue:s(o).value6,"onUpdate:modelValue":l[5]||(l[5]=t=>s(o).value6=t)},null,8,["modelValue"])])]),e("div",null,[e("div",y,[l[13]||(l[13]=e("div",{class:"w-20 flex-none"},"带域名：",-1)),a(i,{class:"w-[500px]","model-value":s(o).value5},null,8,["model-value"])]),e("div",z,[l[14]||(l[14]=e("div",{class:"w-20 flex-none"},"不带域名：",-1)),a(i,{class:"w-[500px]","model-value":s(o).value6},null,8,["model-value"])])])]),_:1})])}}});export{ol as default};
