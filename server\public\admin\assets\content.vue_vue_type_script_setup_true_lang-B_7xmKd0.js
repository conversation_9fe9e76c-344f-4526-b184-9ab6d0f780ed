import{d as j,s as N,o as r,C as _,w as o,m as n,b as l,a as f,r as R,F as S,p as $,I as g,v as A,bJ as D}from"./index-B2xNDy79.js";import{_ as F}from"./index-BuNto3DN.js";import{_ as O}from"./picker-qQ9YEtJl.js";import{_ as P}from"./picker-Cd5l2hZ5.js";import{E as I}from"./el-alert-BUxHh72o.js";import{P as J}from"./index-DFOp_83R.js";import{c as h}from"./index-DSiy6YVt.js";const L={class:"flex flex-wrap p-4"},T={class:"bg-fill-light w-full p-4"},q={class:"flex items-center"},z={class:"mt-4 ml-4"},Y=j({__name:"content",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})},height:{type:String,default:"170px"}},emits:["update:content"],setup(m,{expose:x,emit:k}){const u=k,d=N(),a=m,b=()=>{var e;(e=d.value)==null||e.open()},y=()=>{var e;(e=d.value)==null||e.close()},w=()=>{var e;if(((e=a.content.data)==null?void 0:e.length)<10){const t=h(a.content);t.data.push({image:"",name:"",link:{}}),u("update:content",t)}else g.msgError("最多添加10张图片")},V=e=>{var s;if(((s=a.content.data)==null?void 0:s.length)<=1)return g.msgError("最少保留一个轮播图");const t=h(a.content);t.data.splice(e,1),u("update:content",t)};return x({open:b}),(e,t)=>{const s=I,v=P,C=O,E=F,B=A,U=D;return r(),_(J,{ref_key:"popupRef",ref:d,title:"轮播图设置",async:!0,width:"980px",onConfirm:y},{default:o(()=>[n(s,{title:"最多可添加10张，建议图片尺寸750px*440px",type:"warning"}),n(U,{height:"400px",class:"mt-4"},{default:o(()=>[l("div",L,[(r(!0),f(S,null,R(m.content.data,(c,i)=>(r(),f("div",{key:i,class:"w-[400px] mr-4 mb-4"},[(r(),_(E,{key:i,onClose:p=>V(i),class:"w-full"},{default:o(()=>[l("div",T,[l("div",q,[n(v,{width:"122px",height:"122px",modelValue:c.image,"onUpdate:modelValue":p=>c.image=p,"upload-class":"bg-body","exclude-domain":""},{upload:o(()=>t[0]||(t[0]=[l("div",{class:"w-[122px] h-[122px] flex justify-center items-center"}," 轮播图 ",-1)])),_:2},1032,["modelValue","onUpdate:modelValue"]),n(C,{modelValue:c.link,"onUpdate:modelValue":p=>c.link=p},null,8,["modelValue","onUpdate:modelValue"])])])]),_:2},1032,["onClose"]))]))),128))]),l("div",z,[n(B,{link:"",type:"primary",onClick:w},{default:o(()=>t[1]||(t[1]=[$("+ 添加轮播图")])),_:1})])]),_:1})]),_:1},512)}}});export{Y as _};
