import{d as i,i as v,O as x,z as E,o as c,a as b,m as t,w as o,b as w,e as r,p as l,t as g,B,C,v as D,E as V}from"./index-B2xNDy79.js";import{E as k}from"./el-card-DpH4mUSc.js";import{E as N,a as P}from"./el-form-item-DlU85AZK.js";import{b as z}from"./decoration-C6Bzwzfj.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";const F={class:"decoration-pc min-w-[1100px]"},I=i({name:"decorationPc"}),H=i({...I,setup(q){const a=v({update_time:"",pc_url:""});return(async()=>{try{a.value=await z()}catch(s){console.log(s)}})(),(s,e)=>{const n=D,m=x("router-link"),p=N,_=V,d=P,u=k,f=E("copy");return c(),b("div",F,[t(u,{shadow:"never",class:"!border-none flex-1 flex"},{default:o(()=>[e[3]||(e[3]=w("div",{class:"text-xl font-medium"},"首页装修",-1)),t(m,{to:{path:"/decoration/pc_details",query:{url:r(a).pc_url}}},{default:o(()=>[t(n,{class:"m-5",type:"primary",size:"large"},{default:o(()=>e[1]||(e[1]=[l("去装修")])),_:1})]),_:1},8,["to"]),t(d,null,{default:o(()=>[t(p,{label:"最近更新"},{default:o(()=>[l(g(r(a).update_time),1)]),_:1}),t(p,{label:"PC端链接"},{default:o(()=>[t(_,{style:{width:"350px"},modelValue:r(a).pc_url,"onUpdate:modelValue":e[0]||(e[0]=y=>r(a).pc_url=y),disabled:""},null,8,["modelValue"]),B((c(),C(n,{type:"primary"},{default:o(()=>e[2]||(e[2]=[l("复制")])),_:1})),[[f,r(a).pc_url]])]),_:1})]),_:1})]),_:1})])}}});export{H as default};
