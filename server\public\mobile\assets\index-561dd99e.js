var e=Object.defineProperty,t=(t,n,o)=>(((t,n,o)=>{n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o})(t,"symbol"!=typeof n?n+"":n,o),o);!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const n={},o=function(e,t,o){if(!t||0===t.length)return e();const r=document.getElementsByTagName("link");return Promise.all(t.map((e=>{if((e=function(e){return"/mobile/"+e}(e))in n)return;n[e]=!0;const t=e.endsWith(".css"),i=t?'[rel="stylesheet"]':"";if(!!o)for(let n=r.length-1;n>=0;n--){const o=r[n];if(o.href===e&&(!t||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${e}"]${i}`))return;const s=document.createElement("link");return s.rel=t?"stylesheet":"modulepreload",t||(s.as="script",s.crossOrigin=""),s.href=e,document.head.appendChild(s),t?new Promise(((t,n)=>{s.addEventListener("load",t),s.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0}))).then((()=>e()))};function r(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function i(e){if(k(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=I(o)?c(o):i(o);if(r)for(const e in r)t[e]=r[e]}return t}return I(e)||$(e)?e:void 0}const s=/;(?![^(]*\))/g,a=/:([^]+)/,l=/\/\*.*?\*\//gs;function c(e){const t={};return e.replace(l,"").split(s).forEach((e=>{if(e){const n=e.split(a);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function u(e){let t="";if(I(e))t=e;else if(k(e))for(let n=0;n<e.length;n++){const o=u(e[n]);o&&(t+=o+" ")}else if($(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const f=r("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function h(e){return!!e||""===e}const d=e=>I(e)?e:null==e?"":k(e)||$(e)&&(e.toString===L||!M(e.toString))?JSON.stringify(e,p,2):String(e),p=(e,t)=>t&&t.__v_isRef?p(e,t.value):E(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:O(t)?{[`Set(${t.size})`]:[...t.values()]}:!$(t)||k(t)||B(t)?t:String(t),g={},m=[],v=()=>{},y=()=>!1,b=/^on[^a-z]/,_=e=>b.test(e),w=e=>e.startsWith("onUpdate:"),x=Object.assign,S=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},T=Object.prototype.hasOwnProperty,C=(e,t)=>T.call(e,t),k=Array.isArray,E=e=>"[object Map]"===R(e),O=e=>"[object Set]"===R(e),M=e=>"function"==typeof e,I=e=>"string"==typeof e,P=e=>"symbol"==typeof e,$=e=>null!==e&&"object"==typeof e,A=e=>$(e)&&M(e.then)&&M(e.catch),L=Object.prototype.toString,R=e=>L.call(e),j=e=>R(e).slice(8,-1),B=e=>"[object Object]"===R(e),N=e=>I(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,D=r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),F=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},q=/-(\w)/g,V=F((e=>e.replace(q,((e,t)=>t?t.toUpperCase():"")))),W=/\B([A-Z])/g,z=F((e=>e.replace(W,"-$1").toLowerCase())),H=F((e=>e.charAt(0).toUpperCase()+e.slice(1))),U=F((e=>e?`on${H(e)}`:"")),X=(e,t)=>!Object.is(e,t),Y=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},G=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},J=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Q=e=>{const t=I(e)?Number(e):NaN;return isNaN(t)?e:t};let K;const Z=()=>K||(K="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),ee=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view"].map((e=>"uni-"+e));const te="\n",ne=50,oe="UNI_LOCALE",re=["%","%"],ie=/^([a-z-]+:)?\/\//i,se=/^data:.*,.*/,ae="WEB_INVOKE_APPSERVICE",le="onShow",ce="onHide",ue="onLaunch",fe="onError",he="onThemeChange",de="onPageNotFound",pe="onUnhandledRejection",ge="onLoad",me="onUnload",ve="onInit",ye="onSaveExitState",be="onResize",_e="onBackPress",we="onPageScroll",xe="onTabItemTap",Se="onReachBottom",Te="onPullDownRefresh",Ce="onShareTimeline",ke="onAddToFavorites",Ee="onShareAppMessage",Oe="onNavigationBarButtonTap",Me="onNavigationBarChange",Ie="onNavigationBarSearchInputClicked",Pe="onNavigationBarSearchInputChanged",$e="onNavigationBarSearchInputConfirmed",Ae="onNavigationBarSearchInputFocusChanged",Le="onAppEnterForeground",Re="onAppEnterBackground",je="onWebInvokeAppService";function Be(e){return e&&(e.appContext?e.proxy:e)}function Ne(e){if(!e)return;let t=e.type.name;for(;t&&(n=z(t),-1!==ee.indexOf("uni-"+n.replace("v-uni-","")));)t=(e=e.parent).type.name;var n;return e.proxy}function De(e){return 1===e.nodeType}function Fe(e){return 0===e.indexOf("/")}function qe(e){return Fe(e)?e:"/"+e}function Ve(e){return Fe(e)?e.slice(1):e}const We=(e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n};function ze(e,t){for(const n in t)e.style[n]=t[n]}function He(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function Ue(e){return V(e.substring(5))}const Xe=He((()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[Ue(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[Ue(e)],n.call(this,e)}}));function Ye(e){return x({},e.dataset,e.__uniDataset)}const Ge=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function Je(e){return{passive:e}}function Qe(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:Ye(e),offsetTop:n,offsetLeft:o}}function Ke(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Ze(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=Ke(e[n])}catch(YC){t[n]=e[n]}})),t}const et=/\+/g;function tt(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(et," ");let r=e.indexOf("="),i=Ke(r<0?e:e.slice(0,r)),s=r<0?null:Ke(e.slice(r+1));if(i in t){let e=t[i];k(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function nt(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);r=o((()=>e.apply(this,arguments)),t)};return i.cancel=function(){n(r)},i}class ot{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const rt=[ve,ge,le,ce,me,_e,we,xe,Se,Te,Ce,Ee,ke,ye,Oe,Ie,Pe,$e,Ae],it=[ge,le];const st=[le,ce,ue,fe,he,de,pe,ve,ge,"onReady",me,be,_e,we,xe,Se,Te,Ce,ke,Ee,ye,Oe,Ie,Pe,$e,Ae];const at=[];const lt=He(((e,t)=>{if(M(e._component.onError))return t(e)})),ct=function(){};ct.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t)for(var i=0,s=o.length;i<s;i++)o[i].fn!==t&&o[i].fn._!==t&&r.push(o[i]);return r.length?n[e]=r:delete n[e],this}};var ut=ct;const ft={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function ht(e,t={},n="light"){const o=t[n],r={};return o?(Object.keys(e).forEach((i=>{let s=e[i];r[i]=(()=>{if(B(s))return ht(s,t,n);if(k(s))return s.map((e=>B(e)?ht(e,t,n):e));if(I(s)&&s.startsWith("@")){const t=s.replace("@","");let n=o[t]||s;switch(i){case"titleColor":n="black"===n?"#000000":"#ffffff";break;case"borderStyle":n=(e=n)&&e in ft?ft[e]:e}return n}var e;return s})()})),r):e}let dt;class pt{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=dt,!e&&dt&&(this.index=(dt.scopes||(dt.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=dt;try{return dt=this,e()}finally{dt=t}}}on(){dt=this}off(){dt=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function gt(e){return new pt(e)}const mt=e=>{const t=new Set(e);return t.w=0,t.n=0,t},vt=e=>(e.w&wt)>0,yt=e=>(e.n&wt)>0,bt=new WeakMap;let _t=0,wt=1;const xt=30;let St;const Tt=Symbol(""),Ct=Symbol("");class kt{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=dt){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=St,t=Ot;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=St,St=this,Ot=!0,wt=1<<++_t,_t<=xt?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=wt})(this):Et(this),this.fn()}finally{_t<=xt&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const r=t[o];vt(r)&&!yt(r)?r.delete(e):t[n++]=r,r.w&=~wt,r.n&=~wt}t.length=n}})(this),wt=1<<--_t,St=this.parent,Ot=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){St===this?this.deferStop=!0:this.active&&(Et(this),this.onStop&&this.onStop(),this.active=!1)}}function Et(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Ot=!0;const Mt=[];function It(){Mt.push(Ot),Ot=!1}function Pt(){const e=Mt.pop();Ot=void 0===e||e}function $t(e,t,n){if(Ot&&St){let t=bt.get(e);t||bt.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=mt()),At(o)}}function At(e,t){let n=!1;_t<=xt?yt(e)||(e.n|=wt,n=!vt(e)):n=!e.has(St),n&&(e.add(St),St.deps.push(e))}function Lt(e,t,n,o,r,i){const s=bt.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&k(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":k(e)?N(n)&&a.push(s.get("length")):(a.push(s.get(Tt)),E(e)&&a.push(s.get(Ct)));break;case"delete":k(e)||(a.push(s.get(Tt)),E(e)&&a.push(s.get(Ct)));break;case"set":E(e)&&a.push(s.get(Tt))}if(1===a.length)a[0]&&Rt(a[0]);else{const e=[];for(const t of a)t&&e.push(...t);Rt(mt(e))}}function Rt(e,t){const n=k(e)?e:[...e];for(const o of n)o.computed&&jt(o);for(const o of n)o.computed||jt(o)}function jt(e,t){(e!==St||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const Bt=r("__proto__,__v_isRef,__isVue"),Nt=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(P)),Dt=Ht(),Ft=Ht(!1,!0),qt=Ht(!0),Vt=Wt();function Wt(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=In(this);for(let t=0,r=this.length;t<r;t++)$t(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(In)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){It();const n=In(this)[t].apply(this,e);return Pt(),n}})),e}function zt(e){const t=In(this);return $t(t,0,e),t.hasOwnProperty(e)}function Ht(e=!1,t=!1){return function(n,o,r){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&r===(e?t?wn:_n:t?bn:yn).get(n))return n;const i=k(n);if(!e){if(i&&C(Vt,o))return Reflect.get(Vt,o,r);if("hasOwnProperty"===o)return zt}const s=Reflect.get(n,o,r);return(P(o)?Nt.has(o):Bt(o))?s:(e||$t(n,0,o),t?s:jn(s)?i&&N(o)?s:s.value:$(s)?e?Tn(s):xn(s):s)}}function Ut(e=!1){return function(t,n,o,r){let i=t[n];if(En(i)&&jn(i)&&!jn(o))return!1;if(!e&&(On(o)||En(o)||(i=In(i),o=In(o)),!k(t)&&jn(i)&&!jn(o)))return i.value=o,!0;const s=k(t)&&N(n)?Number(n)<t.length:C(t,n),a=Reflect.set(t,n,o,r);return t===In(r)&&(s?X(o,i)&&Lt(t,"set",n,o):Lt(t,"add",n,o)),a}}const Xt={get:Dt,set:Ut(),deleteProperty:function(e,t){const n=C(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&Lt(e,"delete",t,void 0),o},has:function(e,t){const n=Reflect.has(e,t);return P(t)&&Nt.has(t)||$t(e,0,t),n},ownKeys:function(e){return $t(e,0,k(e)?"length":Tt),Reflect.ownKeys(e)}},Yt={get:qt,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},Gt=x({},Xt,{get:Ft,set:Ut(!0)}),Jt=e=>e,Qt=e=>Reflect.getPrototypeOf(e);function Kt(e,t,n=!1,o=!1){const r=In(e=e.__v_raw),i=In(t);n||(t!==i&&$t(r,0,t),$t(r,0,i));const{has:s}=Qt(r),a=o?Jt:n?An:$n;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function Zt(e,t=!1){const n=this.__v_raw,o=In(n),r=In(e);return t||(e!==r&&$t(o,0,e),$t(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function en(e,t=!1){return e=e.__v_raw,!t&&$t(In(e),0,Tt),Reflect.get(e,"size",e)}function tn(e){e=In(e);const t=In(this);return Qt(t).has.call(t,e)||(t.add(e),Lt(t,"add",e,e)),this}function nn(e,t){t=In(t);const n=In(this),{has:o,get:r}=Qt(n);let i=o.call(n,e);i||(e=In(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?X(t,s)&&Lt(n,"set",e,t):Lt(n,"add",e,t),this}function on(e){const t=In(this),{has:n,get:o}=Qt(t);let r=n.call(t,e);r||(e=In(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&Lt(t,"delete",e,void 0),i}function rn(){const e=In(this),t=0!==e.size,n=e.clear();return t&&Lt(e,"clear",void 0,void 0),n}function sn(e,t){return function(n,o){const r=this,i=r.__v_raw,s=In(i),a=t?Jt:e?An:$n;return!e&&$t(s,0,Tt),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function an(e,t,n){return function(...o){const r=this.__v_raw,i=In(r),s=E(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?Jt:t?An:$n;return!t&&$t(i,0,l?Ct:Tt),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function ln(e){return function(...t){return"delete"!==e&&this}}function cn(){const e={get(e){return Kt(this,e)},get size(){return en(this)},has:Zt,add:tn,set:nn,delete:on,clear:rn,forEach:sn(!1,!1)},t={get(e){return Kt(this,e,!1,!0)},get size(){return en(this)},has:Zt,add:tn,set:nn,delete:on,clear:rn,forEach:sn(!1,!0)},n={get(e){return Kt(this,e,!0)},get size(){return en(this,!0)},has(e){return Zt.call(this,e,!0)},add:ln("add"),set:ln("set"),delete:ln("delete"),clear:ln("clear"),forEach:sn(!0,!1)},o={get(e){return Kt(this,e,!0,!0)},get size(){return en(this,!0)},has(e){return Zt.call(this,e,!0)},add:ln("add"),set:ln("set"),delete:ln("delete"),clear:ln("clear"),forEach:sn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=an(r,!1,!1),n[r]=an(r,!0,!1),t[r]=an(r,!1,!0),o[r]=an(r,!0,!0)})),[e,n,t,o]}const[un,fn,hn,dn]=cn();function pn(e,t){const n=t?e?dn:hn:e?fn:un;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(C(n,o)&&o in t?n:t,o,r)}const gn={get:pn(!1,!1)},mn={get:pn(!1,!0)},vn={get:pn(!0,!1)},yn=new WeakMap,bn=new WeakMap,_n=new WeakMap,wn=new WeakMap;function xn(e){return En(e)?e:Cn(e,!1,Xt,gn,yn)}function Sn(e){return Cn(e,!1,Gt,mn,bn)}function Tn(e){return Cn(e,!0,Yt,vn,_n)}function Cn(e,t,n,o,r){if(!$(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=(a=e).__v_skip||!Object.isExtensible(a)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(j(a));var a;if(0===s)return e;const l=new Proxy(e,2===s?o:n);return r.set(e,l),l}function kn(e){return En(e)?kn(e.__v_raw):!(!e||!e.__v_isReactive)}function En(e){return!(!e||!e.__v_isReadonly)}function On(e){return!(!e||!e.__v_isShallow)}function Mn(e){return kn(e)||En(e)}function In(e){const t=e&&e.__v_raw;return t?In(t):e}function Pn(e){return G(e,"__v_skip",!0),e}const $n=e=>$(e)?xn(e):e,An=e=>$(e)?Tn(e):e;function Ln(e){Ot&&St&&At((e=In(e)).dep||(e.dep=mt()))}function Rn(e,t){const n=(e=In(e)).dep;n&&Rt(n)}function jn(e){return!(!e||!0!==e.__v_isRef)}function Bn(e){return Dn(e,!1)}function Nn(e){return Dn(e,!0)}function Dn(e,t){return jn(e)?e:new Fn(e,t)}class Fn{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:In(e),this._value=t?e:$n(e)}get value(){return Ln(this),this._value}set value(e){const t=this.__v_isShallow||On(e)||En(e);e=t?e:In(e),X(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:$n(e),Rn(this))}}function qn(e){return jn(e)?e.value:e}const Vn={get:(e,t,n)=>qn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return jn(r)&&!jn(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Wn(e){return kn(e)?e:new Proxy(e,Vn)}class zn{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){var n;return null===(n=bt.get(e))||void 0===n?void 0:n.get(t)}(In(this._object),this._key)}}function Hn(e,t,n){const o=e[t];return jn(o)?o:new zn(e,t,n)}var Un;class Xn{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[Un]=!1,this._dirty=!0,this.effect=new kt(e,(()=>{this._dirty||(this._dirty=!0,Rn(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=In(this);return Ln(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function Yn(e,t,n,o){let r;try{r=o?e(...o):e()}catch(i){Jn(i,t,n)}return r}function Gn(e,t,n,o){if(M(e)){const r=Yn(e,t,n,o);return r&&A(r)&&r.catch((e=>{Jn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(Gn(e[i],t,n,o));return r}function Jn(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const r=t.proxy,i=n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void Yn(s,null,10,[e,r,i])}!function(e){console.error(e)}(e,0,0,o)}Un="__v_isReadonly";let Qn=!1,Kn=!1;const Zn=[];let eo=0;const to=[];let no=null,oo=0;const ro=Promise.resolve();let io=null;function so(e){const t=io||ro;return e?t.then(this?e.bind(this):e):t}function ao(e){Zn.length&&Zn.includes(e,Qn&&e.allowRecurse?eo+1:eo)||(null==e.id?Zn.push(e):Zn.splice(function(e){let t=eo+1,n=Zn.length;for(;t<n;){const o=t+n>>>1;fo(Zn[o])<e?t=o+1:n=o}return t}(e.id),0,e),lo())}function lo(){Qn||Kn||(Kn=!0,io=ro.then(po))}function co(e,t=(Qn?eo+1:0)){for(;t<Zn.length;t++){const e=Zn[t];e&&e.pre&&(Zn.splice(t,1),t--,e())}}function uo(e){if(to.length){const e=[...new Set(to)];if(to.length=0,no)return void no.push(...e);for(no=e,no.sort(((e,t)=>fo(e)-fo(t))),oo=0;oo<no.length;oo++)no[oo]();no=null,oo=0}}const fo=e=>null==e.id?1/0:e.id,ho=(e,t)=>{const n=fo(e)-fo(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function po(e){Kn=!1,Qn=!0,Zn.sort(ho);try{for(eo=0;eo<Zn.length;eo++){const e=Zn[eo];e&&!1!==e.active&&Yn(e,null,14)}}finally{eo=0,Zn.length=0,uo(),Qn=!1,io=null,(Zn.length||to.length)&&po()}}function go(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||g;let r=n;const i=t.startsWith("update:"),s=i&&t.slice(7);if(s&&s in o){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:t,trim:i}=o[e]||g;i&&(r=n.map((e=>I(e)?e.trim():e))),t&&(r=n.map(J))}let a,l=o[a=U(t)]||o[a=U(V(t))];!l&&i&&(l=o[a=U(z(t))]),l&&Gn(l,e,6,mo(e,l,r));const c=o[a+"Once"];if(c){if(e.emitted){if(e.emitted[a])return}else e.emitted={};e.emitted[a]=!0,Gn(c,e,6,mo(e,c,r))}}function mo(e,t,n){if(1!==n.length)return n;if(M(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&C(o,"type")&&C(o,"timeStamp")&&C(o,"target")&&C(o,"currentTarget")&&C(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function vo(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!M(e)){const o=e=>{const n=vo(e,t,!0);n&&(a=!0,x(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(k(i)?i.forEach((e=>s[e]=null)):x(s,i),$(e)&&o.set(e,s),s):($(e)&&o.set(e,null),null)}function yo(e,t){return!(!e||!_(t))&&(t=t.slice(2).replace(/Once$/,""),C(e,t[0].toLowerCase()+t.slice(1))||C(e,z(t))||C(e,t))}let bo=null,_o=null;function wo(e){const t=bo;return bo=e,_o=e&&e.type.__scopeId||null,t}function xo(e,t=bo,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Ci(-1);const r=wo(t);let i;try{i=e(...n)}finally{wo(r),o._d&&Ci(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function So(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:l,emit:c,render:u,renderCache:f,data:h,setupState:d,ctx:p,inheritAttrs:g}=e;let m,v;const y=wo(e);try{if(4&n.shapeFlag){const e=r||o;m=Di(u.call(e,e,f,i,d,h,p)),v=l}else{const e=t;0,m=Di(e.length>1?e(i,{attrs:l,slots:a,emit:c}):e(i,null)),v=t.props?l:To(l)}}catch(_){wi.length=0,Jn(_,e,1),m=Ri(bi)}let b=m;if(v&&!1!==g){const e=Object.keys(v),{shapeFlag:t}=b;e.length&&7&t&&(s&&e.some(w)&&(v=Co(v,s)),b=ji(b,v))}return n.dirs&&(b=ji(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),m=b,wo(y),m}const To=e=>{let t;for(const n in e)("class"===n||"style"===n||_(n))&&((t||(t={}))[n]=e[n]);return t},Co=(e,t)=>{const n={};for(const o in e)w(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function ko(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!yo(n,i))return!0}return!1}const Eo=e=>e.__isSuspense;function Oo(e,t){if(Ui){let n=Ui.provides;const o=Ui.parent&&Ui.parent.provides;o===n&&(n=Ui.provides=Object.create(o)),n[e]=t,"app"===Ui.type.mpType&&Ui.appContext.app.provide(e,t)}else;}function Mo(e,t,n=!1){const o=Ui||bo;if(o){const r=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&M(t)?t.call(o.proxy):t}}function Io(e,t){return Ao(e,null,t)}const Po={};function $o(e,t,n){return Ao(e,t,n)}function Ao(e,t,{immediate:n,deep:o,flush:r,onTrack:i,onTrigger:s}=g){const a=dt===(null==Ui?void 0:Ui.scope)?Ui:null;let l,c,u=!1,f=!1;if(jn(e)?(l=()=>e.value,u=On(e)):kn(e)?(l=()=>e,o=!0):k(e)?(f=!0,u=e.some((e=>kn(e)||On(e))),l=()=>e.map((e=>jn(e)?e.value:kn(e)?jo(e):M(e)?Yn(e,a,2):void 0))):l=M(e)?t?()=>Yn(e,a,2):()=>{if(!a||!a.isUnmounted)return c&&c(),Gn(e,a,3,[d])}:v,t&&o){const e=l;l=()=>jo(e())}let h,d=e=>{c=b.onStop=()=>{Yn(e,a,4)}};if(Ki){if(d=v,t?n&&Gn(t,a,3,[l(),f?[]:void 0,d]):l(),"sync"!==r)return v;{const e=ss();h=e.__watcherHandles||(e.__watcherHandles=[])}}let p=f?new Array(e.length).fill(Po):Po;const m=()=>{if(b.active)if(t){const e=b.run();(o||u||(f?e.some(((e,t)=>X(e,p[t]))):X(e,p)))&&(c&&c(),Gn(t,a,3,[e,p===Po?void 0:f&&p[0]===Po?[]:p,d]),p=e)}else b.run()};let y;m.allowRecurse=!!t,"sync"===r?y=m:"post"===r?y=()=>di(m,a&&a.suspense):(m.pre=!0,a&&(m.id=a.uid),y=()=>ao(m));const b=new kt(l,y);t?n?m():p=b.run():"post"===r?di(b.run.bind(b),a&&a.suspense):b.run();const _=()=>{b.stop(),a&&a.scope&&S(a.scope.effects,b)};return h&&h.push(_),_}function Lo(e,t,n){const o=this.proxy,r=I(e)?e.includes(".")?Ro(o,e):()=>o[e]:e.bind(o,o);let i;M(t)?i=t:(i=t.handler,n=t);const s=Ui;Yi(this);const a=Ao(r,i.bind(o),n);return s?Yi(s):Gi(),a}function Ro(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function jo(e,t){if(!$(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),jn(e))jo(e.value,t);else if(k(e))for(let n=0;n<e.length;n++)jo(e[n],t);else if(O(e)||E(e))e.forEach((e=>{jo(e,t)}));else if(B(e))for(const n in e)jo(e[n],t);return e}const Bo=[Function,Array],No={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Bo,onEnter:Bo,onAfterEnter:Bo,onEnterCancelled:Bo,onBeforeLeave:Bo,onLeave:Bo,onAfterLeave:Bo,onLeaveCancelled:Bo,onBeforeAppear:Bo,onAppear:Bo,onAfterAppear:Bo,onAppearCancelled:Bo},Do={name:"BaseTransition",props:No,setup(e,{slots:t}){const n=Xi(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return pr((()=>{e.isMounted=!0})),vr((()=>{e.isUnmounting=!0})),e}();let r;return()=>{const i=t.default&&Uo(t.default(),!0);if(!i||!i.length)return;let s=i[0];if(i.length>1)for(const e of i)if(e.type!==bi){s=e;break}const a=In(e),{mode:l}=a;if(o.isLeaving)return Wo(s);const c=zo(s);if(!c)return Wo(s);const u=Vo(c,a,o,n);Ho(c,u);const f=n.subTree,h=f&&zo(f);let d=!1;const{getTransitionKey:p}=c.type;if(p){const e=p();void 0===r?r=e:e!==r&&(r=e,d=!0)}if(h&&h.type!==bi&&(!Ii(c,h)||d)){const e=Vo(h,a,o,n);if(Ho(h,e),"out-in"===l)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&n.update()},Wo(s);"in-out"===l&&c.type!==bi&&(e.delayLeave=(e,t,n)=>{qo(o,h)[String(h.key)]=h,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return s}}},Fo=Do;function qo(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function Vo(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:f,onLeave:h,onAfterLeave:d,onLeaveCancelled:p,onBeforeAppear:g,onAppear:m,onAfterAppear:v,onAppearCancelled:y}=t,b=String(e.key),_=qo(n,e),w=(e,t)=>{e&&Gn(e,o,9,t)},x=(e,t)=>{const n=t[1];w(e,t),k(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},S={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=g||a}t._leaveCb&&t._leaveCb(!0);const i=_[b];i&&Ii(e,i)&&i.el._leaveCb&&i.el._leaveCb(),w(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=m||l,o=v||c,i=y||u}let s=!1;const a=e._enterCb=t=>{s||(s=!0,w(t?i:o,[e]),S.delayedLeave&&S.delayedLeave(),e._enterCb=void 0)};t?x(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return o();w(f,[t]);let i=!1;const s=t._leaveCb=n=>{i||(i=!0,o(),w(n?p:d,[t]),t._leaveCb=void 0,_[r]===e&&delete _[r])};_[r]=e,h?x(h,[t,s]):s()},clone:e=>Vo(e,t,n,o)};return S}function Wo(e){if(Qo(e))return(e=ji(e)).children=null,e}function zo(e){return Qo(e)?e.children?e.children[0]:void 0:e}function Ho(e,t){6&e.shapeFlag&&e.component?Ho(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Uo(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===vi?(128&s.patchFlag&&r++,o=o.concat(Uo(s.children,t,a))):(t||s.type!==bi)&&o.push(null!=a?ji(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}function Xo(e){return M(e)?{setup:e,name:e.name}:e}const Yo=e=>!!e.type.__asyncLoader;function Go(e){M(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const f=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,f()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return Xo({name:"AsyncComponentWrapper",__asyncLoader:f,get __asyncResolved(){return l},setup(){const e=Ui;if(l)return()=>Jo(l,e);const t=t=>{c=null,Jn(t,e,13,!o)};if(s&&e.suspense||Ki)return f().then((t=>()=>Jo(t,e))).catch((e=>(t(e),()=>o?Ri(o,{error:e}):null)));const a=Bn(!1),u=Bn(),h=Bn(!!r);return r&&setTimeout((()=>{h.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),f().then((()=>{a.value=!0,e.parent&&Qo(e.parent.vnode)&&ao(e.parent.update)})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?Jo(l,e):u.value&&o?Ri(o,{error:u.value}):n&&!h.value?Ri(n):void 0}})}function Jo(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=Ri(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const Qo=e=>e.type.__isKeepAlive;class Ko{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const Zo={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=Xi(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new Ko(e.max);r.pruneCacheEntry=s;let i=null;function s(t){var o;!i||!Ii(t,i)||"key"===e.matchBy&&t.key!==i.key?(sr(o=t),u(o,n,a,!0)):i&&sr(i)}const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:f}}}=o,h=f("div");function d(t){r.forEach(((n,o)=>{const i=lr(n,e.matchBy);!i||t&&t(i)||(r.delete(o),s(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,Y(i.ba),i.isDeactivated=e}c(e,t,n,0,a),l(i.vnode,e,t,n,i,a,o,e.slotScopeIds,r),di((()=>{i.isDeactivated=!1,i.a&&Y(i.a);const t=e.props&&e.props.onVnodeMounted;t&&Wi(t,i.parent,e)}),a)},o.deactivate=e=>{const t=e.component;t.bda&&cr(t.bda),c(e,h,null,1,a),di((()=>{t.bda&&ur(t.bda),t.da&&Y(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&Wi(n,t.parent,e),t.isDeactivated=!0}),a)},$o((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&d((t=>tr(e,t))),t&&d((e=>!tr(t,e)))}),{flush:"post",deep:!0});let p=null;const g=()=>{null!=p&&r.set(p,ar(n.subTree))};return pr(g),mr(g),vr((()=>{r.forEach(((t,o)=>{r.delete(o),s(t);const{subTree:i,suspense:a}=n,l=ar(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&Y(l.component.bda),sr(l);const e=l.component.da;e&&di(e,a)}}))})),()=>{if(p=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!Mi(o)||!(4&o.shapeFlag)&&!Eo(o.type))return i=null,o;let s=ar(o);const a=s.type,l=lr(s,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!tr(c,l))||u&&l&&tr(u,l))return i=s,o;const f=null==s.key?a:s.key,h=r.get(f);return s.el&&(s=ji(s),Eo(o.type)&&(o.ssContent=s)),p=f,h&&(s.el=h.el,s.component=h.component,s.transition&&Ho(s,s.transition),s.shapeFlag|=512),s.shapeFlag|=256,i=s,Eo(o.type)?o:s}}},er=Zo;function tr(e,t){return k(e)?e.some((e=>tr(e,t))):I(e)?e.split(",").includes(t):!!e.test&&e.test(t)}function nr(e,t){rr(e,"a",t)}function or(e,t){rr(e,"da",t)}function rr(e,t,n=Ui){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,fr(t,o,n),n){let e=n.parent;for(;e&&e.parent;)Qo(e.parent.vnode)&&ir(o,t,n,e),e=e.parent}}function ir(e,t,n,o){const r=fr(t,e,o,!0);yr((()=>{S(o[t],r)}),n)}function sr(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function ar(e){return Eo(e.type)?e.ssContent:e}function lr(e,t){if("name"===t){const t=e.type;return ns(Yo(e)?t.__asyncResolved||{}:t)}return String(e.key)}function cr(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function ur(e){e.forEach((e=>e.__called=!1))}function fr(e,t,n=Ui,o=!1){if(n){if(function(e){return rt.indexOf(e)>-1}(e)&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return it.indexOf(e)>-1}(e))){const o=n.proxy;Gn(t.bind(o),n,e,ge===e?[o.$page.options]:[])}}const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;It(),Yi(n);const r=Gn(t,n,e,o);return Gi(),Pt(),r});return o?r.unshift(i):r.push(i),i}}const hr=e=>(t,n=Ui)=>(!Ki||"sp"===e)&&fr(e,((...e)=>t(...e)),n),dr=hr("bm"),pr=hr("m"),gr=hr("bu"),mr=hr("u"),vr=hr("bum"),yr=hr("um"),br=hr("sp"),_r=hr("rtg"),wr=hr("rtc");function xr(e,t=Ui){fr("ec",e,t)}function Sr(e,t){const n=bo;if(null===n)return e;const o=ts(n)||n.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,n,s,a=g]=t[i];e&&(M(e)&&(e={mounted:e,updated:e}),e.deep&&jo(n),r.push({dir:e,instance:o,value:n,oldValue:void 0,arg:s,modifiers:a}))}return e}function Tr(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(It(),Gn(l,n,8,[e.el,a,e,t]),Pt())}}const Cr="components";function kr(e,t){return Mr(Cr,e,!0,t)||e}const Er=Symbol();function Or(e){return I(e)?Mr(Cr,e,!1)||e:e||Er}function Mr(e,t,n=!0,o=!1){const r=bo||Ui;if(r){const n=r.type;if(e===Cr){const e=ns(n,!1);if(e&&(e===t||e===V(t)||e===H(V(t))))return n}const i=Ir(r[e]||n[e],t)||Ir(r.appContext[e],t);return!i&&o?n:i}}function Ir(e,t){return e&&(e[t]||e[V(t)]||e[H(V(t))])}function Pr(e,t,n,o){let r;const i=n&&n[o];if(k(e)||I(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if($(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function $r(e,t,n={},o,r){if(bo.isCE||bo.parent&&Yo(bo.parent)&&bo.parent.isCE)return"default"!==t&&(n.name=t),Ri("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),Si();const s=i&&Ar(i(n)),a=Oi(vi,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Ar(e){return e.some((e=>!Mi(e)||e.type!==bi&&!(e.type===vi&&!Ar(e.children))))?e:null}const Lr=e=>e?Ji(e)?ts(e)||e.proxy:Lr(e.parent):null,Rr=x(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Lr(e.parent),$root:e=>Lr(e.root),$emit:e=>e.emit,$options:e=>Vr(e),$forceUpdate:e=>e.f||(e.f=()=>ao(e.update)),$nextTick:e=>e.n||(e.n=so.bind(e.proxy)),$watch:e=>Lo.bind(e)}),jr=(e,t)=>e!==g&&!e.__isScriptSetup&&C(e,t),Br={get({_:e},t){const{ctx:n,setupState:o,data:r,props:i,accessCache:s,type:a,appContext:l}=e;let c;if("$"!==t[0]){const a=s[t];if(void 0!==a)switch(a){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(jr(o,t))return s[t]=1,o[t];if(r!==g&&C(r,t))return s[t]=2,r[t];if((c=e.propsOptions[0])&&C(c,t))return s[t]=3,i[t];if(n!==g&&C(n,t))return s[t]=4,n[t];Nr&&(s[t]=0)}}const u=Rr[t];let f,h;return u?("$attrs"===t&&$t(e,0,t),u(e)):(f=a.__cssModules)&&(f=f[t])?f:n!==g&&C(n,t)?(s[t]=4,n[t]):(h=l.config.globalProperties,C(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:i}=e;return jr(r,t)?(r[t]=n,!0):o!==g&&C(o,t)?(o[t]=n,!0):!C(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:i}},s){let a;return!!n[s]||e!==g&&C(e,s)||jr(t,s)||(a=i[0])&&C(a,s)||C(o,s)||C(Rr,s)||C(r.config.globalProperties,s)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:C(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let Nr=!0;function Dr(e){const t=Vr(e),n=e.proxy,o=e.ctx;Nr=!1,t.beforeCreate&&Fr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:s,watch:a,provide:l,inject:c,created:u,beforeMount:f,mounted:h,beforeUpdate:d,updated:p,activated:g,deactivated:m,beforeDestroy:y,beforeUnmount:b,destroyed:_,unmounted:w,render:x,renderTracked:S,renderTriggered:T,errorCaptured:C,serverPrefetch:E,expose:O,inheritAttrs:I,components:P,directives:A,filters:L}=t;if(c&&function(e,t,n=v,o=!1){k(e)&&(e=Ur(e));for(const r in e){const n=e[r];let i;i=$(n)?"default"in n?Mo(n.from||r,n.default,!0):Mo(n.from||r):Mo(n),jn(i)&&o?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[r]=i}}(c,o,null,e.appContext.config.unwrapInjectedRef),s)for(const v in s){const e=s[v];M(e)&&(o[v]=e.bind(n))}if(r){const t=r.call(n,n);$(t)&&(e.data=xn(t))}if(Nr=!0,i)for(const k in i){const e=i[k],t=M(e)?e.bind(n,n):M(e.get)?e.get.bind(n,n):v,r=!M(e)&&M(e.set)?e.set.bind(n):v,s=os({get:t,set:r});Object.defineProperty(o,k,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(a)for(const v in a)qr(a[v],o,n,v);if(l){const e=M(l)?l.call(n):l;Reflect.ownKeys(e).forEach((t=>{Oo(t,e[t])}))}function R(e,t){k(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(u&&Fr(u,e,"c"),R(dr,f),R(pr,h),R(gr,d),R(mr,p),R(nr,g),R(or,m),R(xr,C),R(wr,S),R(_r,T),R(vr,b),R(yr,w),R(br,E),k(O))if(O.length){const t=e.exposed||(e.exposed={});O.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});x&&e.render===v&&(e.render=x),null!=I&&(e.inheritAttrs=I),P&&(e.components=P),A&&(e.directives=A);const j=e.appContext.config.globalProperties.$applyOptions;j&&j(t,e,n)}function Fr(e,t,n){Gn(k(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function qr(e,t,n,o){const r=o.includes(".")?Ro(n,o):()=>n[o];if(I(e)){const n=t[e];M(n)&&$o(r,n)}else if(M(e))$o(r,e.bind(n));else if($(e))if(k(e))e.forEach((e=>qr(e,t,n,o)));else{const o=M(e.handler)?e.handler.bind(n):t[e.handler];M(o)&&$o(r,o,e)}}function Vr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>Wr(l,e,s,!0))),Wr(l,t,s)):l=t,$(t)&&i.set(t,l),l}function Wr(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&Wr(e,i,n,!0),r&&r.forEach((t=>Wr(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=zr[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const zr={data:Hr,props:Yr,emits:Yr,methods:Yr,computed:Yr,beforeCreate:Xr,created:Xr,beforeMount:Xr,mounted:Xr,beforeUpdate:Xr,updated:Xr,beforeDestroy:Xr,beforeUnmount:Xr,destroyed:Xr,unmounted:Xr,activated:Xr,deactivated:Xr,errorCaptured:Xr,serverPrefetch:Xr,components:Yr,directives:Yr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=x(Object.create(null),e);for(const o in t)n[o]=Xr(e[o],t[o]);return n},provide:Hr,inject:function(e,t){return Yr(Ur(e),Ur(t))}};function Hr(e,t){return t?e?function(){return x(M(e)?e.call(this,this):e,M(t)?t.call(this,this):t)}:t:e}function Ur(e){if(k(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Xr(e,t){return e?[...new Set([].concat(e,t))]:t}function Yr(e,t){return e?x(x(Object.create(null),e),t):t}function Gr(e,t,n,o){const[r,i]=e.propsOptions;let s,a=!1;if(t)for(let l in t){if(D(l))continue;const c=t[l];let u;r&&C(r,u=V(l))?i&&i.includes(u)?(s||(s={}))[u]=c:n[u]=c:yo(e.emitsOptions,l)||l in o&&c===o[l]||(o[l]=c,a=!0)}if(i){const t=In(n),o=s||g;for(let s=0;s<i.length;s++){const a=i[s];n[a]=Jr(r,t,a,o[a],e,!C(o,a))}}return a}function Jr(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=C(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&M(e)){const{propsDefaults:i}=r;n in i?o=i[n]:(Yi(r),o=i[n]=e.call(null,t),Gi())}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==z(n)||(o=!0))}return o}function Qr(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const i=e.props,s={},a=[];let l=!1;if(!M(e)){const o=e=>{l=!0;const[n,o]=Qr(e,t,!0);x(s,n),o&&a.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!i&&!l)return $(e)&&o.set(e,m),m;if(k(i))for(let u=0;u<i.length;u++){const e=V(i[u]);Kr(e)&&(s[e]=g)}else if(i)for(const u in i){const e=V(u);if(Kr(e)){const t=i[u],n=s[e]=k(t)||M(t)?{type:t}:Object.assign({},t);if(n){const t=ti(Boolean,n.type),o=ti(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||C(n,"default"))&&a.push(e)}}}const c=[s,a];return $(e)&&o.set(e,c),c}function Kr(e){return"$"!==e[0]}function Zr(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function ei(e,t){return Zr(e)===Zr(t)}function ti(e,t){return k(t)?t.findIndex((t=>ei(t,e))):M(t)&&ei(t,e)?0:-1}const ni=e=>"_"===e[0]||"$stable"===e,oi=e=>k(e)?e.map(Di):[Di(e)],ri=(e,t,n)=>{if(t._n)return t;const o=xo(((...e)=>oi(t(...e))),n);return o._c=!1,o},ii=(e,t,n)=>{const o=e._ctx;for(const r in e){if(ni(r))continue;const n=e[r];if(M(n))t[r]=ri(0,n,o);else if(null!=n){const e=oi(n);t[r]=()=>e}}},si=(e,t)=>{const n=oi(t);e.slots.default=()=>n},ai=(e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=In(t),G(t,"_",n)):ii(t,e.slots={})}else e.slots={},t&&si(e,t);G(e.slots,Pi,1)},li=(e,t,n)=>{const{vnode:o,slots:r}=e;let i=!0,s=g;if(32&o.shapeFlag){const e=t._;e?n&&1===e?i=!1:(x(r,t),n||1!==e||delete r._):(i=!t.$stable,ii(t,r)),s=t}else t&&(si(e,t),s={default:1});if(i)for(const a in r)ni(a)||a in s||delete r[a]};function ci(){return{app:null,config:{isNativeTag:y,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ui=0;function fi(e,t){return function(n,o=null){M(n)||(n=Object.assign({},n)),null==o||$(o)||(o=null);const r=ci(),i=new Set;let s=!1;const a=r.app={_uid:ui++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:as,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&M(e.install)?(i.add(e),e.install(a,...t)):M(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=Ri(n,o);return u.appContext=r,l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,ts(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a)};return a}}function hi(e,t,n,o,r=!1){if(k(e))return void e.forEach(((e,i)=>hi(e,t&&(k(t)?t[i]:t),n,o,r)));if(Yo(o)&&!r)return;const i=4&o.shapeFlag?ts(o.component)||o.component.proxy:o.el,s=r?null:i,{i:a,r:l}=e,c=t&&t.r,u=a.refs===g?a.refs={}:a.refs,f=a.setupState;if(null!=c&&c!==l&&(I(c)?(u[c]=null,C(f,c)&&(f[c]=null)):jn(c)&&(c.value=null)),M(l))Yn(l,a,12,[s,u]);else{const t=I(l),o=jn(l);if(t||o){const a=()=>{if(e.f){const n=t?C(f,l)?f[l]:u[l]:l.value;r?k(n)&&S(n,i):k(n)?n.includes(i)||n.push(i):t?(u[l]=[i],C(f,l)&&(f[l]=u[l])):(l.value=[i],e.k&&(u[e.k]=l.value))}else t?(u[l]=s,C(f,l)&&(f[l]=s)):o&&(l.value=s,e.k&&(u[e.k]=s))};s?(a.id=-1,di(a,n)):a()}}}const di=function(e,t){var n;t&&t.pendingBranch?k(e)?t.effects.push(...e):t.effects.push(e):(k(n=e)?to.push(...n):no&&no.includes(n,n.allowRecurse?oo+1:oo)||to.push(n),lo())};function pi(e){return function(e,t){Z().__VUE__=!0;const{insert:n,remove:o,patchProp:r,forcePatchProp:i,createElement:s,createText:a,createComment:l,setText:c,setElementText:u,parentNode:f,nextSibling:h,setScopeId:d=v,insertStaticContent:p}=e,y=(e,t,n,o=null,r=null,i=null,s=!1,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Ii(e,t)&&(o=ee(e),U(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:f}=t;switch(c){case yi:b(e,t,n,o);break;case bi:_(e,t,n,o);break;case _i:null==e&&w(t,n,o,s);break;case vi:$(e,t,n,o,r,i,s,a,l);break;default:1&f?T(e,t,n,o,r,i,s,a,l):6&f?L(e,t,n,o,r,i,s,a,l):(64&f||128&f)&&c.process(e,t,n,o,r,i,s,a,l,ne)}null!=u&&r&&hi(u,e&&e.ref,i,t||e,!t)},b=(e,t,o,r)=>{if(null==e)n(t.el=a(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&c(n,t.children)}},_=(e,t,o,r)=>{null==e?n(t.el=l(t.children||""),o,r):t.el=e.el},w=(e,t,n,o)=>{[e.el,e.anchor]=p(e.children,t,n,o,e.el,e.anchor)},x=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=h(e),n(e,o,r),e=i;n(t,o,r)},S=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=h(e),o(e),e=n;o(t)},T=(e,t,n,o,r,i,s,a,l)=>{s=s||"svg"===t.type,null==e?k(t,n,o,r,i,s,a,l):M(e,t,r,i,s,a,l)},k=(e,t,o,i,a,l,c,f)=>{let h,d;const{type:p,props:g,shapeFlag:m,transition:v,dirs:y}=e;if(h=e.el=s(e.type,l,g&&g.is,g),8&m?u(h,e.children):16&m&&O(e.children,h,null,i,a,l&&"foreignObject"!==p,c,f),y&&Tr(e,null,i,"created"),E(h,e,e.scopeId,c,i),g){for(const t in g)"value"===t||D(t)||r(h,t,null,g[t],l,e.children,i,a,K);"value"in g&&r(h,"value",null,g.value),(d=g.onVnodeBeforeMount)&&Wi(d,i,e)}Object.defineProperty(h,"__vueParentComponent",{value:i,enumerable:!1}),y&&Tr(e,null,i,"beforeMount");const b=(!a||a&&!a.pendingBranch)&&v&&!v.persisted;b&&v.beforeEnter(h),n(h,t,o),((d=g&&g.onVnodeMounted)||b||y)&&di((()=>{d&&Wi(d,i,e),b&&v.enter(h),y&&Tr(e,null,i,"mounted")}),a)},E=(e,t,n,o,r)=>{if(n&&d(e,n),o)for(let i=0;i<o.length;i++)d(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;E(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},O=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?Fi(e[c]):Di(e[c]);y(null,l,t,n,o,r,i,s,a)}},M=(e,t,n,o,s,a,l)=>{const c=t.el=e.el;let{patchFlag:f,dynamicChildren:h,dirs:d}=t;f|=16&e.patchFlag;const p=e.props||g,m=t.props||g;let v;n&&gi(n,!1),(v=m.onVnodeBeforeUpdate)&&Wi(v,n,t,e),d&&Tr(t,e,n,"beforeUpdate"),n&&gi(n,!0);const y=s&&"foreignObject"!==t.type;if(h?I(e.dynamicChildren,h,c,n,o,y,a):l||F(e,t,c,null,n,o,y,a,!1),f>0){if(16&f)P(c,t,p,m,n,o,s);else if(2&f&&p.class!==m.class&&r(c,"class",null,m.class,s),4&f&&r(c,"style",p.style,m.style,s),8&f){const a=t.dynamicProps;for(let t=0;t<a.length;t++){const l=a[t],u=p[l],f=m[l];(f!==u||"value"===l||i&&i(c,l))&&r(c,l,u,f,s,e.children,n,o,K)}}1&f&&e.children!==t.children&&u(c,t.children)}else l||null!=h||P(c,t,p,m,n,o,s);((v=m.onVnodeUpdated)||d)&&di((()=>{v&&Wi(v,n,t,e),d&&Tr(t,e,n,"updated")}),o)},I=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===vi||!Ii(l,c)||70&l.shapeFlag)?f(l.el):n;y(l,c,u,null,o,r,i,s,!0)}},P=(e,t,n,o,s,a,l)=>{if(n!==o){if(n!==g)for(const i in n)D(i)||i in o||r(e,i,n[i],null,l,t.children,s,a,K);for(const c in o){if(D(c))continue;const u=o[c],f=n[c];(u!==f&&"value"!==c||i&&i(e,c))&&r(e,c,f,u,l,t.children,s,a,K)}"value"in o&&r(e,"value",n.value,o.value)}},$=(e,t,o,r,i,s,l,c,u)=>{const f=t.el=e?e.el:a(""),h=t.anchor=e?e.anchor:a("");let{patchFlag:d,dynamicChildren:p,slotScopeIds:g}=t;g&&(c=c?c.concat(g):g),null==e?(n(f,o,r),n(h,o,r),O(t.children,o,h,i,s,l,c,u)):d>0&&64&d&&p&&e.dynamicChildren?(I(e.dynamicChildren,p,o,i,s,l,c),(null!=t.key||i&&t===i.subTree)&&mi(e,t,!0)):F(e,t,o,h,i,s,l,c,u)},L=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):R(t,n,o,r,i,s,l):j(e,t,l)},R=(e,t,n,o,r,i,s)=>{const a=e.component=function(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||zi,i={uid:Hi++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new pt(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Qr(o,r),emitsOptions:vo(o,r),emit:null,emitted:null,propsDefaults:g,inheritAttrs:o.inheritAttrs,ctx:g,data:g,props:g,attrs:g,slots:g,refs:g,setupState:g,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};i.ctx={_:i},i.root=t?t.root:i,i.emit=go.bind(null,i),i.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(i);return i}(e,o,r);if(Qo(e)&&(a.ctx.renderer=ne),function(e,t=!1){Ki=t;const{props:n,children:o}=e.vnode,r=Ji(e);(function(e,t,n,o=!1){const r={},i={};G(i,Pi,1),e.propsDefaults=Object.create(null),Gr(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:Sn(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),ai(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Pn(new Proxy(e.ctx,Br));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=function(e){return new Proxy(e.attrs,{get:(t,n)=>($t(e,0,"$attrs"),t[n])})}(e))},slots:e.slots,emit:e.emit,expose:t}}(e):null;Yi(e),It();const r=Yn(o,e,0,[e.props,n]);if(Pt(),Gi(),A(r)){if(r.then(Gi,Gi),t)return r.then((n=>{Zi(e,n,t)})).catch((t=>{Jn(t,e,0)}));e.asyncDep=r}else Zi(e,r,t)}else es(e,t)}(e,t):void 0;Ki=!1}(a),a.asyncDep){if(r&&r.registerDep(a,B),!e.el){const e=a.subTree=Ri(bi);_(null,e,t,n)}}else B(a,e,t,n,r,i,s)},j=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||ko(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?ko(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!yo(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void N(o,t,n);o.next=t,function(e){const t=Zn.indexOf(e);t>eo&&Zn.splice(t,1)}(o.update),o.update()}else t.el=e.el,o.vnode=t},B=(e,t,n,o,r,i,s)=>{const a=()=>{if(e.isMounted){let t,{next:n,bu:o,u:a,parent:l,vnode:c}=e,u=n;gi(e,!1),n?(n.el=c.el,N(e,n,s)):n=c,o&&Y(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&Wi(t,l,n,c),gi(e,!0);const h=So(e),d=e.subTree;e.subTree=h,y(d,h,f(d.el),ee(d),e,r,i),n.el=h.el,null===u&&function({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}(e,h.el),a&&di(a,r),(t=n.props&&n.props.onVnodeUpdated)&&di((()=>Wi(t,l,n,c)),r)}else{let s;const{el:a,props:l}=t,{bm:c,m:u,parent:f}=e,h=Yo(t);if(gi(e,!1),c&&Y(c),!h&&(s=l&&l.onVnodeBeforeMount)&&Wi(s,f,t),gi(e,!0),a&&re){const n=()=>{e.subTree=So(e),re(a,e.subTree,e,r,null)};h?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const s=e.subTree=So(e);y(null,s,n,o,e,r,i),t.el=s.el}if(u&&di(u,r),!h&&(s=l&&l.onVnodeMounted)){const e=t;di((()=>Wi(s,f,e)),r)}const{ba:d,a:p}=e;(256&t.shapeFlag||f&&Yo(f.vnode)&&256&f.vnode.shapeFlag)&&(d&&cr(d),p&&di(p,r),d&&di((()=>ur(d)),r)),e.isMounted=!0,t=n=o=null}},l=e.effect=new kt(a,(()=>ao(c)),e.scope),c=e.update=()=>l.run();c.id=e.uid,gi(e,!0),c()},N=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=In(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;Gr(e,t,r,i)&&(c=!0);for(const i in a)t&&(C(t,i)||(o=z(i))!==i&&C(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=Jr(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&C(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(yo(e.emitsOptions,s))continue;const u=t[s];if(l)if(C(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=V(s);r[t]=Jr(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&Lt(e,"set","$attrs")}(e,t.props,o,n),li(e,t.children,n),It(),co(),Pt()},F=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,f=e?e.shapeFlag:0,h=t.children,{patchFlag:d,shapeFlag:p}=t;if(d>0){if(128&d)return void W(c,h,n,o,r,i,s,a,l);if(256&d)return void q(c,h,n,o,r,i,s,a,l)}8&p?(16&f&&K(c,r,i),h!==c&&u(n,h)):16&f?16&p?W(c,h,n,o,r,i,s,a,l):K(c,r,i,!0):(8&f&&u(n,""),16&p&&O(h,n,o,r,i,s,a,l))},q=(e,t,n,o,r,i,s,a,l)=>{t=t||m;const c=(e=e||m).length,u=t.length,f=Math.min(c,u);let h;for(h=0;h<f;h++){const o=t[h]=l?Fi(t[h]):Di(t[h]);y(e[h],o,n,null,r,i,s,a,l)}c>u?K(e,r,i,!0,!1,f):O(t,n,o,r,i,s,a,l,f)},W=(e,t,n,o,r,i,s,a,l)=>{let c=0;const u=t.length;let f=e.length-1,h=u-1;for(;c<=f&&c<=h;){const o=e[c],u=t[c]=l?Fi(t[c]):Di(t[c]);if(!Ii(o,u))break;y(o,u,n,null,r,i,s,a,l),c++}for(;c<=f&&c<=h;){const o=e[f],c=t[h]=l?Fi(t[h]):Di(t[h]);if(!Ii(o,c))break;y(o,c,n,null,r,i,s,a,l),f--,h--}if(c>f){if(c<=h){const e=h+1,f=e<u?t[e].el:o;for(;c<=h;)y(null,t[c]=l?Fi(t[c]):Di(t[c]),n,f,r,i,s,a,l),c++}}else if(c>h)for(;c<=f;)U(e[c],r,i,!0),c++;else{const d=c,p=c,g=new Map;for(c=p;c<=h;c++){const e=t[c]=l?Fi(t[c]):Di(t[c]);null!=e.key&&g.set(e.key,c)}let v,b=0;const _=h-p+1;let w=!1,x=0;const S=new Array(_);for(c=0;c<_;c++)S[c]=0;for(c=d;c<=f;c++){const o=e[c];if(b>=_){U(o,r,i,!0);continue}let u;if(null!=o.key)u=g.get(o.key);else for(v=p;v<=h;v++)if(0===S[v-p]&&Ii(o,t[v])){u=v;break}void 0===u?U(o,r,i,!0):(S[u-p]=c+1,u>=x?x=u:w=!0,y(o,t[u],n,null,r,i,s,a,l),b++)}const T=w?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):m;for(v=T.length-1,c=_-1;c>=0;c--){const e=p+c,f=t[e],h=e+1<u?t[e+1].el:o;0===S[c]?y(null,f,n,h,r,i,s,a,l):w&&(v<0||c!==T[v]?H(f,n,h,2):v--)}}},H=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void H(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,ne);if(a===vi){n(s,t,o);for(let e=0;e<c.length;e++)H(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===_i)return void x(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),di((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},U=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:f,dirs:h}=e;if(null!=a&&hi(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const d=1&u&&h,p=!Yo(e);let g;if(p&&(g=s&&s.onVnodeBeforeUnmount)&&Wi(g,t,e),6&u)Q(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);d&&Tr(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,ne,o):c&&(i!==vi||f>0&&64&f)?K(c,t,n,!1,!0):(i===vi&&384&f||!r&&16&u)&&K(l,t,n),o&&X(e)}(p&&(g=s&&s.onVnodeUnmounted)||d)&&di((()=>{g&&Wi(g,t,e),d&&Tr(e,null,t,"unmounted")}),n)},X=e=>{const{type:t,el:n,anchor:r,transition:i}=e;if(t===vi)return void J(n,r);if(t===_i)return void S(e);const s=()=>{o(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:o}=i,r=()=>t(n,s);o?o(e.el,s,r):r()}else s()},J=(e,t)=>{let n;for(;e!==t;)n=h(e),o(e),e=n;o(t)},Q=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&Y(o),r.stop(),i&&(i.active=!1,U(s,e,t,n)),a&&di(a,t),di((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},K=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)U(e[s],t,n,o,r)},ee=e=>6&e.shapeFlag?ee(e.component.subTree):128&e.shapeFlag?e.suspense.next():h(e.anchor||e.el),te=(e,t,n)=>{null==e?t._vnode&&U(t._vnode,null,null,!0):y(t._vnode||null,e,t,null,null,null,n),co(),uo(),t._vnode=e},ne={p:y,um:U,m:H,r:X,mt:R,mc:O,pc:F,pbc:I,n:ee,o:e};let oe,re;t&&([oe,re]=t(ne));return{render:te,hydrate:oe,createApp:fi(te,oe)}}(e)}function gi({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function mi(e,t,n=!1){const o=e.children,r=t.children;if(k(o)&&k(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=Fi(r[i]),t.el=e.el),n||mi(e,t)),t.type===yi&&(t.el=e.el)}}const vi=Symbol(void 0),yi=Symbol(void 0),bi=Symbol(void 0),_i=Symbol(void 0),wi=[];let xi=null;function Si(e=!1){wi.push(xi=e?null:[])}let Ti=1;function Ci(e){Ti+=e}function ki(e){return e.dynamicChildren=Ti>0?xi||m:null,wi.pop(),xi=wi[wi.length-1]||null,Ti>0&&xi&&xi.push(e),e}function Ei(e,t,n,o,r,i){return ki(Li(e,t,n,o,r,i,!0))}function Oi(e,t,n,o,r){return ki(Ri(e,t,n,o,r,!0))}function Mi(e){return!!e&&!0===e.__v_isVNode}function Ii(e,t){return e.type===t.type&&e.key===t.key}const Pi="__vInternal",$i=({key:e})=>null!=e?e:null,Ai=({ref:e,ref_key:t,ref_for:n})=>null!=e?I(e)||jn(e)||M(e)?{i:bo,r:e,k:t,f:!!n}:e:null;function Li(e,t=null,n=null,o=0,r=null,i=(e===vi?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&$i(t),ref:t&&Ai(t),scopeId:_o,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:bo};return a?(qi(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=I(n)?8:16),Ti>0&&!s&&xi&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&xi.push(l),l}const Ri=function(e,t=null,n=null,o=0,r=null,s=!1){e&&e!==Er||(e=bi);if(Mi(e)){const o=ji(e,t,!0);return n&&qi(o,n),Ti>0&&!s&&xi&&(6&o.shapeFlag?xi[xi.indexOf(e)]=o:xi.push(o)),o.patchFlag|=-2,o}a=e,M(a)&&"__vccOpts"in a&&(e=e.__vccOpts);var a;if(t){t=function(e){return e?Mn(e)||Pi in e?x({},e):e:null}(t);let{class:e,style:n}=t;e&&!I(e)&&(t.class=u(e)),$(n)&&(Mn(n)&&!k(n)&&(n=x({},n)),t.style=i(n))}const l=I(e)?1:Eo(e)?128:(e=>e.__isTeleport)(e)?64:$(e)?4:M(e)?2:0;return Li(e,t,n,o,r,l,s,!0)};function ji(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?Vi(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&$i(a),ref:t&&t.ref?n&&r?k(r)?r.concat(Ai(t)):[r,Ai(t)]:Ai(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==vi?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ji(e.ssContent),ssFallback:e.ssFallback&&ji(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Bi(e=" ",t=0){return Ri(yi,null,e,t)}function Ni(e="",t=!1){return t?(Si(),Oi(bi,null,e)):Ri(bi,null,e)}function Di(e){return null==e||"boolean"==typeof e?Ri(bi):k(e)?Ri(vi,null,e.slice()):"object"==typeof e?Fi(e):Ri(yi,null,String(e))}function Fi(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:ji(e)}function qi(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(k(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),qi(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Pi in t?3===o&&bo&&(1===bo.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=bo}}else M(t)?(t={default:t,_ctx:bo},n=32):(t=String(t),64&o?(n=16,t=[Bi(t)]):n=8);e.children=t,e.shapeFlag|=n}function Vi(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=u([t.class,o.class]));else if("style"===e)t.style=i([t.style,o.style]);else if(_(e)){const n=t[e],r=o[e];!r||n===r||k(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function Wi(e,t,n,o=null){Gn(e,t,7,[n,o])}const zi=ci();let Hi=0;let Ui=null;const Xi=()=>Ui||bo,Yi=e=>{Ui=e,e.scope.on()},Gi=()=>{Ui&&Ui.scope.off(),Ui=null};function Ji(e){return 4&e.vnode.shapeFlag}let Qi,Ki=!1;function Zi(e,t,n){M(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:$(t)&&(e.setupState=Wn(t)),es(e,n)}function es(e,t,n){const o=e.type;if(!e.render){if(!t&&Qi&&!o.render){const t=o.template||Vr(e).template;if(t){const{isCustomElement:n,compilerOptions:r}=e.appContext.config,{delimiters:i,compilerOptions:s}=o,a=x(x({isCustomElement:n,delimiters:i},r),s);o.render=Qi(t,a)}}e.render=o.render||v}Yi(e),It(),Dr(e),Pt(),Gi()}function ts(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Wn(Pn(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Rr?Rr[n](e):void 0,has:(e,t)=>t in e||t in Rr}))}function ns(e,t=!0){return M(e)?e.displayName||e.name:e.name||t&&e.__name}const os=(e,t)=>function(e,t,n=!1){let o,r;const i=M(e);return i?(o=e,r=v):(o=e.get,r=e.set),new Xn(o,r,i||!r,n)}(e,0,Ki);function rs(e,t,n){const o=arguments.length;return 2===o?$(t)&&!k(t)?Mi(t)?Ri(e,null,[t]):Ri(e,t):Ri(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Mi(n)&&(n=[n]),Ri(e,t,n))}const is=Symbol(""),ss=()=>Mo(is),as="3.2.47",ls="undefined"!=typeof document?document:null,cs=ls&&ls.createElement("template"),us={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t?ls.createElementNS("http://www.w3.org/2000/svg",e):ls.createElement(e,n?{is:n}:void 0);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>ls.createTextNode(e),createComment:e=>ls.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ls.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{cs.innerHTML=o?`<svg>${e}</svg>`:e;const r=cs.content;if(o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};const fs=/\s*!important$/;function hs(e,t,n){if(k(n))n.forEach((n=>hs(e,t,n)));else if(null==n&&(n=""),n=xs(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=ps[t];if(n)return n;let o=V(t);if("filter"!==o&&o in e)return ps[t]=o;o=H(o);for(let r=0;r<ds.length;r++){const n=ds[r]+o;if(n in e)return ps[t]=n}return t}(e,t);fs.test(n)?e.setProperty(z(o),n.replace(fs,""),"important"):e[o]=n}}const ds=["Webkit","Moz","ms"],ps={};const{unit:gs,unitRatio:ms,unitPrecision:vs}={unit:"rem",unitRatio:10/320,unitPrecision:5},ys=(bs=gs,_s=ms,ws=vs,e=>e.replace(Ge,((e,t)=>{if(!t)return e;if(1===_s)return`${t}${bs}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*_s,ws);return 0===n?"0":`${n}${bs}`})));var bs,_s,ws;const xs=e=>I(e)?ys(e):e,Ss="http://www.w3.org/1999/xlink";function Ts(e,t,n,o,r=null){const i=e._vei||(e._vei={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(Cs.test(e)){let n;for(t={};n=e.match(Cs);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):z(e.slice(2));return[n,t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&k(i)){const n=Ms(e,i);for(let o=0;o<n.length;o++){const i=n[o];Gn(i,t,5,i.__wwe?[e]:r(e))}}else Gn(Ms(e,i),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=Os(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const Cs=/(?:Once|Passive|Capture)$/;let ks=0;const Es=Promise.resolve(),Os=()=>ks||(Es.then((()=>ks=0)),ks=Date.now());function Ms(e,t){if(k(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const Is=/^on[a-z]/;const Ps="transition",$s="animation",As=(e,{slots:t})=>rs(Fo,function(e){const t={};for(const x in e)x in Ls||(t[x]=e[x]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:c=s,appearToClass:u=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:d=`${n}-leave-to`}=e,p=function(e){if(null==e)return null;if($(e))return[Bs(e.enter),Bs(e.leave)];{const t=Bs(e);return[t,t]}}(r),g=p&&p[0],m=p&&p[1],{onBeforeEnter:v,onEnter:y,onEnterCancelled:b,onLeave:_,onLeaveCancelled:w,onBeforeAppear:S=v,onAppear:T=y,onAppearCancelled:C=b}=t,k=(e,t,n)=>{Ds(e,t?u:a),Ds(e,t?c:s),n&&n()},E=(e,t)=>{e._isLeaving=!1,Ds(e,f),Ds(e,d),Ds(e,h),t&&t()},O=e=>(t,n)=>{const r=e?T:y,s=()=>k(t,e,n);Rs(r,[t,s]),Fs((()=>{Ds(t,e?l:i),Ns(t,e?u:a),js(r)||Vs(t,o,g,s)}))};return x(t,{onBeforeEnter(e){Rs(v,[e]),Ns(e,i),Ns(e,s)},onBeforeAppear(e){Rs(S,[e]),Ns(e,l),Ns(e,c)},onEnter:O(!1),onAppear:O(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>E(e,t);Ns(e,f),document.body.offsetHeight,Ns(e,h),Fs((()=>{e._isLeaving&&(Ds(e,f),Ns(e,d),js(_)||Vs(e,o,m,n))})),Rs(_,[e,n])},onEnterCancelled(e){k(e,!1),Rs(b,[e])},onAppearCancelled(e){k(e,!0),Rs(C,[e])},onLeaveCancelled(e){E(e),Rs(w,[e])}})}(e),t);As.displayName="Transition";const Ls={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};As.props=x({},No,Ls);const Rs=(e,t=[])=>{k(e)?e.forEach((e=>e(...t))):e&&e(...t)},js=e=>!!e&&(k(e)?e.some((e=>e.length>1)):e.length>1);function Bs(e){return Q(e)}function Ns(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function Ds(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function Fs(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let qs=0;function Vs(e,t,n,o){const r=e._endId=++qs,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o(`${Ps}Delay`),i=o(`${Ps}Duration`),s=Ws(r,i),a=o(`${$s}Delay`),l=o(`${$s}Duration`),c=Ws(a,l);let u=null,f=0,h=0;t===Ps?s>0&&(u=Ps,f=s,h=i.length):t===$s?c>0&&(u=$s,f=c,h=l.length):(f=Math.max(s,c),u=f>0?s>c?Ps:$s:null,h=u?u===Ps?i.length:l.length:0);const d=u===Ps&&/\b(transform|all)(,|$)/.test(o(`${Ps}Property`).toString());return{type:u,timeout:f,propCount:h,hasTransform:d}}(e,t);if(!s)return o();const c=s+"end";let u=0;const f=()=>{e.removeEventListener(c,h),i()},h=t=>{t.target===e&&++u>=l&&f()};setTimeout((()=>{u<l&&f()}),a+1),e.addEventListener(c,h)}function Ws(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>zs(t)+zs(e[n]))))}function zs(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}const Hs=["ctrl","shift","alt","meta"],Us={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Hs.some((n=>e[`${n}Key`]&&!t.includes(n)))},Xs=(e,t)=>(n,...o)=>{for(let e=0;e<t.length;e++){const o=Us[t[e]];if(o&&o(n,t))return}return e(n,...o)},Ys={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Gs(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Gs(e,!0),o.enter(e)):o.leave(e,(()=>{Gs(e,!1)})):Gs(e,t))},beforeUnmount(e,{value:t}){Gs(e,t)}};function Gs(e,t){e.style.display=t?e._vod:"none"}const Js=x({patchProp:(e,t,n,o,r=!1,i,s,a,l)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;so((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e._vtc;i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,r):"style"===t?function(e,t,n){const o=e.style,r=I(n);if(n&&!r){if(t&&!I(t))for(const e in t)null==n[e]&&hs(o,e,"");for(const e in n)hs(o,e,n[e])}else{const i=o.display;r?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=i)}const{__wxsStyle:i}=e;if(i)for(const s in i)hs(o,s,i[s])}(e,n,o):_(t)?w(t)||Ts(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&Is.test(t)&&M(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(Is.test(t)&&I(n))return!1;return t in e}(e,t,o,r))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const o=null==n?"":n;return e.value===o&&"OPTION"!==e.tagName||(e.value=o),void(null==n&&e.removeAttribute(t))}let a=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=h(n):null==n&&"string"===o?(n="",a=!0):"number"===o&&(n=0,a=!0)}try{e[t]=n}catch(YC){}a&&e.removeAttribute(t)}(e,t,o,i,s,a,l):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Ss,t.slice(6,t.length)):e.setAttributeNS(Ss,t,n);else{const o=f(t);null==n||o&&!h(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,r))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},us);let Qs;const Ks=(...e)=>{const t=(Qs||(Qs=pi(Js))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(I(e)){return document.querySelector(e)}return e}(e);if(!o)return;const r=t._component;M(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const Zs=["{","}"];const ea=/^(?:\d)+/,ta=/^(?:\w)+/;const na="zh-Hans",oa="zh-Hant",ra="en",ia="fr",sa="es",aa=Object.prototype.hasOwnProperty,la=(e,t)=>aa.call(e,t),ca=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Zs){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=ea.test(t)?"list":a&&ta.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function ua(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return na;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?na:e.indexOf("-hant")>-1?oa:(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?oa:na);var n;const o=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,[ra,ia,sa]);return o||void 0}class fa{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale=ra,this.fallbackLocale=ra,this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||ca,this.messages=n||{},this.setLocale(e||ra),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=ua(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{la(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=ua(t,this.messages))&&(o=this.messages[t]):n=t,la(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function ha(e,t={},n,o){"string"!=typeof e&&([e,t]=[t,e]),"string"!=typeof e&&(e="undefined"!=typeof uni&&Uh?Uh():"undefined"!=typeof global&&global.getLocale?global.getLocale():ra),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||ra);const r=new fa({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=xm().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function da(e,t){return e.indexOf(t[0])>-1}
/*!
  * vue-router v4.4.3
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const pa="undefined"!=typeof document;const ga=Object.assign;function ma(e,t){const n={};for(const o in t){const r=t[o];n[o]=ya(r)?r.map(e):e(r)}return n}const va=()=>{},ya=Array.isArray,ba=/#/g,_a=/&/g,wa=/\//g,xa=/=/g,Sa=/\?/g,Ta=/\+/g,Ca=/%5B/g,ka=/%5D/g,Ea=/%5E/g,Oa=/%60/g,Ma=/%7B/g,Ia=/%7C/g,Pa=/%7D/g,$a=/%20/g;function Aa(e){return encodeURI(""+e).replace(Ia,"|").replace(Ca,"[").replace(ka,"]")}function La(e){return Aa(e).replace(Ta,"%2B").replace($a,"+").replace(ba,"%23").replace(_a,"%26").replace(Oa,"`").replace(Ma,"{").replace(Pa,"}").replace(Ea,"^")}function Ra(e){return null==e?"":function(e){return Aa(e).replace(ba,"%23").replace(Sa,"%3F")}(e).replace(wa,"%2F")}function ja(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Ba=/\/$/,Na=e=>e.replace(Ba,"");function Da(e,t,n="/"){let o,r={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),r=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+s,path:o,query:r,hash:ja(s)}}function Fa(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function qa(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Va(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Wa(e[n],t[n]))return!1;return!0}function Wa(e,t){return ya(e)?za(e,t):ya(t)?za(t,e):e===t}function za(e,t){return ya(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}const Ha={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ua,Xa,Ya,Ga;(Xa=Ua||(Ua={})).pop="pop",Xa.push="push",(Ga=Ya||(Ya={})).back="back",Ga.forward="forward",Ga.unknown="";const Ja=/^[^#]+#/;function Qa(e,t){return e.replace(Ja,"#")+t}const Ka=()=>({left:window.scrollX,top:window.scrollY});function Za(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function el(e,t){return(history.state?history.state.position-t:-1)+e}const tl=new Map;let nl=()=>location.protocol+"//"+location.host;function ol(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Fa(n,"")}return Fa(n,e)+o+r}function rl(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?Ka():null}}function il(e){const t=function(e){const{history:t,location:n}=window,o={value:ol(e,n)},r={value:t.state};function i(o,i,s){const a=e.indexOf("#"),l=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:nl()+e+o;try{t[s?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[s?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const s=ga({},r.value,t.state,{forward:e,scroll:Ka()});i(s.current,s,!0),i(e,ga({},rl(o.value,e,null),{position:s.position+1},n),!1),o.value=e},replace:function(e,n){i(e,ga({},t.state,rl(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}(e=function(e){if(!e)if(pa){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),Na(e)}(e)),n=function(e,t,n,o){let r=[],i=[],s=null;const a=({state:i})=>{const a=ol(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=a,t.value=i,s&&s===l)return void(s=null);u=c?i.position-c.position:0}else o(a);r.forEach((e=>{e(n.value,l,{delta:u,type:Ua.pop,direction:u?u>0?Ya.forward:Ya.back:Ya.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(ga({},e.state,{scroll:Ka()}),"")}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=ga({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Qa.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function sl(e){return"string"==typeof e||"symbol"==typeof e}const al=Symbol("");var ll,cl;function ul(e,t){return ga(new Error,{type:e,[al]:!0},t)}function fl(e,t){return e instanceof Error&&al in e&&(null==t||!!(e.type&t))}(cl=ll||(ll={}))[cl.aborted=4]="aborted",cl[cl.cancelled=8]="cancelled",cl[cl.duplicated=16]="duplicated";const hl="[^/]+?",dl={sensitive:!1,strict:!1,start:!0,end:!0},pl=/[.+*?^${}()[\]/\\]/g;function gl(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function ml(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=gl(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(vl(o))return 1;if(vl(r))return-1}return r.length-o.length}function vl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const yl={type:0,value:""},bl=/[a-zA-Z0-9_]/;function _l(e,t,n){const o=function(e,t){const n=ga({},dl,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let s=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(pl,"\\$&"),s+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const f=u||hl;if(f!==hl){s+=10;try{new RegExp(`(${f})`)}catch(a){throw new Error(`Invalid custom RegExp for param "${e}" (${f}): `+a.message)}}let h=n?`((?:${f})(?:/(?:${f}))*)`:`(${f})`;t||(h=c&&l.length<2?`(?:/${h})`:"/"+h),c&&(h+="?"),r+=h,s+=20,c&&(s+=-8),n&&(s+=-20),".*"===f&&(s+=-50)}e.push(s)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const s=new RegExp(r,n.sensitive?"":"i");return{re:s,score:o,keys:i,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:s,optional:a}=e,l=i in t?t[i]:"";if(ya(l)&&!s)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=ya(l)?l.join("/"):l;if(!c){if(!a)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[yl]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function s(){i&&r.push(i),i=[]}let a,l=0,c="",u="";function f(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),c="")}function h(){c+=a}for(;l<e.length;)if(a=e[l++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(c&&f(),s()):":"===a?(f(),n=1):h();break;case 4:h(),n=o;break;case 1:"("===a?n=2:bl.test(a)?h():(f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--);break;case 2:")"===a?"\\"==u[u.length-1]?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:f(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),f(),s(),r}(e.path),n),r=ga(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function wl(e,t){const n=[],o=new Map;function r(e,n,o){const a=!o,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Sl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}(e);l.aliasOf=o&&o.record;const c=kl(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(ga({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l}))}let f,h;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(f=_l(t,n,c),o?o.alias.push(f):(h=h||f,h!==f&&h.alias.push(f),a&&e.name&&!Tl(f)&&i(e.name)),El(f)&&s(f),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],f,o&&o.children[t])}o=o||f}return h?()=>{i(h)}:va}function i(e){if(sl(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(e){const t=function(e,t){let n=0,o=t.length;for(;n!==o;){const r=n+o>>1;ml(e,t[r])<0?o=r:n=r+1}const r=function(e){let t=e;for(;t=t.parent;)if(El(t)&&0===ml(e,t))return t;return}(e);r&&(o=t.lastIndexOf(r,o-1));return o}(e,n);n.splice(t,0,e),e.record.name&&!Tl(e)&&o.set(e.record.name,e)}return t=kl({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,s,a={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw ul(1,{location:e});s=r.record.name,a=ga(xl(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&xl(e.params,r.keys.map((e=>e.name)))),i=r.stringify(a)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(a=r.parse(i),s=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw ul(1,{location:e,currentLocation:t});s=r.record.name,a=ga({},t.params,e.params),i=r.stringify(a)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:s,path:i,params:a,matched:l,meta:Cl(l)}},removeRoute:i,clearRoutes:function(){n.length=0,o.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function xl(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function Sl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function Tl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Cl(e){return e.reduce(((e,t)=>ga(e,t.meta)),{})}function kl(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function El({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Ol(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Ta," "),r=e.indexOf("="),i=ja(r<0?e:e.slice(0,r)),s=r<0?null:ja(e.slice(r+1));if(i in t){let e=t[i];ya(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function Ml(e){let t="";for(let n in e){const o=e[n];if(n=La(n).replace(xa,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(ya(o)?o.map((e=>e&&La(e))):[o&&La(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Il(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=ya(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Pl=Symbol(""),$l=Symbol(""),Al=Symbol(""),Ll=Symbol(""),Rl=Symbol("");function jl(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Bl(e,t,n,o,r,i=e=>e()){const s=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((a,l)=>{const c=e=>{var i;!1===e?l(ul(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(ul(2,{from:t,to:e})):(s&&o.enterCallbacks[r]===s&&"function"==typeof e&&s.push(e),a())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch((e=>l(e)))}))}function Nl(e,t,n,o,r=e=>e()){const i=[];for(const a of e)for(const e in a.components){let l=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if("object"==typeof(s=l)||"displayName"in s||"props"in s||"__vccOpts"in s){const s=(l.__vccOpts||l)[t];s&&i.push(Bl(s,n,o,a,e,r))}else{let s=l();i.push((()=>s.then((i=>{if(!i)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]?i.default:i;var l;a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&Bl(c,n,o,a,e,r)()}))))}}var s;return i}function Dl(e){const t=Mo(Al),n=Mo(Ll),o=os((()=>{const n=qn(e.to);return t.resolve(n)})),r=os((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const s=i.findIndex(qa.bind(null,r));if(s>-1)return s;const a=ql(e[t-2]);return t>1&&ql(r)===a&&i[i.length-1].path!==a?i.findIndex(qa.bind(null,e[t-2])):s})),i=os((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!ya(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),s=os((()=>r.value>-1&&r.value===n.matched.length-1&&Va(n.params,o.value.params)));return{route:o,href:os((()=>o.value.href)),isActive:i,isExactActive:s,navigate:function(n={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)?t[qn(e.replace)?"replace":"push"](qn(e.to)).catch(va):Promise.resolve()}}}const Fl=Xo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Dl,setup(e,{slots:t}){const n=xn(Dl(e)),{options:o}=Mo(Al),r=os((()=>({[Vl(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Vl(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:rs("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function ql(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Vl=(e,t,n)=>null!=e?e:null!=t?t:n,Wl=Xo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=Mo(Rl),r=os((()=>e.route||o.value)),i=Mo($l,0),s=os((()=>{let e=qn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),a=os((()=>r.value.matched[s.value]));Oo($l,os((()=>s.value+1))),Oo(Pl,a),Oo(Rl,r);const l=Bn();return $o((()=>[l.value,a.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&qa(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,s=a.value,c=s&&s.components[i];if(!c)return zl(n.default,{Component:c,route:o});const u=s.props[i],f=u?!0===u?o.params:"function"==typeof u?u(o):u:null,h=rs(c,ga({},f,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(s.instances[i]=null)},ref:l}));return zl(n.default,{Component:h,route:o})||h}}});function zl(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Hl=Wl;function Ul(e){const t=wl(e.routes,e),n=e.parseQuery||Ol,o=e.stringifyQuery||Ml,r=e.history,i=jl(),s=jl(),a=jl(),l=Nn(Ha);let c=Ha;pa&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=ma.bind(null,(e=>""+e)),f=ma.bind(null,Ra),h=ma.bind(null,ja);function d(e,i){if(i=ga({},i||l.value),"string"==typeof e){const o=Da(n,e,i.path),s=t.resolve({path:o.path},i),a=r.createHref(o.fullPath);return ga(o,s,{params:h(s.params),hash:ja(o.hash),redirectedFrom:void 0,href:a})}let s;if(null!=e.path)s=ga({},e,{path:Da(n,e.path,i.path).path});else{const t=ga({},e.params);for(const e in t)null==t[e]&&delete t[e];s=ga({},e,{params:f(t)}),i.params=f(i.params)}const a=t.resolve(s,i),c=e.hash||"";a.params=u(h(a.params));const d=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,ga({},e,{hash:(p=c,Aa(p).replace(Ma,"{").replace(Pa,"}").replace(Ea,"^")),path:a.path}));var p;const g=r.createHref(d);return ga({fullPath:d,hash:c,query:o===Ml?Il(e.query):e.query||{}},a,{redirectedFrom:void 0,href:g})}function p(e){return"string"==typeof e?Da(n,e,l.value.path):ga({},e)}function g(e,t){if(c!==e)return ul(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=p(o):{path:o},o.params={}),ga({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=d(e),r=l.value,i=e.state,s=e.force,a=!0===e.replace,u=v(n);if(u)return y(ga(p(u),{state:"object"==typeof u?ga({},i,u.state):i,force:s,replace:a}),t||n);const f=n;let h;return f.redirectedFrom=t,!s&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&qa(t.matched[o],n.matched[r])&&Va(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(h=ul(16,{to:f,from:r}),P(r,r,!0,!1)),(h?Promise.resolve(h):w(f,r)).catch((e=>fl(e)?fl(e,2)?e:I(e):M(e,f,r))).then((e=>{if(e){if(fl(e,2))return y(ga({replace:a},p(e.to),{state:"object"==typeof e.to?ga({},i,e.to.state):i,force:s}),t||f)}else e=S(f,r,!0,a,i);return x(f,r,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=L.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,a]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const i=t.matched[s];i&&(e.matched.find((e=>qa(e,i)))?o.push(i):n.push(i));const a=e.matched[s];a&&(t.matched.find((e=>qa(e,a)))||r.push(a))}return[n,o,r]}(e,t);n=Nl(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(Bl(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),j(n).then((()=>{n=[];for(const o of i.list())n.push(Bl(o,e,t));return n.push(l),j(n)})).then((()=>{n=Nl(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(Bl(o,e,t))}));return n.push(l),j(n)})).then((()=>{n=[];for(const o of a)if(o.beforeEnter)if(ya(o.beforeEnter))for(const r of o.beforeEnter)n.push(Bl(r,e,t));else n.push(Bl(o.beforeEnter,e,t));return n.push(l),j(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Nl(a,"beforeRouteEnter",e,t,_),n.push(l),j(n)))).then((()=>{n=[];for(const o of s.list())n.push(Bl(o,e,t));return n.push(l),j(n)})).catch((e=>fl(e,8)?e:Promise.reject(e)))}function x(e,t,n){a.list().forEach((o=>_((()=>o(e,t,n)))))}function S(e,t,n,o,i){const s=g(e,t);if(s)return s;const a=t===Ha,c=pa?history.state:{};n&&(o||a?r.replace(e.fullPath,ga({scroll:a&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,P(e,t,n,a),I()}let T;function C(){T||(T=r.listen(((e,t,n)=>{if(!R.listening)return;const o=d(e),i=v(o);if(i)return void y(ga(i,{replace:!0}),o).catch(va);c=o;const s=l.value;pa&&function(e,t){tl.set(e,t)}(el(s.fullPath,n.delta),Ka()),w(o,s).catch((e=>fl(e,12)?e:fl(e,2)?(y(e.to,o).then((e=>{fl(e,20)&&!n.delta&&n.type===Ua.pop&&r.go(-1,!1)})).catch(va),Promise.reject()):(n.delta&&r.go(-n.delta,!1),M(e,o,s)))).then((e=>{(e=e||S(o,s,!1))&&(n.delta&&!fl(e,8)?r.go(-n.delta,!1):n.type===Ua.pop&&fl(e,20)&&r.go(-1,!1)),x(o,s,e)})).catch(va)})))}let k,E=jl(),O=jl();function M(e,t,n){I(e);const o=O.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function I(e){return k||(k=!e,C(),E.list().forEach((([t,n])=>e?n(e):t())),E.reset()),e}function P(t,n,o,r){const{scrollBehavior:i}=e;if(!pa||!i)return Promise.resolve();const s=!o&&function(e){const t=tl.get(e);return tl.delete(e),t}(el(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return so().then((()=>i(t,n,s))).then((e=>e&&Za(e))).catch((e=>M(e,t,n)))}const $=e=>r.go(e);let A;const L=new Set,R={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return sl(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:d,options:e,push:m,replace:function(e){return m(ga(p(e),{replace:!0}))},go:$,back:()=>$(-1),forward:()=>$(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:O.add,isReady:function(){return k&&l.value!==Ha?Promise.resolve():new Promise(((e,t)=>{E.add([e,t])}))},install(e){e.component("RouterLink",Fl),e.component("RouterView",Hl),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>qn(l)}),pa&&!A&&l.value===Ha&&(A=!0,m(r.location).catch((e=>{})));const t={};for(const o in Ha)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(Al,this),e.provide(Ll,Sn(t)),e.provide(Rl,l);const n=e.unmount;L.add(e),e.unmount=function(){L.delete(e),L.size<1&&(c=Ha,T&&T(),T=null,l.value=Ha,A=!1,k=!1),n()}}};function j(e){return e.reduce(((e,t)=>e.then((()=>_(t)))),Promise.resolve())}return R}function Xl(e){return Mo(Ll)}const Yl=He((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let Gl;function Jl(e){return da(e,re)?Zl().f(e,function(){const e=Uh(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),re):e}function Ql(e,t){if(1===t.length){if(e){const n=e=>I(e)&&da(e,re),o=t[0];let r=[];if(k(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return Ql(e&&e[n],t)}function Kl(e,t){const n=Ql(e,t);if(!n)return!1;const o=t[t.length-1];if(k(n))n.forEach((e=>Kl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>Jl(e),set(t){e=t}})}return!0}function Zl(){if(!Gl){let e;if(e=window.localStorage&&localStorage[oe]||__uniConfig.locale||navigator.language,Gl=ha(e),Yl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>Gl.add(e,__uniConfig.locales[e]))),Gl.setLocale(e)}}return Gl}function ec(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const tc=He((()=>{const e="uni.async.",t=["error"];Zl().add(ra,ec(e,t,["The connection timed out, click the screen to try again."]),!1),Zl().add(sa,ec(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),Zl().add(ia,ec(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),Zl().add(na,ec(e,t,["连接服务器超时，点击屏幕重试"]),!1),Zl().add(oa,ec(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),nc=He((()=>{const e="uni.showToast.",t=["unpaired"];Zl().add(ra,ec(e,t,["Please note showToast must be paired with hideToast"]),!1),Zl().add(sa,ec(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),Zl().add(ia,ec(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),Zl().add(na,ec(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),Zl().add(oa,ec(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),oc=He((()=>{const e="uni.showLoading.",t=["unpaired"];Zl().add(ra,ec(e,t,["Please note showLoading must be paired with hideLoading"]),!1),Zl().add(sa,ec(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),Zl().add(ia,ec(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),Zl().add(na,ec(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),Zl().add(oa,ec(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),rc=He((()=>{const e="uni.showModal.",t=["cancel","confirm"];Zl().add(ra,ec(e,t,["Cancel","OK"]),!1),Zl().add(sa,ec(e,t,["Cancelar","OK"]),!1),Zl().add(ia,ec(e,t,["Annuler","OK"]),!1),Zl().add(na,ec(e,t,["取消","确定"]),!1),Zl().add(oa,ec(e,t,["取消","確定"]),!1)})),ic=He((()=>{const e="uni.chooseFile.",t=["notUserActivation"];Zl().add(ra,ec(e,t,["File chooser dialog can only be shown with a user activation"]),!1),Zl().add(sa,ec(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),Zl().add(ia,ec(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),Zl().add(na,ec(e,t,["文件选择器对话框只能在用户激活时显示"]),!1),Zl().add(oa,ec(e,t,["文件選擇器對話框只能在用戶激活時顯示"]),!1)})),sc=He((()=>{const e="uni.setClipboardData.",t=["success","fail"];Zl().add(ra,ec(e,t,["Content copied","Copy failed, please copy manually"]),!1),Zl().add(sa,ec(e,t,["Contenido copiado","Error al copiar, copie manualmente"]),!1),Zl().add(ia,ec(e,t,["Contenu copié","Échec de la copie, copiez manuellement"]),!1),Zl().add(na,ec(e,t,["内容已复制","复制失败，请手动复制"]),!1),Zl().add(oa,ec(e,t,["內容已復制","復制失敗，請手動復製"]),!1)}));function ac(e){const t=new ut;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}const lc="invokeViewApi",cc="invokeServiceApi";let uc=1;const fc=Object.create(null);function hc(e,t){return e+"."+t}function dc(e,t,n){t=hc(e,t),fc[t]||(fc[t]=n)}function pc({id:e,name:t,args:n},o){t=hc(o,t);const r=t=>{e&&$y.publishHandler(lc+"."+e,t)},i=fc[t];i?i(n,r):r({})}const gc=x(ac("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=$y,i=n?uc++:0;n&&o(cc+"."+i,n,!0),r(cc,{id:i,name:e,args:t})}}),mc=350,vc=10,yc=Je(!0);let bc;function _c(){bc&&(clearTimeout(bc),bc=null)}let wc=0,xc=0;function Sc(e){if(_c(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];wc=t,xc=n,bc=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),mc)}function Tc(e){if(!bc)return;if(1!==e.touches.length)return _c();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-wc)>vc||Math.abs(n-xc)>vc?_c():void 0}function Cc(e,t){const n=Number(e);return isNaN(n)?t:n}function kc(){const e=__uniConfig.globalStyle||{},t=Cc(e.rpxCalcMaxDeviceWidth,960),n=Cc(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Ec(){kc(),Xe(),window.addEventListener("touchstart",Sc,yc),window.addEventListener("touchmove",Tc,yc),window.addEventListener("touchend",_c,yc),window.addEventListener("touchcancel",_c,yc)}var Oc,Mc,Ic=["top","left","right","bottom"],Pc={};function $c(){return Mc="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Ac(){if(Mc="string"==typeof Mc?Mc:$c()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(YC){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Ic.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),Oc=!0}else Ic.forEach((function(e){Pc[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Mc+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){Rc.length||setTimeout((function(){var e={};Rc.forEach((function(t){e[t]=Pc[t]})),Rc.length=0,jc.forEach((function(t){t(e)}))}),0);Rc.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Pc,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Lc(e){return Oc||Ac(),Pc[e]}var Rc=[];var jc=[];var Bc={get support(){return 0!=("string"==typeof Mc?Mc:$c()).length},get top(){return Lc("top")},get left(){return Lc("left")},get right(){return Lc("right")},get bottom(){return Lc("bottom")},onChange:function(e){$c()&&(Oc||Ac(),"function"==typeof e&&jc.push(e))},offChange:function(e){var t=jc.indexOf(e);t>=0&&jc.splice(t,1)}};const Nc=Xs((()=>{}),["prevent"]),Dc=Xs((()=>{}),["stop"]);function Fc(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function qc(){const e=Fc(document.documentElement.style,"--window-top");return e?e+Bc.top:0}function Vc(){const e=document.documentElement.style,t=qc(),n=Fc(e,"--window-bottom"),o=Fc(e,"--window-left"),r=Fc(e,"--window-right"),i=Fc(e,"--top-window-height");return{top:t,bottom:n?n+Bc.bottom:0,left:o?o+Bc.left:0,right:r?r+Bc.right:0,topWindowHeight:i||0}}function Wc(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function zc(e){return Wc(e)}function Hc(e){return Symbol(e)}function Uc(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function Xc(e,t=!1){if(t)return function(e){if(!Uc(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>rh(parseFloat(t))+"px"))}(e);if(I(e)){const t=parseInt(e)||0;return Uc(e)?rh(t):t}return e}const Yc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",Gc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z";function Jc(e,t="#000",n=27){return Ri("svg",{width:n,height:n,viewBox:"0 0 32 32"},[Ri("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function Qc(){{const{$pageInstance:e}=Xi();return e&&e.proxy.$page.id}}function Kc(e){const t=Be(e);if(t.$page)return t.$page.id;if(t.$){const{$pageInstance:e}=t.$;return e&&e.proxy.$page.id}}function Zc(){const e=Kg(),t=e.length;if(t)return e[t-1]}function eu(){const e=Zc();if(e)return e.$page.meta}function tu(){const e=eu();return e?e.id:-1}function nu(){const e=Zc();if(e)return e.$vm}const ou=["navigationBar","pullToRefresh"];function ru(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=x({id:t},n,e);ou.forEach((t=>{o[t]=x({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function iu(e,t,n){if(I(e))n=t,t=e,e=nu();else if("number"==typeof e){const t=Kg().find((t=>t.$page.id===e));e=t?t.$vm:nu()}if(!e)return;const o=e.$[t];return o&&We(o,n)}function su(e){e.preventDefault()}let au,lu=0;function cu({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-lu)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(lu=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(au=setTimeout(s,300))),o=!1};return function(){clearTimeout(au),o||requestAnimationFrame(s),o=!0}}function uu(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return uu(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),qe(i.concat(n).join("/"))}function fu(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}class hu{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(De(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&De(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=mu(this.$el.querySelector(e));return t?du(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=mu(n[o]);e&&t.push(du(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||I(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:z(n);(I(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(I(e)&&(e=c(e)),B(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];M(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&$y.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function du(e,t=!0){if(t&&e&&(e=Ne(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new hu(e)),e.$el.__wxsComponentDescriptor}function pu(e,t){return du(e,t)}function gu(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>pu(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=Ne(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,pu(r,!1)]}}function mu(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function vu(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}function yu(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e,s={type:n,timeStamp:o,target:Qe(t?r:vu(r)),detail:{},currentTarget:Qe(i)};return e._stopped&&(s._stopped=!0),e.type.startsWith("touch")&&(s.touches=e.touches,s.changedTouches=e.changedTouches),function(e,t){x(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(s,e),s}function bu(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function _u(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const wu=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=0!==o.tagName.indexOf("UNI-");if(r)return gu(e,t,n,!1)||[e];const i=yu(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=qc();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[bu(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=qc();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[bu(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch"))(e)){const t=qc();i.touches=_u(e.touches,t),i.changedTouches=_u(e.changedTouches,t)}return gu(i,t,n)||[i]},createNativeEvent:yu},Symbol.toStringTag,{value:"Module"});function xu(e){!function(e){const t=e.globalProperties;x(t,wu),t.$gcd=pu}(e._context.config)}let Su=1;function Tu(){return tu()+"."+lc}const Cu=x(ac("view"),{invokeOnCallback:(e,t)=>Ay.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Ay,s=o?Su++:0;o&&r(lc+"."+s,o,!0),i(Tu(),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=Ay,a=Su++,l=lc+"."+a;return r(l,n),s(Tu(),{id:a,name:e,args:t},o),()=>{i(l)}}});function ku(e){iu(Zc(),be,e),Ay.invokeOnCallback("onWindowResize",e)}function Eu(e){const t=Zc();iu(xm(),le,e),iu(t,le)}function Ou(){iu(xm(),ce),iu(Zc(),ce)}const Mu=[we,Se];function Iu(){Mu.forEach((e=>Ay.subscribe(e,function(e){return(t,n)=>{iu(parseInt(n),e,t)}}(e))))}function Pu(){!function(){const{on:e}=Ay;e(be,ku),e(Le,Eu),e(Re,Ou)}(),Iu()}function $u(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new ot(this.$page.id)),e.eventChannel}}function Au(e){e._context.config.globalProperties.getOpenerEventChannel=$u}function Lu(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function Ru(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${rh(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function ju(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(Ru)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?Ru(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const Bu={props:["animation"],watch:{animation:{deep:!0,handler(){ju(this)}}},mounted(){ju(this)}},Nu=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Bu),Du(e)},Du=e=>(e.__reserved=!0,e.compatConfig={MODE:3},Xo(e)),Fu={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function qu(e){const t=Bn(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:function(e){e.touches.length>1||s(e)},onMousedown:function(e){r||(s(e),window.addEventListener("mouseup",l))},onTouchend:function(){a()},onMouseup:function(){r&&l()},onTouchcancel:function(){r=!1,t.value=!1,clearTimeout(n)}}}}function Vu(e,t){return I(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}function Wu(e){return e.__wwe=!0,e}function zu(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){const r=Qe(n);return{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const Hu=Hc("uf"),Uu=Nu({name:"Form",emits:["submit","reset"],setup(e,{slots:t,emit:n}){const o=Bn(null);return function(e){const t=[];Oo(Hu,{addField(e){t.push(e)},removeField(e){t.splice(t.indexOf(e),1)},submit(n){e("submit",n,{value:t.reduce(((e,t)=>{if(t.submit){const[n,o]=t.submit();n&&(e[n]=o)}return e}),Object.create(null))})},reset(n){t.forEach((e=>e.reset&&e.reset())),e("reset",n)}})}(zu(o,n)),()=>Ri("uni-form",{ref:o},[Ri("span",null,[t.default&&t.default()])],512)}});const Xu=Hc("ul");function Yu(e,t,n){const o=Qc();n&&!e||B(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&$y.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?$y.on(r,t[r]):e&&$y.on(`uni-${r}-${o}-${e}`,t[r])}))}function Gu(e,t,n){const o=Qc();n&&!e||B(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&$y.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?$y.off(r,t[r]):e&&$y.off(`uni-${r}-${o}-${e}`,t[r])}))}const Ju=Nu({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=Bn(null),o=Mo(Hu,!1),{hovering:r,binding:i}=qu(e);Zl();const s=Wu(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=Mo(Xu,!1);return a&&(a.addHandler(s),vr((()=>{a.removeHandler(s)}))),function(e,t){Yu(e.id,t),$o((()=>e.id),((e,n)=>{Gu(n,t,!0),Yu(e,t,!0)})),yr((()=>{Gu(e.id,t)}))}(e,{"label-click":s}),()=>{const o=e.hoverClass,a=Vu(e,"disabled"),l=Vu(e,"loading"),c=Vu(e,"plain"),u=o&&"none"!==o;return Ri("uni-button",Vi({ref:n,onClick:s,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick"])}}});function Qu(e){return e.$el}function Ku(e){const{base:t}=__uniConfig.router;return 0===qe(e).indexOf(t)?qe(e):t+e}function Zu(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0===e.indexOf("./static/")||n&&0===e.indexOf("./"+n+"/"))&&(e=e.slice(1)),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Ku(e.slice(1));e="https:"+e}if(ie.test(e)||se.test(e)||0===e.indexOf("blob:"))return e;const o=Kg();return o.length?Ku(uu(o[o.length-1].$page.route,e).slice(1)):e}const ef=navigator.userAgent,tf=/android/i.test(ef),nf=/iphone|ipad|ipod/i.test(ef),of=ef.match(/Windows NT ([\d|\d.\d]*)/i),rf=/Macintosh|Mac/i.test(ef),sf=/Linux|X11/i.test(ef),af=rf&&navigator.maxTouchPoints>0;function lf(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function cf(e){return e&&90===Math.abs(window.orientation)}function uf(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function ff(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function hf(e,t,n,o){Ay.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function df(e,t){const n={},{top:o,topWindowHeight:r}=Vc();if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=Ye(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(k(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(k(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function pf(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function gf(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){return e?e.$el:t.$el}(t,e),s=i.parentElement;if(!s)return o?null:[];const{nodeType:a}=i,l=3===a||8===a;if(o){const e=l?s.querySelector(n):pf(i,n)?i:i.querySelector(n);return e?df(e,r):null}{let e=[];const t=(l?s:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(df(t,r))})),!l&&pf(i,n)&&e.unshift(df(i,r)),e}}(e,t,n,r,i))})),n(o)}const mf=["original","compressed"],vf=["album","camera"],yf=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function bf(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function _f(e,t){return!k(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function wf(e){return function(){try{return e.apply(e,arguments)}catch(YC){console.error(YC)}}}let xf=1;const Sf={};function Tf(e,t,n,o=!1){return Sf[e]={name:t,keepAlive:o,callback:n},e}function Cf(e,t,n){if("number"==typeof e){const o=Sf[e];if(o)return o.keepAlive||delete Sf[e],o.callback(t,n)}return t}const kf="success",Ef="fail",Of="complete";function Mf(e,t={},{beforeAll:n,beforeSuccess:o}={}){B(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];M(o)&&(t[n]=wf(o),delete e[n])}return t}(t),a=M(r),l=M(i),c=M(s),u=xf++;return Tf(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),M(n)&&n(u),u.errMsg===e+":ok"?(M(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const If="success",Pf="fail",$f="complete",Af={},Lf={};function Rf(e,t){return function(n){return e(n,t)||n}}function jf(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Rf(i,n));else{const e=i(t,n);if(A(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Bf(e,t={}){return[If,Pf,$f].forEach((n=>{const o=e[n];if(!k(o))return;const r=t[n];t[n]=function(e){jf(o,e,t).then((e=>M(r)&&r(e)||e))}})),t}function Nf(e,t){const n=[];k(Af.returnValue)&&n.push(...Af.returnValue);const o=Lf[e];return o&&k(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Df(e){const t=Object.create(null);Object.keys(Af).forEach((e=>{"returnValue"!==e&&(t[e]=Af[e].slice())}));const n=Lf[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Ff(e,t,n,o){const r=Df(e);if(r&&Object.keys(r).length){if(k(r.invoke)){return jf(r.invoke,n).then((n=>t(Bf(Df(e),n),...o)))}return t(Bf(r,n),...o)}return t(n,...o)}function qf(e,t){return(n={},...o)=>function(e){return!(!B(e)||![kf,Ef,Of].find((t=>M(e[t]))))}(n)?Nf(e,Ff(e,t,n,o)):Nf(e,new Promise(((r,i)=>{Ff(e,t,x(n,{success:r,fail:i}),o)})))}function Vf(e,t,n,o){return Cf(e,x({errMsg:t+":fail"+(n?" "+n:"")},o))}function Wf(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(I(e))return e}const r=function(e,t){const n=e[0];if(!t||!B(t.formatArgs)&&B(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(M(s)){const o=s(e[0][t],n);if(I(o))return o}else C(n,t)||(n[t]=s)}}(t,o);if(r)return r}function zf(e,t,n){return o=>{!function(e){if(!M(e))throw new Error('Invalid args: type check failed for args "callback". Expected Function')}(o);const r=Wf(0,[o],0,n);if(r)throw new Error(r);const i=!function(e){for(const t in Sf)if(Sf[t].name===e)return!0;return!1}(e);!function(e,t){Tf(xf++,e,t,!0)}(e,o),i&&(!function(e){Ay.on("api."+e,(t=>{for(const n in Sf){const o=Sf[n];o.name===e&&o.callback(t)}}))}(e),t())}}function Hf(e,t,n,o){return n=>{const r=Mf(e,n,o),i=Wf(0,[n],0,o);return i?Vf(r,e,i):t(n,{resolve:t=>function(e,t,n){return Cf(e,x(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Vf(r,e,function(e){return!e||I(e)?e:e.stack?(console.error(e.message+te+e.stack),e.message):e}(t),n)})}}function Uf(e,t,n){return zf(e,t,n)}function Xf(e,t,n,o){return qf(e,Hf(e,t,0,o))}function Yf(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=Wf(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Gf(e,t,n,o){return qf(e,function(e,t,n,o){return Hf(e,t,0,o)}(e,t,0,o))}let Jf=!1,Qf=0,Kf=0,Zf=960,eh=375,th=750;function nh(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=lf(),t=ff(uf(e,cf(e)));return{platform:nf?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();Qf=n,Kf=t,Jf="ios"===e}function oh(e,t){const n=Number(e);return isNaN(n)?t:n}const rh=Yf(0,((e,t)=>{if(0===Qf&&(nh(),function(){const e=__uniConfig.globalStyle||{};Zf=oh(e.rpxCalcMaxDeviceWidth,960),eh=oh(e.rpxCalcBaseDeviceWidth,375),th=oh(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Qf;n=e===th||n<=Zf?n:eh;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==Kf&&Jf?.5:1),e<0?-o:o})),ih=new ut,sh=Yf(0,((e,t)=>(ih.on(e,t),()=>ih.off(e,t)))),ah=Yf(0,((e,t)=>{e?(k(e)||(e=[e]),e.forEach((e=>ih.off(e,t)))):ih.e={}})),lh=Yf(0,((e,...t)=>{ih.emit(e,...t)})),ch=[.5,.8,1,1.25,1.5,2];const uh=(e,t,n,o)=>{!function(e,t,n,o,r){Ay.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};function fh(e,t){return function(n,o){n?o[e]=Math.round(n):void 0!==t&&(o[e]=t)}}const hh=fh("width"),dh=fh("height"),ph={formatArgs:{x:fh("x"),y:fh("y"),width:hh,height:dh}},gh={PNG:"png",JPG:"jpg",JPEG:"jpg"},mh={formatArgs:{x:fh("x",0),y:fh("y",0),width:hh,height:dh,destWidth:fh("destWidth"),destHeight:fh("destHeight"),fileType(e,t){e=(e||"").toUpperCase();let n=gh[e];n||(n=gh.PNG),t.fileType=n},quality(e,t){t.quality=e&&e>0&&e<1?e:1}}};function vh(e,t,n,o,r){Ay.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}var yh=["scale","rotate","translate","setTransform","transform"],bh=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],_h=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];const wh={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function xh(e){var t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(C(wh,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(wh[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class Sh{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,xh(t)])}}class Th{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class Ch{constructor(e){this.width=e}}class kh{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],vh(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new Sh("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new Sh("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new Th(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e){let t=0;return t=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new Ch(t)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],s=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(s.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(s.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(s.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&a()})),1===o.length&&a(),o=s.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function a(){s.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}}const Eh=He((()=>{[...yh,...bh].forEach((function(e){kh.prototype[e]=function(e){switch(e){case"fill":case"stroke":return function(){this.actions.push({method:e+"Path",data:[...this.path]})};case"fillRect":return function(e,t,n,o){this.actions.push({method:"fillPath",data:[{method:"rect",data:[e,t,n,o]}]})};case"strokeRect":return function(e,t,n,o){this.actions.push({method:"strokePath",data:[{method:"rect",data:[e,t,n,o]}]})};case"fillText":case"strokeText":return function(t,n,o,r){var i=[t.toString(),n,o];"number"==typeof r&&i.push(r),this.actions.push({method:e,data:i})};case"drawImage":return function(t,n,o,r,i,s,a,l,c){var u;function f(e){return"number"==typeof e}void 0===c&&(s=n,a=o,l=r,c=i,n=void 0,o=void 0,r=void 0,i=void 0),u=f(n)&&f(o)&&f(r)&&f(i)?[t,s,a,l,c,n,o,r,i]:f(l)&&f(c)?[t,s,a,l,c]:[t,s,a],this.actions.push({method:e,data:u})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)})),_h.forEach((function(e){kh.prototype[e]=function(e){switch(e){case"setFillStyle":case"setStrokeStyle":return function(t){"object"!=typeof t?this.actions.push({method:e,data:["normal",xh(t)]}):this.actions.push({method:e,data:[t.type,t.data,t.colorStop]})};case"setGlobalAlpha":return function(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:e,data:[t]})};case"setShadow":return function(t,n,o,r){r=xh(r),this.actions.push({method:e,data:[t,n,o,r]}),this.state.shadowBlur=o,this.state.shadowColor=r,this.state.shadowOffsetX=t,this.state.shadowOffsetY=n};case"setLineDash":return function(t,n){t=t||[0,0],n=n||0,this.actions.push({method:e,data:[t,n]}),this.state.lineDash=t};case"setFontSize":return function(t){this.state.font=this.state.font.replace(/\d+\.?\d*px/,t+"px"),this.state.fontSize=t,this.actions.push({method:e,data:[t]})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)}))})),Oh=Yf(0,((e,t)=>{if(Eh(),t)return new kh(e,Kc(t));const n=Kc(nu());if(n)return new kh(e,n);Ay.emit(fe,"createCanvasContext:fail")})),Mh=Gf("canvasGetImageData",(({canvasId:e,x:t,y:n,width:o,height:r},{resolve:i,reject:s})=>{const a=Kc(nu());a?vh(e,a,"getImageData",{x:t,y:n,width:o,height:r},(function(e){if(e.errMsg&&-1!==e.errMsg.indexOf("fail"))return void s("",e);let t=e.data;t&&t.length&&(e.data=new Uint8ClampedArray(t)),delete e.compressed,i(e)})):s()}),0,ph),Ih=Gf("canvasToTempFilePath",(({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,canvasId:s,fileType:a,quality:l},{resolve:c,reject:u})=>{var f=Kc(nu());if(!f)return void u();vh(s,f,"toTempFilePath",{x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,fileType:a,quality:l,dirname:`${Rd}/canvas`},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?u("",e):c(e)}))}),0,mh),Ph={thresholds:[0],initialRatio:0,observeAll:!1},$h=["top","right","bottom","left"];let Ah=1;function Lh(e={}){return $h.map((t=>`${Number(e[t])||0}px`)).join(" ")}class Rh{constructor(e,t){this._pageId=Kc(e),this._component=e,this._options=x({},Ph,t)}relativeTo(e,t){return this._options.relativeToSelector=e,this._options.rootMargin=Lh(t),this}relativeToViewport(e){return this._options.relativeToSelector=void 0,this._options.rootMargin=Lh(e),this}observe(e,t){M(t)&&(this._options.selector=e,this._reqId=Ah++,function({reqId:e,component:t,options:n,callback:o}){const r=Qu(t);(r.__io||(r.__io={}))[e]=function(e,t,n){$d();const o=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,r=new IntersectionObserver((e=>{e.forEach((e=>{n({intersectionRatio:Ld(e),intersectionRect:Ad(e.intersectionRect),boundingClientRect:Ad(e.boundingClientRect),relativeRect:Ad(e.rootBounds),time:Date.now(),dataset:Ye(e.target),id:e.target.id})}))}),{root:o,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){r.USE_MUTATION_OBSERVER=!0;const n=e.querySelectorAll(t.selector);for(let e=0;e<n.length;e++)r.observe(n[e])}else{r.USE_MUTATION_OBSERVER=!1;const n=e.querySelector(t.selector);n?r.observe(n):console.warn(`Node ${t.selector} is not found. Intersection observer will not trigger.`)}return r}(r,n,o)}({reqId:this._reqId,component:this._component,options:this._options,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t}){const n=Qu(t),o=n.__io&&n.__io[e];o&&(o.disconnect(),delete n.__io[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const jh=Yf(0,((e,t)=>((e=Be(e))&&!Kc(e)&&(t=e,e=null),new Rh(e||nu(),t))));let Bh=0,Nh={};function Dh(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(Bh++);r.callbackId=e,Nh[e]=o}Ay.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(!function(e,t){e=e||{},I(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?M(e.success)&&e.success(t):M(e.fail)&&e.fail(t),M(e.complete)&&e.complete(t)}(Nh[e],t),delete Nh[e])}))}const Fh={canvas:kh,map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){uh(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){uh(this.id,this.pageId,"moveToLocation",e)}getScale(e){uh(this.id,this.pageId,"getScale",e)}getRegion(e){uh(this.id,this.pageId,"getRegion",e)}includePoints(e){uh(this.id,this.pageId,"includePoints",e)}translateMarker(e){uh(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){uh(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){uh(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){uh(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){uh(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){uh(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){uh(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){uh(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){uh(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){uh(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){uh(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){uh(this.id,this.pageId,"openMapApp",e)}on(e){uh(this.id,this.pageId,"on",e)}},video:class{constructor(e,t){this.id=e,this.pageId=t}play(){hf(this.id,this.pageId,"play")}pause(){hf(this.id,this.pageId,"pause")}stop(){hf(this.id,this.pageId,"stop")}seek(e){hf(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){hf(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~ch.indexOf(e)||(e=1),hf(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){hf(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){hf(this.id,this.pageId,"exitFullScreen")}showStatusBar(){hf(this.id,this.pageId,"showStatusBar")}hideStatusBar(){hf(this.id,this.pageId,"hideStatusBar")}},editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){Dh(this.id,this.pageId,e,t)}}};function qh(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=Fh[n];e.context=new r(t,o),delete e.contextInfo}}class Vh{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery}}class Wh{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return gf(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{k(e)?e.forEach(qh):qh(e);const o=n[t];M(o)&&o.call(this,e)})),M(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=Be(e),this}select(e){return this._nodesRef=new Vh(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new Vh(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new Vh(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const zh=Yf(0,(e=>((e=Be(e))&&!Kc(e)&&(e=null),new Wh(e||nu())))),Hh=Uf("onWindowResize",(()=>{})),Uh=Yf(0,(()=>{const e=xm();return e&&e.$vm?e.$vm.$locale:Zl().getLocale()})),Xh=Gf("setPageMeta",((e,{resolve:t})=>{t(function(e,{pageStyle:t,rootFontSize:n}){t&&(document.querySelector("uni-page-body")||document.body).setAttribute("style",t);n&&document.documentElement.style.fontSize!==n&&(document.documentElement.style.fontSize=n)}(nu(),e))})),Yh={[pe]:[],[de]:[],[fe]:[],[le]:[],[ce]:[]};const Gh=Yf(0,(()=>x({},Fd))),Jh={formatArgs:{showToast:!0},beforeInvoke(){sc()},beforeSuccess(e,t){if(!t.showToast)return;const{t:n}=Zl(),o=n("uni.setClipboardData.success");o&&ty({title:o,icon:"success",mask:!1})}},Qh=(Boolean,{formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=_f(e,mf)},sourceType(e,t){t.sourceType=_f(e,vf)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}}),Kh={formatArgs:{src(e,t){t.src=Zu(e)}}},Zh={formatArgs:{urls(e,t){t.urls=e.map((e=>I(e)&&e?Zu(e):""))},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:I(e)&&e&&(t.current=Zu(e))}}},ed="json",td=["text","arraybuffer"],nd=encodeURIComponent;ArrayBuffer,Boolean;const od={formatArgs:{method(e,t){t.method=bf((e||"").toUpperCase(),yf)},data(e,t){t.data=e||""},url(e,t){t.method===yf[0]&&B(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(C(t,a)){let e=t[a];null==e?e="":B(e)&&(e=JSON.stringify(e)),s[nd(a)]=nd(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==yf[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||ed).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===td.indexOf(t.responseType)&&(t.responseType="text")}}},rd={formatArgs:{filePath(e,t){e&&(t.filePath=Zu(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}};const id={url:{type:String,required:!0}},sd="navigateTo",ad="redirectTo",ld="reLaunch",cd="switchTab",ud="preloadPage",fd="unPreloadPage",hd=(vd(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),vd(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),_d(sd)),dd=_d(ad),pd=_d(ld),gd=_d(cd),md={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(Kg().length-1,e)}}};function vd(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let yd;function bd(){yd=""}function _d(e){return{formatArgs:{url:wd(e)},beforeAll:bd}}function wd(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/"))return e;let t="";const n=Kg();return n.length&&(t=n[n.length-1].$page.route),uu(t,e)}(t)).split("?")[0],r=fu(o,!0);if(!r)return"page `"+t+"` is not found";if(e===sd||e===ad){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if(e===cd&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if(e!==cd&&e!==ud||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!I(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),e!==fd)if(e!==ud){if(yd===t&&"appLaunch"!==n.openType)return`${yd} locked`;__uniConfig.ready&&(yd=t)}else if(r.meta.isTabBar){const e=Kg(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const xd="setNavigationBarColor",Sd={formatArgs:{animation(e,t){e||(e={duration:0,timingFunc:"linear"}),t.animation={duration:e.duration||0,timingFunc:e.timingFunc||"linear"}}}},Td="setNavigationBarTitle",Cd={formatArgs:{duration:300}},kd=(Boolean,{formatArgs:{title:"",mask:!1}}),Ed=(Boolean,{beforeInvoke(){rc()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!C(t,"cancelText")){const{t:e}=Zl();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!C(t,"confirmText")){const{t:e}=Zl();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),Od=["success","loading","none","error"],Md=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=bf(e,Od)},image(e,t){t.image=e?Zu(e):""},duration:1500,mask:!1}}),Id="stopPullDownRefresh",Pd="hideTabBar",$d=function(){if("object"==typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var e=function(){for(var e=window.document,t=r(e);t;)t=r(e=t.ownerDocument);return e}(),t=[],n=null,o=null;s.prototype.THROTTLE_TIMEOUT=100,s.prototype.POLL_INTERVAL=null,s.prototype.USE_MUTATION_OBSERVER=!0,s._setupCrossOriginUpdater=function(){return n||(n=function(e,n){o=e&&n?f(e,n):{top:0,bottom:0,left:0,right:0,width:0,height:0},t.forEach((function(e){e._checkForIntersections()}))}),n},s._resetCrossOriginUpdater=function(){n=null,o=null},s.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},s.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},s.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},s.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},s.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},s.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},s.prototype._monitorIntersections=function(t){var n=t.defaultView;if(n&&-1==this._monitoringDocuments.indexOf(t)){var o=this._checkForIntersections,i=null,s=null;this.POLL_INTERVAL?i=n.setInterval(o,this.POLL_INTERVAL):(a(n,"resize",o,!0),a(t,"scroll",o,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in n&&(s=new n.MutationObserver(o)).observe(t,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(t),this._monitoringUnsubscribes.push((function(){var e=t.defaultView;e&&(i&&e.clearInterval(i),l(e,"resize",o,!0)),l(t,"scroll",o,!0),s&&s.disconnect()}));var c=this.root&&(this.root.ownerDocument||this.root)||e;if(t!=c){var u=r(t);u&&this._monitorIntersections(u.ownerDocument)}}},s.prototype._unmonitorIntersections=function(t){var n=this._monitoringDocuments.indexOf(t);if(-1!=n){var o=this.root&&(this.root.ownerDocument||this.root)||e;if(!this._observationTargets.some((function(e){var n=e.element.ownerDocument;if(n==t)return!0;for(;n&&n!=o;){var i=r(n);if((n=i&&i.ownerDocument)==t)return!0}return!1}))){var i=this._monitoringUnsubscribes[n];if(this._monitoringDocuments.splice(n,1),this._monitoringUnsubscribes.splice(n,1),i(),t!=o){var s=r(t);s&&this._unmonitorIntersections(s.ownerDocument)}}}},s.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},s.prototype._checkForIntersections=function(){if(this.root||!n||o){var e=this._rootIsInDom(),t=e?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach((function(o){var r=o.element,s=c(r),a=this._rootContainsTarget(r),l=o.entry,u=e&&a&&this._computeTargetAndRootIntersection(r,s,t),f=null;this._rootContainsTarget(r)?n&&!this.root||(f=t):f={top:0,bottom:0,left:0,right:0,width:0,height:0};var h=o.entry=new i({time:window.performance&&performance.now&&performance.now(),target:r,boundingClientRect:s,rootBounds:f,intersectionRect:u});l?e&&a?this._hasCrossedThreshold(l,h)&&this._queuedEntries.push(h):l&&l.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},s.prototype._computeTargetAndRootIntersection=function(t,r,i){if("none"!=window.getComputedStyle(t).display){for(var s,a,l,u,h,p,g,m,v=r,y=d(t),b=!1;!b&&y;){var _=null,w=1==y.nodeType?window.getComputedStyle(y):{};if("none"==w.display)return null;if(y==this.root||9==y.nodeType)if(b=!0,y==this.root||y==e)n&&!this.root?!o||0==o.width&&0==o.height?(y=null,_=null,v=null):_=o:_=i;else{var x=d(y),S=x&&c(x),T=x&&this._computeTargetAndRootIntersection(x,S,i);S&&T?(y=x,_=f(S,T)):(y=null,v=null)}else{var C=y.ownerDocument;y!=C.body&&y!=C.documentElement&&"visible"!=w.overflow&&(_=c(y))}if(_&&(s=_,a=v,l=void 0,u=void 0,h=void 0,p=void 0,g=void 0,m=void 0,l=Math.max(s.top,a.top),u=Math.min(s.bottom,a.bottom),h=Math.max(s.left,a.left),p=Math.min(s.right,a.right),m=u-l,v=(g=p-h)>=0&&m>=0&&{top:l,bottom:u,left:h,right:p,width:g,height:m}||null),!v)break;y=y&&d(y)}return v}},s.prototype._getRootRect=function(){var t;if(this.root&&!p(this.root))t=c(this.root);else{var n=p(this.root)?this.root:e,o=n.documentElement,r=n.body;t={top:0,left:0,right:o.clientWidth||r.clientWidth,width:o.clientWidth||r.clientWidth,bottom:o.clientHeight||r.clientHeight,height:o.clientHeight||r.clientHeight}}return this._expandRectByRootMargin(t)},s.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},s.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,o=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==o)for(var r=0;r<this.thresholds.length;r++){var i=this.thresholds[r];if(i==n||i==o||i<n!=i<o)return!0}},s.prototype._rootIsInDom=function(){return!this.root||h(e,this.root)},s.prototype._rootContainsTarget=function(t){var n=this.root&&(this.root.ownerDocument||this.root)||e;return h(n,t)&&(!this.root||n==t.ownerDocument)},s.prototype._registerInstance=function(){t.indexOf(this)<0&&t.push(this)},s.prototype._unregisterInstance=function(){var e=t.indexOf(this);-1!=e&&t.splice(e,1)},window.IntersectionObserver=s,window.IntersectionObserverEntry=i}function r(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(YC){return null}}function i(e){this.time=e.time,this.target=e.target,this.rootBounds=u(e.rootBounds),this.boundingClientRect=u(e.boundingClientRect),this.intersectionRect=u(e.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,o=this.intersectionRect,r=o.width*o.height;this.intersectionRatio=n?Number((r/n).toFixed(4)):this.isIntersecting?1:0}function s(e,t){var n,o,r,i=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(i.root&&1!=i.root.nodeType&&9!=i.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=(n=this._checkForIntersections.bind(this),o=this.THROTTLE_TIMEOUT,r=null,function(){r||(r=setTimeout((function(){n(),r=null}),o))}),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(i.rootMargin),this.thresholds=this._initThresholds(i.threshold),this.root=i.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function a(e,t,n,o){"function"==typeof e.addEventListener?e.addEventListener(t,n,o||!1):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function l(e,t,n,o){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,o||!1):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function c(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function u(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function f(e,t){var n=t.top-e.top,o=t.left-e.left;return{top:n,left:o,height:t.height,width:t.width,bottom:n+t.height,right:o+t.width}}function h(e,t){for(var n=t;n;){if(n==e)return!0;n=d(n)}return!1}function d(t){var n=t.parentNode;return 9==t.nodeType&&t!=e?r(t):(n&&n.assignedSlot&&(n=n.assignedSlot.parentNode),n&&11==n.nodeType&&n.host?n.host:n)}function p(e){return e&&9===e.nodeType}};function Ad(e){const{bottom:t,height:n,left:o,right:r,top:i,width:s}=e||{};return{bottom:t,height:n,left:o,right:r,top:i,width:s}}function Ld(e){const{intersectionRatio:t,boundingClientRect:{height:n,width:o},intersectionRect:{height:r,width:i}}=e;return 0!==t?t:r===n?i/o:r/n}const Rd="",jd={};function Bd(e,t){const n=jd[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const s=new Uint8Array(i);for(;i--;)s[i]=r.charCodeAt(i);return Nd(s,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function Nd(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function Dd(e){for(const n in jd)if(C(jd,n)){if(jd[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return jd[t]=e,t}const Fd=Lu(),qd=Lu();const Vd=Nu({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=Bn(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=xn({width:-1,height:-1});return $o((()=>x({},o)),(e=>t("resize",e))),()=>{const t=e.value;o.width=t.offsetWidth,o.height=t.offsetHeight,n()}}(n,t,o);return function(e,t,n,o){nr(o),pr((()=>{t.initial&&so(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>Ri("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[Ri("div",{onScroll:r},[Ri("div",null,null)],40,["onScroll"]),Ri("div",{onScroll:r},[Ri("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});const Wd=function(){if(navigator.userAgent.includes("jsdom"))return 1;const e=document.createElement("canvas");e.height=e.width=0;const t=e.getContext("2d"),n=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}();function zd(e,t=!0){e.width=e.offsetWidth*(t?Wd:1),e.height=e.offsetHeight*(t?Wd:1),e.getContext("2d").__hidpi__=t}let Hd=!1;function Ud(){if(Hd)return;Hd=!0;const e={fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",transform:[4,5],setTransform:[4,5]},t=CanvasRenderingContext2D.prototype;var n;t.drawImageByCanvas=(n=t.drawImage,function(e,t,o,r,i,s,a,l,c,u){if(!this.__hidpi__)return n.apply(this,arguments);t*=Wd,o*=Wd,r*=Wd,i*=Wd,s*=Wd,a*=Wd,l=u?l*Wd:l,c=u?c*Wd:c,n.call(this,e,t,o,r,i,s,a,l,c)}),1!==Wd&&(!function(e,t){for(const n in e)C(e,n)&&t(e[n],n)}(e,(function(e,n){t[n]=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);let n=Array.prototype.slice.call(arguments);if("all"===e)n=n.map((function(e){return e*Wd}));else if(Array.isArray(e))for(let t=0;t<e.length;t++)n[e[t]]*=Wd;return t.apply(this,n)}}(t[n])})),t.stroke=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.lineWidth*=Wd,e.apply(this,arguments),this.lineWidth/=Wd}}(t.stroke),t.fillText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);const t=Array.prototype.slice.call(arguments);t[1]*=Wd,t[2]*=Wd,t[3]&&"number"==typeof t[3]&&(t[3]*=Wd);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*Wd+n})),e.apply(this,t),this.font=n}}(t.fillText),t.strokeText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=Wd,t[2]*=Wd,t[3]&&"number"==typeof t[3]&&(t[3]*=Wd);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*Wd+n})),e.apply(this,t),this.font=n}}(t.strokeText),t.drawImage=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.scale(Wd,Wd),e.apply(this,arguments),this.scale(1/Wd,1/Wd)}}(t.drawImage))}const Xd=He((()=>Ud()));function Yd(e){return e?Zu(e):e}function Gd(e){return(e=e.slice(0))[3]=e[3]/255,"rgba("+e.join(",")+")"}function Jd(e,t){Array.from(t).forEach((t=>{t.x=t.clientX-e.left,t.y=t.clientY-e.top}))}let Qd;function Kd(e=0,t=0){return Qd||(Qd=document.createElement("canvas")),Qd.width=e,Qd.height=t,Qd}const Zd=Nu({inheritAttrs:!1,name:"Canvas",compatConfig:{MODE:3},props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1},hidpi:{type:Boolean,default:!0}},computed:{id(){return this.canvasId}},setup(e,{emit:t,slots:n}){Xd();const o=Bn(null),r=Bn(null),i=Bn(!1),s=function(e){return(t,n)=>{e(t,yu(n))}}(t),{$attrs:a,$excludeAttrs:l,$listeners:c}=Op({excludeListeners:!0}),{_listeners:u}=function(e,t,n){const o=os((()=>{let o=["onTouchstart","onTouchmove","onTouchend"],r=t.value,i=x({},(()=>{let e={};for(const t in r)if(C(r,t)){const n=r[t];e[t]=n}return e})());return o.forEach((t=>{let o=[];i[t]&&o.push(Wu((e=>{const o=e.currentTarget.getBoundingClientRect();Jd(o,e.touches),Jd(o,e.changedTouches),n(t.replace("on","").toLocaleLowerCase(),e)}))),e.disableScroll&&"onTouchmove"===t&&o.push(Nc),i[t]=o})),i}));return{_listeners:o}}(e,c,s),{_handleSubscribe:f,_resize:h}=function(e,t,n){let o=[],r={};const i=os((()=>e.hidpi?Wd:1));function s(n){let o=t.value;if(!n||o.width!==Math.floor(n.width*i.value)||o.height!==Math.floor(n.height*i.value))if(o.width>0&&o.height>0){let t=o.getContext("2d"),n=t.getImageData(0,0,o.width,o.height);zd(o,e.hidpi),t.putImageData(n,0,0)}else zd(o,e.hidpi)}function a({actions:e,reserve:i},s){if(!e)return;if(n.value)return void o.push([e,i]);let a=t.value,u=a.getContext("2d");i||(u.fillStyle="#000000",u.strokeStyle="#000000",u.shadowColor="#000000",u.shadowBlur=0,u.shadowOffsetX=0,u.shadowOffsetY=0,u.setTransform(1,0,0,1,0,0),u.clearRect(0,0,a.width,a.height)),l(e);for(let t=0;t<e.length;t++){const n=e[t];let o=n.method;const i=n.data,a=i[0];if(/^set/.test(o)&&"setTransform"!==o){const n=o[3].toLowerCase()+o.slice(4);let r;if("fillStyle"===n||"strokeStyle"===n){if("normal"===a)r=Gd(i[1]);else if("linear"===a){const e=u.createLinearGradient(...i[1]);i[2].forEach((function(t){const n=t[0],o=Gd(t[1]);e.addColorStop(n,o)})),r=e}else if("radial"===a){let e=i[1];const t=e[0],n=e[1],o=e[2],s=u.createRadialGradient(t,n,0,t,n,o);i[2].forEach((function(e){const t=e[0],n=Gd(e[1]);s.addColorStop(t,n)})),r=s}else if("pattern"===a){if(!c(i[1],e.slice(t+1),s,(function(e){e&&(u[n]=u.createPattern(e,i[2]))})))break;continue}u[n]=r}else if("globalAlpha"===n)u[n]=Number(a)/255;else if("shadow"===n){let e=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"];i.forEach((function(t,n){u[e[n]]="shadowColor"===e[n]?Gd(t):t}))}else if("fontSize"===n){const e=u.__font__||u.font;u.__font__=u.font=e.replace(/\d+\.?\d*px/,a+"px")}else"lineDash"===n?(u.setLineDash(a),u.lineDashOffset=i[1]||0):"textBaseline"===n?("normal"===a&&(i[0]="alphabetic"),u[n]=a):"font"===n?u.__font__=u.font=a:u[n]=a}else if("fillPath"===o||"strokePath"===o)o=o.replace(/Path/,""),u.beginPath(),i.forEach((function(e){u[e.method].apply(u,e.data)})),u[o]();else if("fillText"===o)u.fillText.apply(u,i);else if("drawImage"===o){if("break"===function(){let n=[...i],o=n[0],a=n.slice(1);if(r=r||{},!c(o,e.slice(t+1),s,(function(e){e&&u.drawImage.apply(u,[e].concat([...a.slice(4,8)],[...a.slice(0,4)]))})))return"break"}())break}else"clip"===o?(i.forEach((function(e){u[e.method].apply(u,e.data)})),u.clip()):u[o].apply(u,i)}n.value||s({errMsg:"drawCanvas:ok"})}function l(e){e.forEach((function(e){let t=e.method,n=e.data,o="";function i(){const e=r[o]=new Image;e.onload=function(){e.ready=!0},function(e){const t=document.createElement("a");return t.href=e,t.origin===location.origin?Promise.resolve(e):Bd(e).then(Dd)}(o).then((t=>{e.src=t})).catch((()=>{e.src=o}))}"drawImage"===t?(o=n[0],o=Yd(o),n[0]=o):"setFillStyle"===t&&"pattern"===n[0]&&(o=n[1],o=Yd(o),n[1]=o),o&&!r[o]&&i()}))}function c(e,t,i,s){let l=r[e];return l.ready?(s(l),!0):(o.unshift([t,!0]),n.value=!0,l.onload=function(){l.ready=!0,s(l),n.value=!1;let e=o.slice(0);o=[];for(let t=e.shift();t;)a({actions:t[0],reserve:t[1]},i),t=e.shift()},!1)}function u({x:e=0,y:n=0,width:o,height:r,destWidth:s,destHeight:a,hidpi:l=!0,dataType:c,quality:u=1,type:f="png"},h){const d=t.value;let p;const g=d.offsetWidth-e;o=o?Math.min(o,g):g;const m=d.offsetHeight-n;r=r?Math.min(r,m):m,l?(s=o,a=r):s||a?s?a||(a=Math.round(r/o*s)):s=Math.round(o/r*a):(s=Math.round(o*i.value),a=Math.round(r*i.value));const v=Kd(s,a),y=v.getContext("2d");let b;"jpeg"!==f&&"jpg"!==f||(f="jpeg",y.fillStyle="#fff",y.fillRect(0,0,s,a)),y.__hidpi__=!0,y.drawImageByCanvas(d,e,n,o,r,0,0,s,a,!1);try{let e;if("base64"===c)p=v.toDataURL(`image/${f}`,u);else{const e=y.getImageData(0,0,s,a);p=Array.prototype.slice.call(e.data)}b={data:p,compressed:e,width:s,height:a}}catch(_){b={errMsg:`canvasGetImageData:fail ${_}`}}if(v.height=v.width=0,y.__hidpi__=!1,!h)return b;h(b)}function f({data:e,x:n,y:o,width:r,height:i,compressed:s},a){try{0,i||(i=Math.round(e.length/4/r));const s=Kd(r,i);s.getContext("2d").putImageData(new ImageData(new Uint8ClampedArray(e),r,i),0,0),t.value.getContext("2d").drawImage(s,n,o,r,i),s.height=s.width=0}catch(l){return void a({errMsg:"canvasPutImageData:fail"})}a({errMsg:"canvasPutImageData:ok"})}function h({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,fileType:s,quality:a,dirname:l},c){const f=u({x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,hidpi:!1,dataType:"base64",type:s,quality:a});var h;f.data&&f.data.length?(h=f.data,((e,t)=>{let n="toTempFilePath:"+(e?"fail":"ok");e&&(n+=` ${e.message}`),c({errMsg:n,tempFilePath:t})})(null,h)):c({errMsg:f.errMsg.replace("canvasPutImageData","toTempFilePath")})}const d={actionsChanged:a,getImageData:u,putImageData:f,toTempFilePath:h};function p(e,t,n){let o=d[e];0!==e.indexOf("_")&&M(o)&&o(t,n)}return x(d,{_resize:s,_handleSubscribe:p})}(e,o,i);return function(e,t,n,o){const r=Xi().proxy;pr((()=>{Cg(t||Tg(r),e,o),!n&&t||$o((()=>r.id),((t,n)=>{Cg(Tg(r,t),e,o),kg(n&&Tg(r,n))}))})),vr((()=>{kg(t||Tg(r),o)}))}(f,function(e){const t=Qc(),n=Xi().proxy,o=n.$options.name.toLowerCase(),r=e||n.id||"context"+Eg++;return pr((()=>{n.$el.__uniContextInfo={id:r,type:o,page:t}})),`${o}.${r}`}(e.canvasId),!0),pr((()=>{h()})),()=>{const{canvasId:t,disableScroll:i}=e;return Ri("uni-canvas",Vi({"canvas-id":t,"disable-scroll":i},a.value,l.value,u.value),[Ri("canvas",{ref:o,class:"uni-canvas-canvas",width:"300",height:"150"},null,512),Ri("div",{style:"position: absolute;top: 0;left: 0;width: 100%;height: 100%;overflow: hidden;"},[n.default&&n.default()]),Ri(Vd,{ref:r,onResize:h},null,8,["onResize"])],16,["canvas-id","disable-scroll"])}}});let ep;function tp(){}const np={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function op(e,t,n){function o(e){const t=os((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(ep),document.addEventListener("click",tp,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",tp,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}$o((()=>t.value),(e=>e&&o(e)))}const rp={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},ip={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},sp={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},ap=Nu({name:"Image",props:rp,setup(e,{emit:t}){const n=Bn(null),o=function(e,t){const n=Bn(""),o=os((()=>{let e="auto",o="";const r=sp[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=xn({rootEl:e,src:os((()=>t.src?Zu(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return pr((()=>{const t=e.value.style;r.origWidth=Number(t.width)||0,r.origHeight=Number(t.height)||0})),r}(n,e),r=zu(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=ip[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){lp&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return $o((()=>t.mode),((e,t)=>{ip[t]&&r(),ip[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:f}=i;a(u,f,l),o(),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:f})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};$o((()=>e.src),(e=>l(e))),$o((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),pr((()=>l(e.src))),vr((()=>c()))}(o,e,n,i,r),()=>Ri("uni-image",{ref:n},[Ri("div",{style:o.modeStyle},null,4),ip[e.mode]?Ri(Vd,{onResize:i},null,8,["onResize"]):Ri("span",null,null)],512)}});const lp="Google Inc."===navigator.vendor;const cp=Je(!0),up=[];let fp,hp=0;const dp=e=>up.forEach((t=>t.userAction=e));function pp(e={userAction:!1}){if(!fp){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!hp&&dp(!0),hp++,setTimeout((()=>{! --hp&&dp(!1)}),0)}),cp)})),fp=!0}up.push(e)}function gp(){const e=xn({userAction:!1});return pr((()=>{pp(e)})),vr((()=>{!function(e){const t=up.indexOf(e);t>=0&&up.splice(t,1)}(e)})),{state:e}}function mp(){const e=xn({attrs:{}});return pr((()=>{let t=Xi();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function vp(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function yp(e,t){return"number"===t&&isNaN(Number(e))&&(e=""),null===e?"":String(e)}const bp=["none","text","decimal","numeric","tel","search","email","url"],_p=x({},{name:{type:String,default:""},modelValue:{type:[String,Number],default:""},value:{type:[String,Number],default:""},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~bp.indexOf(e)}},np),wp=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function xp(e,t,n,o){const r=nt((n=>{t.value=yp(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout});$o((()=>e.modelValue),r),$o((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return dr((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Sp(e,t){gp();const n=os((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}$o((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),pr((()=>{n.value&&so(o)}))}function Tp(e,t,n,o){dc(tu(),"getSelectedTextRange",vp);const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=Bn(null),r=zu(t,n),i=os((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=os((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=os((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=os((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),c=yp(e.modelValue,e.type)||yp(e.value,e.type),u=xn({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return $o((()=>u.focus),(e=>n("update:focus",e))),$o((()=>u.maxlength),(e=>u.value=u.value.slice(0,e))),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=xp(e,i,n,s);Sp(e,r),op(0,r);const{state:l}=mp();!function(e,t){const n=Mo(Hu,!1);if(!n)return;const o=Xi(),r={submit(){const n=o.proxy;return[n[e],I(t)?n[t]:t.value]},reset(){I(t)?o.proxy[t]="":t.value=""}};n.addField(r),vr((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}$o([()=>t.selectionStart,()=>t.selectionEnd],s),$o((()=>t.cursor),a),$o((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),M(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function f(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,f(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),f(e)})),c.addEventListener("compositionupdate",f)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const Cp=Nu({name:"Input",props:x({},_p,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...wp],setup(e,{emit:t}){const n=["text","number","idcard","digit","password","tel"],o=["off","one-time-code"],r=os((()=>{let t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~n.includes(e.type)?e.type:"text"}return e.password?"password":t})),i=os((()=>{const t=o.indexOf(e.textContentType),n=o.indexOf(z(e.textContentType));return o[-1!==t?t:-1!==n?n:0]}));let s,a=Bn("");const l=Bn(null),{fieldRef:c,state:u,scopedAttrsState:f,fixDisabledColor:h,trigger:d}=Tp(e,l,t,((e,t)=>{const n=e.target;if("number"===r.value){if(s&&(n.removeEventListener("blur",s),s=null),n.validity&&!n.validity.valid){if((!a.value||!n.value)&&"-"===e.data||"-"===a.value[0]&&"deleteContentBackward"===e.inputType)return a.value="-",t.value="",s=()=>{a.value=n.value=""},n.addEventListener("blur",s),!1;if(a.value)if(-1!==a.value.indexOf(".")){if("."!==e.data&&"deleteContentBackward"===e.inputType){const e=a.value.indexOf(".");return a.value=n.value=t.value=a.value.slice(0,e),!0}}else if("."===e.data)return a.value+=".",s=()=>{a.value=n.value=a.value.slice(0,-1)},n.addEventListener("blur",s),!1;return a.value=t.value=n.value="-"===a.value?"":a.value,!1}a.value=n.value;const o=t.maxlength;if(o>0&&n.value.length>o)return n.value=n.value.slice(0,o),t.value=n.value,!1}}));$o((()=>u.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t)}));const p=["number","digit"],g=os((()=>p.includes(e.type)?e.step:""));function m(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),d("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return()=>{let t=e.disabled&&h?Ri("input",{key:"disabled-input",ref:c,value:u.value,tabindex:"-1",readonly:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,class:"uni-input-input",onFocus:e=>e.target.blur()},null,40,["value","readonly","type","maxlength","step","onFocus"]):Ri("input",{key:"input",ref:c,value:u.value,disabled:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",autocomplete:i.value,onKeyup:m,inputmode:e.inputmode},null,40,["value","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return Ri("uni-input",{ref:l},[Ri("div",{class:"uni-input-wrapper"},[Sr(Ri("div",Vi(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Ys,!(u.value.length||"-"===a.value)]]),"search"===e.confirmType?Ri("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const kp=["class","style"],Ep=/^on[A-Z]+/,Op=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=Xi(),r=Nn({}),i=Nn({}),s=Nn({}),a=n.concat(kp);return o.attrs=xn(o.attrs),Io((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(a.includes(n)?e.exclude[n]=o:Ep.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,s.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:s}};function Mp(e){const t=[];return k(e)&&e.forEach((e=>{Mi(e)?e.type===vi?t.push(...Mp(e.children)):t.push(e):k(e)&&t.push(...Mp(e))})),t}const Ip=Nu({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=Bn(null),o=Bn(!1);let{setContexts:r,events:i}=function(e,t){const n=Bn(0),o=Bn(0),r=xn({x:null,y:null}),i=Bn(null);let s=null,a=[];function l(t){t&&1!==t&&(e.scaleArea?a.forEach((function(e){e._setScale(t)})):s&&s._setScale(t))}function c(e,n=a){let o=t.value;function r(e){for(let t=0;t<n.length;t++){const o=n[t];if(e===o.rootRef.value)return o}return e===o||e===document.body||e===document?null:r(e.parentNode)}return r(e)}const u=Wu((t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(i.value=Pp(t),r.x=t.x,r.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);s=e&&e===t?e:null}}})),f=Wu((e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==r.x&&i.value&&i.value>0){l(Pp(n)/i.value)}r.x=n.x,r.y=n.y}})),h=Wu((t=>{let n=t.touches;n&&n.length||t.changedTouches&&(r.x=0,r.y=0,i.value=null,e.scaleArea?a.forEach((function(e){e._endScale()})):s&&s._endScale())}));function d(){p(),a.forEach((function(e,t){e.setParent()}))}function p(){let e=window.getComputedStyle(t.value),r=t.value.getBoundingClientRect();n.value=r.width-["Left","Right"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0),o.value=r.height-["Top","Bottom"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0)}return Oo("movableAreaWidth",n),Oo("movableAreaHeight",o),{setContexts(e){a=e},events:{_onTouchstart:u,_onTouchmove:f,_onTouchend:h,_resize:d}}}(e,n);const{$listeners:s,$attrs:a,$excludeAttrs:l}=Op(),c=s.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{let t=c[e],n=i[`_${e}`];c[e]=t?[].concat(t,n):n})),pr((()=>{i._resize(),o.value=!0}));let u=[];const f=[];function h(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const o=f.find((e=>n===e.rootRef.value));o&&e.push(Pn(o))}r(e)}return Oo("_isMounted",o),Oo("movableAreaRootRef",n),Oo("addMovableViewContext",(e=>{f.push(e),h()})),Oo("removeMovableViewContext",(e=>{const t=f.indexOf(e);t>=0&&(f.splice(t,1),h())})),()=>{const e=t.default&&t.default();return u=Mp(e),Ri("uni-movable-area",Vi({ref:n},a.value,l.value,c),[Ri(Vd,{onResize:i._resize},null,8,["onResize"]),u],16)}}});function Pp(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const $p=function(e,t,n,o){e.addEventListener(t,(e=>{M(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let Ap,Lp;function Rp(e,t,n){vr((()=>{document.removeEventListener("mousemove",Ap),document.removeEventListener("mouseup",Lp)}));let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;$p(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)})),$p(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)})),$p(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}}));const f=Ap=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",f),$p(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const h=Lp=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",h),$p(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function jp(e,t,n){return e>t-n&&e<t+n}function Bp(e,t){return jp(e,0,t)}function Np(){}function Dp(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function Fp(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function qp(e,t,n){this._springX=new Fp(e,t,n),this._springY=new Fp(e,t,n),this._springScale=new Fp(e,t,n),this._startTime=0}Np.prototype.x=function(e){return Math.sqrt(e)},Dp.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},Dp.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},Dp.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},Dp.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},Dp.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},Dp.prototype.dt=function(){return-this._x_v/this._x_a},Dp.prototype.done=function(){const e=jp(this.s().x,this._endPositionX)||jp(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},Dp.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},Dp.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},Fp.prototype._solve=function(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}},Fp.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},Fp.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},Fp.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Bp(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(Bp(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Bp(t,.1)&&(t=0),Bp(o,.1)&&(o=0),o+=this._endPosition),this._solution&&Bp(o-e,.1)&&Bp(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},Fp.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},Fp.prototype.done=function(e){return e||(e=(new Date).getTime()),jp(this.x(),this._endPosition,.1)&&Bp(this.dx(),.1)},Fp.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},Fp.prototype.springConstant=function(){return this._k},Fp.prototype.damping=function(){return this._c},Fp.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},qp.prototype.setEnd=function(e,t,n,o){const r=(new Date).getTime();this._springX.setEnd(e,o,r),this._springY.setEnd(t,o,r),this._springScale.setEnd(n,o,r),this._startTime=r},qp.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},qp.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},qp.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function Vp(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const Wp=Nu({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=Bn(null),r=zu(o,n),{setParent:i}=function(e,t,n){const o=Mo("_isMounted",Bn(!1)),r=Mo("addMovableViewContext",(()=>{})),i=Mo("removeMovableViewContext",(()=>{}));let s,a,l=Bn(1),c=Bn(1),u=Bn(!1),f=Bn(0),h=Bn(0),d=null,p=null,g=!1,m=null,v=null;const y=new Np,b=new Np,_={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=os((()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new Dp(1,w.value);$o((()=>e.disabled),(()=>{H()}));const{_updateOldScale:S,_endScale:T,_setScale:C,scaleValueSync:k,_updateBoundary:E,_updateOffset:O,_updateWH:M,_scaleOffset:I,minX:P,minY:$,maxX:A,maxY:L,FAandSFACancel:R,_getLimitXY:j,_setTransform:B,_revise:N,dampingNumber:D,xMove:F,yMove:q,xSync:V,ySync:W,_STD:z}=function(e,t,n,o,r,i,s,a,l,c){const u=os((()=>{let t=Number(e.scaleMin);return isNaN(t)?.5:t})),f=os((()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t})),h=Bn(Number(e.scaleValue)||1);$o(h,(e=>{B(e)})),$o(u,(()=>{j()})),$o(f,(()=>{j()})),$o((()=>e.scaleValue),(e=>{h.value=Number(e)||0}));const{_updateBoundary:d,_updateOffset:p,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_}=function(e,t,n){const o=Mo("movableAreaWidth",Bn(0)),r=Mo("movableAreaHeight",Bn(0)),i=Mo("movableAreaRootRef"),s={x:0,y:0},a={x:0,y:0},l=Bn(0),c=Bn(0),u=Bn(0),f=Bn(0),h=Bn(0),d=Bn(0);function p(){let e=0-s.x+a.x,t=o.value-l.value-s.x-a.x;u.value=Math.min(e,t),h.value=Math.max(e,t);let n=0-s.y+a.y,i=r.value-c.value-s.y-a.y;f.value=Math.min(n,i),d.value=Math.max(n,i)}function g(){s.x=Up(e.value,i.value),s.y=Xp(e.value,i.value)}function m(o){o=o||t.value,o=n(o);let r=e.value.getBoundingClientRect();c.value=r.height/t.value,l.value=r.width/t.value;let i=c.value*o,s=l.value*o;a.x=(s-l.value)/2,a.y=(i-c.value)/2}return{_updateBoundary:p,_updateOffset:g,_updateWH:m,_scaleOffset:a,minX:u,minY:f,maxX:h,maxY:d}}(t,o,R),{FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:T,_revise:C,dampingNumber:k,xMove:E,yMove:O,xSync:M,ySync:I,_STD:P}=function(e,t,n,o,r,i,s,a,l,c,u,f,h,d){const p=os((()=>{let e=Number(t.damping);return isNaN(e)?20:e})),g=os((()=>"all"===t.direction||"horizontal"===t.direction)),m=os((()=>"all"===t.direction||"vertical"===t.direction)),v=Bn(Gp(t.x)),y=Bn(Gp(t.y));$o((()=>t.x),(e=>{v.value=Gp(e)})),$o((()=>t.y),(e=>{y.value=Gp(e)})),$o(v,(e=>{C(e)})),$o(y,(e=>{k(e)}));const b=new qp(1,9*Math.pow(p.value,2)/40,p.value);function _(e,t){let n=!1;return e>r.value?(e=r.value,n=!0):e<s.value&&(e=s.value,n=!0),t>i.value?(t=i.value,n=!0):t<a.value&&(t=a.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){f&&f.cancel(),u&&u.cancel()}function x(e,n,r,i,s,a){w(),g.value||(e=l.value),m.value||(n=c.value),t.scale||(r=o.value);let f=_(e,n);e=f.x,n=f.y,t.animation?(b._springX._solution=null,b._springY._solution=null,b._springScale._solution=null,b._springX._endPosition=l.value,b._springY._endPosition=c.value,b._springScale._endPosition=o.value,b.setEnd(e,n,r,1),u=Yp(b,(function(){let e=b.x();S(e.x,e.y,e.scale,i,s,a)}),(function(){u.cancel()}))):S(e,n,r,i,s,a)}function S(r,i,s,a="",u,f){null!==r&&"NaN"!==r.toString()&&"number"==typeof r||(r=l.value||0),null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=c.value||0),r=Number(r.toFixed(1)),i=Number(i.toFixed(1)),s=Number(s.toFixed(1)),l.value===r&&c.value===i||u||d("change",{},{x:Vp(r,n.x),y:Vp(i,n.y),source:a}),t.scale||(s=o.value),s=+(s=h(s)).toFixed(3),f&&s!==o.value&&d("scale",{},{x:r,y:i,scale:s});let p="translateX("+r+"px) translateY("+i+"px) translateZ(0px) scale("+s+")";e.value&&(e.value.style.transform=p,e.value.style.webkitTransform=p,l.value=r,c.value=i,o.value=s)}function T(e){let t=_(l.value,c.value),n=t.x,r=t.y,i=t.outOfBounds;return i&&x(n,r,o.value,e),i}function C(e){if(g.value){if(e+n.x===l.value)return l;u&&u.cancel(),x(e+n.x,y.value+n.y,o.value)}return e}function k(e){if(m.value){if(e+n.y===c.value)return c;u&&u.cancel(),x(v.value+n.x,e+n.y,o.value)}return e}return{FAandSFACancel:w,_getLimitXY:_,_animationTo:x,_setTransform:S,_revise:T,dampingNumber:p,xMove:g,yMove:m,xSync:v,ySync:y,_STD:b}}(t,e,m,o,b,_,v,y,s,a,l,c,R,n);function $(t,n){if(e.scale){t=R(t),g(t),d();const e=x(s.value,a.value),o=e.x,r=e.y;n?S(o,r,t,"",!0,!0):Hp((function(){T(o,r,t,"",!0,!0)}))}}function A(){i.value=!0}function L(e){r.value=e}function R(e){return e=Math.max(.5,u.value,e),e=Math.min(10,f.value,e)}function j(){if(!e.scale)return!1;$(o.value,!0),L(o.value)}function B(t){return!!e.scale&&($(t=R(t),!0),L(t),t)}function N(){i.value=!1,L(o.value)}function D(e){e&&(e=r.value*e,A(),$(e))}return{_updateOldScale:L,_endScale:N,_setScale:D,scaleValueSync:h,_updateBoundary:d,_updateOffset:p,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_,FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:T,_revise:C,dampingNumber:k,xMove:E,yMove:O,xSync:M,ySync:I,_STD:P}}(e,n,t,l,c,u,f,h,d,p);function H(){u.value||e.disabled||(R(),_.historyX=[0,0],_.historyY=[0,0],_.historyT=[0,0],F.value&&(s=f.value),q.value&&(a=h.value),n.value.style.willChange="transform",m=null,v=null,g=!0)}function U(t){if(!u.value&&!e.disabled&&g){let n=f.value,o=h.value;if(null===v&&(v=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),F.value&&(n=t.detail.dx+s,_.historyX.shift(),_.historyX.push(n),q.value||null!==m||(m=Math.abs(t.detail.dx/t.detail.dy)<1)),q.value&&(o=t.detail.dy+a,_.historyY.shift(),_.historyY.push(o),F.value||null!==m||(m=Math.abs(t.detail.dy/t.detail.dx)<1)),_.historyT.shift(),_.historyT.push(t.detail.timeStamp),!m){t.preventDefault();let r="touch";n<P.value?e.outOfBounds?(r="touch-out-of-bounds",n=P.value-y.x(P.value-n)):n=P.value:n>A.value&&(e.outOfBounds?(r="touch-out-of-bounds",n=A.value+y.x(n-A.value)):n=A.value),o<$.value?e.outOfBounds?(r="touch-out-of-bounds",o=$.value-b.x($.value-o)):o=$.value:o>L.value&&(e.outOfBounds?(r="touch-out-of-bounds",o=L.value+b.x(o-L.value)):o=L.value),Hp((function(){B(n,o,l.value,r)}))}}}function X(){if(!u.value&&!e.disabled&&g&&(n.value.style.willChange="auto",g=!1,!m&&!N("out-of-bounds")&&e.inertia)){const e=1e3*(_.historyX[1]-_.historyX[0])/(_.historyT[1]-_.historyT[0]),t=1e3*(_.historyY[1]-_.historyY[0])/(_.historyT[1]-_.historyT[0]),n=f.value,o=h.value;x.setV(e,t),x.setS(n,o);const r=x.delta().x,i=x.delta().y;let s=r+n,a=i+o;s<P.value?(s=P.value,a=o+(P.value-n)*i/r):s>A.value&&(s=A.value,a=o+(A.value-n)*i/r),a<$.value?(a=$.value,s=n+($.value-o)*r/i):a>L.value&&(a=L.value,s=n+(L.value-o)*r/i),x.setEnd(s,a),p=Yp(x,(function(){let e=x.s(),t=e.x,n=e.y;B(t,n,l.value,"friction")}),(function(){p.cancel()}))}e.outOfBounds||e.inertia||R()}function Y(){if(!o.value)return;R();let t=e.scale?k.value:1;O(),M(t),E();let n=j(V.value+I.x,W.value+I.y),r=n.x,i=n.y;B(r,i,t,"",!0),S(t)}return pr((()=>{Rp(n.value,(e=>{switch(e.detail.state){case"start":H();break;case"move":U(e);break;case"end":X()}})),Y(),x.reconfigure(1,w.value),z.reconfigure(1,9*Math.pow(D.value,2)/40,D.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:Y,_endScale:T,_setScale:C};r(e),yr((()=>{i(e)}))})),yr((()=>{R()})),{setParent:Y}}(e,r,o);return()=>Ri("uni-movable-view",{ref:o},[Ri(Vd,{onResize:i},null,8,["onResize"]),t.default&&t.default()],512)}});let zp=!1;function Hp(e){zp||(zp=!0,requestAnimationFrame((function(){e(),zp=!1})))}function Up(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=Up(e.offsetParent,t):0}function Xp(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=Xp(e.offsetParent,t):0}function Yp(e,t,n){let o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);let i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}function Gp(e){return/\d+[ur]px$/i.test(e)?rh(parseFloat(e)):Number(e)||0}const Jp=["navigate","redirect","switchTab","reLaunch","navigateBack"],Qp=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],Kp=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],Zp={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~Jp.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||Qp.concat(Kp).includes(e)},animationDuration:{type:[String,Number],default:300}};const eg=Nu({name:"Navigator",inheritAttrs:!1,compatConfig:{MODE:3},props:x({},Zp,{renderLink:{type:Boolean,default:!0}}),setup(e,{slots:t}){const n=Xi(),o=n&&n.vnode.scopeId||"",{hovering:r,binding:i}=qu(e),s=function(e){return()=>{if("navigateBack"!==e.openType&&!e.url)return void console.error("<navigator/> should have url attribute when using navigateTo, redirectTo, reLaunch or switchTab");const t=parseInt(e.animationDuration);switch(e.openType){case"navigate":Iv({url:e.url,animationType:e.animationType||"pop-in",animationDuration:t});break;case"redirect":Pv({url:e.url,exists:e.exists});break;case"switchTab":Lv({url:e.url});break;case"reLaunch":$v({url:e.url});break;case"navigateBack":Ov({delta:e.delta,animationType:e.animationType||"pop-out",animationDuration:t})}}}(e);return()=>{const{hoverClass:a,url:l}=e,c=e.hoverClass&&"none"!==e.hoverClass,u=Ri("uni-navigator",Vi({class:c&&r.value?a:""},c&&i,n?n.attrs:{},{[o]:""},{onClick:s}),[t.default&&t.default()],16,["onClick"]);return e.renderLink?Ri("a",{class:"navigator-wrap",href:l,onClick:Nc,onMousedown:Nc},[u],40,["href","onClick","onMousedown"]):u}}});const tg=Nu({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return k(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=Bn(null),r=Bn(null),i=zu(o,n),s=function(e){const t=xn([...e.value]),n=xn({value:t,height:34});return $o((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),a=Bn(null);pr((()=>{const e=a.value;s.height=e.$el.offsetHeight}));let l=Bn([]),c=Bn([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==bi));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return Oo("getPickerViewColumn",(function(e){return os({get(){const t=u(e.vnode);return s.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(s.value[o]!==t){s.value[o]=t;const e=s.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),Oo("pickerViewProps",e),Oo("pickerViewState",s),()=>{const e=t.default&&t.default();{const t=Mp(e);l.value=t,so((()=>{c.value=t}))}return Ri("uni-picker-view",{ref:o},[Ri(Vd,{ref:a,onResize:({height:e})=>s.height=e},null,8,["onResize"]),Ri("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class ng{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function og(e,t,n){return e>t-n&&e<t+n}function rg(e,t){return og(e,0,t)}class ig{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!rg(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(rg(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),rg(t,.4)&&(t=0),rg(o,.4)&&(o=0),o+=this._endPosition),this._solution&&rg(o-e,.4)&&rg(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),og(this.x(),this._endPosition,.4)&&rg(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class sg{constructor(e,t,n){this._extent=e,this._friction=t||new ng(.01),this._spring=n||new ig(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class ag{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new sg(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),M(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),M(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(M(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),M(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}let lg=0;const cg=Nu({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=Bn(null),r=Bn(null),i=Mo("getPickerViewColumn"),s=Xi(),a=i?i(s):Bn(0),l=Mo("pickerViewProps"),c=Mo("pickerViewState"),u=Bn(34),f=Bn(null);pr((()=>{const e=f.value;u.value=e.$el.offsetHeight}));const h=os((()=>(c.height-u.value)/2)),{state:d}=mp(),p=function(e){const t="uni-picker-view-content-"+lg++;return $o((()=>e.value),(function(){const n=document.createElement("style");n.innerText=`.uni-picker-view-content.${t}>*{height: ${e.value}px;overflow: hidden;}`,document.head.appendChild(n)})),t}(u);let g;const m=xn({current:a.value,length:0});let v;function y(){g&&!v&&(v=!0,so((()=>{v=!1;let e=Math.min(m.current,m.length-1);e=Math.max(e,0),g.update(e*u.value,void 0,u.value)})))}$o((()=>a.value),(e=>{e!==m.current&&(m.current=e,y())})),$o((()=>m.current),(e=>a.value=e)),$o([()=>u.value,()=>m.length,()=>c.height],y);let b=0;function _(e){const t=b+e.deltaY;if(Math.abs(t)>10){b=0;let e=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=e=Math.max(e,0),g.scrollTo(e*u.value)}else b=t;e.preventDefault()}function w({clientY:e}){const t=o.value;if(!g.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(m.current+t,m.length-1);m.current=r=Math.max(r,0),g.scrollTo(r*u.value)}}}const x=()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:s,handleTouchEnd:a}=function(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new ag(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],s=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(s-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new ng(1e-4),spring:new ig(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});g=n,Rp(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":s(e),e.stopPropagation();break;case"end":case"cancel":a(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),y()};return pr(x),()=>{const e=t.default&&t.default();m.length=Mp(e).length;const n=`${h.value}px 0`;return Ri("uni-picker-view-column",{ref:o},[Ri("div",{onWheel:_,onClick:w,class:"uni-picker-view-group"},[Ri("div",Vi(d.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${h.value}px;${l.maskStyle}`}),null,16),Ri("div",Vi(d.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[Ri(Vd,{ref:f,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),Ri("div",{ref:r,class:["uni-picker-view-content",p],style:{padding:n}},[e],6)],40,["onWheel","onClick"])],512)}}}),ug=Je(!0),fg=Nu({name:"ScrollView",compatConfig:{MODE:3},props:{scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"back"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n}){const o=Bn(null),r=Bn(null),i=Bn(null),s=Bn(null),a=Bn(null),l=zu(o,t),{state:c,scrollTopNumber:u,scrollLeftNumber:f}=function(e){const t=os((()=>Number(e.scrollTop)||0)),n=os((()=>Number(e.scrollLeft)||0)),o=xn({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshRotate:0,refreshState:""});return{state:o,scrollTopNumber:t,scrollLeftNumber:n}}(e);!function(e,t,n,o,r,i,s,a,l){let c=!1,u=0,f=!1,h=()=>{};const d=os((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),p=os((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function g(e,t){const n=s.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=a.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",h),i.removeEventListener("webkitTransitionEnd",h),h=()=>_(e,t),i.addEventListener("transitionend",h),i.addEventListener("webkitTransitionEnd",h),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function m(n){const o=n.target;r("scroll",n,{scrollLeft:o.scrollLeft,scrollTop:o.scrollTop,scrollHeight:o.scrollHeight,scrollWidth:o.scrollWidth,deltaX:t.lastScrollLeft-o.scrollLeft,deltaY:t.lastScrollTop-o.scrollTop}),e.scrollY&&(o.scrollTop<=d.value&&t.lastScrollTop-o.scrollTop>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",n,{direction:"top"}),t.lastScrollToUpperTime=n.timeStamp),o.scrollTop+o.offsetHeight+p.value>=o.scrollHeight&&t.lastScrollTop-o.scrollTop<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",n,{direction:"bottom"}),t.lastScrollToLowerTime=n.timeStamp)),e.scrollX&&(o.scrollLeft<=d.value&&t.lastScrollLeft-o.scrollLeft>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",n,{direction:"left"}),t.lastScrollToUpperTime=n.timeStamp),o.scrollLeft+o.offsetWidth+p.value>=o.scrollWidth&&t.lastScrollLeft-o.scrollLeft<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",n,{direction:"right"}),t.lastScrollToLowerTime=n.timeStamp)),t.lastScrollTop=o.scrollTop,t.lastScrollLeft=o.scrollLeft}function v(t){e.scrollY&&(e.scrollWithAnimation?g(t,"y"):s.value.scrollTop=t)}function y(t){e.scrollX&&(e.scrollWithAnimation?g(t,"x"):s.value.scrollLeft=t)}function b(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=s.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(e.scrollX){let n=o.left-t.left,r=s.value.scrollLeft+n;e.scrollWithAnimation?g(r,"x"):s.value.scrollLeft=r}if(e.scrollY){let n=o.top-t.top,r=s.value.scrollTop+n;e.scrollWithAnimation?g(r,"y"):s.value.scrollTop=r}}}}function _(t,n){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let o=s.value;"x"===n?(o.style.overflowX=e.scrollX?"auto":"hidden",o.scrollLeft=t):"y"===n&&(o.style.overflowY=e.scrollY?"auto":"hidden",o.scrollTop=t),a.value.removeEventListener("transitionend",h),a.value.removeEventListener("webkitTransitionEnd",h)}function w(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherrefresh",{},{}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(f=!1,r("refresherrestore",{},{})),"refresherabort"===n&&f&&(f=!1,r("refresherabort",{},{}))}t.refreshState=n}}pr((()=>{so((()=>{v(n.value),y(o.value)})),b(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),m(e)},a={x:0,y:0},l=null,h=function(n){if(null===a)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,h=s.value;if(Math.abs(o-a.x)>Math.abs(i-a.y))if(e.scrollX){if(0===h.scrollLeft&&o>a.x)return void(l=!1);if(h.scrollWidth===h.offsetWidth+h.scrollLeft&&o<a.x)return void(l=!1);l=!0}else l=!1;else if(e.scrollY)if(0===h.scrollTop&&i>a.y)l=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(h.scrollHeight===h.offsetHeight+h.scrollTop&&i<a.y)return void(l=!1);l=!0}else l=!1;if(l&&n.stopPropagation(),0===h.scrollTop&&1===n.touches.length&&w("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-a.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,f=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(f=!0,r("refresherpulling",n,{deltaY:o})));const s=t.refresherHeight/e.refresherThreshold;t.refreshRotate=360*(s>1?1:s)}},d=function(e){1===e.touches.length&&(a={x:e.touches[0].pageX,y:e.touches[0].pageY})},p=function(n){a=null,t.refresherHeight>=e.refresherThreshold?w("refreshing"):w("refresherabort")};s.value.addEventListener("touchstart",d,ug),s.value.addEventListener("touchmove",h,Je(!1)),s.value.addEventListener("scroll",i,Je(!1)),s.value.addEventListener("touchend",p,ug),vr((()=>{s.value.removeEventListener("touchstart",d),s.value.removeEventListener("touchmove",h),s.value.removeEventListener("scroll",i),s.value.removeEventListener("touchend",p)}))})),nr((()=>{e.scrollY&&(s.value.scrollTop=t.lastScrollTop),e.scrollX&&(s.value.scrollLeft=t.lastScrollLeft)})),$o(n,(e=>{v(e)})),$o(o,(e=>{y(e)})),$o((()=>e.scrollIntoView),(e=>{b(e)})),$o((()=>e.refresherTriggered),(e=>{!0===e?w("refreshing"):!1===e&&w("restore")}))}(e,c,u,f,l,o,r,s,t);const h=os((()=>{let t="";return e.scrollX?t+="overflow-x:auto;":t+="overflow-x:hidden;",e.scrollY?t+="overflow-y:auto;":t+="overflow-y:hidden;",t}));return()=>{const{refresherEnabled:t,refresherBackground:l,refresherDefaultStyle:u}=e,{refresherHeight:f,refreshState:d,refreshRotate:p}=c;return Ri("uni-scroll-view",{ref:o},[Ri("div",{ref:i,class:"uni-scroll-view"},[Ri("div",{ref:r,style:h.value,class:"uni-scroll-view"},[Ri("div",{ref:s,class:"uni-scroll-view-content"},[t?Ri("div",{ref:a,style:{backgroundColor:l,height:f+"px"},class:"uni-scroll-view-refresher"},["none"!==u?Ri("div",{class:"uni-scroll-view-refresh"},[Ri("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==d?Ri("svg",{key:"refresh__icon",style:{transform:"rotate("+p+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[Ri("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),Ri("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==d?Ri("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[Ri("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"==u?n.refresher&&n.refresher():null],4):null,n.default&&n.default()],512)],4)],512)],512)}}});function hg(e,t,n,o,r,i){function s(){c&&(clearTimeout(c),c=null)}let a,l,c=null,u=!0,f=0,h=1,d=null,p=!1,g=0,m="";const v=os((()=>n.value.length>t.displayMultipleItems)),y=os((()=>e.circular&&v.value));function b(r){Math.floor(2*f)===Math.floor(2*r)&&Math.ceil(2*f)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,s=o+t.displayMultipleItems,a=0;a<i;a++){const t=r[a],n=Math.floor(o/i)*i+a,l=n+i,c=n-i,u=Math.max(o-(n+1),n-s,0),f=Math.max(o-(l+1),l-s,0),h=Math.max(o-(c+1),c-s,0),d=Math.min(u,f,h),p=[n,l,c][[u,f,h].indexOf(d)];t.updatePosition(p,e.vertical)}}(r);const s="translate("+(e.vertical?"0":100*-r*h+"%")+", "+(e.vertical?100*-r*h+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=s,l.style.transform=s),f=r,!a){if(r%1==0)return;a=r}r-=Math.floor(a);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=a%1>.5||a<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function _(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){d=null}function x(){if(!d)return void(p=!1);const e=d,o=e.toPos,r=e.acc,s=e.endTime,c=e.source,u=s-Date.now();if(u<=0){b(o),d=null,p=!1,a=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function S(e,o,r){w();const i=t.duration,s=n.value.length;let a=f;if(y.value)if(r<0){for(;a<e;)a+=s;for(;a-s>e;)a-=s}else if(r>0){for(;a>e;)a-=s;for(;a+s<e;)a+=s;a+s-e<e-a&&(a+=s)}else{for(;a+s<e;)a+=s;for(;a-s>e;)a-=s;a+s-e<e-a&&(a+=s)}else"click"===o&&(e=e+t.displayMultipleItems-1<s?e:0);d={toPos:e,acc:2*(a-e)/(i*i),endTime:Date.now()+i,source:o},p||(p=!0,l=requestAnimationFrame(x))}function T(){s();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=_(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function C(e){e?T():s()}return $o([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),$o([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){s(),d&&(b(d.toPos),d=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);h=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();h=e.width/t.width,h>0&&h<1||(h=1)}const a=f;f=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(a+l-g),g=l):(b(l),e.autoplay&&T())):(u=!0,b(-t.displayMultipleItems-1))})),$o((()=>t.interval),(()=>{c&&(s(),T())})),$o((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const s=n.value;if(!r){const t=s.length;S(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const a=s[e];if(a){const e=t.currentItemId=a.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),$o((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),$o((()=>e.autoplay&&!t.userTracking),C),C(e.autoplay&&!t.userTracking),pr((()=>{let r=!1,i=0,a=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=_(f+o);e?b(g):(m="touch",t.current=r,S(r,"touch",0!==o?o:0===r&&y.value&&f>=1?1:0))}Rp(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,s(),g=f,i=0,a=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&T())}return function(r){const s=a;a=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const f=a-s||1,h=o.value;e.vertical?u(-r.dy/h.offsetHeight,-r.ddy/f):u(-r.dx/h.offsetWidth,-r.ddx/f)}(c.detail),!1}}}))})),yr((()=>{s(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){S(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const dg=Nu({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=Bn(null),r=zu(o,n),i=Bn(null),s=Bn(null),a=function(e){return xn({interval:os((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:os((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:os((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=os((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:Xc(e.previousMargin,!0),bottom:Xc(e.nextMargin,!0)}:{top:0,bottom:0,left:Xc(e.previousMargin,!0),right:Xc(e.nextMargin,!0)}),t})),c=os((()=>{const t=Math.abs(100/a.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const f=[],h=Bn([]);function d(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=f.find((e=>n===e.rootRef.value));o&&e.push(Pn(o))}h.value=e}Oo("addSwiperContext",(function(e){f.push(e),d()}));Oo("removeSwiperContext",(function(e){const t=f.indexOf(e);t>=0&&(f.splice(t,1),d())}));const{onSwiperDotClick:p,circularEnabled:g,swiperEnabled:m}=hg(e,a,h,s,n,r);let v=()=>null;return v=pg(o,e,a,p,h,g,m),()=>{const n=t.default&&t.default();return u=Mp(n),Ri("uni-swiper",{ref:o},[Ri("div",{ref:i,class:"uni-swiper-wrapper"},[Ri("div",{class:"uni-swiper-slides",style:l.value},[Ri("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&Ri("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[h.value.map(((t,n,o)=>Ri("div",{onClick:()=>p(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<a.current+a.displayMultipleItems&&n>=a.current||n<a.current+a.displayMultipleItems-o.length},style:{background:n===a.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),pg=(e,t,n,o,r,i,s)=>{let a=!1,l=!1,c=!1,u=Bn(!1);function f(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Io((()=>{a="auto"===t.navigation,u.value=!0!==t.navigation||a,y()})),Io((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,c=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,s.value||(l=!0,c=!0,a&&(u.value=!0))}));const h={onMouseover:e=>f(e,"over"),onMouseout:e=>f(e,"out")};function d(e,t,s){if(e.stopPropagation(),s)return;const a=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=a-1);break;case"next":l++,l>=a&&i.value&&(l=0)}o(l)}const p=()=>Jc("M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",t.navigationColor,26);let g;const m=n=>{clearTimeout(g);const{clientX:o,clientY:r}=n,{left:i,right:s,top:a,bottom:l,width:c,height:f}=e.value.getBoundingClientRect();let h=!1;if(h=t.vertical?!(r-a<f/3||l-r<f/3):!(o-i<c/3||s-o<c/3),h)return g=setTimeout((()=>{u.value=h}),300);u.value=h},v=()=>{u.value=!0};function y(){e.value&&(e.value.removeEventListener("mousemove",m),e.value.removeEventListener("mouseleave",v),a&&(e.value.addEventListener("mousemove",m),e.value.addEventListener("mouseleave",v)))}return pr(y),function(){const e={"uni-swiper-navigation-hide":u.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?Ri(vi,null,[Ri("div",Vi({class:["uni-swiper-navigation uni-swiper-navigation-prev",x({"uni-swiper-navigation-disabled":l},e)],onClick:e=>d(e,"prev",l)},h),[p()],16,["onClick"]),Ri("div",Vi({class:["uni-swiper-navigation uni-swiper-navigation-next",x({"uni-swiper-navigation-disabled":c},e)],onClick:e=>d(e,"next",c)},h),[p()],16,["onClick"])]):null}},gg=Nu({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=Bn(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,s=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=s,i.style.transform=s)}};return pr((()=>{const e=Mo("addSwiperContext");e&&e(o)})),yr((()=>{const e=Mo("removeSwiperContext");e&&e(o)})),()=>Ri("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),mg={ensp:" ",emsp:" ",nbsp:" "};function vg(e,t){return e.replace(/\\n/g,te).split(te).map((e=>function(e,{space:t,decode:n}){if(!e)return e;t&&mg[t]&&(e=e.replace(/ /g,mg[t]));if(!n)return e;return e.replace(/&nbsp;/g,mg.nbsp).replace(/&ensp;/g,mg.ensp).replace(/&emsp;/g,mg.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")}(e,t)))}const yg=Nu({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup:(e,{slots:t})=>()=>{const n=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==bi){const o=vg(t.children,{space:e.space,decode:e.decode}),r=o.length-1;o.forEach(((e,t)=>{(0!==t||e)&&n.push(Bi(e)),t!==r&&n.push(Ri("br"))}))}else n.push(t)})),Ri("uni-text",{selectable:!!e.selectable||null},[Ri("span",null,n)],8,["selectable"])}}),bg=x({},_p,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>wg.concat("return").includes(e)}});let _g=!1;const wg=["done","go","next","search","send"];const xg=Nu({name:"Textarea",props:bg,emits:["confirm","linechange",...wp],setup(e,{emit:t}){const n=Bn(null),o=Bn(null),{fieldRef:r,state:i,scopedAttrsState:s,fixDisabledColor:a,trigger:l}=Tp(e,n,t),c=os((()=>i.value.split(te))),u=os((()=>wg.includes(e.confirmType))),f=Bn(0),h=Bn(null);function d({height:e}){f.value=e}function p(e){"Enter"===e.key&&u.value&&e.preventDefault()}function g(t){if("Enter"===t.key&&u.value){!function(e){l("confirm",e,{value:i.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return $o((()=>f.value),(t=>{const r=n.value,i=h.value,s=o.value;let a=parseFloat(getComputedStyle(r).lineHeight);isNaN(a)&&(a=i.offsetHeight);var c=Math.round(t/a);l("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:c}),e.autoHeight&&(r.style.height="auto",s.style.height=t+"px")})),function(){const e="(prefers-color-scheme: dark)";_g=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),()=>{let t=e.disabled&&a?Ri("textarea",{key:"disabled-textarea",ref:r,value:i.value,tabindex:"-1",readonly:!!e.disabled,maxlength:i.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":_g},style:{overflowY:e.autoHeight?"hidden":"auto"},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):Ri("textarea",{key:"textarea",ref:r,value:i.value,disabled:!!e.disabled,maxlength:i.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":_g},style:{overflowY:e.autoHeight?"hidden":"auto"},onKeydown:p,onKeyup:g},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return Ri("uni-textarea",{ref:n},[Ri("div",{ref:o,class:"uni-textarea-wrapper"},[Sr(Ri("div",Vi(s.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Ys,!i.value.length]]),Ri("div",{ref:h,class:"uni-textarea-line"},[" "],512),Ri("div",{class:"uni-textarea-compute"},[c.value.map((e=>Ri("div",null,[e.trim()?e:"."]))),Ri(Vd,{initial:!0,onResize:d},null,8,["initial","onResize"])]),"search"===e.confirmType?Ri("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),Sg=Nu({name:"View",props:x({},Fu),setup(e,{slots:t}){const{hovering:n,binding:o}=qu(e);return()=>{const r=e.hoverClass;return r&&"none"!==r?Ri("uni-view",Vi({class:n.value?r:""},o),[t.default&&t.default()],16):Ri("uni-view",null,[t.default&&t.default()])}}});function Tg(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function Cg(e,t,n){e&&dc(n||tu(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function kg(e,t){e&&function(e,t){t=hc(e,t),delete fc[t]}(t||tu(),e)}let Eg=0;function Og(e,t,n,o){M(t)&&fr(e,t.bind(n),o)}function Mg(e,t,n){var o;const r=e.mpType||n.$mpType;if(r&&"component"!==r&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!M(t))&&(st.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];k(r)?r.forEach((e=>Og(o,e,n,t))):Og(o,r,n,t)}})),"page"===r)){t.__isVisible=!0;try{iu(n,ge,t.attrs.__pageQuery),delete t.attrs.__pageQuery,"preloadPage"!==(null==(o=n.$page)?void 0:o.openType)&&iu(n,le)}catch(YC){console.error(YC.message+te+YC.stack)}}}function Ig(e,t,n){Mg(e,t,n)}function Pg(e,t,n){return e[t]=n}function $g(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;iu(r.proxy,fe,t)}}function Ag(e,t){return e?[...new Set([].concat(e,t))]:t}function Lg(e){const t=e._context.config;var n;t.errorHandler=lt(e,$g),n=t.optionMergeStrategies,st.forEach((e=>{n[e]=Ag}));const o=t.globalProperties;o.$set=Pg,o.$applyOptions=Ig,function(e){at.forEach((t=>t(e)))}(e)}const Rg=Hc("upm");function jg(){return Mo(Rg)}function Bg(e){const t=function(e){return xn(function(e){if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==Kg().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(ru(Xl().meta,e)))))}(e);return Oo(Rg,t),t}function Ng(){return Xl()}function Dg(){return history.state&&history.state.__id__||1}let Fg;function qg(){var e;return Fg||(Fg=__uniConfig.tabBar&&xn((e=__uniConfig.tabBar,Yl()&&e.list&&e.list.forEach((e=>{Kl(e,["text"])})),e))),Fg}const Vg=window.CSS&&window.CSS.supports;function Wg(e){return Vg&&(Vg(e)||Vg.apply(window.CSS,e.split(":")))}const zg=Wg("top:env(a)"),Hg=Wg("top:constant(a)"),Ug=Wg("backdrop-filter:blur(10px)"),Xg=(()=>zg?"env":Hg?"constant":"")();function Yg(e){return Xg?`calc(${e}px + ${Xg}(safe-area-inset-bottom))`:`${e}px`}const Gg="$$",Jg=new Map;function Qg(){return Jg}function Kg(){const e=[],t=Jg.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function Zg(e,t=!0){const n=Jg.get(e);n.$.__isUnload=!0,iu(n,me),Jg.delete(e),t&&function(e){const t=rm.get(e);t&&(rm.delete(e),im.pruneCacheEntry(t))}(e)}let em=Dg();function tm(e){const t=jg();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:s,route:a}=o,l=ht(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:qe(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#000000"===l?"dark":"light"}}("navigateTo",n,{},t)}function nm(e){const t=tm(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),Jg.set(om(t.path,t.id),e)}function om(e,t){return e+Gg+t}const rm=new Map,im={get:e=>rm.get(e),set(e,t){!function(e){const t=parseInt(e.split(Gg)[1]);if(!t)return;im.forEach(((e,n)=>{const o=parseInt(n.split(Gg)[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;im.delete(n),im.pruneCacheEntry(e),so((()=>{Jg.forEach(((e,t)=>{e.$.isUnmounted&&Jg.delete(t)}))}))}}))}(e),rm.set(e,t)},delete(e){rm.get(e)&&rm.delete(e)},forEach(e){rm.forEach(e)}};function sm(e,t){!function(e){const t=lm(e),{body:n}=document;cm&&n.removeAttribute(cm),t&&n.setAttribute(t,""),cm=t}(e),function(e){let t=0;if(e.isTabBar){const e=qg();e.shown&&(t=parseInt(e.height))}var n;zc({"--window-top":(n=0,Xg?`calc(${n}px + ${Xg}(safe-area-inset-top))`:`${n}px`),"--window-bottom":Yg(t)})}(t),function(e){const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}(t),function(e,t){document.removeEventListener("touchmove",su),um&&document.removeEventListener("scroll",um);if(t.disableScroll)return document.addEventListener("touchmove",su);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!n&&!o&&!r)return;const i={},s=e.proxy.$page.id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&$y.publishHandler(we,{scrollTop:o},e),n&&$y.emit(e+"."+we,{scrollTop:o})}}(s,n,r));o&&(i.onReachBottomDistance=t.onReachBottomDistance||ne,i.onReachBottom=()=>$y.publishHandler(Se,{},s));um=cu(i),requestAnimationFrame((()=>document.addEventListener("scroll",um)))}(e,t)}function am(e){const t=lm(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function lm(e){return e.type.__scopeId}let cm,um;function fm(e){const t=Ul({history:dm(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:hm});e.router=t,e.use(t)}const hm=(e,t,n)=>{if(n)return n};function dm(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=il(e);return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=Kg(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=t[r].$page;Zg(om(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const pm={install(e){Lg(e),xu(e),Au(e),e.config.warnHandler||(e.config.warnHandler=gm),fm(e)}};function gm(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const mm={class:"uni-async-loading"},vm=Ri("i",{class:"uni-loading"},null,-1),ym=Du({name:"AsyncLoading",render:()=>(Si(),Oi("div",mm,[vm]))});function bm(){window.location.reload()}const _m=Du({name:"AsyncError",setup(){tc();const{t:e}=Zl();return()=>Ri("div",{class:"uni-async-error",onClick:bm},[e("uni.async.error")],8,["onClick"])}});let wm;function xm(){return wm}function Sm(e){wm=e,Object.defineProperty(wm.$.ctx,"$children",{get:()=>Kg().map((e=>e.$vm))});const t=wm.$.appContext.app;t.component(ym.name)||t.component(ym.name,ym),t.component(_m.name)||t.component(_m.name,_m),function(e){e.$vm=e,e.$mpType="app";const t=Bn(Zl().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(wm),function(e,t){const n=e.$options||{};n.globalData=x(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(wm),Pu(),Ec()}function Tm(e,{clone:t,init:n,setup:o,before:r}){t&&(e=x({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=Xi();n(r.proxy);const s=o(r);if(i)return i(s||e,t)},e}function Cm(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Tm(e.default,t):Tm(e,t)}function km(e){return Cm(e,{clone:!0,init:nm,setup(e){e.$pageInstance=e;const t=Ng(),n=Ze(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n;const o=jg();var r,i,s;return dr((()=>{sm(e,o)})),pr((()=>{am(e);const{onReady:n}=e;n&&Y(n),Im(t)})),rr((()=>{if(!e.__isVisible){sm(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&Y(n),so((()=>{Im(t)}))}}),"ba",r),function(e,t){rr(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&Y(t)}})),i=o.id,$y.subscribe(hc(i,lc),s?s(pc):pc),vr((()=>{!function(e){$y.unsubscribe(hc(e,lc)),Object.keys(fc).forEach((t=>{0===t.indexOf(e+".")&&delete fc[t]}))}(o.id)})),n}})}function Em(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=Um(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Ay.emit(be,{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function Om(e){B(e.data)&&e.data.type===ae&&Ay.emit(je,e.data.data,e.data.pageId)}function Mm(){const{emit:e}=Ay;"visible"===document.visibilityState?e(Le,x({},qd)):e(Re)}function Im(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&iu("onTabItemTap",{index:n,text:t,pagePath:o})}const Pm=({name:e,arg:t})=>{"postMessage"===e||uni[e](t)},$m=He((()=>Ay.on(je,Pm))),Am=Nu({inheritAttrs:!1,name:"WebView",props:{src:{type:String,default:""},fullscreen:{type:Boolean,default:!0}},setup(e){$m();const t=Bn(null),n=Bn(null),{$attrs:o,$excludeAttrs:r,$listeners:i}=Op({excludeListeners:!0});let s;return(()=>{const r=document.createElement("iframe");Io((()=>{for(const e in o.value)if(C(o.value,e)){const t=o.value[e];r[e]=t}})),Io((()=>{r.src=Zu(e.src)})),n.value=r,s=function(e,t,n){const o=()=>{var o,r;if(n){const{top:n,left:o,width:r,height:i}=e.value.getBoundingClientRect();ze(t.value,{position:"absolute",display:"block",border:"0",top:n+"px",left:o+"px",width:r+"px",height:i+"px"})}else ze(t.value,{width:(null==(o=e.value)?void 0:o.style.width)||"300px",height:(null==(r=e.value)?void 0:r.style.height)||"150px"})};return o}(t,n,e.fullscreen),e.fullscreen&&document.body.appendChild(r)})(),pr((()=>{var o;s(),!e.fullscreen&&(null==(o=t.value)||o.appendChild(n.value))})),nr((()=>{e.fullscreen&&(n.value.style.display="block")})),or((()=>{e.fullscreen&&(n.value.style.display="none")})),vr((()=>{e.fullscreen&&document.body.removeChild(n.value)})),()=>Ri(vi,null,[Ri("uni-web-view",Vi({class:e.fullscreen?"uni-webview--fullscreen":""},i.value,r.value,{ref:t}),[Ri(Vd,{onResize:s},null,8,["onResize"])],16)])}});const Lm="__DC_STAT_UUID",Rm=window.localStorage||window.sessionStorage||{};let jm;function Bm(){if(jm=jm||Rm[Lm],!jm){jm=Date.now()+""+Math.floor(1e7*Math.random());try{Rm[Lm]=jm}catch(e){}}return jm}function Nm(){if(!0!==__uniConfig.darkmode)return I(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function Dm(){let e,t="0",n="",o="phone";const r=navigator.language;if(nf){e="iOS";const o=ef.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=ef.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(tf){e="Android";const o=ef.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=ef.match(/\((.+?)\)/),i=r?r[1].split(";"):ef.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(af)n="iPad",e="iOS",o="pad",t=M(window.BigInt)?"14.0":"13.0";else if(of||rf||sf){n="PC",e="PC",o="pc",t="0";let r=ef.match(/\((.+?)\)/)[1];if(of){switch(e="Windows",of[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(rf){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(sf){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(ef)&&(a=t[n],l=ef.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:ef,osname:e,osversion:t,theme:Nm()}}const Fm=Yf(0,(()=>{const e=window.devicePixelRatio,t=lf(),n=cf(t),o=uf(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=ff(o);let s=window.innerHeight;const a=Bc.top,l={left:Bc.left,right:i-Bc.right,top:Bc.top,bottom:s-Bc.bottom,width:i-Bc.left-Bc.right,height:s-Bc.top-Bc.bottom},{top:c,bottom:u}=Vc();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:Bc.top,right:Bc.right,bottom:Bc.bottom,left:Bc.left},screenTop:r-s}}));let qm,Vm=!0;function Wm(){Vm&&(qm=Dm())}const zm=Yf(0,(()=>{Wm();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a}=qm;return{brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:Bm(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i}})),Hm=Yf(0,(()=>{Wm();const{theme:e,language:t,browserName:n,browserVersion:o}=qm;return{appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Uh?Uh():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""}})),Um=Yf(0,(()=>{Vm=!0,Wm(),Vm=!1;const e=Fm(),t=zm(),n=Hm();Vm=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=qm,l=x(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return B(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),Xm=!!window.navigator.vibrate,Ym=Gf("vibrateShort",((e,{resolve:t,reject:n})=>{Xm&&window.navigator.vibrate(15)?t():n("vibrateLong:fail")}));const Gm=Gf("setClipboardData",((e,t)=>{return n=void 0,o=[e,t],r=function*({data:e},{resolve:t,reject:n}){try{yield navigator.clipboard.writeText(e),t()}catch(o){!function(e,t,n){const o=document.getElementById("#clipboard");o&&o.remove();const r=document.createElement("textarea");r.id="#clipboard",r.style.position="fixed",r.style.top="-9999px",r.style.zIndex="-9999",document.body.appendChild(r),r.value=e,r.select(),r.setSelectionRange(0,r.value.length);const i=document.execCommand("Copy",!1);r.blur(),i?t():n()}(e,t,n)}},new Promise(((e,t)=>{var i=e=>{try{a(r.next(e))}catch(YC){t(YC)}},s=e=>{try{a(r.throw(e))}catch(YC){t(YC)}},a=t=>t.done?e(t.value):Promise.resolve(t.value).then(i,s);a((r=r.apply(n,o)).next())}));var n,o,r}),0,Jh);const Jm=Yf(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)})),Qm=Gf("setStorage",(({key:e,data:t},{resolve:n,reject:o})=>{try{Jm(e,t),n()}catch(r){o(r.message)}}));function Km(e){const t=localStorage&&localStorage.getItem(e);if(!I(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=I(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const Zm=Yf(0,(e=>{try{return Km(e)}catch(t){return""}})),ev=Yf(0,(e=>{localStorage&&localStorage.removeItem(e)})),tv=Gf("hideKeyboard",((e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())}));const nv=Gf("getImageInfo",(({src:e},{resolve:t,reject:n})=>{const o=new Image;o.onload=function(){t({width:o.naturalWidth,height:o.naturalHeight,path:0===e.indexOf("/")?window.location.protocol+"//"+window.location.host+e:e})},o.onerror=function(){n()},o.src=e}),0,Kh),ov={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function rv({count:e,sourceType:t,type:n,extension:o}){const r=document.createElement("input");return r.type="file",ze(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${ov[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}pp();let iv=null;const sv=Gf("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{ic();const{t:i}=Zl();iv&&(document.body.removeChild(iv),iv=null),iv=rv({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(iv),iv.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||Dd(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),iv.click(),hp||console.warn(i("uni.chooseFile.notUserActivation"))}),0,Qh),av={esc:["Esc","Escape"],enter:["Enter"]},lv=Object.keys(av);const cv=Ri("div",{class:"uni-mask"},null,-1);function uv(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Ks(Xo({setup:()=>()=>(Si(),Oi(e,t,null,16))}))}function fv(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function hv(e,{onEsc:t,onEnter:n}){const o=Bn(e.visible),{key:r,disable:i}=function(){const e=Bn(""),t=Bn(!1),n=n=>{if(t.value)return;const o=lv.find((e=>-1!==av[e].indexOf(n.key)));o&&(e.value=o),so((()=>e.value=""))};return pr((()=>{document.addEventListener("keyup",n)})),vr((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}();return $o((()=>e.visible),(e=>o.value=e)),$o((()=>o.value),(e=>i.value=!e)),Io((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let dv=0,pv="";function gv(e){let t=dv;dv+=e?1:-1,dv=Math.max(0,dv),dv>0?0===t&&(pv=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=pv,pv="")}const mv=Du({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=xn({direction:"none"});let n=1,o=0,r=0,i=0,s=0;function a({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();o=t.width,r=t.height}function c(e){const t=e.target.getBoundingClientRect();i=t.width,s=t.height,f(e)}function u(e){const a=n*o>i,l=n*r>s;t.direction=a&&l?"all":a?"horizontal":l?"vertical":"none",f(e)}function f(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return Ri(Ip,{style:n,onTouchstart:Wu(c),onTouchmove:Wu(f),onTouchend:Wu(u)},{default:()=>[Ri(Wp,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:a},{default:()=>[Ri("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function vv(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const yv=Du({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){pr((()=>gv(!0))),yr((()=>gv(!1)));const n=Bn(null),o=Bn(vv(e));let r;function i(){r||so((()=>{t("close")}))}function s(e){o.value=e.detail.current}$o((()=>e.current),(()=>o.value=vv(e))),pr((()=>{const e=n.value;let t=0,o=0;e.addEventListener("mousedown",(e=>{r=!1,t=e.clientX,o=e.clientY})),e.addEventListener("mouseup",(e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-o)>20)&&(r=!0)}))}));const a={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return Ri("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:i},[Ri(dg,{navigation:"auto",current:o.value,onChange:s,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(r=t=e.urls.map((e=>Ri(gg,null,{default:()=>[Ri(mv,{src:e},null,8,["src"])]}))),"function"==typeof r||"[object Object]"===Object.prototype.toString.call(r)&&!Mi(r)?t:{default:()=>[t],_:1}),8,["current","onChange"]),Ri("div",{style:a},[Jc("M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z","#ffffff",26)],4)],8,["onClick"]);var r}}});let bv,_v=null;const wv=()=>{_v=null,so((()=>{null==bv||bv.unmount(),bv=null}))},xv=Gf("previewImage",((e,{resolve:t})=>{_v?x(_v,e):(_v=xn(e),so((()=>{bv=uv(yv,_v,wv),bv.mount(fv("u-a-p"))}))),t()}),0,Zh),Sv=Xf("request",(({url:e,data:t,header:n,method:o,dataType:r,responseType:i,withCredentials:s,timeout:a=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const f=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(I(t)||t instanceof ArrayBuffer)u=t;else if("json"===f)try{u=JSON.stringify(t)}catch(g){u=t.toString()}else if("urlencoded"===f){const e=[];for(const n in t)C(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const h=new XMLHttpRequest,d=new Tv(h);h.open(o,e);for(const m in n)C(n,m)&&h.setRequestHeader(m,n[m]);const p=setTimeout((function(){h.onload=h.onabort=h.onerror=null,d.abort(),c("timeout")}),a);return h.responseType=i,h.onload=function(){clearTimeout(p);const e=h.status;let t="text"===i?h.responseText:h.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(g){}l({data:t,statusCode:e,header:Cv(h.getAllResponseHeaders()),cookies:[]})},h.onabort=function(){clearTimeout(p),c("abort")},h.onerror=function(){clearTimeout(p),c()},h.withCredentials=s,h.send(u),d}),0,od);class Tv{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function Cv(e){const t={};return e.split(te).forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class kv{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){M(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const Ev=Xf("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i,formData:s,timeout:a=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new kv;return k(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(Nd(e)):Bd(t)))).then((function(t){var n,o=new XMLHttpRequest,f=new FormData;Object.keys(s).forEach((e=>{f.append(e,s[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];f.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c()},o.onabort=function(){clearTimeout(n),c("abort")},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort"):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout")}),a),o.send(f),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,rd),Ov=Gf("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===iu(_e,{from:e.from||"navigateBack"})&&(o=!1),o?(xm().$router.go(-e.delta),t()):n(_e)}),0,md);function Mv({type:e,url:t,tabBarText:n,events:o},r){const i=xm().$router,{path:s,query:a}=function(e){const[t,n]=e.split("?",2);return{path:t,query:tt(n||"")}}(t);return new Promise(((t,l)=>{const c=function(e,t){return{__id__:t||++em,__type__:e}}(e,r);i["navigateTo"===e?"push":"replace"]({path:s,query:a,state:c,force:!0}).then((r=>{if(fl(r))return l(r.message);if("switchTab"===e&&(i.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=i.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new ot(c.__id__,o),t({eventChannel:e.eventChannel})}return t()}))}))}const Iv=Gf(sd,(({url:e,events:t},{resolve:n,reject:o})=>Mv({type:sd,url:e,events:t}).then(n).catch(o)),0,hd);const Pv=Gf(ad,(({url:e},{resolve:t,reject:n})=>(function(){const e=Zc();if(!e)return;const t=e.$page;Zg(om(t.path,t.id))}(),Mv({type:ad,url:e}).then(t).catch(n))),0,dd);const $v=Gf(ld,(({url:e},{resolve:t,reject:n})=>(function(){const e=Qg().keys();for(const t of e)Zg(t)}(),Mv({type:ld,url:e}).then(t).catch(n))),0,pd);function Av(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}const Lv=Gf(cd,(({url:e,tabBarText:t},{resolve:n,reject:o})=>(function(){const e=nu();if(!e)return;const t=Qg(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:Zg(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,iu(e,ce))}(),Mv({type:cd,url:e,tabBarText:t},function(e){const t=Qg().values();for(const n of t){const t=n.$page;if(Av(e,t))return n.$.__isActive=!0,t.id}}(e)).then(n).catch(o))),0,gd);function Rv(e){__uniConfig.darkmode&&Ay.on(he,e)}function jv(e){Ay.off(he,e)}function Bv(e){let t={};return __uniConfig.darkmode&&(t=ht(e,__uniConfig.themeConfig,Nm())),__uniConfig.darkmode?t:e}const Nv={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},Dv=Xo({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=Bn(""),o=()=>s.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),s=hv(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),a=function(e){const t=Bn(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=Nv[e].cancelColor})(e,t)};return Io((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===Nm()&&n({theme:"dark"}),Rv(n))):jv(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:f,placeholderText:h}=e;return n.value=o,Ri(As,{name:"uni-fade"},{default:()=>[Sr(Ri("uni-modal",{onTouchmove:Nc},[cv,Ri("div",{class:"uni-modal"},[t&&Ri("div",{class:"uni-modal__hd"},[Ri("strong",{class:"uni-modal__title",textContent:t},null,8,["textContent"])]),f?Ri("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:h,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):Ri("div",{class:"uni-modal__bd",onTouchmovePassive:Dc,textContent:o},null,40,["onTouchmovePassive","textContent"]),Ri("div",{class:"uni-modal__ft"},[l&&Ri("div",{style:{color:a.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),Ri("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Ys,s.value]])]})}}});let Fv;const qv=He((()=>{Ay.on("onHidePopup",(()=>Fv.visible=!1))}));let Vv;function Wv(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&Fv.editable&&(o.content=t),Vv&&Vv(o)}const zv=Gf("showModal",((e,{resolve:t})=>{qv(),Vv=t,Fv?(x(Fv,e),Fv.visible=!0):(Fv=xn(e),so((()=>(uv(Dv,Fv,Wv).mount(fv("u-a-m")),so((()=>Fv.visible=!0))))))}),0,Ed),Hv={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==Od.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},Uv="uni-toast__icon",Xv={light:"#fff",dark:"rgba(255,255,255,0.9)"},Yv=e=>Xv[e],Gv=Xo({name:"Toast",props:Hv,setup(e){nc(),oc();const{Icon:t}=function(e){const t=Bn(Yv(Nm())),n=({theme:e})=>t.value=Yv(e);Io((()=>{e.visible?Rv(n):jv(n)}));const o=os((()=>{switch(e.icon){case"success":return Ri(Jc(Yc,t.value,38),{class:Uv});case"error":return Ri(Jc(Gc,t.value,38),{class:Uv});case"loading":return Ri("i",{class:[Uv,"uni-loading"]},null,2);default:return null}}));return{Icon:o}}(e),n=hv(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return Ri(As,{name:"uni-fade"},{default:()=>[Sr(Ri("uni-toast",{"data-duration":r},[o?Ri("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Nc},null,40,["onTouchmove"]):"",s||t.value?Ri("div",{class:"uni-toast"},[s?Ri("img",{src:s,class:Uv},null,10,["src"]):t.value,Ri("p",{class:"uni-toast__content"},[i])]):Ri("div",{class:"uni-sample-toast"},[Ri("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Ys,n.value]])]})}}});let Jv,Qv,Kv="";const Zv=gt();function ey(e){Jv?x(Jv,e):(Jv=xn(x(e,{visible:!1})),so((()=>{Zv.run((()=>{$o([()=>Jv.visible,()=>Jv.duration],(([e,t])=>{if(e){if(Qv&&clearTimeout(Qv),"onShowLoading"===Kv)return;Qv=setTimeout((()=>{iy("onHideToast")}),t)}else Qv&&clearTimeout(Qv)}))})),Ay.on("onHidePopup",(()=>iy("onHidePopup"))),uv(Gv,Jv,(()=>{})).mount(fv("u-a-t"))}))),setTimeout((()=>{Jv.visible=!0}),10)}const ty=Gf("showToast",((e,{resolve:t,reject:n})=>{ey(e),Kv="onShowToast",t()}),0,Md),ny={icon:"loading",duration:1e8,image:""},oy=Gf("showLoading",((e,{resolve:t,reject:n})=>{x(e,ny),ey(e),Kv="onShowLoading",t()}),0,kd),ry=Gf("hideLoading",((e,{resolve:t,reject:n})=>{iy("onHideLoading"),t()}));function iy(e){const{t:t}=Zl();if(!Kv)return;let n="";if("onHideToast"===e&&"onShowToast"!==Kv?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==Kv&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);Kv="",setTimeout((()=>{Jv.visible=!1}),10)}const sy=Gf("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:s,featureSettings:a}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),s&&i.push(`font-variant:${s}`),a&&i.push(`font-feature-settings:${a}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t,n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function ay(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Ay.emit(Me,{titleText:t})}Io(t),nr(t)}function ly(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case xd:const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:s}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=s;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case Td:const{title:a}=n;i.titleText=a}o()}const cy=Gf(xd,((e,{resolve:t,reject:n})=>{ly(eu(),xd,e,t,n)}),0,Sd),uy=Gf(Td,((e,{resolve:t,reject:n})=>{ly(eu(),Td,e,t,n)})),fy=Gf("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(I(e)){const t=document.querySelector(e);if(t){const{height:o,top:r}=t.getBoundingClientRect();e=r+window.pageYOffset,n&&(e-=o)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:r,scrollHeight:i}=o;if(e=Math.min(e,i-r),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const s=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),s(t-10)}))};s(t)}(t||e||0,n,!0),o()}),0,Cd),hy=Gf(Id,((e,{resolve:t})=>{Ay.invokeViewMethod(Id,{},tu()),t()})),dy=["text","iconPath","iconfont","selectedIconPath","visible"],py=["color","selectedColor","backgroundColor","borderStyle","midButton"],gy=["badge","redDot"];function my(e,t,n){t.forEach((function(t){C(n,t)&&(e[t]=n[t])}))}function vy(e,t,n){const o=qg();switch(e){case"showTabBar":o.shown=!0;break;case Pd:o.shown=!1;break;case"setTabBarItem":const{index:e}=t,n=o.list[e],r=n.pagePath;my(n,dy,t);const{pagePath:i}=t;if(i){const t=qe(i);t!==r&&function(e,t,n){const o=fu(qe(t));if(o){const{meta:e}=o;delete e.tabBarIndex,e.isQuit=e.isTabBar=!1}const r=fu(qe(n));if(r){const{meta:t}=r;t.tabBarIndex=e,t.isQuit=t.isTabBar=!0;const o=__uniConfig.tabBar;o&&o.list&&o.list[e]&&(o.list[e].pagePath=Ve(n))}}(e,r,t)}break;case"setTabBarStyle":my(o,py,t);break;case"showTabBarRedDot":my(o.list[t.index],gy,{badge:"",redDot:!0});break;case"setTabBarBadge":my(o.list[t.index],gy,{badge:t.text,redDot:!0});break;case"hideTabBarRedDot":case"removeTabBarBadge":my(o.list[t.index],gy,{badge:"",redDot:!1})}n()}const yy=Gf(Pd,((e,{resolve:t})=>{vy(Pd,e||{},t)})),by=Du({name:"TabBar",setup(){const e=Bn([]),t=qg(),n=xn(Bv(t));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}Bn(x({type:"midButton"},e.midButton)),Io(n)}(n,e),function(e){$o((()=>e.shown),(t=>{zc({"--window-bottom":Yg(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return Io((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=qe(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?Lv({from:"tabBar",url:i,tabBarText:r}):iu("onTabItemTap",{index:n,text:r,pagePath:o})}}(Xl(),n,e),{style:r,borderStyle:i,placeholderStyle:s}=function(e){const t=os((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||Ug&&n&&"none"!==n&&(t=Sy[n]),{backgroundColor:t||_y,backdropFilter:"none"!==n?"blur(10px)":n}})),n=os((()=>{const{borderStyle:t}=e;return{backgroundColor:Ty[t]||t}})),o=os((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return Rv((()=>{const e=Bv(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))})),pr((()=>{n.iconfontSrc&&sy({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,s)=>{const a=o===s;return function(e,t,n,o,r,i,s,a){return Ri("div",{key:s,class:"uni-tabbar__item",onClick:a(r,s)},[Cy(e,t||"",n,o,r,i)],8,["onClick"])}(a?r:i,a&&n.selectedIconPath||n.iconPath||"",n.iconfont?a&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?a&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,s,t)}))}(n,o,e);return Ri("uni-tabbar",{class:"uni-tabbar-"+n.position},[Ri("div",{class:"uni-tabbar",style:r.value},[Ri("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),Ri("div",{class:"uni-placeholder",style:s.value},null,4)],2)}}});const _y="#f7f7fa",wy="rgb(0, 0, 0, 0.8)",xy="rgb(250, 250, 250, 0.8)",Sy={dark:wy,light:xy,extralight:xy},Ty={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function Cy(e,t,n,o,r,i){const{height:s}=i;return Ri("div",{class:"uni-tabbar__bd",style:{height:s}},[n?Ey(n,o||wy,r,i):t&&ky(t,r,i),r.text&&Oy(e,r,i),r.redDot&&My(r.badge)],4)}function ky(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return Ri("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&Ri("img",{src:Zu(e)},null,8,["src"])],6)}function Ey(e,t,n,o){var r;const{type:i,text:s}=n,{iconWidth:a}=o,l="uni-tabbar__icon"+(s?" uni-tabbar__icon__diff":""),c={width:a,height:a},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||a,color:t};return Ri("div",{class:l,style:c},["midButton"!==i&&Ri("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function Oy(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:s}=n;return Ri("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?s:"inherit"}},[r],4)}function My(e){return Ri("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const Iy="0px",Py=Du({name:"Layout",setup(e,{emit:t}){const n=Bn(null);Wc({"--status-bar-height":Iy,"--top-window-height":Iy,"--window-left":Iy,"--window-right":Iy,"--window-margin":Iy,"--tab-bar-height":Iy});const o=function(){const e=Xl();return{routeKey:os((()=>om("/"+e.meta.route,Dg()))),isTabBar:os((()=>e.meta.isTabBar)),routeCache:im}}(),{layoutState:r,windowState:i}=function(){Ng();{const e=xn({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return $o((()=>e.marginWidth),(e=>Wc({"--window-margin":e+"px"}))),$o((()=>e.leftWindowWidth+e.marginWidth),(e=>{Wc({"--window-left":e+"px"})})),$o((()=>e.rightWindowWidth+e.marginWidth),(e=>{Wc({"--window-right":e+"px"})})),{layoutState:e,windowState:os((()=>({})))}}}();!function(e,t){const n=Ng();function o(){const o=document.body.clientWidth,r=Kg();let i={};if(r.length>0){i=r[r.length-1].$page.meta}else{const e=fu(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((C(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,so((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,so((()=>{const e=t.value;e&&e.removeAttribute("style")})))}$o([()=>n.path],o),pr((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const s=function(){const e=Ng(),t=qg(),n=os((()=>e.meta.isTabBar&&t.shown));return Wc({"--tab-bar-height":t.height}),n}(),a=function(e){const t=Bn(!1);return os((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(s);return()=>{const e=function(e){const t=function({routeKey:e,isTabBar:t,routeCache:n}){return Ri(Hl,null,{default:xo((({Component:o})=>[(Si(),Oi(er,{matchBy:"key",cache:n},[(Si(),Oi(Or(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e);return t}(o),t=function(e){return Sr(Ri(by,null,null,512),[[Ys,e.value]])}(s);return Ri("uni-app",{ref:n,class:a.value},[e,t],2)}}});const $y=x(gc,{publishHandler(e,t,n){Ay.subscribeHandler(e,t,n)}}),Ay=x(Cu,{publishHandler(e,t,n){$y.subscribeHandler(e,t,n)}}),Ly=Du({name:"PageBody",setup:(e,t)=>()=>Ri(vi,null,[!1,Ri("uni-page-wrapper",null,[Ri("uni-page-body",null,[$r(t.slots,"default")])],16)])}),Ry=Du({name:"Page",setup(e,t){const n=Bg(Dg());return n.navigationBar,ay(n),()=>Ri("uni-page",{"data-page":n.route},[jy(t)])}});function jy(e){return Si(),Oi(Ly,{key:0},{default:xo((()=>[$r(e.slots,"page")])),_:3})}const By={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=rh;const Ny=Object.assign({}),Dy=Object.assign;window.__uniConfig=Dy({globalStyle:{backgroundColor:"#F8F8F8",navigationBar:{backgroundColor:"#FFFFFF",titleText:"商城",style:"custom",type:"default",titleColor:"#000000"},isNVue:!1},tabBar:{position:"bottom",color:"#999",selectedColor:"#007aff",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"20px",spacing:"3px",height:"50px",custom:!0,list:[{iconPath:"/static/images/tabbar/home.png",selectedIconPath:"/static/images/tabbar/home_s.png",pagePath:"pages/index/index",text:"首页"},{iconPath:"/static/images/tabbar/news.png",selectedIconPath:"/static/images/tabbar/news_s.png",pagePath:"pages/news/news",text:"资讯"},{iconPath:"/static/images/tabbar/user.png",selectedIconPath:"/static/images/tabbar/user_s.png",pagePath:"pages/user/user",text:"我的"}],selectedIndex:0,shown:!0},easycom:{custom:{"router-navigate":"uniapp-router-next/components/router-navigate/router-navigate.vue","^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)":"z-paging/components/z-paging$1/z-paging$1.vue","^w-(.*)":"@/components/widgets/$1/$1.vue"}},compilerVersion:"3.7.9"},{appId:"",appName:"",appVersion:"1.0.0",appVersionCode:"100",async:By,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(Ny).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return Dy(e[n]||(e[n]={}),Ny[t].default),e}),{}),router:{mode:"history",base:"/mobile/",assets:"assets",routerBase:"/mobile/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const Fy={delay:By.delay,timeout:By.timeout,suspensible:By.suspensible};By.loading&&(Fy.loadingComponent={name:"SystemAsyncLoading",render:()=>Ri(kr(By.loading))}),By.error&&(Fy.errorComponent={name:"SystemAsyncError",render:()=>Ri(kr(By.error))});const qy=()=>o((()=>import("./pages-index-index.9a292d2e.js")),["assets/pages-index-index.9a292d2e.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-search.1f3b26b2.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/u-search-7f865d2b.css","assets/u-image.e9ed38ca.js","assets/u-image-7673150b.css","assets/news-card.1749442f.js","assets/icon_visit.713e13e8.js","assets/news-card-9e59d7ca.css","assets/router-navigate.3c22c13e.js","assets/tabbar.vue_vue_type_script_setup_true_lang.b3a4c4a3.js","assets/u-badge.45c73cfd.js","assets/u-badge-971d8f79.css","assets/tabbar-b6b077e3.css","assets/index-9a7af3e7.css"]).then((e=>km(e.default||e))),Vy=Go(Dy({loader:qy},Fy)),Wy=()=>o((()=>import("./pages-news-news.3801f3e2.js")),["assets/pages-news-news.3801f3e2.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-search.1f3b26b2.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/u-search-7f865d2b.css","assets/u-badge.45c73cfd.js","assets/u-badge-971d8f79.css","assets/tabbar.vue_vue_type_script_setup_true_lang.b3a4c4a3.js","assets/tabbar-b6b077e3.css","assets/news-card.1749442f.js","assets/u-image.e9ed38ca.js","assets/u-image-7673150b.css","assets/icon_visit.713e13e8.js","assets/news-card-9e59d7ca.css","assets/z-paging.9764c1e2.js","assets/z-paging-fd0d435c.css","assets/news.a3153aee.js","assets/news-a4f8c181.css"]).then((e=>km(e.default||e))),zy=Go(Dy({loader:Wy},Fy)),Hy=()=>o((()=>import("./pages-user-user.180d459d.js")),["assets/pages-user-user.180d459d.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-avatar.878580e7.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/u-avatar-4ce082fd.css","assets/u-image.e9ed38ca.js","assets/u-image-7673150b.css","assets/tabbar.vue_vue_type_script_setup_true_lang.b3a4c4a3.js","assets/u-badge.45c73cfd.js","assets/u-badge-971d8f79.css","assets/tabbar-b6b077e3.css","assets/user-a80d1e15.css"]).then((e=>km(e.default||e))),Uy=Go(Dy({loader:Hy},Fy)),Xy=()=>o((()=>import("./pages-login-login.6f45dbf2.js")),["assets/pages-login-login.6f45dbf2.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-button.e98befd5.js","assets/u-button-c14b4be4.css","assets/u-input.adb6d3eb.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/u-verification-code.f6f16d21.js","assets/u-verification-code-e2ab8652.css","assets/u-modal.208f51b9.js","assets/u-loading.8620e4d6.js","assets/u-loading-ae83b4a2.css","assets/u-popup.6496bd54.js","assets/u-popup-3a489e4e.css","assets/u-modal-135cc8f7.css","assets/useLockFn.7b7abf35.js","assets/login-762e2249.css"]).then((e=>km(e.default||e))),Yy=Go(Dy({loader:Xy},Fy)),Gy=()=>o((()=>import("./pages-register-register.f73c58f3.js")),["assets/pages-register-register.f73c58f3.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-input.adb6d3eb.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/router-navigate.3c22c13e.js","assets/u-modal.208f51b9.js","assets/u-loading.8620e4d6.js","assets/u-loading-ae83b4a2.css","assets/u-popup.6496bd54.js","assets/u-popup-3a489e4e.css","assets/u-modal-135cc8f7.css","assets/u-button.e98befd5.js","assets/u-button-c14b4be4.css","assets/register-a29758e0.css"]).then((e=>km(e.default||e))),Jy=Go(Dy({loader:Gy},Fy)),Qy=()=>o((()=>import("./pages-forget_pwd-forget_pwd.18372bc8.js")),["assets/pages-forget_pwd-forget_pwd.18372bc8.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-input.adb6d3eb.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/u-form-item.3b95431b.js","assets/u-form-item-d6a97f32.css","assets/u-verification-code.f6f16d21.js","assets/u-verification-code-e2ab8652.css","assets/u-form.cc690975.js","assets/u-button.e98befd5.js","assets/u-button-c14b4be4.css","assets/forget_pwd-dc8beb00.css"]).then((e=>km(e.default||e))),Ky=Go(Dy({loader:Qy},Fy)),Zy=()=>o((()=>import("./pages-customer_service-customer_service.38e35c38.js")),["assets/pages-customer_service-customer_service.38e35c38.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-parse.f0500461.js","assets/u-parse-1e32a619.css"]).then((e=>km(e.default||e))),eb=Go(Dy({loader:Zy},Fy)),tb=()=>o((()=>import("./pages-news_detail-news_detail.f48180c8.js")),["assets/pages-news_detail-news_detail.f48180c8.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-parse.f0500461.js","assets/u-parse-1e32a619.css","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/icon_visit.713e13e8.js","assets/news.a3153aee.js","assets/news_detail-fe8aa1d3.css"]).then((e=>km(e.default||e))),nb=Go(Dy({loader:tb},Fy)),ob=()=>o((()=>import("./pages-user_set-user_set.4bfd0dcc.js")),["assets/pages-user_set-user_set.4bfd0dcc.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-avatar.878580e7.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/u-avatar-4ce082fd.css","assets/u-button.e98befd5.js","assets/u-button-c14b4be4.css","assets/u-popup.6496bd54.js","assets/u-popup-3a489e4e.css","assets/useLockFn.7b7abf35.js","assets/user_set-8a47fa05.css"]).then((e=>km(e.default||e))),rb=Go(Dy({loader:ob},Fy)),ib=()=>o((()=>import("./pages-collection-collection.d2da991d.js")),["assets/pages-collection-collection.d2da991d.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/news-card.1749442f.js","assets/u-image.e9ed38ca.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/u-image-7673150b.css","assets/icon_visit.713e13e8.js","assets/news-card-9e59d7ca.css","assets/z-paging.9764c1e2.js","assets/z-paging-fd0d435c.css","assets/news.a3153aee.js","assets/collection-00d40f02.css"]).then((e=>km(e.default||e))),sb=Go(Dy({loader:ib},Fy)),ab=()=>o((()=>import("./pages-as_us-as_us.f4602c9e.js")),["assets/pages-as_us-as_us.f4602c9e.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/as_us-842bbad8.css"]).then((e=>km(e.default||e))),lb=Go(Dy({loader:ab},Fy)),cb=()=>o((()=>import("./pages-agreement-agreement.8d74d85c.js")),["assets/pages-agreement-agreement.8d74d85c.js","assets/u-parse.f0500461.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-parse-1e32a619.css"]).then((e=>km(e.default||e))),ub=Go(Dy({loader:cb},Fy)),fb=()=>o((()=>import("./pages-change_password-change_password.c04f6a70.js")),["assets/pages-change_password-change_password.c04f6a70.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-input.adb6d3eb.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/u-form-item.3b95431b.js","assets/u-form-item-d6a97f32.css","assets/u-form.cc690975.js","assets/u-button.e98befd5.js","assets/u-button-c14b4be4.css","assets/change_password-54df9346.css"]).then((e=>km(e.default||e))),hb=Go(Dy({loader:fb},Fy)),db=()=>o((()=>import("./pages-user_data-user_data.91d39d43.js")),["assets/pages-user_data-user_data.91d39d43.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/_commonjsHelpers.02d3be64.js","assets/u-button.e98befd5.js","assets/u-button-c14b4be4.css","assets/u-form-item.3b95431b.js","assets/emitter.1571a5d9.js","assets/u-form-item-d6a97f32.css","assets/u-popup.6496bd54.js","assets/u-popup-3a489e4e.css","assets/u-input.adb6d3eb.js","assets/u-input-10187c76.css","assets/u-verification-code.f6f16d21.js","assets/u-verification-code-e2ab8652.css","assets/user_data-f676d70f.css"]).then((e=>km(e.default||e))),pb=Go(Dy({loader:db},Fy)),gb=()=>o((()=>import("./pages-search-search.04338974.js")),["assets/pages-search-search.04338974.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-search.1f3b26b2.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/u-search-7f865d2b.css","assets/news-card.1749442f.js","assets/u-image.e9ed38ca.js","assets/u-image-7673150b.css","assets/icon_visit.713e13e8.js","assets/news-card-9e59d7ca.css","assets/z-paging.9764c1e2.js","assets/z-paging-fd0d435c.css","assets/news.a3153aee.js","assets/search-61079152.css"]).then((e=>km(e.default||e))),mb=Go(Dy({loader:gb},Fy)),vb=()=>o((()=>import("./pages-webview-webview.e44f4877.js")),[]).then((e=>km(e.default||e))),yb=Go(Dy({loader:vb},Fy)),bb=()=>o((()=>import("./pages-bind_mobile-bind_mobile.cd228af7.js")),["assets/pages-bind_mobile-bind_mobile.cd228af7.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-input.adb6d3eb.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/u-verification-code.f6f16d21.js","assets/u-verification-code-e2ab8652.css","assets/u-button.e98befd5.js","assets/u-button-c14b4be4.css","assets/bind_mobile-e5f0d6db.css"]).then((e=>km(e.default||e))),_b=Go(Dy({loader:bb},Fy)),wb=()=>o((()=>import("./pages-empty-empty.ee1ae84a.js")),["assets/pages-empty-empty.ee1ae84a.js","assets/_plugin-vue_export-helper.1b428a4d.js"]).then((e=>km(e.default||e))),xb=Go(Dy({loader:wb},Fy)),Sb=()=>o((()=>import("./pages-payment_result-payment_result.f333a4fb.js")),["assets/pages-payment_result-payment_result.f333a4fb.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-empty.36fe4845.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/u-empty-d9a13c75.css","assets/u-image.e9ed38ca.js","assets/u-image-7673150b.css","assets/u-button.e98befd5.js","assets/u-button-c14b4be4.css","assets/pay.7dd9e537.js","assets/u-loading.8620e4d6.js","assets/u-loading-ae83b4a2.css","assets/pay-c1be7a68.css","assets/payment_result-b802ebea.css"]).then((e=>km(e.default||e))),Tb=Go(Dy({loader:Sb},Fy)),Cb=()=>o((()=>import("./uni_modules-vk-uview-ui-components-u-avatar-cropper-u-avatar-cropper.f657239d.js")),["assets/uni_modules-vk-uview-ui-components-u-avatar-cropper-u-avatar-cropper.f657239d.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-avatar-cropper-a78b55d6.css"]).then((e=>km(e.default||e))),kb=Go(Dy({loader:Cb},Fy)),Eb=()=>o((()=>import("./packages-pages-404-404.ea390375.js")),["assets/packages-pages-404-404.ea390375.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-empty.36fe4845.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/u-empty-d9a13c75.css","assets/router-navigate.3c22c13e.js"]).then((e=>km(e.default||e))),Ob=Go(Dy({loader:Eb},Fy)),Mb=()=>o((()=>import("./packages-pages-user_wallet-user_wallet.f53bcbc4.js")),["assets/packages-pages-user_wallet-user_wallet.f53bcbc4.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-badge.45c73cfd.js","assets/u-badge-971d8f79.css","assets/z-paging.9764c1e2.js","assets/z-paging-fd0d435c.css","assets/recharge.89103b5a.js","assets/user_wallet-477c9a51.css"]).then((e=>km(e.default||e))),Ib=Go(Dy({loader:Mb},Fy)),Pb=()=>o((()=>import("./packages-pages-recharge-recharge.7251aff0.js")),["assets/packages-pages-recharge-recharge.7251aff0.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-button.e98befd5.js","assets/u-button-c14b4be4.css","assets/u-empty.36fe4845.js","assets/u-icon.f1b72599.js","assets/u-icon-c1e43160.css","assets/u-empty-d9a13c75.css","assets/emitter.1571a5d9.js","assets/pay.7dd9e537.js","assets/u-loading.8620e4d6.js","assets/u-loading-ae83b4a2.css","assets/pay-c1be7a68.css","assets/u-popup.6496bd54.js","assets/u-popup-3a489e4e.css","assets/useLockFn.7b7abf35.js","assets/recharge.89103b5a.js","assets/recharge-ef1eec83.css"]).then((e=>km(e.default||e))),$b=Go(Dy({loader:Pb},Fy)),Ab=()=>o((()=>import("./packages-pages-recharge_record-recharge_record.725152cd.js")),["assets/packages-pages-recharge_record-recharge_record.725152cd.js","assets/page-meta.438f2c32.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/z-paging.9764c1e2.js","assets/z-paging-fd0d435c.css","assets/recharge.89103b5a.js"]).then((e=>km(e.default||e))),Lb=Go(Dy({loader:Ab},Fy));function Rb(e,t){return Si(),Oi(Ry,null,{page:xo((()=>[Ri(e,Dy({},t,{ref:"page"}),null,512)])),_:1})}function jb(e,t){return I(e)?t:e}window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(Vy,t)}},loader:qy,meta:{isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,navigationBar:{titleText:"首页",type:"default"},isNVue:!1}},{path:"/pages/news/news",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(zy,t)}},loader:Wy,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,disableScroll:!0,navigationBar:{titleText:"资讯",type:"default"},isNVue:!1}},{path:"/pages/user/user",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(Uy,t)}},loader:Hy,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:2,navigationBar:{titleText:"个人中心",type:"default"},isNVue:!1}},{path:"/pages/login/login",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(Yy,t)}},loader:Xy,meta:{navigationBar:{titleText:"登录",type:"default"},isNVue:!1}},{path:"/pages/register/register",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(Jy,t)}},loader:Gy,meta:{navigationBar:{titleText:"注册",type:"default"},isNVue:!1}},{path:"/pages/forget_pwd/forget_pwd",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(Ky,t)}},loader:Qy,meta:{navigationBar:{titleText:"忘记密码",type:"default"},isNVue:!1}},{path:"/pages/customer_service/customer_service",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(eb,t)}},loader:Zy,meta:{navigationBar:{titleText:"联系客服",type:"default"},isNVue:!1}},{path:"/pages/news_detail/news_detail",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(nb,t)}},loader:tb,meta:{navigationBar:{titleText:"详情",type:"default"},isNVue:!1}},{path:"/pages/user_set/user_set",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(rb,t)}},loader:ob,meta:{navigationBar:{titleText:"个人设置",type:"default"},isNVue:!1}},{path:"/pages/collection/collection",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(sb,t)}},loader:ib,meta:{navigationBar:{titleText:"我的收藏",type:"default"},isNVue:!1}},{path:"/pages/as_us/as_us",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(lb,t)}},loader:ab,meta:{navigationBar:{titleText:"关于我们",type:"default"},isNVue:!1}},{path:"/pages/agreement/agreement",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(ub,t)}},loader:cb,meta:{navigationBar:{titleText:"协议",type:"default"},isNVue:!1}},{path:"/pages/change_password/change_password",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(hb,t)}},loader:fb,meta:{navigationBar:{titleText:"修改密码",type:"default"},isNVue:!1}},{path:"/pages/user_data/user_data",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(pb,t)}},loader:db,meta:{navigationBar:{titleText:"个人资料",type:"default"},isNVue:!1}},{path:"/pages/search/search",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(mb,t)}},loader:gb,meta:{navigationBar:{titleText:"搜索",type:"default"},isNVue:!1}},{path:"/pages/webview/webview",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(yb,t)}},loader:vb,meta:{navigationBar:{},isNVue:!1}},{path:"/pages/bind_mobile/bind_mobile",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(_b,t)}},loader:bb,meta:{navigationBar:{titleText:"绑定手机号",type:"default"},isNVue:!1}},{path:"/pages/empty/empty",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(xb,t)}},loader:wb,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/payment_result/payment_result",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(Tb,t)}},loader:Sb,meta:{navigationBar:{titleText:"支付结果",type:"default"},isNVue:!1}},{path:"/uni_modules/vk-uview-ui/components/u-avatar-cropper/u-avatar-cropper",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(kb,t)}},loader:Cb,meta:{navigationBar:{backgroundColor:"#000000",titleText:"头像裁剪",type:"default"},isNVue:!1}},{path:"/packages/pages/404/404",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(Ob,t)}},loader:Eb,meta:{navigationBar:{titleText:"404",type:"default"},isNVue:!1}},{path:"/packages/pages/user_wallet/user_wallet",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(Ib,t)}},loader:Mb,meta:{navigationBar:{titleText:"我的钱包",type:"default"},isNVue:!1}},{path:"/packages/pages/recharge/recharge",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb($b,t)}},loader:Pb,meta:{navigationBar:{titleText:"充值",type:"default"},isNVue:!1}},{path:"/packages/pages/recharge_record/recharge_record",component:{setup(){const e=xm(),t=e&&e.$route&&e.$route.query||{};return()=>Rb(Lb,t)}},loader:Ab,meta:{navigationBar:{titleText:"充值记录",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const Bb=e=>(t,n=Xi())=>{!Ki&&fr(e,t,n)},Nb=Bb(le),Db=Bb(ue),Fb=Bb(ge),qb=Bb(me),Vb=Bb(we);
/*!
  * pinia v2.0.20
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */
let Wb;const zb=e=>Wb=e,Hb=Symbol();function Ub(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var Xb,Yb;(Yb=Xb||(Xb={})).direct="direct",Yb.patchObject="patch object",Yb.patchFunction="patch function";const Gb=()=>{};function Jb(e,t,n,o=Gb){e.push(t);const r=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),o())};return!n&&Xi()&&yr(r),r}function Qb(e,...t){e.slice().forEach((e=>{e(...t)}))}function Kb(e,t){for(const n in t){if(!t.hasOwnProperty(n))continue;const o=t[n],r=e[n];Ub(r)&&Ub(o)&&e.hasOwnProperty(n)&&!jn(o)&&!kn(o)?e[n]=Kb(r,o):e[n]=o}return e}const Zb=Symbol();const{assign:e_}=Object;function t_(e,t,n,o){const{state:r,actions:i,getters:s}=t,a=n.state.value[e];let l;return l=n_(e,(function(){a||(n.state.value[e]=r?r():{});const t=function(e){const t=k(e)?new Array(e.length):{};for(const n in e)t[n]=Hn(e,n);return t}(n.state.value[e]);return e_(t,i,Object.keys(s||{}).reduce(((t,o)=>(t[o]=Pn(os((()=>{zb(n);const t=n._s.get(e);return s[o].call(t,t)}))),t)),{}))}),t,n,o,!0),l.$reset=function(){const e=r?r():{};this.$patch((t=>{e_(t,e)}))},l}function n_(e,t,n={},o,r,i){let s;const a=e_({actions:{}},n),l={deep:!0};let c,u,f,h=Pn([]),d=Pn([]);const p=o.state.value[e];let g;function m(t){let n;c=u=!1,"function"==typeof t?(t(o.state.value[e]),n={type:Xb.patchFunction,storeId:e,events:f}):(Kb(o.state.value[e],t),n={type:Xb.patchObject,payload:t,storeId:e,events:f});const r=g=Symbol();so().then((()=>{g===r&&(c=!0)})),u=!0,Qb(h,n,o.state.value[e])}i||p||(o.state.value[e]={}),Bn({});const v=Gb;function y(t,n){return function(){zb(o);const r=Array.from(arguments),i=[],s=[];let a;Qb(d,{args:r,name:t,store:_,after:function(e){i.push(e)},onError:function(e){s.push(e)}});try{a=n.apply(this&&this.$id===e?this:_,r)}catch(l){throw Qb(s,l),l}return a instanceof Promise?a.then((e=>(Qb(i,e),e))).catch((e=>(Qb(s,e),Promise.reject(e)))):(Qb(i,a),a)}}const b={_p:o,$id:e,$onAction:Jb.bind(null,d),$patch:m,$reset:v,$subscribe(t,n={}){const r=Jb(h,t,n.detached,(()=>i())),i=s.run((()=>$o((()=>o.state.value[e]),(o=>{("sync"===n.flush?u:c)&&t({storeId:e,type:Xb.direct,events:f},o)}),e_({},l,n))));return r},$dispose:function(){s.stop(),h=[],d=[],o._s.delete(e)}},_=xn(e_({},b));o._s.set(e,_);const w=o._e.run((()=>(s=gt(),s.run((()=>t())))));for(const T in w){const t=w[T];if(jn(t)&&(!jn(S=t)||!S.effect)||kn(t))i||(!p||Ub(x=t)&&x.hasOwnProperty(Zb)||(jn(t)?t.value=p[T]:Kb(t,p[T])),o.state.value[e][T]=t);else if("function"==typeof t){const e=y(T,t);w[T]=e,a.actions[T]=t}}var x,S;return e_(_,w),e_(In(_),w),Object.defineProperty(_,"$state",{get:()=>o.state.value[e],set:e=>{m((t=>{e_(t,e)}))}}),o._p.forEach((e=>{e_(_,s.run((()=>e({store:_,app:o._a,pinia:o,options:a}))))})),p&&i&&n.hydrate&&n.hydrate(_.$state,p),c=!0,u=!0,_}function o_(e,t,n){let o,r;const i="function"==typeof t;function s(e,n){const s=Xi();(e=e||s&&Mo(Hb))&&zb(e),(e=Wb)._s.has(o)||(i?n_(o,t,r,e):t_(o,r,e));return e._s.get(o)}return"string"==typeof e?(o=e,r=i?n:t):(r=e,o=e.id),s.$id=o,s}function r_(e){{e=In(e);const t={};for(const n in e){const o=e[n];(jn(o)||kn(o))&&(t[n]=Hn(e,n))}return t}}const i_="object"==typeof global&&global&&global.Object===Object&&global;var s_="object"==typeof self&&self&&self.Object===Object&&self;const a_=i_||s_||Function("return this")();const l_=a_.Symbol;var c_=Object.prototype,u_=c_.hasOwnProperty,f_=c_.toString,h_=l_?l_.toStringTag:void 0;var d_=Object.prototype.toString;var p_="[object Null]",g_="[object Undefined]",m_=l_?l_.toStringTag:void 0;function v_(e){return null==e?void 0===e?g_:p_:m_&&m_ in Object(e)?function(e){var t=u_.call(e,h_),n=e[h_];try{e[h_]=void 0;var o=!0}catch(YC){}var r=f_.call(e);return o&&(t?e[h_]=n:delete e[h_]),r}(e):function(e){return d_.call(e)}(e)}function y_(e){return null!=e&&"object"==typeof e}const b_=Array.isArray;function __(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function w_(e){return e}var x_="[object AsyncFunction]",S_="[object Function]",T_="[object GeneratorFunction]",C_="[object Proxy]";function k_(e){if(!__(e))return!1;var t=v_(e);return t==S_||t==T_||t==x_||t==C_}const E_=a_["__core-js_shared__"];var O_,M_=(O_=/[^.]+$/.exec(E_&&E_.keys&&E_.keys.IE_PROTO||""))?"Symbol(src)_1."+O_:"";var I_=Function.prototype.toString;var P_=/^\[object .+?Constructor\]$/,$_=Function.prototype,A_=Object.prototype,L_=$_.toString,R_=A_.hasOwnProperty,j_=RegExp("^"+L_.call(R_).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function B_(e){return!(!__(e)||(t=e,M_&&M_ in t))&&(k_(e)?j_:P_).test(function(e){if(null!=e){try{return I_.call(e)}catch(YC){}try{return e+""}catch(YC){}}return""}(e));var t}function N_(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return B_(n)?n:void 0}var D_=Object.create;const F_=function(){function e(){}return function(t){if(!__(t))return{};if(D_)return D_(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();var q_=Date.now;var V_=function(){try{var e=N_(Object,"defineProperty");return e({},"",{}),e}catch(YC){}}();const W_=V_;var z_=W_?function(e,t){return W_(e,"toString",{configurable:!0,enumerable:!1,value:(n=t,function(){return n}),writable:!0});var n}:w_;var H_,U_,X_;const Y_=(H_=z_,U_=0,X_=0,function(){var e=q_(),t=16-(e-X_);if(X_=e,t>0){if(++U_>=800)return arguments[0]}else U_=0;return H_.apply(void 0,arguments)});var G_=9007199254740991,J_=/^(?:0|[1-9]\d*)$/;function Q_(e,t){var n=typeof e;return!!(t=null==t?G_:t)&&("number"==n||"symbol"!=n&&J_.test(e))&&e>-1&&e%1==0&&e<t}function K_(e,t,n){"__proto__"==t&&W_?W_(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function Z_(e,t){return e===t||e!=e&&t!=t}var ew=Object.prototype.hasOwnProperty;function tw(e,t,n){var o=e[t];ew.call(e,t)&&Z_(o,n)&&(void 0!==n||t in e)||K_(e,t,n)}var nw=Math.max;function ow(e,t){return Y_(function(e,t,n){return t=nw(void 0===t?e.length-1:t,0),function(){for(var o=arguments,r=-1,i=nw(o.length-t,0),s=Array(i);++r<i;)s[r]=o[t+r];r=-1;for(var a=Array(t+1);++r<t;)a[r]=o[r];return a[t]=n(s),function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}(e,this,a)}}(e,t,w_),e+"")}var rw=9007199254740991;function iw(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=rw}function sw(e){return null!=e&&iw(e.length)&&!k_(e)}var aw=Object.prototype;function lw(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||aw)}function cw(e){return y_(e)&&"[object Arguments]"==v_(e)}var uw=Object.prototype,fw=uw.hasOwnProperty,hw=uw.propertyIsEnumerable;const dw=cw(function(){return arguments}())?cw:function(e){return y_(e)&&fw.call(e,"callee")&&!hw.call(e,"callee")};var pw="object"==typeof exports&&exports&&!exports.nodeType&&exports,gw=pw&&"object"==typeof module&&module&&!module.nodeType&&module,mw=gw&&gw.exports===pw?a_.Buffer:void 0;const vw=(mw?mw.isBuffer:void 0)||function(){return!1};var yw={};yw["[object Float32Array]"]=yw["[object Float64Array]"]=yw["[object Int8Array]"]=yw["[object Int16Array]"]=yw["[object Int32Array]"]=yw["[object Uint8Array]"]=yw["[object Uint8ClampedArray]"]=yw["[object Uint16Array]"]=yw["[object Uint32Array]"]=!0,yw["[object Arguments]"]=yw["[object Array]"]=yw["[object ArrayBuffer]"]=yw["[object Boolean]"]=yw["[object DataView]"]=yw["[object Date]"]=yw["[object Error]"]=yw["[object Function]"]=yw["[object Map]"]=yw["[object Number]"]=yw["[object Object]"]=yw["[object RegExp]"]=yw["[object Set]"]=yw["[object String]"]=yw["[object WeakMap]"]=!1;var bw="object"==typeof exports&&exports&&!exports.nodeType&&exports,_w=bw&&"object"==typeof module&&module&&!module.nodeType&&module,ww=_w&&_w.exports===bw&&i_.process,xw=function(){try{var e=_w&&_w.require&&_w.require("util").types;return e||ww&&ww.binding&&ww.binding("util")}catch(YC){}}();var Sw=xw&&xw.isTypedArray,Tw=Sw?function(e){return function(t){return e(t)}}(Sw):function(e){return y_(e)&&iw(e.length)&&!!yw[v_(e)]};const Cw=Tw;var kw=Object.prototype.hasOwnProperty;function Ew(e,t){var n=b_(e),o=!n&&dw(e),r=!n&&!o&&vw(e),i=!n&&!o&&!r&&Cw(e),s=n||o||r||i,a=s?function(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}(e.length,String):[],l=a.length;for(var c in e)!t&&!kw.call(e,c)||s&&("length"==c||r&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Q_(c,l))||a.push(c);return a}var Ow=Object.prototype.hasOwnProperty;function Mw(e){if(!__(e))return function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}(e);var t=lw(e),n=[];for(var o in e)("constructor"!=o||!t&&Ow.call(e,o))&&n.push(o);return n}function Iw(e){return sw(e)?Ew(e,!0):Mw(e)}const Pw=N_(Object,"create");var $w=Object.prototype.hasOwnProperty;var Aw=Object.prototype.hasOwnProperty;function Lw(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function Rw(e,t){for(var n=e.length;n--;)if(Z_(e[n][0],t))return n;return-1}Lw.prototype.clear=function(){this.__data__=Pw?Pw(null):{},this.size=0},Lw.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Lw.prototype.get=function(e){var t=this.__data__;if(Pw){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return $w.call(t,e)?t[e]:void 0},Lw.prototype.has=function(e){var t=this.__data__;return Pw?void 0!==t[e]:Aw.call(t,e)},Lw.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Pw&&void 0===t?"__lodash_hash_undefined__":t,this};var jw=Array.prototype.splice;function Bw(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}Bw.prototype.clear=function(){this.__data__=[],this.size=0},Bw.prototype.delete=function(e){var t=this.__data__,n=Rw(t,e);return!(n<0)&&(n==t.length-1?t.pop():jw.call(t,n,1),--this.size,!0)},Bw.prototype.get=function(e){var t=this.__data__,n=Rw(t,e);return n<0?void 0:t[n][1]},Bw.prototype.has=function(e){return Rw(this.__data__,e)>-1},Bw.prototype.set=function(e,t){var n=this.__data__,o=Rw(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this};const Nw=N_(a_,"Map");function Dw(e,t){var n,o,r=e.__data__;return("string"==(o=typeof(n=t))||"number"==o||"symbol"==o||"boolean"==o?"__proto__"!==n:null===n)?r["string"==typeof t?"string":"hash"]:r.map}function Fw(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}Fw.prototype.clear=function(){this.size=0,this.__data__={hash:new Lw,map:new(Nw||Bw),string:new Lw}},Fw.prototype.delete=function(e){var t=Dw(this,e).delete(e);return this.size-=t?1:0,t},Fw.prototype.get=function(e){return Dw(this,e).get(e)},Fw.prototype.has=function(e){return Dw(this,e).has(e)},Fw.prototype.set=function(e,t){var n=Dw(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this};var qw=function(e,t){return function(n){return e(t(n))}}(Object.getPrototypeOf,Object);const Vw=qw;var Ww="[object Object]",zw=Function.prototype,Hw=Object.prototype,Uw=zw.toString,Xw=Hw.hasOwnProperty,Yw=Uw.call(Object);function Gw(e){var t=this.__data__=new Bw(e);this.size=t.size}Gw.prototype.clear=function(){this.__data__=new Bw,this.size=0},Gw.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Gw.prototype.get=function(e){return this.__data__.get(e)},Gw.prototype.has=function(e){return this.__data__.has(e)},Gw.prototype.set=function(e,t){var n=this.__data__;if(n instanceof Bw){var o=n.__data__;if(!Nw||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new Fw(o)}return n.set(e,t),this.size=n.size,this};var Jw="object"==typeof exports&&exports&&!exports.nodeType&&exports,Qw=Jw&&"object"==typeof module&&module&&!module.nodeType&&module,Kw=Qw&&Qw.exports===Jw?a_.Buffer:void 0,Zw=Kw?Kw.allocUnsafe:void 0;const ex=a_.Uint8Array;function tx(e,t){var n,o,r=t?(n=e.buffer,o=new n.constructor(n.byteLength),new ex(o).set(new ex(n)),o):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}var nx,ox=function(e,t,n){for(var o=-1,r=Object(e),i=n(e),s=i.length;s--;){var a=i[nx?s:++o];if(!1===t(r[a],a,r))break}return e};const rx=ox;function ix(e,t,n){(void 0!==n&&!Z_(e[t],n)||void 0===n&&!(t in e))&&K_(e,t,n)}function sx(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function ax(e){return function(e,t,n,o){var r=!n;n||(n={});for(var i=-1,s=t.length;++i<s;){var a=t[i],l=o?o(n[a],e[a],a,n,e):void 0;void 0===l&&(l=e[a]),r?K_(n,a,l):tw(n,a,l)}return n}(e,Iw(e))}function lx(e,t,n,o,r,i,s){var a=sx(e,n),l=sx(t,n),c=s.get(l);if(c)ix(e,n,c);else{var u,f=i?i(a,l,n+"",e,t,s):void 0,h=void 0===f;if(h){var d=b_(l),p=!d&&vw(l),g=!d&&!p&&Cw(l);f=l,d||p||g?b_(a)?f=a:y_(u=a)&&sw(u)?f=function(e,t){var n=-1,o=e.length;for(t||(t=Array(o));++n<o;)t[n]=e[n];return t}(a):p?(h=!1,f=function(e,t){if(t)return e.slice();var n=e.length,o=Zw?Zw(n):new e.constructor(n);return e.copy(o),o}(l,!0)):g?(h=!1,f=tx(l,!0)):f=[]:function(e){if(!y_(e)||v_(e)!=Ww)return!1;var t=Vw(e);if(null===t)return!0;var n=Xw.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Uw.call(n)==Yw}(l)||dw(l)?(f=a,dw(a)?f=ax(a):__(a)&&!k_(a)||(f=function(e){return"function"!=typeof e.constructor||lw(e)?{}:F_(Vw(e))}(l))):h=!1}h&&(s.set(l,f),r(f,l,o,i,s),s.delete(l)),ix(e,n,f)}}function cx(e,t,n,o,r){e!==t&&rx(t,(function(i,s){if(r||(r=new Gw),__(i))lx(e,t,s,n,cx,o,r);else{var a=o?o(sx(e,s),i,s+"",e,t,r):void 0;void 0===a&&(a=i),ix(e,s,a)}}),Iw)}var ux;const fx=(ux=function(e,t,n){cx(e,t,n)},ow((function(e,t){var n=-1,o=t.length,r=o>1?t[o-1]:void 0,i=o>2?t[2]:void 0;for(r=ux.length>3&&"function"==typeof r?(o--,r):void 0,i&&function(e,t,n){if(!__(n))return!1;var o=typeof t;return!!("number"==o?sw(n)&&Q_(t,n.length):"string"==o&&t in n)&&Z_(n[t],e)}(t[0],t[1],i)&&(r=o<3?void 0:r,o=1),e=Object(e);++n<o;){var s=t[n];s&&ux(e,s,n,r)}return e})));var hx=(e=>(e.GET="GET",e.POST="POST",e))(hx||{}),dx=(e=>(e[e.SUCCESS=1]="SUCCESS",e[e.FAILED=0]="FAILED",e[e.TOKEN_INVALID=-1]="TOKEN_INVALID",e))(dx||{}),px=(e=>(e.ABORT="request:fail abort",e.TIMEOUT="request:fail timeout",e))(px||{});const gx=new Map,mx=class{static createInstance(){return this.instance??(this.instance=new mx)}add(e,t){this.remove(e),gx.has(e)&&gx.delete(e),gx.set(e,t)}remove(e){if(gx.has(e)){const t=gx.get(e);t&&t.abort(),gx.delete(e)}}};let vx=mx;t(vx,"instance");const yx=vx.createInstance();class bx{constructor(e){t(this,"options"),this.options=e}retryRequest(e,t){var n;const{retryCount:o,retryTimeout:r}=t;return o&&(null==(n=e.method)?void 0:n.toUpperCase())!=hx.POST?(oy({title:"加载中..."}),t.hasRetryCount=t.hasRetryCount??0,t.hasRetryCount>=o?Promise.reject():(t.hasRetryCount++,t.requestHooks.requestInterceptorsHook=e=>e,new Promise((e=>setTimeout(e,r))).then((()=>this.request(e,t))).finally((()=>ry())))):Promise.reject()}get(e,t){return this.request({...e,method:hx.GET},t)}post(e,t){return this.request({...e,method:hx.POST},t)}uploadFile(e,t){let n=fx({},this.options.requestOptions,e);const o=fx({},this.options,t),{requestInterceptorsHook:r,responseInterceptorsHook:i,responseInterceptorsCatchHook:s}=o.requestHooks||{};return r&&M(r)&&(n=r(n,o)),new Promise(((e,t)=>{Ev({...n,success:async n=>{if(200==n.statusCode){if(n.data=JSON.parse(n.data),i&&M(i)){try{n=await i(n,o),e(n)}catch(r){t(r)}return}e(n)}},fail:async e=>{s&&M(s)?t(await s(n,e)):t(e)}})}))}async request(e,t){let n=fx({},this.options.requestOptions,e);const o=fx({},this.options,t),{requestInterceptorsHook:r,responseInterceptorsHook:i,responseInterceptorsCatchHook:s}=o.requestHooks||{};return r&&M(r)&&(n=r(n,o)),new Promise(((t,r)=>{const a=Sv({...n,async success(e){if(i&&M(i))try{e=await i(e,o),t(e)}catch(n){r(n)}else t(e)},fail:async e=>{e.errMsg!=px.TIMEOUT?s&&M(s)?r(await s(n,e)):r(e):this.retryRequest(n,o).then((e=>t(e))).catch((e=>r(e)))},complete(t){t.errMsg!==px.ABORT&&yx.remove(e.url)}}),{ignoreCancel:l}=o;!l&&yx.add(e.url,a)}))}}const _x="token",xx="history",Sx="back_url",Tx={key:"app_",set(e,t,n){e=this.getKey(e);let o={expire:n?this.time()+n:"",value:t};"object"==typeof o&&(o=JSON.stringify(o));try{Jm(e,o)}catch(YC){return null}},get(e){e=this.getKey(e);try{const t=Zm(e);if(!t)return null;const{value:n,expire:o}=JSON.parse(t);return o&&o<this.time()?(ev(e),null):n}catch(YC){return null}},time:()=>Math.round((new Date).getTime()/1e3),remove(e){e=this.getKey(e),ev(e)},getKey(e){return this.key+e}};function Cx(){return Tx.get(_x)}function kx(){return Lx.get({url:"/user/info"},{isAuth:!0})}function Ex(e){return Lx.post({url:"/user/setInfo",data:e},{isAuth:!0})}function Ox(e,t){return Lx.post({url:"/user/bindMobile",data:e,header:t},{isAuth:!0})}function Mx(e){return Lx.post({url:"/user/changePassword",data:e},{isAuth:!0})}function Ix(e){return Lx.post({url:"/user/resetPassword",data:e})}function Px(e){return Lx.get({url:"/account_log/lists",data:e})}const $x=o_({id:"userStore",state:()=>({userInfo:{},token:Tx.get(_x)||null,temToken:null}),getters:{isLogin:e=>!!e.token},actions:{async getUser(){const e=await(t={token:this.token||this.temToken},Lx.get({url:"/user/center",header:t},{ignoreCancel:!0}));var t;this.userInfo=e},login(e){this.token=e,Tx.set(_x,e)},logout(){this.token="",this.userInfo={},Tx.remove(_x)}}});const Ax={version:"1.9.0",baseUrl:"/",urlPrefix:"api",timeout:6e4};const Lx=new bx(fx({requestOptions:{timeout:Ax.timeout},baseUrl:Ax.baseUrl,isReturnDefaultResponse:!1,isTransformResponse:!0,urlPrefix:"api",ignoreCancel:!1,withToken:!0,isAuth:!1,retryCount:2,retryTimeout:1e3,requestHooks:{requestInterceptorsHook(e,t){const{urlPrefix:n,baseUrl:o,withToken:r,isAuth:i}=t;e.header=e.header??{},n&&(e.url=`${n}${e.url}`),o&&(e.url=`${o}${e.url}`);const s=Cx();return r&&!e.header.token&&(e.header.token=s),e.header.version=Ax.version,e},async responseInterceptorsHook(e,t){const{isTransformResponse:n,isReturnDefaultResponse:o,isAuth:r}=t;if(o)return e;if(!n)return e.data;const{logout:i}=$x(),{code:s,data:a,msg:l,show:c}=e.data;switch(s){case dx.SUCCESS:return l&&c&&uni.$u.toast(l),a;case dx.FAILED:return uni.$u.toast(l),Promise.reject(l);case dx.TOKEN_INVALID:return i(),r&&!Cx()&&Iv({url:"/pages/login/login"}),Promise.reject(l);default:return a}},async responseInterceptorsCatchHook(e,t){var n;return(null==(n=e.method)?void 0:n.toUpperCase())==hx.POST&&uni.$u.toast("请求失败，请重试"),Promise.reject(t)}}},Rx||{}));var Rx;function jx(e){return Lx.post({url:"/sms/sendCode",data:e})}function Bx(e){return Lx.get({url:"/index/policy",data:e})}function Nx(e,t){return Lx.uploadFile({url:"/upload/image",filePath:e,name:"file",header:{token:t},fileType:"image"})}const Dx=o_({id:"appStore",state:()=>({config:{}}),getters:{getWebsiteConfig:e=>e.config.website||{},getLoginConfig:e=>e.config.login||{},getTabbarConfig:e=>e.config.tabbar||[],getStyleConfig:e=>e.config.style||{},getH5Config:e=>e.config.webPage||{},getCopyrightConfig:e=>e.config.copyright||[]},actions:{getImageUrl(e){return e.indexOf("http")?`${this.config.domain}${e}`:e},async getConfig(){const e=await Lx.get({url:"/index/config"});this.config=e}}});function Fx(){return Lx.get({url:"/index/index"})}function qx(e){return Lx.get({url:"/index/decorate",data:e},{ignoreCancel:!0})}function Vx(){return Lx.get({url:"/search/hotLists"})}var Wx,zx,Hx={},Ux=function(e,t,n){for(var o=0,r={},i=0;i<n.length;i++)if(e==n.substr(i,e.length))"start"in r||(r.start=i),o++;else if(t==n.substr(i,t.length)&&"start"in r&&! --o)return r.end=i,r.pre=n.substr(0,r.start),r.body=r.end-r.start>1?n.substring(r.start+e.length,r.end):"",r.post=n.slice(r.end+t.length),r},Xx={};Wx={get exports(){return Xx},set exports(e){Xx=e}},zx=function(){function e(t,o,r,i){"object"==typeof o&&(r=o.depth,i=o.prototype,o.filter,o=o.circular);var s=[],a=[],l="undefined"!=typeof Buffer;return void 0===o&&(o=!0),void 0===r&&(r=1/0),function t(r,c){if(null===r)return null;if(0==c)return r;var u,f;if("object"!=typeof r)return r;if(e.__isArray(r))u=[];else if(e.__isRegExp(r))u=new RegExp(r.source,n(r)),r.lastIndex&&(u.lastIndex=r.lastIndex);else if(e.__isDate(r))u=new Date(r.getTime());else{if(l&&Buffer.isBuffer(r))return u=Buffer.allocUnsafe?Buffer.allocUnsafe(r.length):new Buffer(r.length),r.copy(u),u;void 0===i?(f=Object.getPrototypeOf(r),u=Object.create(f)):(u=Object.create(i),f=i)}if(o){var h=s.indexOf(r);if(-1!=h)return a[h];s.push(r),a.push(u)}for(var d in r){var p;f&&(p=Object.getOwnPropertyDescriptor(f,d)),p&&null==p.set||(u[d]=t(r[d],c-1))}return u}(t,r)}function t(e){return Object.prototype.toString.call(e)}function n(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return e.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},e.__objToStr=t,e.__isDate=function(e){return"object"==typeof e&&"[object Date]"===t(e)},e.__isArray=function(e){return"object"==typeof e&&"[object Array]"===t(e)},e.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===t(e)},e.__getRegExpFlags=n,e}(),Wx.exports&&(Wx.exports=zx);var Yx={},Gx={get exports(){return Yx},set exports(e){Yx=e}},Jx={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},Qx=Jx,Kx={};for(var Zx in Qx)Qx.hasOwnProperty(Zx)&&(Kx[Qx[Zx]]=Zx);var eS=Gx.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var tS in eS)if(eS.hasOwnProperty(tS)){if(!("channels"in eS[tS]))throw new Error("missing channels property: "+tS);if(!("labels"in eS[tS]))throw new Error("missing channel labels property: "+tS);if(eS[tS].labels.length!==eS[tS].channels)throw new Error("channel and label counts mismatch: "+tS);var nS=eS[tS].channels,oS=eS[tS].labels;delete eS[tS].channels,delete eS[tS].labels,Object.defineProperty(eS[tS],"channels",{value:nS}),Object.defineProperty(eS[tS],"labels",{value:oS})}eS.rgb.hsl=function(e){var t,n,o=e[0]/255,r=e[1]/255,i=e[2]/255,s=Math.min(o,r,i),a=Math.max(o,r,i),l=a-s;return a===s?t=0:o===a?t=(r-i)/l:r===a?t=2+(i-o)/l:i===a&&(t=4+(o-r)/l),(t=Math.min(60*t,360))<0&&(t+=360),n=(s+a)/2,[t,100*(a===s?0:n<=.5?l/(a+s):l/(2-a-s)),100*n]},eS.rgb.hsv=function(e){var t,n,o,r,i,s=e[0]/255,a=e[1]/255,l=e[2]/255,c=Math.max(s,a,l),u=c-Math.min(s,a,l),f=function(e){return(c-e)/6/u+.5};return 0===u?r=i=0:(i=u/c,t=f(s),n=f(a),o=f(l),s===c?r=o-n:a===c?r=1/3+t-o:l===c&&(r=2/3+n-t),r<0?r+=1:r>1&&(r-=1)),[360*r,100*i,100*c]},eS.rgb.hwb=function(e){var t=e[0],n=e[1],o=e[2];return[eS.rgb.hsl(e)[0],100*(1/255*Math.min(t,Math.min(n,o))),100*(o=1-1/255*Math.max(t,Math.max(n,o)))]},eS.rgb.cmyk=function(e){var t,n=e[0]/255,o=e[1]/255,r=e[2]/255;return[100*((1-n-(t=Math.min(1-n,1-o,1-r)))/(1-t)||0),100*((1-o-t)/(1-t)||0),100*((1-r-t)/(1-t)||0),100*t]},eS.rgb.keyword=function(e){var t=Kx[e];if(t)return t;var n,o,r,i=1/0;for(var s in Qx)if(Qx.hasOwnProperty(s)){var a=Qx[s],l=(o=e,r=a,Math.pow(o[0]-r[0],2)+Math.pow(o[1]-r[1],2)+Math.pow(o[2]-r[2],2));l<i&&(i=l,n=s)}return n},eS.keyword.rgb=function(e){return Qx[e]},eS.rgb.xyz=function(e){var t=e[0]/255,n=e[1]/255,o=e[2]/255;return[100*(.4124*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)+.3576*(n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92)+.1805*(o=o>.04045?Math.pow((o+.055)/1.055,2.4):o/12.92)),100*(.2126*t+.7152*n+.0722*o),100*(.0193*t+.1192*n+.9505*o)]},eS.rgb.lab=function(e){var t=eS.rgb.xyz(e),n=t[0],o=t[1],r=t[2];return o/=100,r/=108.883,n=(n/=95.047)>.008856?Math.pow(n,1/3):7.787*n+16/116,[116*(o=o>.008856?Math.pow(o,1/3):7.787*o+16/116)-16,500*(n-o),200*(o-(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116))]},eS.hsl.rgb=function(e){var t,n,o,r,i,s=e[0]/360,a=e[1]/100,l=e[2]/100;if(0===a)return[i=255*l,i,i];t=2*l-(n=l<.5?l*(1+a):l+a-l*a),r=[0,0,0];for(var c=0;c<3;c++)(o=s+1/3*-(c-1))<0&&o++,o>1&&o--,i=6*o<1?t+6*(n-t)*o:2*o<1?n:3*o<2?t+(n-t)*(2/3-o)*6:t,r[c]=255*i;return r},eS.hsl.hsv=function(e){var t=e[0],n=e[1]/100,o=e[2]/100,r=n,i=Math.max(o,.01);return n*=(o*=2)<=1?o:2-o,r*=i<=1?i:2-i,[t,100*(0===o?2*r/(i+r):2*n/(o+n)),100*((o+n)/2)]},eS.hsv.rgb=function(e){var t=e[0]/60,n=e[1]/100,o=e[2]/100,r=Math.floor(t)%6,i=t-Math.floor(t),s=255*o*(1-n),a=255*o*(1-n*i),l=255*o*(1-n*(1-i));switch(o*=255,r){case 0:return[o,l,s];case 1:return[a,o,s];case 2:return[s,o,l];case 3:return[s,a,o];case 4:return[l,s,o];case 5:return[o,s,a]}},eS.hsv.hsl=function(e){var t,n,o,r=e[0],i=e[1]/100,s=e[2]/100,a=Math.max(s,.01);return o=(2-i)*s,n=i*a,[r,100*(n=(n/=(t=(2-i)*a)<=1?t:2-t)||0),100*(o/=2)]},eS.hwb.rgb=function(e){var t,n,o,r,i,s,a,l=e[0]/360,c=e[1]/100,u=e[2]/100,f=c+u;switch(f>1&&(c/=f,u/=f),o=6*l-(t=Math.floor(6*l)),1&t&&(o=1-o),r=c+o*((n=1-u)-c),t){default:case 6:case 0:i=n,s=r,a=c;break;case 1:i=r,s=n,a=c;break;case 2:i=c,s=n,a=r;break;case 3:i=c,s=r,a=n;break;case 4:i=r,s=c,a=n;break;case 5:i=n,s=c,a=r}return[255*i,255*s,255*a]},eS.cmyk.rgb=function(e){var t=e[0]/100,n=e[1]/100,o=e[2]/100,r=e[3]/100;return[255*(1-Math.min(1,t*(1-r)+r)),255*(1-Math.min(1,n*(1-r)+r)),255*(1-Math.min(1,o*(1-r)+r))]},eS.xyz.rgb=function(e){var t,n,o,r=e[0]/100,i=e[1]/100,s=e[2]/100;return n=-.9689*r+1.8758*i+.0415*s,o=.0557*r+-.204*i+1.057*s,t=(t=3.2406*r+-1.5372*i+-.4986*s)>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:12.92*n,o=o>.0031308?1.055*Math.pow(o,1/2.4)-.055:12.92*o,[255*(t=Math.min(Math.max(0,t),1)),255*(n=Math.min(Math.max(0,n),1)),255*(o=Math.min(Math.max(0,o),1))]},eS.xyz.lab=function(e){var t=e[0],n=e[1],o=e[2];return n/=100,o/=108.883,t=(t/=95.047)>.008856?Math.pow(t,1/3):7.787*t+16/116,[116*(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116)-16,500*(t-n),200*(n-(o=o>.008856?Math.pow(o,1/3):7.787*o+16/116))]},eS.lab.xyz=function(e){var t,n,o,r=e[0];t=e[1]/500+(n=(r+16)/116),o=n-e[2]/200;var i=Math.pow(n,3),s=Math.pow(t,3),a=Math.pow(o,3);return n=i>.008856?i:(n-16/116)/7.787,t=s>.008856?s:(t-16/116)/7.787,o=a>.008856?a:(o-16/116)/7.787,[t*=95.047,n*=100,o*=108.883]},eS.lab.lch=function(e){var t,n=e[0],o=e[1],r=e[2];return(t=360*Math.atan2(r,o)/2/Math.PI)<0&&(t+=360),[n,Math.sqrt(o*o+r*r),t]},eS.lch.lab=function(e){var t,n=e[0],o=e[1];return t=e[2]/360*2*Math.PI,[n,o*Math.cos(t),o*Math.sin(t)]},eS.rgb.ansi16=function(e){var t=e[0],n=e[1],o=e[2],r=1 in arguments?arguments[1]:eS.rgb.hsv(e)[2];if(0===(r=Math.round(r/50)))return 30;var i=30+(Math.round(o/255)<<2|Math.round(n/255)<<1|Math.round(t/255));return 2===r&&(i+=60),i},eS.hsv.ansi16=function(e){return eS.rgb.ansi16(eS.hsv.rgb(e),e[2])},eS.rgb.ansi256=function(e){var t=e[0],n=e[1],o=e[2];return t===n&&n===o?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(n/255*5)+Math.round(o/255*5)},eS.ansi16.rgb=function(e){var t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),[t=t/10.5*255,t,t];var n=.5*(1+~~(e>50));return[(1&t)*n*255,(t>>1&1)*n*255,(t>>2&1)*n*255]},eS.ansi256.rgb=function(e){if(e>=232){var t=10*(e-232)+8;return[t,t,t]}var n;return e-=16,[Math.floor(e/36)/5*255,Math.floor((n=e%36)/6)/5*255,n%6/5*255]},eS.rgb.hex=function(e){var t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},eS.hex.rgb=function(e){var t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];var n=t[0];3===t[0].length&&(n=n.split("").map((function(e){return e+e})).join(""));var o=parseInt(n,16);return[o>>16&255,o>>8&255,255&o]},eS.rgb.hcg=function(e){var t,n=e[0]/255,o=e[1]/255,r=e[2]/255,i=Math.max(Math.max(n,o),r),s=Math.min(Math.min(n,o),r),a=i-s;return t=a<=0?0:i===n?(o-r)/a%6:i===o?2+(r-n)/a:4+(n-o)/a+4,t/=6,[360*(t%=1),100*a,100*(a<1?s/(1-a):0)]},eS.hsl.hcg=function(e){var t=e[1]/100,n=e[2]/100,o=1,r=0;return(o=n<.5?2*t*n:2*t*(1-n))<1&&(r=(n-.5*o)/(1-o)),[e[0],100*o,100*r]},eS.hsv.hcg=function(e){var t=e[1]/100,n=e[2]/100,o=t*n,r=0;return o<1&&(r=(n-o)/(1-o)),[e[0],100*o,100*r]},eS.hcg.rgb=function(e){var t=e[0]/360,n=e[1]/100,o=e[2]/100;if(0===n)return[255*o,255*o,255*o];var r,i=[0,0,0],s=t%1*6,a=s%1,l=1-a;switch(Math.floor(s)){case 0:i[0]=1,i[1]=a,i[2]=0;break;case 1:i[0]=l,i[1]=1,i[2]=0;break;case 2:i[0]=0,i[1]=1,i[2]=a;break;case 3:i[0]=0,i[1]=l,i[2]=1;break;case 4:i[0]=a,i[1]=0,i[2]=1;break;default:i[0]=1,i[1]=0,i[2]=l}return r=(1-n)*o,[255*(n*i[0]+r),255*(n*i[1]+r),255*(n*i[2]+r)]},eS.hcg.hsv=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t),o=0;return n>0&&(o=t/n),[e[0],100*o,100*n]},eS.hcg.hsl=function(e){var t=e[1]/100,n=e[2]/100*(1-t)+.5*t,o=0;return n>0&&n<.5?o=t/(2*n):n>=.5&&n<1&&(o=t/(2*(1-n))),[e[0],100*o,100*n]},eS.hcg.hwb=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t);return[e[0],100*(n-t),100*(1-n)]},eS.hwb.hcg=function(e){var t=e[1]/100,n=1-e[2]/100,o=n-t,r=0;return o<1&&(r=(n-o)/(1-o)),[e[0],100*o,100*r]},eS.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},eS.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},eS.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},eS.gray.hsl=eS.gray.hsv=function(e){return[0,0,e[0]]},eS.gray.hwb=function(e){return[0,100,e[0]]},eS.gray.cmyk=function(e){return[0,0,0,e[0]]},eS.gray.lab=function(e){return[e[0],0,0]},eS.gray.hex=function(e){var t=255&Math.round(e[0]/100*255),n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n},eS.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]};var rS=Yx;function iS(e){var t=function(){for(var e={},t=Object.keys(rS),n=t.length,o=0;o<n;o++)e[t[o]]={distance:-1,parent:null};return e}(),n=[e];for(t[e].distance=0;n.length;)for(var o=n.pop(),r=Object.keys(rS[o]),i=r.length,s=0;s<i;s++){var a=r[s],l=t[a];-1===l.distance&&(l.distance=t[o].distance+1,l.parent=o,n.unshift(a))}return t}function sS(e,t){return function(n){return t(e(n))}}function aS(e,t){for(var n=[t[e].parent,e],o=rS[t[e].parent][e],r=t[e].parent;t[r].parent;)n.unshift(t[r].parent),o=sS(rS[t[r].parent][r],o),r=t[r].parent;return o.conversion=n,o}var lS=Yx,cS=function(e){for(var t=iS(e),n={},o=Object.keys(t),r=o.length,i=0;i<r;i++){var s=o[i];null!==t[s].parent&&(n[s]=aS(s,t))}return n},uS={};Object.keys(lS).forEach((function(e){uS[e]={},Object.defineProperty(uS[e],"channels",{value:lS[e].channels}),Object.defineProperty(uS[e],"labels",{value:lS[e].labels});var t=cS(e);Object.keys(t).forEach((function(n){var o=t[n];uS[e][n]=function(e){var t=function(t){if(null==t)return t;arguments.length>1&&(t=Array.prototype.slice.call(arguments));var n=e(t);if("object"==typeof n)for(var o=n.length,r=0;r<o;r++)n[r]=Math.round(n[r]);return n};return"conversion"in e&&(t.conversion=e.conversion),t}(o),uS[e][n].raw=function(e){var t=function(t){return null==t?t:(arguments.length>1&&(t=Array.prototype.slice.call(arguments)),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(o)}))}));var fS=uS,hS=Jx,dS={getRgba:pS,getHsla:gS,getRgb:function(e){var t=pS(e);return t&&t.slice(0,3)},getHsl:function(e){var t=gS(e);return t&&t.slice(0,3)},getHwb:mS,getAlpha:function(e){var t=pS(e);if(t)return t[3];if(t=gS(e))return t[3];if(t=mS(e))return t[3]},hexString:function(e){return"#"+wS(e[0])+wS(e[1])+wS(e[2])},rgbString:function(e,t){if(t<1||e[3]&&e[3]<1)return vS(e,t);return"rgb("+e[0]+", "+e[1]+", "+e[2]+")"},rgbaString:vS,percentString:function(e,t){if(t<1||e[3]&&e[3]<1)return yS(e,t);var n=Math.round(e[0]/255*100),o=Math.round(e[1]/255*100),r=Math.round(e[2]/255*100);return"rgb("+n+"%, "+o+"%, "+r+"%)"},percentaString:yS,hslString:function(e,t){if(t<1||e[3]&&e[3]<1)return bS(e,t);return"hsl("+e[0]+", "+e[1]+"%, "+e[2]+"%)"},hslaString:bS,hwbString:function(e,t){void 0===t&&(t=void 0!==e[3]?e[3]:1);return"hwb("+e[0]+", "+e[1]+"%, "+e[2]+"%"+(void 0!==t&&1!==t?", "+t:"")+")"},keyword:function(e){return xS[e.slice(0,3)]}};function pS(e){if(e){var t=[0,0,0],n=1,o=e.match(/^#([a-fA-F0-9]{3})$/);if(o){o=o[1];for(var r=0;r<t.length;r++)t[r]=parseInt(o[r]+o[r],16)}else if(o=e.match(/^#([a-fA-F0-9]{6})$/)){o=o[1];for(r=0;r<t.length;r++)t[r]=parseInt(o.slice(2*r,2*r+2),16)}else if(o=e.match(/^rgba?\(\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)){for(r=0;r<t.length;r++)t[r]=parseInt(o[r+1]);n=parseFloat(o[4])}else if(o=e.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)){for(r=0;r<t.length;r++)t[r]=Math.round(2.55*parseFloat(o[r+1]));n=parseFloat(o[4])}else if(o=e.match(/(\D+)/)){if("transparent"==o[1])return[0,0,0,0];if(!(t=hS[o[1]]))return}for(r=0;r<t.length;r++)t[r]=_S(t[r],0,255);return n=n||0==n?_S(n,0,1):1,t[3]=n,t}}function gS(e){if(e){var t=e.match(/^hsla?\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(t){var n=parseFloat(t[4]);return[_S(parseInt(t[1]),0,360),_S(parseFloat(t[2]),0,100),_S(parseFloat(t[3]),0,100),_S(isNaN(n)?1:n,0,1)]}}}function mS(e){if(e){var t=e.match(/^hwb\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(t){var n=parseFloat(t[4]);return[_S(parseInt(t[1]),0,360),_S(parseFloat(t[2]),0,100),_S(parseFloat(t[3]),0,100),_S(isNaN(n)?1:n,0,1)]}}}function vS(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"rgba("+e[0]+", "+e[1]+", "+e[2]+", "+t+")"}function yS(e,t){return"rgba("+Math.round(e[0]/255*100)+"%, "+Math.round(e[1]/255*100)+"%, "+Math.round(e[2]/255*100)+"%, "+(t||e[3]||1)+")"}function bS(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"hsla("+e[0]+", "+e[1]+"%, "+e[2]+"%, "+t+")"}function _S(e,t,n){return Math.min(Math.max(t,e),n)}function wS(e){var t=e.toString(16).toUpperCase();return t.length<2?"0"+t:t}var xS={};for(var SS in hS)xS[hS[SS]]=SS;var TS=Xx,CS=fS,kS=dS,ES=function(e){if(e instanceof ES)return e;if(!(this instanceof ES))return new ES(e);var t;if(this.values={rgb:[0,0,0],hsl:[0,0,0],hsv:[0,0,0],hwb:[0,0,0],cmyk:[0,0,0,0],alpha:1},"string"==typeof e)if(t=kS.getRgba(e))this.setValues("rgb",t);else if(t=kS.getHsla(e))this.setValues("hsl",t);else{if(!(t=kS.getHwb(e)))throw new Error('Unable to parse color from string "'+e+'"');this.setValues("hwb",t)}else if("object"==typeof e)if(void 0!==(t=e).r||void 0!==t.red)this.setValues("rgb",t);else if(void 0!==t.l||void 0!==t.lightness)this.setValues("hsl",t);else if(void 0!==t.v||void 0!==t.value)this.setValues("hsv",t);else if(void 0!==t.w||void 0!==t.whiteness)this.setValues("hwb",t);else{if(void 0===t.c&&void 0===t.cyan)throw new Error("Unable to parse color from object "+JSON.stringify(e));this.setValues("cmyk",t)}};ES.prototype={rgb:function(){return this.setSpace("rgb",arguments)},hsl:function(){return this.setSpace("hsl",arguments)},hsv:function(){return this.setSpace("hsv",arguments)},hwb:function(){return this.setSpace("hwb",arguments)},cmyk:function(){return this.setSpace("cmyk",arguments)},rgbArray:function(){return this.values.rgb},hslArray:function(){return this.values.hsl},hsvArray:function(){return this.values.hsv},hwbArray:function(){return 1!==this.values.alpha?this.values.hwb.concat([this.values.alpha]):this.values.hwb},cmykArray:function(){return this.values.cmyk},rgbaArray:function(){return this.values.rgb.concat([this.values.alpha])},rgbaArrayNormalized:function(){for(var e=this.values.rgb,t=[],n=0;n<3;n++)t[n]=e[n]/255;return t.push(this.values.alpha),t},hslaArray:function(){return this.values.hsl.concat([this.values.alpha])},alpha:function(e){return void 0===e?this.values.alpha:(this.setValues("alpha",e),this)},red:function(e){return this.setChannel("rgb",0,e)},green:function(e){return this.setChannel("rgb",1,e)},blue:function(e){return this.setChannel("rgb",2,e)},hue:function(e){return e&&(e=(e%=360)<0?360+e:e),this.setChannel("hsl",0,e)},saturation:function(e){return this.setChannel("hsl",1,e)},lightness:function(e){return this.setChannel("hsl",2,e)},saturationv:function(e){return this.setChannel("hsv",1,e)},whiteness:function(e){return this.setChannel("hwb",1,e)},blackness:function(e){return this.setChannel("hwb",2,e)},value:function(e){return this.setChannel("hsv",2,e)},cyan:function(e){return this.setChannel("cmyk",0,e)},magenta:function(e){return this.setChannel("cmyk",1,e)},yellow:function(e){return this.setChannel("cmyk",2,e)},black:function(e){return this.setChannel("cmyk",3,e)},hexString:function(){return kS.hexString(this.values.rgb)},rgbString:function(){return kS.rgbString(this.values.rgb,this.values.alpha)},rgbaString:function(){return kS.rgbaString(this.values.rgb,this.values.alpha)},percentString:function(){return kS.percentString(this.values.rgb,this.values.alpha)},hslString:function(){return kS.hslString(this.values.hsl,this.values.alpha)},hslaString:function(){return kS.hslaString(this.values.hsl,this.values.alpha)},hwbString:function(){return kS.hwbString(this.values.hwb,this.values.alpha)},keyword:function(){return kS.keyword(this.values.rgb,this.values.alpha)},rgbNumber:function(){return this.values.rgb[0]<<16|this.values.rgb[1]<<8|this.values.rgb[2]},luminosity:function(){for(var e=this.values.rgb,t=[],n=0;n<e.length;n++){var o=e[n]/255;t[n]=o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4)}return.2126*t[0]+.7152*t[1]+.0722*t[2]},contrast:function(e){var t=this.luminosity(),n=e.luminosity();return t>n?(t+.05)/(n+.05):(n+.05)/(t+.05)},level:function(e){var t=this.contrast(e);return t>=7.1?"AAA":t>=4.5?"AA":""},dark:function(){var e=this.values.rgb;return(299*e[0]+587*e[1]+114*e[2])/1e3<128},light:function(){return!this.dark()},negate:function(){for(var e=[],t=0;t<3;t++)e[t]=255-this.values.rgb[t];return this.setValues("rgb",e),this},lighten:function(e){return this.values.hsl[2]+=this.values.hsl[2]*e,this.setValues("hsl",this.values.hsl),this},darken:function(e){return this.values.hsl[2]-=this.values.hsl[2]*e,this.setValues("hsl",this.values.hsl),this},saturate:function(e){return this.values.hsl[1]+=this.values.hsl[1]*e,this.setValues("hsl",this.values.hsl),this},desaturate:function(e){return this.values.hsl[1]-=this.values.hsl[1]*e,this.setValues("hsl",this.values.hsl),this},whiten:function(e){return this.values.hwb[1]+=this.values.hwb[1]*e,this.setValues("hwb",this.values.hwb),this},blacken:function(e){return this.values.hwb[2]+=this.values.hwb[2]*e,this.setValues("hwb",this.values.hwb),this},greyscale:function(){var e=this.values.rgb,t=.3*e[0]+.59*e[1]+.11*e[2];return this.setValues("rgb",[t,t,t]),this},clearer:function(e){return this.setValues("alpha",this.values.alpha-this.values.alpha*e),this},opaquer:function(e){return this.setValues("alpha",this.values.alpha+this.values.alpha*e),this},rotate:function(e){var t=this.values.hsl[0];return t=(t=(t+e)%360)<0?360+t:t,this.values.hsl[0]=t,this.setValues("hsl",this.values.hsl),this},mix:function(e,t){var n=this,o=e,r=void 0===t?.5:t,i=2*r-1,s=n.alpha()-o.alpha(),a=((i*s==-1?i:(i+s)/(1+i*s))+1)/2,l=1-a;return this.rgb(a*n.red()+l*o.red(),a*n.green()+l*o.green(),a*n.blue()+l*o.blue()).alpha(n.alpha()*r+o.alpha()*(1-r))},toJSON:function(){return this.rgb()},clone:function(){var e=new ES;return e.values=TS(this.values),e}},ES.prototype.getValues=function(e){for(var t={},n=0;n<e.length;n++)t[e.charAt(n)]=this.values[e][n];return 1!==this.values.alpha&&(t.a=this.values.alpha),t},ES.prototype.setValues=function(e,t){var n,o,r={rgb:["red","green","blue"],hsl:["hue","saturation","lightness"],hsv:["hue","saturation","value"],hwb:["hue","whiteness","blackness"],cmyk:["cyan","magenta","yellow","black"]},i={rgb:[255,255,255],hsl:[360,100,100],hsv:[360,100,100],hwb:[360,100,100],cmyk:[100,100,100,100]},s=1;if("alpha"===e)s=t;else if(t.length)this.values[e]=t.slice(0,e.length),s=t[e.length];else if(void 0!==t[e.charAt(0)]){for(n=0;n<e.length;n++)this.values[e][n]=t[e.charAt(n)];s=t.a}else if(void 0!==t[r[e][0]]){var a=r[e];for(n=0;n<e.length;n++)this.values[e][n]=t[a[n]];s=t.alpha}if(this.values.alpha=Math.max(0,Math.min(1,void 0===s?this.values.alpha:s)),"alpha"===e)return!1;for(n=0;n<e.length;n++)o=Math.max(0,Math.min(i[e][n],this.values[e][n])),this.values[e][n]=Math.round(o);for(var l in r)for(l!==e&&(this.values[l]=CS[e][l](this.values[e])),n=0;n<l.length;n++)o=Math.max(0,Math.min(i[l][n],this.values[l][n])),this.values[l][n]=Math.round(o);return!0},ES.prototype.setSpace=function(e,t){var n=t[0];return void 0===n?this.getValues(e):("number"==typeof n&&(n=Array.prototype.slice.call(t)),this.setValues(e,n),this)},ES.prototype.setChannel=function(e,t,n){return void 0===n?this.values[e][t]:(n===this.values[e][t]||(this.values[e][t]=n,this.setValues(e,this.values[e])),this)};var OS,MS,IS=ES,PS={};function $S(){if(MS)return OS;MS=1;var e=1e3,t=60*e,n=60*t,o=24*n,r=7*o,i=365.25*o;function s(e,t,n,o){var r=t>=1.5*n;return Math.round(e/n)+" "+o+(r?"s":"")}return OS=function(a,l){l=l||{};var c=typeof a;if("string"===c&&a.length>0)return function(s){if((s=String(s)).length>100)return;var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(s);if(!a)return;var l=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return l*i;case"weeks":case"week":case"w":return l*r;case"days":case"day":case"d":return l*o;case"hours":case"hour":case"hrs":case"hr":case"h":return l*n;case"minutes":case"minute":case"mins":case"min":case"m":return l*t;case"seconds":case"second":case"secs":case"sec":case"s":return l*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return l;default:return}}(a);if("number"===c&&isFinite(a))return l.long?function(r){var i=Math.abs(r);if(i>=o)return s(r,i,o,"day");if(i>=n)return s(r,i,n,"hour");if(i>=t)return s(r,i,t,"minute");if(i>=e)return s(r,i,e,"second");return r+" ms"}(a):function(r){var i=Math.abs(r);if(i>=o)return Math.round(r/o)+"d";if(i>=n)return Math.round(r/n)+"h";if(i>=t)return Math.round(r/t)+"m";if(i>=e)return Math.round(r/e)+"s";return r+"ms"}(a);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(a))}}var AS=function(e){function t(e){for(var t=0,o=0;o<e.length;o++)t=(t<<5)-t+e.charCodeAt(o),t|=0;return n.colors[Math.abs(t)%n.colors.length]}function n(e){var i;function s(){if(s.enabled){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];var r=s,a=Number(new Date),l=a-(i||a);r.diff=l,r.prev=i,r.curr=a,i=a,t[0]=n.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");var c=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,(function(e,o){if("%%"===e)return e;c++;var i=n.formatters[o];if("function"==typeof i){var s=t[c];e=i.call(r,s),t.splice(c,1),c--}return e})),n.formatArgs.call(r,t),(r.log||n.log).apply(r,t)}}return s.namespace=e,s.enabled=n.enabled(e),s.useColors=n.useColors(),s.color=t(e),s.destroy=o,s.extend=r,"function"==typeof n.init&&n.init(s),n.instances.push(s),s}function o(){var e=n.instances.indexOf(this);return-1!==e&&(n.instances.splice(e,1),!0)}function r(e,t){return n(this.namespace+(void 0===t?":":t)+e)}return n.debug=n,n.default=n,n.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},n.disable=function(){n.enable("")},n.enable=function(e){var t;n.save(e),n.names=[],n.skips=[];var o=("string"==typeof e?e:"").split(/[\s,]+/),r=o.length;for(t=0;t<r;t++)o[t]&&("-"===(e=o[t].replace(/\*/g,".*?"))[0]?n.skips.push(new RegExp("^"+e.substr(1)+"$")):n.names.push(new RegExp("^"+e+"$")));for(t=0;t<n.instances.length;t++){var i=n.instances[t];i.enabled=n.enabled(i.namespace)}},n.enabled=function(e){if("*"===e[e.length-1])return!0;var t,o;for(t=0,o=n.skips.length;t<o;t++)if(n.skips[t].test(e))return!1;for(t=0,o=n.names.length;t<o;t++)if(n.names[t].test(e))return!0;return!1},n.humanize=$S(),Object.keys(e).forEach((function(t){n[t]=e[t]})),n.instances=[],n.names=[],n.skips=[],n.formatters={},n.selectColor=t,n.enable(n.load()),n};!function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.log=function(){var e;return"object"===("undefined"==typeof console?"undefined":n(console))&&console.log&&(e=console).log.apply(e,arguments)},t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;var n="color: "+this.color;t.splice(1,0,n,"color: inherit");var o=0,r=0;t[0].replace(/%[a-zA-Z%]/g,(function(e){"%%"!==e&&(o++,"%c"===e&&(r=o))})),t.splice(r,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(n){}},t.load=function(){var e;try{e=t.storage.getItem("debug")}catch(n){}!e&&"undefined"!=typeof process&&"env"in process&&(e={}.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.exports=AS(t),e.exports.formatters.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}}({get exports(){return PS},set exports(e){PS=e}},PS);var LS=Ux,RS=PS("css-color-function:parse"),jS=function(e){"string"!=typeof e&&(e=e.toString());function t(t){var n=t.exec(e);if(n)return e=e.slice(n[0].length),n.slice(1)}function n(){t(/^\s+/)}function o(){var e=t(/^([\+\-\*])/);if(e){var n={type:"modifier"};return n.value=e[0],RS("modifier %o",n),n}}function r(){var e=t(/^([^\)\s]+)/);if(e){var n={type:"number"};return n.value=e[0],RS("number %o",n),n}}function i(){var e=t(/^(\w+)\(/);if(e){n();var i={type:"function"};return i.name=e[0],i.arguments=function(){for(var e,t=[];e=o()||a()||r();)t.push(e),n();return RS("args %o",t),t}(),function(){var e=t(/^\)/);if(e)RS("rparen")}(),RS("adjuster %o",i),i}}function s(){var e={type:"color"},o=t(/([^\)\s]+)/)[0];-1!=o.indexOf("(")&&(o+=t(/([^\)]*?\))/)[0]);return e.value=o,n(),e}function a(){if(e.match(/^color\(/)){var t=LS("(",")",e);if(!t)throw new SyntaxError("Missing closing parenthese for '"+e+"'");if(""===t.body)throw new SyntaxError("color() function cannot be empty");e=t.body,n();var o,r={};for(r.type="function",r.name="color",r.arguments=[a()||s()],RS("function arguments %o",r.arguments);o=i();)r.arguments.push(o),n();return e=t.post,n(),RS("function %o",r),r}}return RS("string %s",e),a()};var BS={};!function(e){var t=IS;function n(e){return function(t,n){var o;"modifier"==n[0].type&&(o=n.shift().value);var i=n[0].value;-1!=i.indexOf("%")?(i=parseInt(i,10)/100,o?"*"!=o&&(i=t[e]()*i):i*="alpha"==e?1:255):i=Number(i),t[e](r(t[e](),i,o))}}function o(e){return function(t,n){var o;"modifier"==n[0].type&&(o=n.shift().value);var i=parseFloat(n[0].value,10);t[e](r(t[e](),i,o))}}function r(e,t,n){switch(n){case"+":return e+t;case"-":return e-t;case"*":return e*t;default:return t}}e.red=n("red"),e.blue=n("blue"),e.green=n("green"),e.alpha=e.a=n("alpha"),e.rgb=function(){},e.hue=e.h=o("hue"),e.saturation=e.s=o("saturation"),e.lightness=e.l=o("lightness"),e.whiteness=e.w=o("whiteness"),e.blackness=e.b=o("blackness"),e.blend=function(e,n){var o=e.alpha();e.alpha(1);var r=new t(n[0].value),i=1-parseInt(n[1].value,10)/100;e.mix(r,i).alpha(o)},e.tint=function(t,n){n.unshift({type:"argument",value:"white"}),e.blend(t,n)},e.shade=function(t,n){n.unshift({type:"argument",value:"black"}),e.blend(t,n)},e.contrast=function(e,n){0==n.length&&n.push({type:"argument",value:"100%"});var o=1-parseInt(n[0].value,10)/100,r=e.luminosity()<.5?new t({h:e.hue(),w:100,b:0}):new t({h:e.hue(),w:0,b:100}),i=r;if(e.contrast(r)>4.5){i=function(e,t,n){t.hue();var o=t.clone(),r=t.whiteness(),i=t.blackness(),s=n.whiteness(),a=n.blackness();for(;Math.abs(r-s)>1||Math.abs(i-a)>1;){var l=Math.round((s+r)/2),c=Math.round((a+i)/2);o.whiteness(l),o.blackness(c),o.contrast(t)>e?(s=l,a=c):(r=l,i=c)}return o}(4.5,e,r);var s=i.alpha();i.alpha(1),i.mix(r,o).alpha(s)}e.hwb(i.hwb())}}(BS);var NS=Ux,DS=IS,FS=jS,qS=BS,VS=function e(t){var n=t.indexOf("color(");if(-1==n)return t;if(t=t.slice(n),!(t=NS("(",")",t)))throw new SyntaxError("Missing closing parenthese for '"+t+"'");return WS(FS("color("+t.body+")"))+e(t.post)};function WS(e){var t=new DS("function"==e.arguments[0].type?WS(e.arguments[0]):e.arguments[0].value);return e.arguments.slice(1).forEach((function(e){var n=e.name;if(!qS[n])throw new Error("Unknown <color-adjuster> '"+n+"'");e.arguments.forEach((function(e){"function"==e.type&&"color"==e.name&&(e.value=WS(e),e.type="color",delete e.name)})),qS[n](t,e.arguments)})),t.rgbString()}var zS=VS,HS=jS;Hx.convert=zS,Hx.parse=HS;const US={"dark-2":"shade(20%)","light-3":"tint(30%)","light-5":"tint(50%)","light-7":"tint(70%)","light-9":"tint(90%)"},XS={"light-3":"shade(20%)","light-5":"shade(30%)","light-7":"shade(50%)","light-9":"shade(70%)","dark-2":"tint(20%)"},YS=(e,t={},n=!1)=>{const o=Object.keys(e).reduce(((t,o)=>Object.assign(t,((e,t="primary",n=!1)=>{const o={[`--color-${t}`]:e},r=n?XS:US;for(const i in r)o[`--color-${t}-${i}`]=`color(${e} ${r[i]})`;return o})(e[o],o,n))),t),r=Object.keys(o).reduce(((e,t)=>`${e}${t}:${Hx.convert(o[t])};`),"");return r},GS=o_({id:"themeStore",state:()=>({primaryColor:"",minorColor:"",btnColor:"white",navColor:"#000000",navBgColor:"#ffffff",vars:""}),actions:{async getTheme(){const e=await qx({id:5}),{themeColor1:t,themeColor2:n,buttonColor:o,navigationBarColor:r,topTextColor:i}=JSON.parse(e.data);this.primaryColor=t,this.minorColor=n,this.btnColor=o,this.navColor="white"===i?"#ffffff":"#000000",this.navBgColor=r||t,this.vars=YS({primary:t},{"--color-minor":n,"--color-btn-text":o})},setTheme(e){this.primaryColor=e}}});function JS(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e,reset:function(){e=[]}}}const QS=/#/g,KS=/&/g,ZS=/\+/g,eT=/%5B/g,tT=/%5D/g,nT=/%5E/g,oT=/%60/g,rT=/%7B/g,iT=/%7C/g,sT=/%7D/g,aT=/%20/g;function lT(e){return function(e){return encodeURI(`${e}`).replace(iT,"|").replace(eT,"[").replace(tT,"]")}(e).replace(ZS,"%2B").replace(aT,"+").replace(QS,"%23").replace(KS,"%26").replace(oT,"`").replace(rT,"{").replace(sT,"}").replace(nT,"^")}function cT(e){try{return decodeURIComponent(`${e}`)}catch(t){uT(`Error decoding "${e}". Using original value`)}return`${e}`}function uT(e,t=!1,...n){t&&console.warn(`[uni-router warn]: ${e}`,...n)}const fT=Symbol("navigation failure"),hT={1:({location:e})=>`Navigation ${"string"==typeof e?e:JSON.stringify(e)} is not found`,2:({from:e,to:t})=>`Redirected from "${JSON.stringify(e)}" to "${JSON.stringify(t)}" via a navigation guard.`,4:({from:e,to:t})=>`Navigation aborted from "${JSON.stringify(e)}" to "${JSON.stringify(t)}" via a navigation guard.`,8:({from:e,to:t})=>`Navigation cancelled from "${JSON.stringify(e)}" to "${JSON.stringify(t)}" with a new navigation.`,16:({from:e,to:t})=>`Avoided redundant navigation to current location: "${JSON.stringify(e)}".`};function dT(e,t){return e instanceof Error&&fT in e&&(null==t||!!(e.type&t))}function pT(e,t){return Object.assign(new Error(hT[e](t)),{type:e,[fT]:!0},t)}const gT=Array.isArray,mT=e=>"function"==typeof e,vT=e=>"string"==typeof e,yT=/(^mp-weixin$)|(^mp-baidu$)|(^mp-alipay$)|(^mp-toutiao$)|(^mp-qq$)|(^mp-360$)/g,bT=["navigateTo","redirectTo","reLaunch","switchTab","navigateBack"];var _T,wT;(wT=_T||(_T={})).navigate="navigateTo",wT.redirect="redirectTo",wT.reLaunch="reLaunch",wT.switchTab="switchTab",wT.navigateBack="navigateBack";const xT={path:"/",name:"",query:{},fullPath:"/",meta:{}},ST="*",TT=Symbol(),CT=Symbol();function kT(){return Mo(TT)}function ET(){return Mo(CT)}const OT={navigateTo:Iv,redirectTo:Pv,reLaunch:$v,switchTab:Lv,navigateBack:Ov};function MT(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace("+"," "),r=e.indexOf("="),i=cT(r<0?e:e.slice(0,r)),s=r<0?null:cT(e.slice(r+1));t[i]=s}return t}function IT(e){for(const t in e){const n=e[t];null!=n&&(e[t]=cT(n))}return e}function PT(e){let t="";for(let n in e){const o=e[n];n=lT(n),null!=o?void 0!==o&&(t+=(t.length?"&":"")+n,null!=o&&(t+=`=${lT(o)}`)):void 0!==o&&(t+=(t.length?"&":"")+n)}return t}function $T(e,t,n="/"){if(t===ST)return{path:t,query:{}};let o,r={},i="";const s=t.indexOf("?");return s>-1?(o=t.slice(0,s),i=t.slice(s+1),r=e(i)):o=t,o=function(e,t){if(e.startsWith("/"))return e;if(!t.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,s,a=n.length-1;for(i=0;i<o.length;i++)if(s=o[i],"."!==s){if(".."!==s)break;a>1&&a--}return`${n.slice(0,a).join("/")}/${o.slice(i-(i===o.length?1:0)).join("/")}`}(null!=o?o:t,n),{path:o,query:r}}function AT(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n}function LT(e){const t=new Map,n=new Map,o=new Map;return e.routes.forEach((r=>function(r){let{path:i,aliasPath:s,name:a}=r;const l=JSON.stringify(r);null!=i&&void 0!==i||uT(`当前路由对象route：${l}不规范，必须含有\`path\``,e.debug),0!==i.indexOf("/")&&"*"!==i&&uT(`当前路由对象route：${l} \`path\`缺少前缀 ‘/’`,e.debug),s=s||i,n.set(i,r),t.set(s,r),a&&(o.has(a)&&uT(`当前路由对象route：${l} 的\`name\`已存在路由表中，将会覆盖旧值`,e.debug),o.set(a,r))}(r))),{getRouteByAliasPath:function(e){return t.get(e)},getRouteByPath:function(e){return n.get(e)},getRouteByName:function(e){return o.get(e)}}}function RT(e,t){var n;let{fullPath:o,path:r,name:i,query:s,meta:a}=t;const{getRouteByAliasPath:l,getRouteByPath:c}=e.routeMatcher,u=Object.assign({},xT);if("h5"===e.options.platform){const u="/"===r?l(r):c(r);s=t.query=IT(null===(n=$T(e.parseQuery,o))||void 0===n?void 0:n.query),o=AT(e.stringifyQuery,t),a=Object.assign({},null==u?void 0:u.meta,a),i=null==u?void 0:u.name}return u.fullPath=o,u.meta=a,u.path=r,u.name=i,u.query=s,u}function jT(e){return"string"==typeof e||e&&"object"==typeof e}function BT(e,t){return!(!e.fullPath&&!t.fullPath)&&e.fullPath===t.fullPath}function NT(e,t){const n=e.resolve(ST);if(!n||void 0===n.redirect)throw uT("未匹配到*通配符路径，或者*通配符必须配合 redirect 使用。redirect: string | Location",e.options.debug),pT(1,{location:t});let o;o=mT(n.redirect)?n.redirect(t):n.redirect;if(void 0===e.resolve(o))throw uT(`无法解析解析出redirect：${JSON.stringify(o)}中的内容，`,e.options.debug),pT(1,{location:t});return pT(2,{to:o,from:t})}function DT(e){return e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve())}function FT(e,t,n){return()=>new Promise(((o,r)=>{const i=e=>{!1===e?r(pT(4,{to:t,from:n})):e instanceof Error?r(e):jT(e)?r(pT(2,{to:e,from:t})):o()},s=e(t,n,i);let a=Promise.resolve(s);"object"==typeof s&&"then"in s?a=a.then(i):void 0!==s&&i(s),a.catch((e=>r(e)))}))}function qT(e,t){let n=e.$scope.route;n=n.startsWith("/")?n:`/${n}`;return{path:n,query:e.$scope.options||{}}}function VT(e,t,n,o){return(...r)=>{t.call(n,r,(t=>{e.apply(n,t)}),o)}}const WT={onLoad([e],t,n){t([IT(e)])},onShow(e,t,n){console.log(e);const o=Kg().length;let r,i;if(n.fromRoute)r=n.currentRoute.value,i=n.fromRoute,n.fromRoute=void 0;else{const o=qT(this);if(r=RT(n,n.resolve(o)),i=n.currentRoute.value,BT(r,i))return t(e);n.currentRoute.value=r}const s=[];for(const a of n.guards.afterGuards.list())s.push((()=>{return e=this,t=null,n=function*(){a(r,i)},new Promise(((o,r)=>{var i=e=>{try{a(n.next(e))}catch(YC){r(YC)}},s=e=>{try{a(n.throw(e))}catch(YC){r(YC)}},a=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,s);a((n=n.apply(e,t)).next())}));var e,t,n}));DT(s),n.level=o,t(e)}};function zT(e,t){return{path:e.$page.path,query:e.$page.options||{}}}const HT={onShow(e){const t=Kg().length;let n,o;if(e.fromRoute)n=e.currentRoute.value,o=e.fromRoute,e.fromRoute=void 0;else{const t=zT(this);if(n=RT(e,e.resolve(t)),o=e.currentRoute.value,BT(n,o))return;e.currentRoute.value=n}const r=[];for(const i of e.guards.afterGuards.list())r.push((()=>{return e=this,t=null,r=function*(){i(n,o)},new Promise(((n,o)=>{var i=e=>{try{a(r.next(e))}catch(YC){o(YC)}},s=e=>{try{a(r.throw(e))}catch(YC){o(YC)}},a=e=>e.done?n(e.value):Promise.resolve(e.value).then(i,s);a((r=r.apply(e,t)).next())}));var e,t,r}));DT(r),e.level=t}};function UT(e,t){const n=function(e){let t=e.options.platform;yT.test(t)&&(t="applets");const n={app:{beforeCreate(){"page"===this.$mpType&&function(e,t){if(HT&&e.$)for(const n in HT){const o=HT[n],r=e.$[n];gT(r)?r.unshift(o.bind(e,t)):e.$[n]=[o.bind(e,t)]}}(this,e)}},h5:{},applets:{beforeCreate(){"page"===this.$mpType&&function(e,t){if(console.log(e),WT&&e.$scope)for(const n in WT){const o=WT[n],r=e.$scope[n];r&&(e.$scope[n]=VT(r,o,e,t))}}(this,e)}}};return n[t]||{}}(t);e.mixin(n)}function XT(e,t,n){var o;const r={beforeGuards:()=>{var t;null===(t=e.vueRouter)||void 0===t||t.beforeEach(((t,o,r)=>{const i=e.resolve(t),s=RT(e,t),a=RT(e,o);let l;void 0===i&&(l=NT(e,s));const c=t=>{if(!jT(t)||t instanceof Error)r(t);else if(vT(t)||!t.navType){const n=e.resolve(t);n&&r({path:n.path,query:n.query})}else{const n=t.navType;e.navigate(t,n)}};if(dT(l,2))return void e.redirectTo(null==l?void 0:l.to);const u=n(s,a,c);"object"==typeof u&&"then"in u?u.then(c).catch((()=>{c(!1)})):void 0!==u&&c(u)}))},afterGuards:()=>{var t;null===(t=e.vueRouter)||void 0===t||t.afterEach(((t,o)=>{const r=RT(e,t),i=RT(e,o);n(r,i)}))}};null===(o=r[t])||void 0===o||o.call(r)}var YT=Object.defineProperty,GT=Object.defineProperties,JT=Object.getOwnPropertyDescriptors,QT=Object.getOwnPropertySymbols,KT=Object.prototype.hasOwnProperty,ZT=Object.prototype.propertyIsEnumerable,eC=(e,t,n)=>t in e?YT(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,tC=(e,t)=>{for(var n in t||(t={}))KT.call(t,n)&&eC(e,n,t[n]);if(QT)for(var n of QT(t))ZT.call(t,n)&&eC(e,n,t[n]);return e},nC=(e,t)=>GT(e,JT(t));var oC={resolve:function(e,t,n="navigateTo"){const{getRouteByName:o,getRouteByPath:r,getRouteByAliasPath:i}=e.routeMatcher;if(vT(t)&&(t={path:t}),Reflect.has(t,"delta")&&"navigateBack"===n||"backbutton"===t.from)return t;Reflect.has(t,"url")&&(t=nC(tC({},t),{path:t.url}));const s=e.currentRoute.value.path,a="/"===s?i(s):r(s);if(Reflect.has(t,"path")){const n=$T(e.parseQuery,t.path,null==a?void 0:a.path);let o=r(n.path);if(void 0===o&&(o=i(n.path)),void 0===o)return;const s=Object.assign({},n.query,null==t?void 0:t.query),l=AT(e.stringifyQuery,{path:o.path,query:s});return nC(tC({},t),{path:o.path,meta:o.meta||{},name:o.name,redirect:o.redirect,fullPath:l,query:s})}if(Reflect.has(t,"name")){let n=o(t.name);if(void 0===n)return void(n=r(ST));const i=Object.assign({},t.query),s=AT(e.stringifyQuery,{path:n.path,query:i});return nC(tC({},t),{path:n.path,meta:n.meta||{},name:n.name,redirect:n.redirect,fullPath:s,query:i})}},mount:function(e,t){!function(e,t){const{h5:n}=t.options,o=e.config.globalProperties.$router,r=o.options.scrollBehavior;Object.assign(o.options,n),o.options.scrollBehavior=function(e,t,o){return(null==n?void 0:n.scrollBehavior)?null==n?void 0:n.scrollBehavior(e,t,o):r(e,t,o)},t.vueRouter=o;for(const[i,s]of Object.entries(t.guards))s.list().forEach((e=>{XT(t,i,e)}));o.afterEach((e=>{t.currentRoute.value=RT(t,e)}))}(e,t)},navigate:function(e,t,n="navigateTo",o){let r,i=e.resolve(t,n);void 0===i&&(r=NT(e,t));const s=e.currentRoute.value,a=Kg();return"navigateBack"===n&&i.delta>=a.length&&(i=e.resolve("/","reLaunch"),n="reLaunch"),(r?Promise.resolve(r):e.jump(i,n)).catch((e=>dT(e,2)?e:Promise.reject(e))).then((t=>{if(t){if(dT(t,2)){const n=e.resolve(null==t?void 0:t.to);return i&&BT(i,n)&&o&&(o._count=o._count?o._count+1:1)>30?(s.fullPath,i.fullPath,e.options.debug,Promise.reject(new Error("Infinite redirect in navigation guard"))):e.navigate(n,n.navType,o||i)}return Promise.resolve(t)}}))},jump:function(e,t,n){return new Promise(((e,o)=>{OT[n](nC(tC({},t),{url:t.fullPath,success(n){var o;e(n),null===(o=t.success)||void 0===o||o.call(t,n)},fail(e){var n;o(e),null===(n=t.fail)||void 0===n||n.call(t,e)},complete(e){var n;null===(n=t.complete)||void 0===n||n.call(t,e)}}))}))},forceGuardEach:function(e){return t=this,n=null,o=function*(){throw new Error("在h5端上使用：forceGuardEach 是无意义的，目前 forceGuardEach 仅支持在非h5端上使用")},new Promise(((e,r)=>{var i=e=>{try{a(o.next(e))}catch(YC){r(YC)}},s=e=>{try{a(o.throw(e))}catch(YC){r(YC)}},a=t=>t.done?e(t.value):Promise.resolve(t.value).then(i,s);a((o=o.apply(t,n)).next())}));var t,n,o}},rC=Object.defineProperty,iC=Object.defineProperties,sC=Object.getOwnPropertyDescriptors,aC=Object.getOwnPropertySymbols,lC=Object.prototype.hasOwnProperty,cC=Object.prototype.propertyIsEnumerable,uC=(e,t,n)=>t in e?rC(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,fC=(e,t)=>{for(var n in t||(t={}))lC.call(t,n)&&uC(e,n,t[n]);if(aC)for(var n of aC(t))cC.call(t,n)&&uC(e,n,t[n]);return e},hC=(e,t)=>iC(e,sC(t)),dC=(e,t,n)=>new Promise(((o,r)=>{var i=e=>{try{a(n.next(e))}catch(YC){r(YC)}},s=e=>{try{a(n.throw(e))}catch(YC){r(YC)}},a=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,s);a((n=n.apply(e,t)).next())}));var pC={resolve:function(e,t,n="navigateTo"){const{getRouteByName:o,getRouteByPath:r,getRouteByAliasPath:i}=e.routeMatcher;if(vT(t)&&(t={path:t}),Reflect.has(t,"delta")&&"navigateBack"===n||"backbutton"===t.from){t.delta=t.delta||1;const n=Kg();let o,r=0;n.length>t.delta&&(r=n.length-1-t.delta),o="app"===e.options.platform?zT(n[r]):qT(n[r].$vm),t=hC(fC(fC({},o),t),{force:"backbutton"===t.from})}Reflect.has(t,"url")&&(t=hC(fC({},t),{path:t.url}));const s=e.currentRoute.value.path,a="/"===s?i(s):r(s);if(Reflect.has(t,"path")){const n=$T(e.parseQuery,t.path,null==a?void 0:a.path);let o=r(n.path);if(void 0===o&&(o=i(n.path)),void 0===o)return;const s=Object.assign({},n.query,null==t?void 0:t.query),l=AT(e.stringifyQuery,{path:o.path,query:s});return hC(fC({},t),{path:o.path,meta:o.meta||{},name:o.name,redirect:o.redirect,fullPath:l,query:s})}if(Reflect.has(t,"name")){const n=o(t.name);if(void 0===n)return;const r=Object.assign({},t.query),i=AT(e.stringifyQuery,{path:n.path,query:r});return hC(fC({},t),{path:n.path,meta:n.meta||{},name:n.name,redirect:n.redirect,fullPath:i,query:r})}},mount:function(e,t){t.forceGuardEach()},navigate:function(e,t,n="navigateTo",o){try{const r=e.resolve(t,n),i=null==r?void 0:r.force;if(e.lock&&!i)return Promise.resolve();e.lock=!0;const s=e.currentRoute.value;let a;if(void 0===r)a=NT(e,t);else if(!i&&BT(r,s)){a=pT(16,{to:RT(e,r),from:s})}return(a?Promise.resolve(a):e.jump(r,n)).catch((e=>dT(e,2)?e:Promise.reject(e))).then((t=>{if(t){if(dT(t,2)){const n=e.resolve(null==t?void 0:t.to);return r&&BT(r,n)&&o&&(o._count=o._count?o._count+1:1)>30?(uT(`检测到从“${s.fullPath}”到“${r.fullPath}”时导航守卫中可能存在无限重定向。中止以避免堆栈溢出。\n            是否总是在导航防护中返回新位置？这将导致此错误。仅在重定向或中止时返回，这应该可以解决此问题。如果未修复，这可能会在生产中中断`,e.options.debug),Promise.reject(new Error("Infinite redirect in navigation guard"))):(e.lock=!1,e.navigate(n,n.navType,o||r))}return Promise.resolve(t)}})).finally((()=>{e.lock=!1}))}catch(r){return e.lock=!1,Promise.reject(r)}},jump:function(e,t,n){return new Promise(((o,r)=>{const i=RT(e,t);Promise.resolve().then((()=>{const t=[];for(const n of e.guards.beforeGuards.list())t.push(FT(n,i,e.currentRoute.value));return DT(t)})).then((()=>{e.fromRoute=e.currentRoute.value,e.currentRoute.value=i,OT[n](hC(fC({},t),{url:t.fullPath,success(e){var n;null===(n=t.success)||void 0===n||n.call(t,e),o(e)},fail(e){var n;null===(n=t.fail)||void 0===n||n.call(t,e),r(e)},complete(e){var n;null===(n=t.complete)||void 0===n||n.call(t,e)}}))})).catch(r)}))},forceGuardEach:function(e){return dC(this,null,(function*(){const t=function(){const e=Gh();return{path:`/${e.path}`,query:e.query||{}}}(),n=e.resolve(t);let o;if(void 0===n)o=NT(e,t);else{const t=RT(e,n),i=[];for(const n of e.guards.beforeGuards.list())i.push(FT(n,t,xT));try{yield DT(i),e.currentRoute.value=t;const n=[];for(const o of e.guards.afterGuards.list())n.push((()=>dC(this,null,(function*(){o(t,xT)}))));yield DT(n)}catch(r){o=r}}if(dT(o,2))return e.reLaunch(null==o?void 0:o.to)}))}};const gC=Xo({__name:"App",setup(e){const t=Dx(),{getUser:n}=$x(),{getTheme:o}=GS(),r=kT(),i=ET(),s=()=>{const e=t.getWebsiteConfig;let n=document.querySelector('link[rel="icon"]');n?n.href=e.h5_favicon:(n=document.createElement("link"),n.rel="icon",n.href=e.h5_favicon,document.head.appendChild(n))};return Db((async()=>{o(),(async()=>{await t.getConfig(),s();const{status:e,page_status:n,page_url:o}=t.getH5Config;if(!i.meta.webview&&0==e){if(1==n)return location.href=o;r.reLaunch("/pages/empty/empty")}})(),s(),await n()})),()=>{}}});Cm(gC,{init:Sm,setup(e){const t=Ng(),n=()=>{var n;n=e,Object.keys(Yh).forEach((e=>{Yh[e].forEach((t=>{fr(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i}=e,s=function({path:e,query:t}){return x(Fd,{path:e,query:t}),x(qd,Fd),x({},Fd)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:Ze(t.query)});if(o&&Y(o,s),r&&Y(r,s),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};i&&Y(i,e)}};return Mo(Al).isReady().then(n),pr((()=>{window.addEventListener("resize",nt(Em,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",Om),document.addEventListener("visibilitychange",Mm),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}e&&e.addEventListener("change",(e=>{Ay.emit(he,{theme:e.matches?"dark":"light"})}))}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(Si(),Oi(Py));e.setup=(e,o)=>{const r=t&&t(e,o);return M(r)?n:r},e.render=n}});const mC=function(){const e=gt(!0),t=e.run((()=>Bn({})));let n=[],o=[];const r=Pn({install(e){zb(r),r._a=e,e.provide(Hb,r),e.config.globalProperties.$pinia=r,o.forEach((e=>n.push(e))),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}(),vC=Object.freeze(Object.defineProperty({__proto__:null,default:e=>{e.use(mC)}},Symbol.toStringTag,{value:"Module"})),yC={data:()=>({}),onLoad(){this.$u.getRect=this.$uGetRect},methods:{$uGetRect(e,t){return new Promise((n=>{zh().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent=!1),this.parent=this.$u.$parent.call(this,e),this.parent&&(Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]})),this.parentData.value=this.parent.modelValue)},preventEvent(e){e&&e.stopPropagation&&e.stopPropagation()}},onReachBottom(){lh("uOnReachBottom")},beforeUnmount(){if(this.parent&&uni.$u.test.array(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}};function bC(e){if([null,void 0,NaN,!1].includes(e))return e;if("object"!=typeof e&&"function"!=typeof e)return e;var t,n=(t=e,"[object Array]"===Object.prototype.toString.call(t)?[]:{});for(let o in e)e.hasOwnProperty(o)&&(n[o]="object"==typeof e[o]?bC(e[o]):e[o]);return n}function _C(e={},t={}){if("object"!=typeof(e=bC(e))||"object"!=typeof t)return!1;for(var n in t)t.hasOwnProperty(n)&&(n in e?"object"!=typeof e[n]||"object"!=typeof t[n]?e[n]=t[n]:e[n].concat&&t[n].concat?e[n]=e[n].concat(t[n]):e[n]=_C(e[n],t[n]):e[n]=t[n]);return e}function wC(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(var t in e)return!1;return!0}return!1}const xC={email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},mobile:function(e){return/^1[23456789]\d{9}$/.test(e)},url:function(e){return/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-.\/?%&=]*)?/.test(e)},date:function(e){return!/Invalid|NaN/.test(new Date(e).toString())},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:function(e){return/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e)},digits:function(e){return/^\d+$/.test(e)},idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},contains:function(e,t){return e.indexOf(t)>=0},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},empty:wC,isEmpty:wC,jsonString:function(e){if("string"==typeof e)try{var t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(YC){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},object:function(e){return"[object Object]"===Object.prototype.toString.call(e)},array:function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)}};const SC=new class{setConfig(e){this.config=_C(this.config,e)}request(e={}){if(this.interceptor.request&&"function"==typeof this.interceptor.request){let t=this.interceptor.request(e);if(!1===t)return new Promise((()=>{}));this.options=t}return e.dataType=e.dataType||this.config.dataType,e.responseType=e.responseType||this.config.responseType,e.url=e.url||"",e.params=e.params||{},e.header=Object.assign({},this.config.header,e.header),e.method=e.method||this.config.method,new Promise(((t,n)=>{e.complete=e=>{if(ry(),clearTimeout(this.config.timer),this.config.timer=null,this.config.originalData)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e);!1!==o?t(o):n(e)}else t(e);else if(200==e.statusCode)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e.data);!1!==o?t(o):n(e.data)}else t(e.data);else n(e)},e.url=xC.url(e.url)?e.url:this.config.baseUrl+(0==e.url.indexOf("/")?e.url:"/"+e.url),this.config.showLoading&&!this.config.timer&&(this.config.timer=setTimeout((()=>{oy({title:this.config.loadingText,mask:this.config.loadingMask}),this.config.timer=null}),this.config.loadingTime)),Sv(e)}))}constructor(){this.config={baseUrl:"",header:{},method:"POST",dataType:"json",responseType:"text",showLoading:!0,loadingText:"请求中...",loadingTime:800,timer:null,originalData:!1,loadingMask:!0},this.interceptor={request:null,response:null},this.get=(e,t={},n={})=>this.request({method:"GET",url:e,header:n,data:t}),this.post=(e,t={},n={})=>this.request({url:e,method:"POST",header:n,data:t}),this.put=(e,t={},n={})=>this.request({url:e,method:"PUT",header:n,data:t}),this.delete=(e,t={},n={})=>this.request({url:e,method:"DELETE",header:n,data:t})}};const TC=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=uni.$u.queryParams(t,!1),e+"&"+n):(n=uni.$u.queryParams(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=uni.$u.deepClone(e,this.config),n.url=this.mixinParam(e.url,e.params)),t.intercept&&(this.config.intercept=t.intercept),n.params=t,n=uni.$u.deepMerge(this.config,n),"function"==typeof uni.$u.routeIntercept){await new Promise(((e,t)=>{uni.$u.routeIntercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i}=e;"navigateTo"!=e.type&&"to"!=e.type||Iv({url:t,animationType:r,animationDuration:i}),"redirectTo"!=e.type&&"redirect"!=e.type||Pv({url:t}),"switchTab"!=e.type&&"tab"!=e.type||Lv({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||$v({url:t}),"navigateBack"!=e.type&&"back"!=e.type||Ov({delta:o})}}).route;function CC(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n,o=new Date(e),r={"y+":o.getFullYear().toString(),"m+":(o.getMonth()+1).toString(),"d+":o.getDate().toString(),"h+":o.getHours().toString(),"M+":o.getMinutes().toString(),"s+":o.getSeconds().toString()};for(let i in r)n=new RegExp("("+i+")").exec(t),n&&(t=t.replace(n[1],1==n[1].length?r[i]:r[i].padStart(n[1].length,"0")));return t}function kC(e,t=!0){if((e=e.toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}let n=[];for(let t=1;t<7;t+=2)n.push(parseInt("0x"+e.slice(t,t+2)));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function EC(e){let t=e;if(/^(rgb|RGB)/.test(t)){let e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(","),n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?"0"+o:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{let e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");let n=this;if(n.length>=e)return String(n);let o=e-n.length,r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const OC={colorGradient:function(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){let o=kC(e,!1),r=o[0],i=o[1],s=o[2],a=kC(t,!1),l=(a[0]-r)/n,c=(a[1]-i)/n,u=(a[2]-s)/n,f=[];for(let h=0;h<n;h++){let e=EC("rgb("+Math.round(l*h+r)+","+Math.round(c*h+i)+","+Math.round(u*h+s)+")");f.push(e)}return f},hexToRgb:kC,rgbToHex:EC,colorToRgba:function(e,t=.3){let n=(e=EC(e)).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){var o="#";for(let e=1;e<4;e+=1)o+=n.slice(e,e+1).concat(n.slice(e,e+1));n=o}var r=[];for(let e=1;e<7;e+=2)r.push(parseInt("0x"+n.slice(e,e+2)));return"rgba("+r.join(",")+","+t+")"}return n}};let MC=null;let IC=[],PC=[];let $C="1.10.1";const AC={v:$C,version:$C,type:["primary","success","info","error","warning"]};const LC={queryParams:function(e={},t=!0,n="brackets"){let o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(let i in e){let t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(i+"["+n+"]="+t[n]);break;case"brackets":default:t.forEach((e=>{r.push(i+"[]="+e)}));break;case"repeat":t.forEach((e=>{r.push(i+"="+e)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(i+"="+e)}else r.push(i+"="+t)}return r.length?o+r.join("&"):""},route:TC,timeFormat:CC,date:CC,timeFrom:function(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n=+new Date(Number(e)),o=(Number(new Date)-n)/1e3,r="";switch(!0){case o<300:r="刚刚";break;case o>=300&&o<3600:r=parseInt(o/60)+"分钟前";break;case o>=3600&&o<86400:r=parseInt(o/3600)+"小时前";break;case o>=86400&&o<2592e3:r=parseInt(o/86400)+"天前";break;default:r=!1===t?o>=2592e3&&o<31536e3?parseInt(o/2592e3)+"个月前":parseInt(o/31536e3)+"年前":CC(n,t)}return r},colorGradient:OC.colorGradient,colorToRgba:OC.colorToRgba,guid:function(e=32,t=!0,n=null){let o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),"u"+r.join("")):r.join("")},color:{primary:"#2979ff",primaryDark:"#2b85e4",primaryDisabled:"#a0cfff",primaryLight:"#ecf5ff",bgColor:"#f3f4f6",info:"#909399",infoDark:"#82848a",infoDisabled:"#c8c9cc",infoLight:"#f4f4f5",warning:"#ff9900",warningDark:"#f29100",warningDisabled:"#fcbd71",warningLight:"#fdf6ec",error:"#fa3534",errorDark:"#dd6161",errorDisabled:"#fab6b6",errorLight:"#fef0f0",success:"#19be6b",successDark:"#18b566",successDisabled:"#71d5a1",successLight:"#dbf1e1",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},sys:function(){return Um()},os:function(){return Um().platform},type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},wranning:function(e){},get:SC.get,post:SC.post,put:SC.put,delete:SC.delete,hexToRgb:OC.hexToRgb,rgbToHex:OC.rgbToHex,test:xC,random:function(e,t){if(e>=0&&t>0&&t>=e){let n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},deepClone:bC,deepMerge:_C,getParent:function(e,t){let n=this.$parent;for(;n;){if(n.$options.name===e){let e={};if(Array.isArray(t))t.map((t=>{e[t]=n[t]?n[t]:""}));else for(let o in t)Array.isArray(t[o])?t[o].length?e[o]=t[o]:e[o]=n[o]:t[o].constructor===Object?Object.keys(t[o]).length?e[o]=t[o]:e[o]=n[o]:e[o]=t[o]||!1===t[o]?t[o]:n[o];return e}n=n.$parent}return{}},$parent:function(e=void 0){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1},addUnit:function(e="auto",t="rpx"){return e=String(e),xC.number(e)?`${e}${t}`:e},trim:function(e,t="both"){return"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e},type:["primary","success","error","warning","info"],http:SC,toast:function(e,t=1500){ty({title:e,icon:"none",duration:t})},config:AC,zIndex:{toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965},debounce:function(e,t=500,n=!1){if(null!==MC&&clearTimeout(MC),n){var o=!MC;MC=setTimeout((function(){MC=null}),t),o&&"function"==typeof e&&e()}else MC=setTimeout((function(){"function"==typeof e&&e()}),t)},throttle:function(e,t=500,n=!0,o="default"){IC[o]||(IC[o]=null),n?PC[o]||(PC[o]=!0,"function"==typeof e&&e(),IC[o]=setTimeout((()=>{PC[o]=!1}),t)):PC[o]||(PC[o]=!0,IC[o]=setTimeout((()=>{PC[o]=!1,"function"==typeof e&&e()}),t))}};uni.$u=LC;const RC={install:e=>{e.mixin(yC),e.config.globalProperties.$u=LC}},jC=Object.freeze(Object.defineProperty({__proto__:null,default:e=>{e.use(RC)}},Symbol.toStringTag,{value:"Module"})),BC=Object.freeze(Object.defineProperty({__proto__:null,default:async()=>{const e=new URL(location.href);if("47b1e3a9d33e6064e58cc4796c708447"==new URLSearchParams(e.search).get("vconsole")){return new(0,(await o((()=>import("./vconsole.min.61e7227c.js").then((e=>e.v))),["assets/vconsole.min.61e7227c.js","assets/_commonjsHelpers.02d3be64.js"])).default)}}},Symbol.toStringTag,{value:"Module"})),NC=Object.assign({"./modules/pinia.ts":vC,"./modules/uview.ts":jC,"./modules/vconsole.ts":BC}),DC={install:e=>{for(const t of Object.values(NC)){const n=t.default;M(n)&&n(e)}}};var FC=(e=>(e[e.MP_WEIXIN=1]="MP_WEIXIN",e[e.OA_WEIXIN=2]="OA_WEIXIN",e[e.H5=3]="H5",e[e.IOS=5]="IOS",e[e.ANDROID=6]="ANDROID",e))(FC||{}),qC=(e=>(e.LOGIN="YZMDL",e.BIND_MOBILE="BDSJHM",e.CHANGE_MOBILE="BGSJHM",e.FIND_PASSWORD="ZHDLMM",e))(qC||{}),VC=(e=>(e.NONE="",e.AVATAR="avatar",e.USERNAME="account",e.NICKNAME="nickname",e.SEX="sex",e))(VC||{}),WC=(e=>(e.SUCCESS="success",e.FAIL="fail",e.PENDING="pending",e))(WC||{}),zC=(e=>(e.LOADING="loading",e.NORMAL="normal",e.ERROR="error",e.EMPTY="empty",e))(zC||{});const HC=()=>/MicroMessenger/i.test(navigator.userAgent);const UC=({MP_WEIXIN:e,OA_WEIXIN:t,H5:n,IOS:o,ANDROID:r,OTHER:i})=>HC()?t():n(),XC=UC({MP_WEIXIN:()=>FC.MP_WEIXIN,OA_WEIXIN:()=>FC.OA_WEIXIN,H5:()=>FC.H5,IOS:()=>FC.IOS,ANDROID:()=>FC.ANDROID,OTHER:()=>null});var YC,GC={};function JC(e){return Lx.post({url:"/login/account",data:{...e,terminal:XC}})}function QC(e){return Lx.post({url:"/login/register",data:{...e,channel:XC}})}function KC(e){return Lx.post({url:"/login/oaLogin",data:e})}function ZC(e){return Lx.post({url:"/login/oaAuthBind",data:e})}YC=window,{get exports(){return GC},set exports(e){GC=e}}.exports=function(e,t){if(!e.jWeixin){var n,o={config:"preVerifyJSAPI",onMenuShareTimeline:"menu:share:timeline",onMenuShareAppMessage:"menu:share:appmessage",onMenuShareQQ:"menu:share:qq",onMenuShareWeibo:"menu:share:weiboApp",onMenuShareQZone:"menu:share:QZone",previewImage:"imagePreview",getLocation:"geoLocation",openProductSpecificView:"openProductViewWithPid",addCard:"batchAddCard",openCard:"batchViewCard",chooseWXPay:"getBrandWCPayRequest",openEnterpriseRedPacket:"getRecevieBizHongBaoRequest",startSearchBeacons:"startMonitoringBeacons",stopSearchBeacons:"stopMonitoringBeacons",onSearchBeacons:"onBeaconsInRange",consumeAndShareCard:"consumedShareCard",openAddress:"editAddress"},r=function(){var e={};for(var t in o)e[o[t]]=t;return e}(),i=e.document,s=i.title,a=navigator.userAgent.toLowerCase(),l=navigator.platform.toLowerCase(),c=!(!l.match("mac")&&!l.match("win")),u=-1!=a.indexOf("wxdebugger"),f=-1!=a.indexOf("micromessenger"),h=-1!=a.indexOf("android"),d=-1!=a.indexOf("iphone")||-1!=a.indexOf("ipad"),p=(n=a.match(/micromessenger\/(\d+\.\d+\.\d+)/)||a.match(/micromessenger\/(\d+\.\d+)/))?n[1]:"",g={initStartTime:$(),initEndTime:0,preVerifyStartTime:0,preVerifyEndTime:0},m={version:1,appId:"",initTime:0,preVerifyTime:0,networkType:"",isPreVerifyOk:1,systemType:d?1:h?2:-1,clientVersion:p,url:encodeURIComponent(location.href)},v={},y={_completes:[]},b={state:0,data:{}};A((function(){g.initEndTime=$()}));var _=!1,w=[],x={config:function(t){P("config",v=t);var n=!1!==v.check;A((function(){if(n)C(o.config,{verifyJsApiList:I(v.jsApiList),verifyOpenTagList:I(v.openTagList)},function(){y._complete=function(e){g.preVerifyEndTime=$(),b.state=1,b.data=e},y.success=function(e){m.isPreVerifyOk=0},y.fail=function(e){y._fail?y._fail(e):b.state=-1};var e=y._completes;return e.push((function(){!function(){if(!(c||u||v.debug||p<"6.0.2"||m.systemType<0)){var e=new Image;m.appId=v.appId,m.initTime=g.initEndTime-g.initStartTime,m.preVerifyTime=g.preVerifyEndTime-g.preVerifyStartTime,x.getNetworkType({isInnerInvoke:!0,success:function(t){m.networkType=t.networkType;var n="https://open.weixin.qq.com/sdk/report?v="+m.version+"&o="+m.isPreVerifyOk+"&s="+m.systemType+"&c="+m.clientVersion+"&a="+m.appId+"&n="+m.networkType+"&i="+m.initTime+"&p="+m.preVerifyTime+"&u="+m.url;e.src=n}})}}()})),y.complete=function(t){for(var n=0,o=e.length;n<o;++n)e[n]();y._completes=[]},y}()),g.preVerifyStartTime=$();else{b.state=1;for(var e=y._completes,t=0,r=e.length;t<r;++t)e[t]();y._completes=[]}})),x.invoke||(x.invoke=function(t,n,o){e.WeixinJSBridge&&WeixinJSBridge.invoke(t,E(n),o)},x.on=function(t,n){e.WeixinJSBridge&&WeixinJSBridge.on(t,n)})},ready:function(e){0!=b.state?e():(y._completes.push(e),!f&&v.debug&&e())},error:function(e){p<"6.0.2"||(-1==b.state?e(b.data):y._fail=e)},checkJsApi:function(e){C("checkJsApi",{jsApiList:I(e.jsApiList)},(e._complete=function(e){if(h){var t=e.checkResult;t&&(e.checkResult=JSON.parse(t))}e=function(e){var t=e.checkResult;for(var n in t){var o=r[n];o&&(t[o]=t[n],delete t[n])}return e}(e)},e))},onMenuShareTimeline:function(e){k(o.onMenuShareTimeline,{complete:function(){C("shareTimeline",{title:e.title||s,desc:e.title||s,img_url:e.imgUrl||"",link:e.link||location.href,type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareAppMessage:function(e){k(o.onMenuShareAppMessage,{complete:function(t){"favorite"===t.scene?C("sendAppMessage",{title:e.title||s,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""}):C("sendAppMessage",{title:e.title||s,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareQQ:function(e){k(o.onMenuShareQQ,{complete:function(){C("shareQQ",{title:e.title||s,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareWeibo:function(e){k(o.onMenuShareWeibo,{complete:function(){C("shareWeiboApp",{title:e.title||s,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareQZone:function(e){k(o.onMenuShareQZone,{complete:function(){C("shareQZone",{title:e.title||s,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},updateTimelineShareData:function(e){C("updateTimelineShareData",{title:e.title,link:e.link,imgUrl:e.imgUrl},e)},updateAppMessageShareData:function(e){C("updateAppMessageShareData",{title:e.title,desc:e.desc,link:e.link,imgUrl:e.imgUrl},e)},startRecord:function(e){C("startRecord",{},e)},stopRecord:function(e){C("stopRecord",{},e)},onVoiceRecordEnd:function(e){k("onVoiceRecordEnd",e)},playVoice:function(e){C("playVoice",{localId:e.localId},e)},pauseVoice:function(e){C("pauseVoice",{localId:e.localId},e)},stopVoice:function(e){C("stopVoice",{localId:e.localId},e)},onVoicePlayEnd:function(e){k("onVoicePlayEnd",e)},uploadVoice:function(e){C("uploadVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadVoice:function(e){C("downloadVoice",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},translateVoice:function(e){C("translateVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},chooseImage:function(e){C("chooseImage",{scene:"1|2",count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"]},(e._complete=function(e){if(h){var t=e.localIds;try{t&&(e.localIds=JSON.parse(t))}catch(n){}}},e))},getLocation:function(e){},previewImage:function(e){C(o.previewImage,{current:e.current,urls:e.urls},e)},uploadImage:function(e){C("uploadImage",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadImage:function(e){C("downloadImage",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},getLocalImgData:function(e){!1===_?(_=!0,C("getLocalImgData",{localId:e.localId},(e._complete=function(e){if(_=!1,0<w.length){var t=w.shift();wx.getLocalImgData(t)}},e))):w.push(e)},getNetworkType:function(e){C("getNetworkType",{},(e._complete=function(e){e=function(e){var t=e.errMsg;e.errMsg="getNetworkType:ok";var n=e.subtype;if(delete e.subtype,n)e.networkType=n;else{var o=t.indexOf(":"),r=t.substring(o+1);switch(r){case"wifi":case"edge":case"wwan":e.networkType=r;break;default:e.errMsg="getNetworkType:fail"}}return e}(e)},e))},openLocation:function(e){C("openLocation",{latitude:e.latitude,longitude:e.longitude,name:e.name||"",address:e.address||"",scale:e.scale||28,infoUrl:e.infoUrl||""},e)},getLocation:function(e){C(o.getLocation,{type:(e=e||{}).type||"wgs84"},(e._complete=function(e){delete e.type},e))},hideOptionMenu:function(e){C("hideOptionMenu",{},e)},showOptionMenu:function(e){C("showOptionMenu",{},e)},closeWindow:function(e){C("closeWindow",{},e=e||{})},hideMenuItems:function(e){C("hideMenuItems",{menuList:e.menuList},e)},showMenuItems:function(e){C("showMenuItems",{menuList:e.menuList},e)},hideAllNonBaseMenuItem:function(e){C("hideAllNonBaseMenuItem",{},e)},showAllNonBaseMenuItem:function(e){C("showAllNonBaseMenuItem",{},e)},scanQRCode:function(e){C("scanQRCode",{needResult:(e=e||{}).needResult||0,scanType:e.scanType||["qrCode","barCode"]},(e._complete=function(e){if(d){var t=e.resultStr;if(t){var n=JSON.parse(t);e.resultStr=n&&n.scan_code&&n.scan_code.scan_result}}},e))},openAddress:function(e){C(o.openAddress,{},(e._complete=function(e){var t;(t=e).postalCode=t.addressPostalCode,delete t.addressPostalCode,t.provinceName=t.proviceFirstStageName,delete t.proviceFirstStageName,t.cityName=t.addressCitySecondStageName,delete t.addressCitySecondStageName,t.countryName=t.addressCountiesThirdStageName,delete t.addressCountiesThirdStageName,t.detailInfo=t.addressDetailInfo,delete t.addressDetailInfo,e=t},e))},openProductSpecificView:function(e){C(o.openProductSpecificView,{pid:e.productId,view_type:e.viewType||0,ext_info:e.extInfo},e)},addCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var s=t[r],a={card_id:s.cardId,card_ext:s.cardExt};n.push(a)}C(o.addCard,{card_list:n},(e._complete=function(e){var t=e.card_list;if(t){for(var n=0,o=(t=JSON.parse(t)).length;n<o;++n){var r=t[n];r.cardId=r.card_id,r.cardExt=r.card_ext,r.isSuccess=!!r.is_succ,delete r.card_id,delete r.card_ext,delete r.is_succ}e.cardList=t,delete e.card_list}},e))},chooseCard:function(e){C("chooseCard",{app_id:v.appId,location_id:e.shopId||"",sign_type:e.signType||"SHA1",card_id:e.cardId||"",card_type:e.cardType||"",card_sign:e.cardSign,time_stamp:e.timestamp+"",nonce_str:e.nonceStr},(e._complete=function(e){e.cardList=e.choose_card_info,delete e.choose_card_info},e))},openCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var s=t[r],a={card_id:s.cardId,code:s.code};n.push(a)}C(o.openCard,{card_list:n},e)},consumeAndShareCard:function(e){C(o.consumeAndShareCard,{consumedCardId:e.cardId,consumedCode:e.code},e)},chooseWXPay:function(e){C(o.chooseWXPay,O(e),e)},openEnterpriseRedPacket:function(e){C(o.openEnterpriseRedPacket,O(e),e)},startSearchBeacons:function(e){C(o.startSearchBeacons,{ticket:e.ticket},e)},stopSearchBeacons:function(e){C(o.stopSearchBeacons,{},e)},onSearchBeacons:function(e){k(o.onSearchBeacons,e)},openEnterpriseChat:function(e){C("openEnterpriseChat",{useridlist:e.userIds,chatname:e.groupName},e)},launchMiniProgram:function(e){C("launchMiniProgram",{targetAppId:e.targetAppId,path:function(e){if("string"==typeof e&&0<e.length){var t=e.split("?")[0],n=e.split("?")[1];return t+=".html",void 0!==n?t+"?"+n:t}}(e.path),envVersion:e.envVersion},e)},openBusinessView:function(e){C("openBusinessView",{businessType:e.businessType,queryString:e.queryString||"",envVersion:e.envVersion},(e._complete=function(e){if(h){var t=e.extraData;if(t)try{e.extraData=JSON.parse(t)}catch(n){e.extraData={}}}},e))},miniProgram:{navigateBack:function(e){e=e||{},A((function(){C("invokeMiniProgramAPI",{name:"navigateBack",arg:{delta:e.delta||1}},e)}))},navigateTo:function(e){A((function(){C("invokeMiniProgramAPI",{name:"navigateTo",arg:{url:e.url}},e)}))},redirectTo:function(e){A((function(){C("invokeMiniProgramAPI",{name:"redirectTo",arg:{url:e.url}},e)}))},switchTab:function(e){A((function(){C("invokeMiniProgramAPI",{name:"switchTab",arg:{url:e.url}},e)}))},reLaunch:function(e){A((function(){C("invokeMiniProgramAPI",{name:"reLaunch",arg:{url:e.url}},e)}))},postMessage:function(e){A((function(){C("invokeMiniProgramAPI",{name:"postMessage",arg:e.data||{}},e)}))},getEnv:function(t){A((function(){t({miniprogram:"miniprogram"===e.__wxjs_environment})}))}}},S=1,T={};return i.addEventListener("error",(function(e){if(!h){var t=e.target,n=t.tagName,o=t.src;if(("IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n)&&-1!=o.indexOf("wxlocalresource://")){e.preventDefault(),e.stopPropagation();var r=t["wx-id"];if(r||(r=S++,t["wx-id"]=r),T[r])return;T[r]=!0,wx.ready((function(){wx.getLocalImgData({localId:o,success:function(e){t.src=e.localData}})}))}}}),!0),i.addEventListener("load",(function(e){if(!h){var t=e.target,n=t.tagName;if(t.src,"IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n){var o=t["wx-id"];o&&(T[o]=!1)}}}),!0),t&&(e.wx=e.jWeixin=x),x}function C(t,n,o){e.WeixinJSBridge?WeixinJSBridge.invoke(t,E(n),(function(e){M(t,e,o)})):P(t,o)}function k(t,n,o){e.WeixinJSBridge?WeixinJSBridge.on(t,(function(e){o&&o.trigger&&o.trigger(e),M(t,e,n)})):P(t,o||n)}function E(e){return(e=e||{}).appId=v.appId,e.verifyAppId=v.appId,e.verifySignType="sha1",e.verifyTimestamp=v.timestamp+"",e.verifyNonceStr=v.nonceStr,e.verifySignature=v.signature,e}function O(e){return{timeStamp:e.timestamp+"",nonceStr:e.nonceStr,package:e.package,paySign:e.paySign,signType:e.signType||"SHA1"}}function M(e,t,n){"openEnterpriseChat"!=e&&"openBusinessView"!==e||(t.errCode=t.err_code),delete t.err_code,delete t.err_desc,delete t.err_detail;var o=t.errMsg;o||(o=t.err_msg,delete t.err_msg,o=function(e,t){var n=e,o=r[n];o&&(n=o);var i="ok";if(t){var s=t.indexOf(":");"confirm"==(i=t.substring(s+1))&&(i="ok"),"failed"==i&&(i="fail"),-1!=i.indexOf("failed_")&&(i=i.substring(7)),-1!=i.indexOf("fail_")&&(i=i.substring(5)),"access denied"!=(i=(i=i.replace(/_/g," ")).toLowerCase())&&"no permission to execute"!=i||(i="permission denied"),"config"==n&&"function not exist"==i&&(i="ok"),""==i&&(i="fail")}return n+":"+i}(e,o),t.errMsg=o),(n=n||{})._complete&&(n._complete(t),delete n._complete),o=t.errMsg||"",v.debug&&!n.isInnerInvoke&&alert(JSON.stringify(t));var i=o.indexOf(":");switch(o.substring(i+1)){case"ok":n.success&&n.success(t);break;case"cancel":n.cancel&&n.cancel(t);break;default:n.fail&&n.fail(t)}n.complete&&n.complete(t)}function I(e){if(e){for(var t=0,n=e.length;t<n;++t){var r=e[t],i=o[r];i&&(e[t]=i)}return e}}function P(e,t){if(!(!v.debug||t&&t.isInnerInvoke)){var n=r[e];n&&(e=n),t&&t._complete&&delete t._complete,console.log('"'+e+'",',t||"")}}function $(){return(new Date).getTime()}function A(t){f&&(e.WeixinJSBridge?t():i.addEventListener&&i.addEventListener("WeixinJSBridgeReady",t,!1))}}(YC);const ek=(e,t=!1,n)=>new Promise(((o,r)=>{let i=zh();n&&(i=zh().in(n)),i[t?"selectAll":"select"](e).boundingClientRect((function(e){return t&&Array.isArray(e)&&e.length||!t&&e?o(e):void r("找不到元素")})).exec()}));function tk(e,t="navigateTo"){if("mini_program"===e.type)return void function(e){const t=e.query;window.open(`weixin://dl/business/?appid=${null==t?void 0:t.appId}&path=${null==t?void 0:t.path}&env_version=${null==t?void 0:t.env_version}&query=${encodeURIComponent(null==t?void 0:t.query)}`)}(e);const n=(null==e?void 0:e.query)?`${e.path}?${rk(null==e?void 0:e.query)}`:e.path;("switchTab"==t||e.canTab)&&Lv({url:n}),"navigateTo"==t&&Iv({url:n}),"reLaunch"==t&&$v({url:n})}const nk=(e,t)=>{const n=[];for(let o=0;o<Math.ceil(e.length/t);o++){const r=o*t,i=r+t;n.push(e.slice(r,i))}return n},ok=e=>null==e&&void 0===e;function rk(e){let t="";for(const n of Object.keys(e)){const o=e[n],r=encodeURIComponent(n)+"=";if(!ok(o))if(console.log(encodeURIComponent(n),$(o)),$(o)){for(const e of Object.keys(o))if(!ok(o[e])){t+=encodeURIComponent(n+"["+e+"]")+"="+encodeURIComponent(o[e])+"&"}}else t+=r+encodeURIComponent(o)+"&"}return t.slice(0,-1)}const ik=(e,t="rpx")=>Object.is(Number(e),NaN)?e:`${e}${t}`;function sk({price:e,take:t="all",prec:n}){let[o,r=""]=(e+"").split(".");if(void 0!==n){for(let e=n-r.length;e>0;--e)r+="0";r=r.substr(0,n)}switch(t){case"int":return o;case"dec":return r;case"all":return o+"."+r}}function ak(...e){return function(){return new Promise(((t,n)=>{const o=e.values(),r=e=>{const i=o.next();i.done?t(e):Promise.resolve(i.value(e)).then(r).catch(n)};r()}))}}var lk=(e=>(e.LOGIN="login",e.PC_LOGIN="pcLogin",e.BIND_WX="bindWx",e.BASE="base",e))(lk||{});const ck={_authData:{code:"",scene:""},setAuthData(e={}){this._authData=e},getAuthData(){return this._authData},getSignLink:()=>(void 0!==window.signLink&&""!==window.signLink||(window.signLink=location.href.split("#")[0]),function(){const e=navigator.userAgent;return e.indexOf("Android")>-1||e.indexOf("Adr")>-1}()?location.href.split("#")[0]:window.signLink),getUrl(e,t="snsapi_userinfo",n={}){const o=`${location.href}${location.search?"&":"?"}scene=${e||""}&${rk(n)}`;return new Promise(((e,n)=>{var r;(r={url:o,scope:t},Lx.get({url:"/login/codeUrl",data:r})).then((t=>{location.href=t.url,e()}),n)}))},config(){return new Promise(((e,t)=>{var n;(n={url:this.getSignLink()},Lx.get({url:"/wechat/jsConfig",data:n})).then((n=>{GC.config({...n,success:()=>{e("success")},fail:e=>{t("wx config is fail")}})}))}))},miniProgram:GC.miniProgram,ready:()=>new Promise(((e,t)=>{GC.ready((()=>{e()})),GC.error((()=>{t()}))})),pay(e){return new Promise(((t,n)=>{this.ready().then((()=>{GC.chooseWXPay({timestamp:e.timeStamp,nonceStr:e.nonceStr,package:e.package,signType:e.signType,paySign:e.paySign,success:e=>{"chooseWXPay:ok"===e.errMsg?t(e):n(e.errMsg)},cancel:e=>{n(e)},fail:e=>{n(e)}})})).catch((e=>{n(e)}))}))},async share(e){return new Promise(((t,n)=>{this.ready().then((()=>{const{title:o,link:r,imgUrl:i,desc:s}=e,a=["updateTimelineShareData","updateAppMessageShareData"];for(const e of a)GC[e]({title:o,link:r,imgUrl:i,desc:s,success(){t()},fail(){n()}})})).catch(n)}))},getAddress(){return new Promise(((e,t)=>{this.ready().then((()=>{GC.openAddress({success:t=>{e(t)},fail:e=>{t(e)}})}))}))},getLocation(){return new Promise(((e,t)=>{this.ready().then((()=>{GC.getLocation({type:"gcj02",success:t=>{e(t)},fail:e=>{t(e)}})}))}))},hideMenuItems(e){return new Promise(((t,n)=>{this.ready().then((()=>{GC.hideMenuItems({menuList:e,success:e=>{t(e)},fail:e=>{n(e)}})}))}))},showMenuItems(e){return new Promise(((t,n)=>{this.ready().then((()=>{GC.showMenuItems({menuList:e,success:e=>{t(e)},fail:e=>{n(e)}})}))}))}},uk=function(e){const t={beforeGuards:JS(),afterGuards:JS()},n=Nn(xT),o=LT(e),r="h5"===e.platform?oC:pC;function i(e,t="navigateTo",n){return new Promise(((o,i)=>{let a={};var l;null!=(l=e)&&"object"==typeof l&&(a=e),r.navigate(s,e,t,n).then((e=>{o(e)})).catch((e=>{var t,n;null===(t=a.fail)||void 0===t||t.call(a,e),null===(n=a.complete)||void 0===n||n.call(a,e),i(e)}))}))}const s={level:0,lock:!1,currentRoute:n,guards:t,options:e,vueRouter:null,routeMatcher:o,parseQuery:e.parseQuery||MT,stringifyQuery:e.stringifyQuery||PT,jump:function(e,t){return new Promise(((n,o)=>{r.jump(s,e,t).then(n).catch(o)}))},navigateTo:e=>i(e,"navigateTo"),switchTab:e=>i(e,"switchTab"),redirectTo:e=>i(e,"redirectTo"),reLaunch:e=>i(e,"reLaunch"),navigateBack:(e={delta:1})=>i(e,"navigateBack"),navigate:i,resolve:function(e,t="navigateTo"){return r.resolve(s,e,t)},forceGuardEach:function(){return new Promise(((e,t)=>{r.forceGuardEach(s).then(e).catch(t)}))},beforeEach(e){t.beforeGuards.add(e)},afterEach(e){t.afterGuards.add(e)},install(e){const t={};for(const r in xT)t[r]=os((()=>n.value[r]));e.config.globalProperties.$uniRouter=s,Object.defineProperty(e.config.globalProperties,"$uniRoute",{enumerable:!0,get:()=>qn(n)}),e.provide(TT,s),e.provide(CT,xn(t));const o=e.mount;e.mount=function(...t){return function(e){bT.forEach((t=>{uni[t]=function(n){return e[t](n)}}))}(s),r.mount(e,s),UT(e,s),console.log("%c uni-router %c v1.2.7 ","padding: 2px 1px; border-radius: 3px 0 0 3px; color: #fff; background: #606060; font-weight: bold;","padding: 2px 1px; border-radius: 0 3px 3px 0; color: #fff; background: #42c02e; font-weight: bold;"),o(...t)}}};return s}({routes:[{path:"/pages/index/index",aliasPath:"/"},{path:"/pages/news/news"},{path:"/pages/user/user"},{path:"/pages/login/login",meta:{white:!0}},{path:"/pages/register/register",meta:{white:!0}},{path:"/pages/forget_pwd/forget_pwd",meta:{white:!0}},{path:"/pages/customer_service/customer_service",meta:{white:!0}},{path:"/pages/news_detail/news_detail"},{path:"/pages/user_set/user_set",meta:{auth:!0}},{path:"/pages/collection/collection",meta:{auth:!0}},{path:"/pages/as_us/as_us"},{path:"/pages/agreement/agreement"},{path:"/pages/change_password/change_password",meta:{auth:!0}},{path:"/pages/user_data/user_data",meta:{auth:!0}},{path:"/pages/search/search"},{path:"/pages/webview/webview"},{path:"/pages/bind_mobile/bind_mobile"},{path:"/pages/empty/empty"},{path:"/pages/payment_result/payment_result",meta:{auth:!0}},{path:"/uni_modules/vk-uview-ui/components/u-avatar-cropper/u-avatar-cropper"},{path:"/packages/pages/404/404",name:"404",meta:{white:!0}},{path:"/packages/pages/user_wallet/user_wallet",meta:{auth:!0}},{path:"/packages/pages/recharge/recharge",meta:{auth:!0}},{path:"/packages/pages/recharge_record/recharge_record",meta:{auth:!0}},{path:"*",redirect:()=>({name:"404"})}],debug:!1,platform:"h5",h5:{}});let fk=!0;uk.beforeEach((async(e,t)=>{if(fk){$x().isLogin||e.meta.white||Tx.set(Sx,e.fullPath),fk=!1}})),uk.afterEach(((e,t)=>{$x().isLogin||e.meta.white||Tx.set(Sx,e.fullPath)})),uk.beforeEach((async(e,t)=>{if(!$x().isLogin&&e.meta.auth)return"/pages/login/login"})),uk.beforeEach((async(e,t)=>{const{code:n,state:o,scene:r}=e.query;if(n&&o&&r)return ck.setAuthData({code:n,scene:r}),delete e.query.code,delete e.query.state,{path:e.path,force:!0,navType:"reLaunch",query:e.query}})),uk.afterEach(((e,t)=>{setTimeout((async()=>{XC!=FC.OA_WEIXIN||e.meta.webview||await ck.config()}))}));const hk={computed:{$theme(){const e=GS(),t=Dx();return{primaryColor:e.primaryColor,pageStyle:e.vars,navColor:e.navColor,navBgColor:e.navBgColor,title:t.getWebsiteConfig.shop_name}}}};(function(){const e=Ks(gC);return function(e){e.mixin(hk)}(e),e.use(DC),e.use(uk),{app:e}})().app.use(pm).mount("#app");export{Nb as $,$o as A,nk as B,rh as C,fy as D,$r as E,vi as F,xn as G,Vb as H,Fb as I,Fx as J,uy as K,Mo as L,pr as M,Xi as N,jh as O,Oo as P,so as Q,ek as R,gg as S,zh as T,fg as U,Nn as V,Gm as W,Iv as X,Li as Y,$x as Z,r_ as _,Or as a,Zd as a$,qx as a0,yy as a1,Kg as a2,yg as a3,Vi as a4,HC as a5,ck as a6,lk as a7,oy as a8,ry as a9,VC as aA,qb as aB,kx as aC,Ox as aD,Ex as aE,Cp as aF,Uu as aG,xx as aH,Sr as aI,Ys as aJ,Vx as aK,zv as aL,tv as aM,Lx as aN,Am as aO,Zm as aP,ev as aQ,Qm as aR,xg as aS,zC as aT,Oh as aU,nv as aV,Ih as aW,Mh as aX,cy as aY,sv as aZ,lh as a_,ET as aa,jx as ab,qC as ac,Tx as ad,Sx as ae,KC as af,JC as ag,QC as ah,Ov as ai,Ix as aj,ZC as ak,Ym as al,Wp as am,Ip as an,Bx as ao,Um as ap,xv as aq,Mx as ar,ik as as,sh as at,yr as au,ah as av,Nx as aw,Ju as ax,cg as ay,tg as az,Oi as b,_T as b0,Px as b1,sk as b2,UC as b3,WC as b4,jn as b5,ak as b6,FC as b7,XC as b8,Jm as b9,xm as ba,ha as bb,Uh as bc,hy as bd,kr as be,Hh as bf,Xh as bg,os as c,Xo as d,Ri as e,Ni as f,Ei as g,Pr as h,eg as i,u as j,Xs as k,Bi as l,ap as m,i as n,Si as o,Sg as p,dg as q,jb as r,Io as s,d as t,Dx as u,kT as v,xo as w,qn as x,tk as y,Bn as z};
