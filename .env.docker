# Docker 环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# 数据库配置
DATABASE_TYPE=mysql
DATABASE_HOSTNAME=likeadmin-mysql
DATABASE_DATABASE=localhost_likeadmin
DATABASE_USERNAME=root
DATABASE_PASSWORD=root
DATABASE_HOSTPORT=3306
DATABASE_CHARSET=utf8mb4
DATABASE_PREFIX=la_

# Redis 配置
CACHE_DRIVER=redis
CACHE_HOST=likeadmin-redis
CACHE_PORT=6379
CACHE_PASSWORD=

# 应用配置
APP_DEBUG=false
APP_TRACE=false

# 其他配置
TIMEZONE=Asia/Shanghai
