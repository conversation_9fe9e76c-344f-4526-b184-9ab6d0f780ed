# Docker 环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# 数据库配置
database.type=mysql
database.hostname=likeadmin-mysql
database.database=localhost_likeadmin
database.username=root
database.password=root
database.hostport=3306
database.charset=utf8mb4
database.prefix=la_

# Redis 配置
cache.driver=redis
cache.host=likeadmin-redis
cache.port=6379
cache.password=

# 应用配置
APP_DEBUG=false
APP_TRACE=false

# 其他配置
TIMEZONE=Asia/Shanghai
