import{d as u,eN as l,u as p,c as _,ci as t,o as g,C as f,w as a,b as o,m as h,bm as b,e as y,q as N,ea as w,x as v}from"./index-B2xNDy79.js";const x={class:"image-slot"},I=u({__name:"decoration-img",props:{width:{type:[String,Number],default:"auto"},height:{type:[String,Number],default:"auto"},radius:{type:[String,Number],default:0},...l},setup(r){const e=r,{getImageUrl:i}=p(),n=_(()=>({width:t(e.width),height:t(e.height),borderRadius:t(e.radius)}));return(c,s)=>{const d=N,m=w;return g(),f(m,b({style:n.value},e,{src:y(i)(c.src)}),{placeholder:a(()=>s[0]||(s[0]=[o("div",{class:"image-slot"},null,-1)])),error:a(()=>[o("div",x,[h(d,{name:"el-icon-Picture",size:30})])]),_:1},16,["style","src"])}}}),B=v(I,[["__scopeId","data-v-2e762fcb"]]);export{B as default};
