import{d as f,eO as _,o as x,a as V,m as l,e as s,D as a,w as C,p as k,eP as v,E as y,v as E}from"./index-B2xNDy79.js";const B={class:"color-select flex flex-1"},N=f({__name:"index",props:{modelValue:{type:String,default:""},resetColor:{type:String,default:""}},emits:["update:modelValue"],setup(r,{emit:u}){const n=r,e=_(n,"modelValue",u),d=["#FF2C3C","#f7971e","#fa444d","#e0a356","#2f80ed","#2ec840"],p=()=>{e.value=n.resetColor};return(w,o)=>{const m=v,c=y,i=E;return x(),V("div",B,[l(m,{modelValue:s(e),"onUpdate:modelValue":o[0]||(o[0]=t=>a(e)?e.value=t:null),predefine:d},null,8,["modelValue"]),l(c,{modelValue:s(e),"onUpdate:modelValue":o[1]||(o[1]=t=>a(e)?e.value=t:null),class:"mx-[10px] flex-1",type:"text",readonly:""},null,8,["modelValue"]),l(i,{type:"text",onClick:p},{default:C(()=>o[2]||(o[2]=[k("重置")])),_:1})])}}});export{N as _};
