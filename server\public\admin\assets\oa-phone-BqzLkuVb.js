import{d as w,W as B,e as t,X as F,c as V,O as _,o as s,a as n,b as o,m as l,w as p,F as h,r as f,Y as j,t as v,B as D,R as G,G as N,S as O,x as P}from"./index-B2xNDy79.js";import{u as x}from"./useMenuOa-CrPjRcCC.js";import"./wx_oa-3-DCeMZg.js";const A={class:"oa-phone mr-[35px]"},E={class:"flex oa-phone-menu"},L={class:"flex items-center justify-center oa-phone-menu-switch"},M=["onClick"],z={class:"oa-phone-menu-subitem"},R=w({__name:"oa-phone",setup(W){B(k=>({"3a57b789":t(b)}));const C=F(),b=V(()=>C.theme||"#4A5DFF"),{menuList:i,menuIndex:r,handleAddMenu:u}=x(x);return(k,a)=>{const y=_("Grid"),d=O,g=_("Plus");return s(),n("div",A,[a[1]||(a[1]=o("div",{class:"oa-phone-content"},null,-1)),o("div",E,[o("div",L,[l(d,null,{default:p(()=>[l(y)]),_:1})]),(s(!0),n(h,null,f(t(i),(e,c)=>(s(),n("div",{key:c,class:"relative flex-1",onClick:m=>r.value=c},[o("div",{class:j(["flex items-center justify-center flex-1 text-sm oa-phone-menu-item",{"active-menu":t(r)===c}])},v(e.name),3),D(o("div",z,[(s(!0),n(h,null,f(e.sub_button,(m,S)=>(s(),n("div",{key:S,class:"oa-phone-menu-subitem-title"},v(m.name),1))),128))],512),[[G,e.sub_button.length&&e.has_menu]])],8,M))),128)),t(i).length<=2?(s(),n("div",{key:0,class:"flex items-center justify-center flex-1 h-full",onClick:a[0]||(a[0]=(...e)=>t(u)&&t(u)(...e))},[l(d,null,{default:p(()=>[l(g)]),_:1})])):N("",!0)])])}}}),q=P(R,[["__scopeId","data-v-de73b1d1"]]);export{q as default};
