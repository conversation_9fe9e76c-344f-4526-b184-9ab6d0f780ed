import{d as E,j as K,N,O as $,z as D,o as i,a as v,m as e,w as l,e as t,n as R,F as j,r as q,a0 as A,C as _,p as u,B as g,Q as I,b as O,D as M,E as Q,v as S,$ as G,K as H}from"./index-B2xNDy79.js";import{_ as J}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as W,a as X}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as Y}from"./el-card-DpH4mUSc.js";import{E as Z,a as ee}from"./el-form-item-DlU85AZK.js";import{_ as te}from"./index.vue_vue_type_script_setup_true_lang-DpKD7KQ8.js";import{E as ae,a as oe}from"./el-select-BRdnbwTl.js";import{_ as le}from"./index.vue_vue_type_script_setup_true_lang-C6FOnW93.js";import{b as w}from"./consumer-ZarxRGL3.js";import{u as ne}from"./usePaging-Dm2wALfy.js";import"./isEqual-CLGO95LP.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./_baseClone-CdezRMKA.js";/* empty css                       */import"./el-radio-CKcO4hVq.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";const re={class:"flex justify-end mt-4"},se=E({name:"consumerLists"}),Le=E({...se,setup(ie){const n=K({keyword:"",channel:"",create_time_start:"",create_time_end:""}),{pager:s,getLists:p,resetPage:c,resetParams:k}=ne({fetchFun:w,params:n});return N(()=>{p()}),p(),(me,a)=>{const C=Q,m=Z,y=le,V=ae,h=oe,d=S,x=te,T=ee,f=Y,z=G,r=W,B=$("router-link"),U=X,F=J,L=D("perms"),P=H;return i(),v("div",null,[e(f,{class:"!border-none",shadow:"never"},{default:l(()=>[e(T,{ref:"formRef",class:"mb-[-16px]",model:t(n),inline:!0},{default:l(()=>[e(m,{class:"w-[280px]",label:"用户信息"},{default:l(()=>[e(C,{modelValue:t(n).keyword,"onUpdate:modelValue":a[0]||(a[0]=o=>t(n).keyword=o),placeholder:"账号/昵称/手机号码",clearable:"",onKeyup:R(t(c),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(m,{label:"注册时间"},{default:l(()=>[e(y,{startTime:t(n).create_time_start,"onUpdate:startTime":a[1]||(a[1]=o=>t(n).create_time_start=o),endTime:t(n).create_time_end,"onUpdate:endTime":a[2]||(a[2]=o=>t(n).create_time_end=o)},null,8,["startTime","endTime"])]),_:1}),e(m,{class:"w-[280px]",label:"注册来源"},{default:l(()=>[e(h,{modelValue:t(n).channel,"onUpdate:modelValue":a[3]||(a[3]=o=>t(n).channel=o)},{default:l(()=>[(i(!0),v(j,null,q(t(A),(o,b)=>(i(),_(V,{key:b,label:o,value:b},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(m,null,{default:l(()=>[e(d,{type:"primary",onClick:t(c)},{default:l(()=>a[5]||(a[5]=[u("查询")])),_:1},8,["onClick"]),e(d,{onClick:t(k)},{default:l(()=>a[6]||(a[6]=[u("重置")])),_:1},8,["onClick"]),e(x,{class:"ml-2.5","fetch-fun":t(w),params:t(n),"page-size":t(s).size},null,8,["fetch-fun","params","page-size"])]),_:1})]),_:1},8,["model"])]),_:1}),e(f,{class:"!border-none mt-4",shadow:"never"},{default:l(()=>[g((i(),_(U,{size:"large",data:t(s).lists},{default:l(()=>[e(r,{label:"头像","min-width":"100"},{default:l(({row:o})=>[e(z,{src:o.avatar,size:50},null,8,["src"])]),_:1}),e(r,{label:"昵称",prop:"nickname","min-width":"100"}),e(r,{label:"账号",prop:"account","min-width":"120"}),e(r,{label:"手机号码",prop:"mobile","min-width":"100"}),e(r,{label:"注册来源",prop:"channel","min-width":"100"}),e(r,{label:"注册时间",prop:"create_time","min-width":"120"}),e(r,{label:"操作",width:"120",fixed:"right"},{default:l(({row:o})=>[g((i(),_(d,{type:"primary",link:""},{default:l(()=>[e(B,{to:{path:t(I)("user.user/detail"),query:{id:o.id}}},{default:l(()=>a[7]||(a[7]=[u(" 详情 ")])),_:2},1032,["to"])]),_:2},1024)),[[L,["user.user/detail"]]])]),_:1})]),_:1},8,["data"])),[[P,t(s).loading]]),O("div",re,[e(F,{modelValue:t(s),"onUpdate:modelValue":a[4]||(a[4]=o=>M(s)?s.value=o:null),onChange:t(p)},null,8,["modelValue","onChange"])])]),_:1})])}}});export{Le as default};
