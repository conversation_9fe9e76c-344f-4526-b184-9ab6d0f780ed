import{d as S,s as c,j as w,o as d,a as I,m as a,w as o,e as l,b as x,t as T,C as i,G as _,p as y,E as A}from"./index-B2xNDy79.js";import{E as K,a as U}from"./el-form-item-DlU85AZK.js";/* empty css                       */import{E as Y,a as q}from"./el-radio-CKcO4hVq.js";import{b as B,c as N}from"./message-BcHkSWHo.js";import{P as F}from"./index-DFOp_83R.js";const G={class:"edit-popup"},Q=S({__name:"edit",emits:["success"],setup(j,{expose:E,emit:g}){const V=g,n=c(),m=c(),t=w({name:"",type:"",sign:"",app_key:"",app_id:"",secret_key:"",secret_id:"",status:0}),k={sign:[{required:!0,message:"请输入短信签名",trigger:"blur"}],app_id:[{required:!0,message:"请输入APP_ID",trigger:"blur"}],app_key:[{required:!0,message:"请输入APP_KEY",trigger:"blur"}],secret_key:[{required:!0,message:"请输入SECRET_KEY",trigger:"blur"}],secret_id:[{required:!0,message:"请输入SECRET_ID",trigger:"blur"}]},b=async()=>{var s,e;await((s=n.value)==null?void 0:s.validate()),await B(t),(e=m.value)==null||e.close(),V("success")},C=async()=>{const s=await N({type:t.type});for(const e in s)t[e]=s[e]},P=s=>{var e;t.type=s,(e=m.value)==null||e.open(),C()},R=()=>{var s;(s=n.value)==null||s.resetFields()};return E({open:P}),(s,e)=>{const p=K,u=A,f=Y,v=q,D=U;return d(),I("div",G,[a(F,{ref_key:"popupRef",ref:m,title:"设置短信",async:!0,width:"550px",onConfirm:b,onClose:R},{default:o(()=>[a(D,{ref_key:"formRef",ref:n,model:l(t),"label-width":"120px",rules:k},{default:o(()=>[a(p,{label:"短信渠道"},{default:o(()=>[x("div",null,T(l(t).name),1)]),_:1}),a(p,{label:"短信签名",prop:"sign"},{default:o(()=>[a(u,{modelValue:l(t).sign,"onUpdate:modelValue":e[0]||(e[0]=r=>l(t).sign=r),placeholder:"请输入短信签名"},null,8,["modelValue"])]),_:1}),l(t).type=="ali"?(d(),i(p,{key:0,label:"APP_KEY",prop:"app_key"},{default:o(()=>[a(u,{modelValue:l(t).app_key,"onUpdate:modelValue":e[1]||(e[1]=r=>l(t).app_key=r),placeholder:"请输入APP_KEY"},null,8,["modelValue"])]),_:1})):_("",!0),l(t).type=="tencent"?(d(),i(p,{key:1,label:"APP_ID",prop:"app_id"},{default:o(()=>[a(u,{modelValue:l(t).app_id,"onUpdate:modelValue":e[2]||(e[2]=r=>l(t).app_id=r),placeholder:"请输入APP_ID"},null,8,["modelValue"])]),_:1})):_("",!0),l(t).type=="tencent"?(d(),i(p,{key:2,label:"SECRET_ID",prop:"secret_id"},{default:o(()=>[a(u,{modelValue:l(t).secret_id,"onUpdate:modelValue":e[3]||(e[3]=r=>l(t).secret_id=r),placeholder:"请输入SECRET_ID"},null,8,["modelValue"])]),_:1})):_("",!0),a(p,{label:"SECRET_KEY",prop:"secret_key"},{default:o(()=>[a(u,{modelValue:l(t).secret_key,"onUpdate:modelValue":e[4]||(e[4]=r=>l(t).secret_key=r),placeholder:"请输入SECRET_KEY"},null,8,["modelValue"])]),_:1}),a(p,{label:"状态",prop:"status"},{default:o(()=>[a(v,{modelValue:l(t).status,"onUpdate:modelValue":e[5]||(e[5]=r=>l(t).status=r)},{default:o(()=>[a(f,{value:0},{default:o(()=>e[6]||(e[6]=[y("关闭")])),_:1}),a(f,{value:1},{default:o(()=>e[7]||(e[7]=[y("开启")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},512)])}}});export{Q as _};
