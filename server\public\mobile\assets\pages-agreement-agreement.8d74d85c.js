import{d as t,z as a,I as e,o as s,b as r,w as p,e as n,ao as l,K as o,r as u,a as i,p as m}from"./index-561dd99e.js";import{_ as c}from"./u-parse.f0500461.js";import"./_plugin-vue_export-helper.1b428a4d.js";const _=t({__name:"agreement",setup(t){let _=a("");const d=a("");return e((t=>{t.type&&(_=t.type,(async t=>{const a=await l({type:t});d.value=a.content,o({title:String(a.title)})})(_))})),(t,a)=>{const e=u(i("u-parse"),c),l=m;return s(),r(l,{class:"p-[30rpx]"},{default:p((()=>[n(e,{html:d.value},null,8,["html"])])),_:1})}}});export{_ as default};
