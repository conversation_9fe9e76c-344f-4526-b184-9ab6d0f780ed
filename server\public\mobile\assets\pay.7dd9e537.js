import{_ as a}from"./u-loading.8620e4d6.js";import{d as s,aT as t,x as e,o,b as r,w as u,j as p,E as i,p as d,f as n,e as l,r as f,a as y,aN as g}from"./index-561dd99e.js";import{_ as m}from"./_plugin-vue_export-helper.1b428a4d.js";const _=m(s({__name:"page-status",props:{status:{type:String,default:t.LOADING},fixed:{type:Boolean,default:!0}},setup:s=>(g,m)=>{const _=f(y("u-loading"),a),c=d;return s.status!==e(t).NORMAL?(o(),r(c,{key:0,class:p(["page-status",{"page-status--fixed":s.fixed}])},{default:u((()=>[n(" Loading "),s.status===e(t).LOADING?i(g.$slots,"loading",{key:0},(()=>[l(_,{size:60,mode:"flower"})]),!0):n("v-if",!0),n(" Error "),s.status===e(t).ERROR?i(g.$slots,"error",{key:1},void 0,!0):n("v-if",!0),n(" Empty "),s.status===e(t).EMPTY?i(g.$slots,"empty",{key:2},void 0,!0):n("v-if",!0)])),_:3},8,["class"])):i(g.$slots,"default",{key:1},void 0,!0)}}),[["__scopeId","data-v-0a39ed99"]]);function c(a){return g.get({url:"/pay/payWay",data:a},{isAuth:!0})}function v(a){return g.post({url:"/pay/prepay",data:a},{isAuth:!0})}function x(a){return g.get({url:"/pay/payStatus",data:a},{isAuth:!0})}export{_,c as a,x as g,v as p};
