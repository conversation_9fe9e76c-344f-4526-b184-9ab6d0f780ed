import{r as e,a as t,o as a,b as s,w as l,n as o,e as u,l as r,t as i,f as n,g as c,F as p,h as d,k as f,j as m,a3 as _,p as h,d as x,u as b,Z as g,c as y,z as v,a5 as w,$ as k,I as C,a8 as V,ak as S,x as I,v as j,i as z,a6 as A,a9 as B}from"./index-561dd99e.js";import{_ as $}from"./page-meta.438f2c32.js";import{_ as T}from"./u-avatar.878580e7.js";import{_ as R}from"./u-icon.f1b72599.js";import{_ as U}from"./u-button.e98befd5.js";import{_ as E}from"./u-popup.6496bd54.js";import{_ as N}from"./_plugin-vue_export-helper.1b428a4d.js";import{u as F}from"./useLockFn.7b7abf35.js";const Z=N({name:"u-action-sheet",emits:["update:modelValue","input","click","close"],props:{value:{type:Boolean,default:!1},modelValue:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!0},list:{type:Array,default:()=>[]},tips:{type:Object,default:()=>({text:"",color:"",fontSize:"26"})},cancelBtn:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:!1},borderRadius:{type:[String,Number],default:0},zIndex:{type:[String,Number],default:0},cancelText:{type:String,default:"取消"},labelName:{type:String,default:"text"},blur:{type:[Number,String],default:0}},computed:{valueCom(){return this.modelValue},tipsStyle(){let e={};return this.tips.color&&(e.color=this.tips.color),this.tips.fontSize&&(e.fontSize=this.tips.fontSize+"rpx"),e},itemStyle(){return e=>{let t={};return this.list[e].color&&(t.color=this.list[e].color),this.list[e].fontSize&&(t.fontSize=this.list[e].fontSize+"rpx"),this.list[e].disabled&&(t.color="#c0c4cc"),t}},uZIndex(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},data:()=>({popupValue:!1}),watch:{valueCom(e,t){this.popupValue=e}},methods:{close(){this.popupClose(),this.$emit("close")},popupClose(){this.$emit("input",!1),this.$emit("update:modelValue",!1)},itemClick(e){this.list[e].disabled||(this.$emit("click",e),this.$emit("input",!1),this.$emit("update:modelValue",!1))}}},[["render",function(x,b,g,y,v,w){const k=_,C=h,V=e(t("u-popup"),E);return a(),s(V,{blur:g.blur,mode:"bottom","border-radius":g.borderRadius,popup:!1,modelValue:v.popupValue,"onUpdate:modelValue":b[2]||(b[2]=e=>v.popupValue=e),maskCloseAble:g.maskCloseAble,length:"auto",safeAreaInsetBottom:g.safeAreaInsetBottom,onClose:w.popupClose,"z-index":w.uZIndex},{default:l((()=>[g.tips.text?(a(),s(C,{key:0,class:"u-tips u-border-bottom",style:o([w.tipsStyle])},{default:l((()=>[u(k,null,{default:l((()=>[r(i(g.tips.text),1)])),_:1})])),_:1},8,["style"])):n("v-if",!0),(a(!0),c(p,null,d(g.list,((e,t)=>(a(),s(C,{key:t,onTouchmove:b[0]||(b[0]=f((()=>{}),["stop","prevent"])),onClick:e=>w.itemClick(t),style:o([w.itemStyle(t)]),class:m(["u-action-sheet-item u-line-1",[t<g.list.length-1?"u-border-bottom":""]]),"hover-stay-time":150},{default:l((()=>[u(k,null,{default:l((()=>[r(i(e[g.labelName]),1)])),_:2},1024),e.subText?(a(),s(k,{key:0,class:"u-action-sheet-item__subtext u-line-1"},{default:l((()=>[r(i(e.subText),1)])),_:2},1024)):n("v-if",!0)])),_:2},1032,["onClick","style","class"])))),128)),g.cancelBtn?(a(),s(C,{key:1,class:"u-gab"})):n("v-if",!0),g.cancelBtn?(a(),s(C,{key:2,onTouchmove:b[1]||(b[1]=f((()=>{}),["stop","prevent"])),class:"u-actionsheet-cancel u-action-sheet-item","hover-class":"u-hover-class","hover-stay-time":150,onClick:w.close},{default:l((()=>[r(i(g.cancelText),1)])),_:1},8,["onClick"])):n("v-if",!0)])),_:1},8,["blur","border-radius","modelValue","maskCloseAble","safeAreaInsetBottom","onClose","z-index"])}],["__scopeId","data-v-5831dcf0"]]);var L=(e=>(e.PRIVACY="privacy",e.SERVICE="service",e))(L||{});const P=N(x({__name:"user_set",setup(o){const d=j(),f=b(),m=g(),_=y((()=>m.userInfo)),x=v([{text:"修改密码"},{text:"忘记密码"}]),N=v(!0);N.value=w();const P=v(!1),Y=v(!1),q=e=>{switch(e){case 0:d.navigateTo("/pages/change_password/change_password");break;case 1:d.navigateTo("/pages/forget_pwd/forget_pwd")}},D=()=>{if(!_.value.has_password)return d.navigateTo("/pages/change_password/change_password?type=set");P.value=!0},O=()=>{m.logout(),d.redirectTo("/pages/login/login")},{lockFn:G}=F((async()=>{if(!_.value.is_auth)try{V({title:"请稍后..."}),N.value&&A.getUrl(),await m.getUser(),B()}catch(e){B(),uni.$u.toast(e)}}));return k((()=>{m.getUser()})),C((async e=>{const{code:t}=e;if(N.value&&t){V({title:"请稍后..."});try{await S({code:t}),await m.getUser()}catch(a){}d.redirectTo("/pages/user_set/user_set")}})),(o,d)=>{const m=e(t("page-meta"),$),b=e(t("u-avatar"),T),g=h,y=e(t("u-icon"),R),v=z,w=e(t("u-button"),U),k=e(t("u-action-sheet"),Z),C=e(t("u-popup"),E);return a(),c(p,null,[u(m,{"page-style":o.$theme.pageStyle},null,8,["page-style"]),u(g,{class:"user-set"},{default:l((()=>[u(v,{url:"/pages/user_data/user_data"},{default:l((()=>[u(g,{class:"item flex bg-white mt-[20rpx]"},{default:l((()=>[u(b,{src:I(_).avatar,shape:"square",size:100},null,8,["src"]),u(g,{class:"ml-[20rpx] flex flex-1 justify-between items-center"},{default:l((()=>[u(g,null,{default:l((()=>[u(g,{class:"mb-[15rpx] text-xl font-medium"},{default:l((()=>[r(i(I(_).nickname),1)])),_:1}),u(g,{class:"text-content text-xs"},{default:l((()=>[r("账号："+i(I(_).account),1)])),_:1})])),_:1}),u(y,{name:"arrow-right",color:"#666"})])),_:1})])),_:1})])),_:1}),u(g,{class:"item bg-white mt-[20rpx] btn-border flex flex-1 justify-between",onClick:D},{default:l((()=>[u(g,{class:""},{default:l((()=>[r("登录密码")])),_:1}),u(y,{name:"arrow-right",color:"#666"})])),_:1}),N.value?(a(),s(g,{key:0,class:"item bg-white flex flex-1 justify-between",onClick:I(G)},{default:l((()=>[u(g,{class:""},{default:l((()=>[r("绑定微信")])),_:1}),u(g,{class:"flex justify-between"},{default:l((()=>[u(g,{class:"text-muted mr-[20rpx]"},{default:l((()=>[r(i(I(_).is_auth?"已绑定":"未绑定"),1)])),_:1}),0==I(_).is_auth?(a(),s(y,{key:0,name:"arrow-right",color:"#666"})):n("v-if",!0)])),_:1})])),_:1},8,["onClick"])):n("v-if",!0),u(v,{url:`/pages/agreement/agreement?type=${I(L).PRIVACY}`},{default:l((()=>[u(g,{class:"item bg-white mt-[20rpx] btn-border flex flex-1 justify-between"},{default:l((()=>[u(g,{class:""},{default:l((()=>[r("隐私政策")])),_:1}),u(y,{name:"arrow-right",color:"#666"})])),_:1})])),_:1},8,["url"]),u(v,{url:`/pages/agreement/agreement?type=${I(L).SERVICE}`},{default:l((()=>[u(g,{class:"item bg-white btn-border flex flex-1 justify-between"},{default:l((()=>[u(g,{class:""},{default:l((()=>[r("服务协议")])),_:1}),u(y,{name:"arrow-right",color:"#666"})])),_:1})])),_:1},8,["url"]),u(v,{url:"/pages/as_us/as_us"},{default:l((()=>[u(g,{class:"item bg-white flex flex-1 justify-between"},{default:l((()=>[u(g,{class:""},{default:l((()=>[r("关于我们")])),_:1}),u(g,{class:"flex justify-between"},{default:l((()=>[u(g,{class:"text-muted mr-[20rpx]"},{default:l((()=>[r(i(I(f).config.version),1)])),_:1}),u(y,{name:"arrow-right",color:"#666"})])),_:1})])),_:1})])),_:1}),u(g,{class:"mt-[60rpx] mx-[26rpx]"},{default:l((()=>[u(w,{type:"primary",shape:"circle",onClick:d[0]||(d[0]=e=>Y.value=!0)},{default:l((()=>[r(" 退出登录")])),_:1})])),_:1}),u(k,{list:x.value,modelValue:P.value,"onUpdate:modelValue":d[1]||(d[1]=e=>P.value=e),onClick:q,"safe-area-inset-bottom":!0},null,8,["list","modelValue"]),u(C,{class:"pay-popup",modelValue:Y.value,"onUpdate:modelValue":d[3]||(d[3]=e=>Y.value=e),round:"",mode:"center",borderRadius:"10",maskCloseAble:!1},{default:l((()=>[u(g,{class:"content bg-white w-[560rpx] p-[40rpx]"},{default:l((()=>[u(g,{class:"text-2xl font-medium text-center"},{default:l((()=>[r(" 温馨提示")])),_:1}),u(g,{class:"pt-[30rpx] pb-[40rpx]"},{default:l((()=>[u(g,null,{default:l((()=>[r(" 是否清除当前登录信息，退出登录？")])),_:1})])),_:1}),u(g,{class:"flex"},{default:l((()=>[u(g,{class:"flex-1 mr-[20rpx]"},{default:l((()=>[u(w,{shape:"circle",type:"primary",plain:"",size:"medium","hover-class":"none",customStyle:{width:"100%"},onClick:d[2]||(d[2]=e=>Y.value=!1)},{default:l((()=>[r(" 取消 ")])),_:1})])),_:1}),u(g,{class:"flex-1"},{default:l((()=>[u(w,{shape:"circle",type:"primary",size:"medium","hover-class":"none",customStyle:{width:"100%"},onClick:O},{default:l((()=>[r(" 确认 ")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"])])),_:1})],64)}}}),[["__scopeId","data-v-a5b3b4d7"]]);export{P as default};
