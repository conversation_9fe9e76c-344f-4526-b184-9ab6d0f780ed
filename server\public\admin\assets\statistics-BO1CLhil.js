import{_ as k}from"./index-onOHNH0j.js";import{d as _,i as C,j as g,k as h,z as B,o as c,a as D,m as s,w as a,b as r,e as l,p as i,B as V,C as I,E as S,v as N}from"./index-B2xNDy79.js";import{E as F,a as M}from"./el-form-item-DlU85AZK.js";import{E as R}from"./el-card-DpH4mUSc.js";import{e as j,f as z}from"./website-B_jWLJYu.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";const T={class:"website-statistics"},U={class:"flex flex-col"},W={class:"w-80"},q=_({name:"webInformation"}),X=_({...q,setup(A){const n=C(),e=g({clarity_code:""}),d={},m=async()=>{const o=await j();for(const t in e)e[t]=o[t]},p=async()=>{var o;await((o=n.value)==null?void 0:o.validate()),await z(e),m()};return h(()=>{m()}),(o,t)=>{const f=S,u=F,w=R,y=M,b=N,v=k,x=B("perms");return c(),D("div",T,[s(y,{ref_key:"formRef",ref:n,rules:d,class:"ls-form",model:l(e),"scroll-to-error":"","label-width":"120px"},{default:a(()=>[s(w,{shadow:"never",class:"!border-none"},{default:a(()=>[t[2]||(t[2]=r("div",{class:"text-xl font-medium mb-[20px]"},"Clarity配置",-1)),s(u,{label:"应用ID",prop:"clarity_code"},{default:a(()=>[r("div",U,[r("div",W,[s(f,{modelValue:l(e).clarity_code,"onUpdate:modelValue":t[0]||(t[0]=E=>l(e).clarity_code=E),modelModifiers:{trim:!0},placeholder:"请填写应用ID",maxlength:"10","show-word-limit":""},null,8,["modelValue"])]),t[1]||(t[1]=r("div",{class:"form-tips"},[i(" 请前往"),r("a",{class:"text-primary",href:"https://clarity.microsoft.com",target:"_blank"}," 《Clarity官网》 "),i(" 创建Clarity统计应用 ")],-1))])]),_:1})]),_:1})]),_:1},8,["model"]),V((c(),I(v,null,{default:a(()=>[s(b,{type:"primary",onClick:p},{default:a(()=>t[3]||(t[3]=[i("保存")])),_:1})]),_:1})),[[x,["setting.web.web_setting/setWebsite"]]])])}}});export{X as default};
