import{y as m,d as P,s as U,i as G,c as J,j as C,o as u,a as w,m as t,w as d,e as o,b as p,F as q,r as D,C as f,G as R,E as z,J as H}from"./index-B2xNDy79.js";import{E as K,a as M}from"./el-form-item-DlU85AZK.js";import"./el-tag-CuODyGk4.js";import{E as Q,a as W}from"./el-select-BRdnbwTl.js";import"./el-tree-8o9N7gsQ.js";import"./el-checkbox-3_Bu4Dnb.js";import{E as X}from"./el-tree-select-BPB13nyH.js";import{_ as Y}from"./picker-Cd5l2hZ5.js";import{d as Z}from"./department-v2Z7R8-4.js";import{e as $}from"./post-zozQIMRx.js";import{r as ee}from"./role-BYPWLZ0d.js";import{P as le}from"./index-DFOp_83R.js";import{u as oe}from"./useDictOptions-D0QsC3Dl.js";function Ee(s){return m.get({url:"/auth.admin/lists",params:s},{ignoreCancelToken:!0})}function ae(s){return m.post({url:"/auth.admin/add",params:s})}function te(s){return m.post({url:"/auth.admin/edit",params:s})}function ye(s){return m.post({url:"/auth.admin/delete",params:s})}function re(s){return m.get({url:"/auth.admin/detail",params:s})}const de={class:"edit-popup"},xe=P({__name:"edit",emits:["success","close"],setup(s,{expose:F,emit:A}){const g=A,E=U(),c=U(),_=G("add"),B=J(()=>_.value=="edit"?"编辑管理员":"新增管理员"),l=C({id:"",account:"",name:"",dept_id:[],jobs_id:[],role_id:[],avatar:"",password:"",password_confirm:"",disable:0,multipoint_login:1,root:0}),y=(i,e,r)=>{l.password&&(e||r(new Error("请再次输入密码")),e!==l.password&&r(new Error("两次输入密码不一致!"))),r()},b=C({account:[{required:!0,message:"请输入账号",trigger:["blur"]}],name:[{required:!0,message:"请输入名称",trigger:["blur"]}],role_id:[{required:!0,validator:(i,e,r)=>{l.root||l.role_id.length?r():r(new Error("请选择角色"))}}],password:[{required:!0,message:"请输入密码",trigger:["blur"]}],password_confirm:[{required:!0,message:"请输入确认密码",trigger:["blur"]},{validator:y,trigger:"blur"}]}),{optionsData:V}=oe({role:{api:ee},jobs:{api:$},dept:{api:Z}}),I=async()=>{var i,e;await((i=E.value)==null?void 0:i.validate()),_.value=="edit"?await te(l):await ae(l),(e=c.value)==null||e.close(),g("success")},S=(i="add")=>{var e;_.value=i,(e=c.value)==null||e.open()},N=async i=>{b.password=[],b.password_confirm=[{validator:y,trigger:"blur"}];const e=await re({id:i.id});for(const r in l)e[r]!=null&&e[r]!=null&&(l[r]=e[r])},T=()=>{g("close")};return F({open:S,setFormData:N}),(i,e)=>{const r=z,n=K,h=Y,L=X,x=Q,j=W,k=H,O=M;return u(),w("div",de,[t(le,{ref_key:"popupRef",ref:c,title:o(B),async:!0,width:"550px",onConfirm:I,onClose:T},{default:d(()=>[t(O,{ref_key:"formRef",ref:E,model:o(l),"label-width":"84px",rules:o(b)},{default:d(()=>[t(n,{label:"账号",prop:"account"},{default:d(()=>[t(r,{modelValue:o(l).account,"onUpdate:modelValue":e[0]||(e[0]=a=>o(l).account=a),disabled:o(l).root==1,placeholder:"请输入账号",clearable:""},null,8,["modelValue","disabled"])]),_:1}),t(n,{label:"头像"},{default:d(()=>[p("div",null,[p("div",null,[t(h,{modelValue:o(l).avatar,"onUpdate:modelValue":e[1]||(e[1]=a=>o(l).avatar=a),limit:1},null,8,["modelValue"])]),e[10]||(e[10]=p("div",{class:"form-tips"},"建议尺寸：100*100px，支持jpg，jpeg，png格式",-1))])]),_:1}),t(n,{label:"名称",prop:"name"},{default:d(()=>[t(r,{modelValue:o(l).name,"onUpdate:modelValue":e[2]||(e[2]=a=>o(l).name=a),placeholder:"请输入名称",clearable:""},null,8,["modelValue"])]),_:1}),t(n,{label:"归属部门",prop:"dept_id"},{default:d(()=>[t(L,{class:"flex-1",modelValue:o(l).dept_id,"onUpdate:modelValue":e[3]||(e[3]=a=>o(l).dept_id=a),data:o(V).dept,clearable:"",multiple:"","node-key":"id",props:{value:"id",label:"name",disabled(a){return a.status!==1}},"check-strictly":"","default-expand-all":!0,placeholder:"请选择上级部门"},null,8,["modelValue","data","props"])]),_:1}),t(n,{label:"岗位",prop:"jobs_id"},{default:d(()=>[t(j,{class:"flex-1",modelValue:o(l).jobs_id,"onUpdate:modelValue":e[4]||(e[4]=a=>o(l).jobs_id=a),clearable:"",multiple:"",placeholder:"请选择岗位"},{default:d(()=>[(u(!0),w(q,null,D(o(V).jobs,(a,v)=>(u(),f(x,{key:v,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(l).root!=1?(u(),f(n,{key:0,label:"角色",prop:"role_id"},{default:d(()=>[t(j,{modelValue:o(l).role_id,"onUpdate:modelValue":e[5]||(e[5]=a=>o(l).role_id=a),disabled:o(l).root==1,class:"flex-1",multiple:"",placeholder:"请选择角色",clearable:""},{default:d(()=>[(u(!0),w(q,null,D(o(V).role,(a,v)=>(u(),f(x,{key:v,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})):R("",!0),t(n,{label:"密码",prop:"password"},{default:d(()=>[t(r,{modelValue:o(l).password,"onUpdate:modelValue":e[6]||(e[6]=a=>o(l).password=a),"show-password":"",clearable:"",placeholder:"请输入密码"},null,8,["modelValue"])]),_:1}),t(n,{label:"确认密码",prop:"password_confirm"},{default:d(()=>[t(r,{modelValue:o(l).password_confirm,"onUpdate:modelValue":e[7]||(e[7]=a=>o(l).password_confirm=a),"show-password":"",clearable:"",placeholder:"请输入确认密码"},null,8,["modelValue"])]),_:1}),o(l).root!=1?(u(),f(n,{key:1,label:"管理员状态"},{default:d(()=>[t(k,{modelValue:o(l).disable,"onUpdate:modelValue":e[8]||(e[8]=a=>o(l).disable=a),"active-value":0,"inactive-value":1},null,8,["modelValue"])]),_:1})):R("",!0),t(n,{label:"多处登录"},{default:d(()=>[p("div",null,[t(k,{modelValue:o(l).multipoint_login,"onUpdate:modelValue":e[9]||(e[9]=a=>o(l).multipoint_login=a),"active-value":1,"inactive-value":0},null,8,["modelValue"]),e[11]||(e[11]=p("div",{class:"form-tips"},"允许多人同时在线登录",-1))])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title"])])}}});export{xe as _,Ee as a,te as b,ye as c};
