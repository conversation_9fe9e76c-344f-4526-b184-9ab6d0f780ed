import{d as B,s as _,i as N,c as F,j as I,o as b,a as S,m as o,w as n,e as t,C as q,G as A,b as V,E as M,L as P,J as T}from"./index-B2xNDy79.js";import{E as j,a as G}from"./el-form-item-DlU85AZK.js";import"./el-tag-CuODyGk4.js";import"./el-select-BRdnbwTl.js";import"./el-tree-8o9N7gsQ.js";import"./el-checkbox-3_Bu4Dnb.js";import{E as J}from"./el-tree-select-BPB13nyH.js";import{d as L,a as O,b as $,c as z}from"./department-v2Z7R8-4.js";import{P as H}from"./index-DFOp_83R.js";import{u as K}from"./useDictOptions-D0QsC3Dl.js";const Q={class:"edit-popup"},de=B({__name:"edit",emits:["success","close"],setup(W,{expose:v,emit:g}){const u=g,c=_(),m=_(),i=N("add"),w=F(()=>i.value=="edit"?"编辑部门":"新增部门"),l=I({id:"",pid:"",name:"",leader:"",mobile:"",sort:0,status:1}),E={pid:[{required:!0,message:"请选择上级部门",trigger:["change"]}],name:[{required:!0,message:"请输入部门名称",trigger:["blur"]}],mobile:[{validator:(a,e,d)=>{if(e){const r=/^[1][3,4,5,6,7,8,9][0-9]{9}$/;if(console.log(r.test(e)),r.test(e))d();else return d(new Error("请输入正确的手机号"))}else return d()},trigger:["blur"]}]},{optionsData:x}=K({dept:{api:L}}),k=async()=>{var a,e;await((a=c.value)==null?void 0:a.validate()),i.value=="edit"?await O(l):await $(l),(e=m.value)==null||e.close(),u("success")},y=(a="add")=>{var e;i.value=a,(e=m.value)==null||e.open()},f=a=>{for(const e in l)a[e]!=null&&a[e]!=null&&(l[e]=a[e])},C=async a=>{const e=await z({id:a.id});f(e)},h=()=>{u("close")};return v({open:y,setFormData:f,getDetail:C}),(a,e)=>{const d=J,r=j,p=M,D=P,R=T,U=G;return b(),S("div",Q,[o(H,{ref_key:"popupRef",ref:m,title:t(w),async:!0,width:"550px",onConfirm:k,onClose:h},{default:n(()=>[o(U,{ref_key:"formRef",ref:c,model:t(l),"label-width":"84px",rules:E},{default:n(()=>[t(l).pid!==0?(b(),q(r,{key:0,label:"上级部门",prop:"pid"},{default:n(()=>[o(d,{class:"flex-1",modelValue:t(l).pid,"onUpdate:modelValue":e[0]||(e[0]=s=>t(l).pid=s),data:t(x).dept,clearable:"","node-key":"id",props:{value:"id",label:"name"},"check-strictly":"","default-expand-all":!0,placeholder:"请选择上级部门"},null,8,["modelValue","data"])]),_:1})):A("",!0),o(r,{label:"部门名称",prop:"name"},{default:n(()=>[o(p,{modelValue:t(l).name,"onUpdate:modelValue":e[1]||(e[1]=s=>t(l).name=s),placeholder:"请输入部门名称",maxlength:100},null,8,["modelValue"])]),_:1}),o(r,{label:"负责人",prop:"leader"},{default:n(()=>[o(p,{modelValue:t(l).leader,"onUpdate:modelValue":e[2]||(e[2]=s=>t(l).leader=s),placeholder:"请输入负责人姓名",maxlength:30},null,8,["modelValue"])]),_:1}),o(r,{label:"联系电话",prop:"mobile"},{default:n(()=>[o(p,{modelValue:t(l).mobile,"onUpdate:modelValue":e[3]||(e[3]=s=>t(l).mobile=s),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),o(r,{label:"排序",prop:"sort"},{default:n(()=>[V("div",null,[o(D,{modelValue:t(l).sort,"onUpdate:modelValue":e[4]||(e[4]=s=>t(l).sort=s),min:0,max:9999},null,8,["modelValue"]),e[6]||(e[6]=V("div",{class:"form-tips"},"默认为0， 数值越大越排前",-1))])]),_:1}),o(r,{label:"部门状态"},{default:n(()=>[o(R,{modelValue:t(l).status,"onUpdate:modelValue":e[5]||(e[5]=s=>t(l).status=s),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{de as _};
