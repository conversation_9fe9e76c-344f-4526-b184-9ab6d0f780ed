var e=Object.defineProperty;import{d as t,c as a,b2 as s,o as l,b as r,w as i,e as o,j as n,n as u,f as d,E as c,l as p,t as h,x as m,p as f,a3 as y,r as _,a as w,ai as b,b3 as g,b4 as x,a6 as S,z as v,aT as C,Z as D,A as k,g as I,F as z,h as P,b5 as A,a9 as N,b6 as $,b7 as E,b8 as j,aL as L,X as F,a8 as V,G as U,I as O,$ as R,aF as B,i as W}from"./index-561dd99e.js";import{_ as T}from"./page-meta.438f2c32.js";import{_ as q}from"./u-button.e98befd5.js";import{_ as G}from"./u-empty.36fe4845.js";import{_ as H}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as X}from"./u-icon.f1b72599.js";import{E as M}from"./emitter.1571a5d9.js";import{_ as Y,g as J,a as K,p as Q}from"./pay.7dd9e537.js";import{_ as Z}from"./u-popup.6496bd54.js";import{u as ee}from"./useLockFn.7b7abf35.js";import{r as te,a as ae}from"./recharge.89103b5a.js";import"./u-loading.8620e4d6.js";const se=H(t({__name:"price",props:{content:{default:""},prec:{default:2},autoPrec:{type:Boolean,default:!0},color:{default:"#FA8919"},mainSize:{default:"36rpx"},minorSize:{default:"28rpx"},lineThrough:{type:Boolean,default:!1},fontWeight:{default:"normal"},prefix:{default:"￥"},suffix:{default:""}},setup(e){const t=e,_=a((()=>s({price:t.content,take:"int"}))),w=a((()=>{let e=s({price:t.content,take:"dec",prec:t.prec});return e=e%10==0?e.substr(0,e.length-1):e,t.autoPrec?1*e?"."+e:"":t.prec?"."+e:""}));return(t,a)=>{const s=f,b=y;return l(),r(s,{class:"price-container"},{default:i((()=>[o(s,{class:n(["price-wrap",{"price-wrap--disabled":e.lineThrough}]),style:u({color:e.color})},{default:i((()=>[d(" Prefix "),o(s,{class:"fix-pre",style:u({fontSize:e.minorSize})},{default:i((()=>[c(t.$slots,"prefix",{},(()=>[p(h(e.prefix),1)]),!0)])),_:3},8,["style"]),d(" Content "),o(s,{style:u({"font-weight":e.fontWeight})},{default:i((()=>[d(" Integer "),o(b,{style:u({fontSize:e.mainSize})},{default:i((()=>[p(h(m(_)),1)])),_:1},8,["style"]),d(" Decimals "),o(b,{style:u({fontSize:e.minorSize})},{default:i((()=>[p(h(m(w)),1)])),_:1},8,["style"])])),_:1},8,["style"]),d(" Suffix "),o(s,{class:"fix-suf",style:u({fontSize:e.minorSize})},{default:i((()=>[c(t.$slots,"suffix",{},(()=>[p(h(e.suffix),1)]),!0)])),_:3},8,["style"])])),_:3},8,["class","style"])])),_:3})}}}),[["__scopeId","data-v-a74822ce"]]);const le=H({name:"u-radio",emits:["change"],props:{name:{type:[String,Number],default:""},size:{type:[String,Number],default:34},shape:{type:String,default:""},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""}},data:()=>({parentData:{iconSize:null,labelDisabled:null,disabled:null,shape:null,activeColor:null,size:null,width:null,height:null,value:null,wrap:null}}),created(){this.parent=!1,this.updateParentData(),this.parent.children.push(this)},computed:{elDisabled(){return""!==this.disabled?this.disabled:null!==this.parentData.disabled&&this.parentData.disabled},elLabelDisabled(){return""!==this.labelDisabled?this.labelDisabled:null!==this.parentData.labelDisabled&&this.parentData.labelDisabled},elSize(){return this.size?this.size:this.parentData.size?this.parentData.size:34},elIconSize(){return this.iconSize?this.iconSize:this.parentData.iconSize?this.parentData.iconSize:20},elActiveColor(){return this.activeColor?this.activeColor:this.parentData.activeColor?this.parentData.activeColor:"primary"},elShape(){return this.shape?this.shape:this.parentData.shape?this.parentData.shape:"circle"},iconStyle(){let e={};return this.elActiveColor&&this.parentData.value===this.name&&!this.elDisabled&&(e.borderColor=this.elActiveColor,e.backgroundColor=this.elActiveColor),e.width=this.$u.addUnit(this.elSize),e.height=this.$u.addUnit(this.elSize),e},iconColor(){return this.name===this.parentData.value?"#ffffff":"transparent"},iconClass(){let e=[];return e.push("u-radio__icon-wrap--"+this.elShape),this.name===this.parentData.value&&e.push("u-radio__icon-wrap--checked"),this.elDisabled&&e.push("u-radio__icon-wrap--disabled"),this.name===this.parentData.value&&this.elDisabled&&e.push("u-radio__icon-wrap--disabled--checked"),e.join(" ")},radioStyle(){let e={};return this.parentData.width&&(e.width=this.$u.addUnit(this.parentData.width),e.flex=`0 0 ${this.$u.addUnit(this.parentData.width)}`),this.parentData.wrap&&(e.width="100%",e.flex="0 0 100%"),e}},methods:{updateParentData(){this.getParentData("u-radio-group")},onClickLabel(){this.elLabelDisabled||this.elDisabled||this.setRadioCheckedStatus()},toggle(){this.elDisabled||this.setRadioCheckedStatus()},emitEvent(){this.parentData.value!=this.name&&this.$emit("change",this.name)},setRadioCheckedStatus(){this.emitEvent(),this.parent&&(this.parent.setValue(this.name),this.parentData.value=this.name)}}},[["render",function(e,t,a,s,d,p){const h=_(w("u-icon"),X),m=f;return l(),r(m,{class:"u-radio",style:u([p.radioStyle])},{default:i((()=>[o(m,{class:n(["u-radio__icon-wrap",[p.iconClass]]),onClick:p.toggle,style:u([p.iconStyle])},{default:i((()=>[o(h,{class:"u-radio__icon-wrap__icon",name:"checkbox-mark",size:p.elIconSize,color:p.iconColor},null,8,["size","color"])])),_:1},8,["onClick","class","style"]),o(m,{class:"u-radio__label",onClick:p.onClickLabel,style:u({fontSize:e.$u.addUnit(a.labelSize)})},{default:i((()=>[c(e.$slots,"default",{},void 0,!0)])),_:3},8,["onClick","style"])])),_:3},8,["style"])}],["__scopeId","data-v-65c0e1b1"]]),re={name:"u-radio-group",emits:["update:modelValue","input","change"],mixins:[M],props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},disabled:{type:Boolean,default:!1},activeColor:{type:String,default:"#2979ff"},size:{type:[String,Number],default:34},labelDisabled:{type:Boolean,default:!1},shape:{type:String,default:"circle"},iconSize:{type:[String,Number],default:20},width:{type:[String,Number],default:"auto"},wrap:{type:Boolean,default:!1}},data:()=>({uFromData:{inputAlign:"left"}}),created(){this.children=[]},mounted(){let e=this.$u.$parent.call(this,"u-form");e&&Object.keys(this.uFromData).map((t=>{this.uFromData[t]=e[t]}))},watch:{parentData(){this.children.length&&this.children.map((e=>{"function"==typeof e.updateParentData&&e.updateParentData()}))}},computed:{valueCom(){return this.modelValue},parentData(){return[this.valueCom,this.disabled,this.activeColor,this.size,this.labelDisabled,this.shape,this.iconSize,this.width,this.wrap]}},methods:{setValue(e){this.children.map((t=>{t.parentData.value!=e&&(t.parentData.value="")})),this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),setTimeout((()=>{this.dispatch("u-form-item","onFieldChange",e)}),60)}}};const ie=H(re,[["render",function(e,t,a,s,o,u){const d=f;return l(),r(d,{class:n(["u-radio-group u-clearfix","right"==o.uFromData.inputAlign?"flex-end":""])},{default:i((()=>[c(e.$slots,"default",{},void 0,!0)])),_:3},8,["class"])}],["__scopeId","data-v-5c6ccee1"]]),oe=class{static inject(e,t){this.modules.set(e,t)}constructor(){for(const[e,t]of oe.modules.entries())t.init(e,this)}async payment(e,t){try{const a=this[de[e]];if(!a)throw new Error(`can not find pay way ${e}`);return await a.run(t)}catch(a){return Promise.reject(a)}}};let ne=oe;var ue;((t,a,s)=>{a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s})(ne,"symbol"!=typeof(ue="modules")?ue+"":ue,new Map);var de=(e=>(e[e.BALANCE=1]="BALANCE",e[e.WECHAT=2]="WECHAT",e[e.ALIPAY=3]="ALIPAY",e))(de||{});const ce=new class{init(e,t){t[e]=this}async run(e){try{return await g({MP_WEIXIN:()=>new Promise((t=>{uni.requestPayment({provider:"wxpay",...e,success(){t(x.SUCCESS)},fail(){t(x.FAIL)}})})),OA_WEIXIN:()=>new Promise((t=>{S.pay(e).then((()=>{t(x.SUCCESS)})).catch((()=>{t(x.FAIL)}))})),H5:()=>new Promise((t=>{window.open(e,"_self"),t(x.PENDING)}))})}catch(t){return Promise.reject(t)}}};ne.inject(de[2],ce);const pe=new class{init(e,t){t[e]=this}openNewPage(e){b();const t=window.open("","_self");t.document.body.innerHTML=e,t.document.forms[0].submit()}async run(e){try{return await g({H5:()=>new Promise((t=>{this.openNewPage(e),t(x.PENDING)})),OA_WEIXIN:()=>new Promise((t=>{this.openNewPage(e),t(x.PENDING)})),ANDROID:()=>new Promise(((t,a)=>{uni.requestPayment({provider:"alipay",orderInfo:e,success(){t(x.SUCCESS)},fail(){t(x.FAIL)}})})),IOS:()=>new Promise(((t,a)=>{uni.requestPayment({provider:"alipay",orderInfo:e,success(){t(x.SUCCESS)},fail(){t(x.FAIL)}})}))})}catch(t){return Promise.reject(t)}}};ne.inject(de[3],pe);const he=new ne,me=t({__name:"payment",props:{show:{type:Boolean,required:!0},showCheck:{type:Boolean},orderId:{type:Number,required:!0},from:{type:String,required:!0},redirect:{type:String}},emits:["update:showCheck","update:show","close","success","fail"],setup(e,{emit:t}){const s=e,n=v(),u=v(C.LOADING),d=v({order_amount:"",lists:[]}),c=a({get:()=>s.showCheck,set(e){t("update:showCheck",e)}}),y=a({get:()=>s.show,set(e){t("update:show",e)}}),b=()=>{y.value=!1,t("close")},g=D(),S=$((async()=>{if(0==g.userInfo.is_auth&&[E.OA_WEIXIN,E.MP_WEIXIN].includes(j)&&n.value==de.WECHAT)return(await L({title:"温馨提示",content:"当前账号未绑定微信，无法完成支付",confirmText:"去绑定"})).confirm&&F({url:"/pages/user_set/user_set"}),Promise.reject()}),(async()=>(V({title:"正在支付中"}),await Q({order_id:s.orderId,from:s.from,pay_way:n.value,redirect:s.redirect}))),(async e=>{try{return await he.payment(e.pay_way,e.config)}catch(t){return Promise.reject(t)}})),{isLock:U,lockFn:O}=ee((async()=>{try{const e=await S();R(e),N()}catch(e){N(),console.log(e)}})),R=e=>{switch(e){case x.SUCCESS:t("success");break;case x.FAIL:t("fail")}},B=async(e=!0)=>{0===(await J({order_id:s.orderId,from:s.from})).pay_status?(1==e&&uni.$u.toast("您的订单还未支付，请重新支付"),y.value=!0,R(x.FAIL)):(0==e&&uni.$u.toast("您的订单已经支付，请勿重新支付"),R(x.SUCCESS)),c.value=!1};return k((()=>s.show),(e=>{if(e){if(!s.orderId)return void(u.value=C.ERROR);(async()=>{u.value=C.LOADING;try{d.value=await K({order_id:s.orderId,from:s.from}),u.value=C.NORMAL;const e=d.value.lists.find((e=>e.is_default))||d.value.lists[0];n.value=null==e?void 0:e.pay_way}catch(e){u.value=C.ERROR}})()}}),{immediate:!0}),(e,t)=>{const a=_(w("u-empty"),G),s=_(w("price"),se),g=f,x=_(w("u-icon"),X),S=_(w("u-radio"),le),v=_(w("u-radio-group"),ie),C=_(w("u-button"),q),D=_(w("page-status"),Y),k=_(w("u-popup"),Z);return l(),I(z,null,[o(k,{modelValue:m(y),"onUpdate:modelValue":t[1]||(t[1]=e=>A(y)?y.value=e:null),mode:"bottom","safe-area-inset-bottom":"","mask-close-able":!1,"border-radius":"14",closeable:"",onClose:b},{default:i((()=>[o(g,{class:"h-[900rpx]"},{default:i((()=>[o(D,{status:u.value,fixed:!1},{error:i((()=>[o(a,{text:"订单信息错误，无法查询到订单信息",mode:"order"})])),default:i((()=>[o(g,{class:"payment h-full w-full flex flex-col"},{default:i((()=>[o(g,{class:"header py-[50rpx] flex flex-col items-center"},{default:i((()=>[o(s,{content:d.value.order_amount,mainSize:"44rpx",minorSize:"40rpx",fontWeight:"500",color:"#333"},null,8,["content"])])),_:1}),o(g,{class:"main flex-1 mx-[20rpx]"},{default:i((()=>[o(g,null,{default:i((()=>[o(g,{class:"payway-lists"},{default:i((()=>[o(v,{modelValue:n.value,"onUpdate:modelValue":t[0]||(t[0]=e=>n.value=e),class:"w-full"},{default:i((()=>[(l(!0),I(z,null,P(d.value.lists,((e,t)=>(l(),r(g,{class:"p-[20rpx] flex items-center w-full payway-item",key:t,onClick:t=>{return a=e.pay_way,void(n.value=a);var a}},{default:i((()=>[o(x,{class:"flex-none",size:48,name:e.icon},null,8,["name"]),o(g,{class:"mx-[16rpx] flex-1"},{default:i((()=>[o(g,{class:"payway-item--name flex-1"},{default:i((()=>[p(h(e.name),1)])),_:2},1024),o(g,{class:"text-muted text-xs"},{default:i((()=>[p(h(e.extra),1)])),_:2},1024)])),_:2},1024),o(S,{activeColor:"var(--color-primary)",class:"mr-[-20rpx]",name:e.pay_way},null,8,["name"])])),_:2},1032,["onClick"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),o(g,{class:"submit-btn p-[20rpx]"},{default:i((()=>[o(C,{onClick:m(O),shape:"circle",type:"primary",loading:m(U)},{default:i((()=>[p(" 立即支付 ")])),_:1},8,["onClick","loading"])])),_:1})])),_:1})])),_:1},8,["status"])])),_:1})])),_:1},8,["modelValue"]),o(k,{class:"pay-popup",modelValue:m(c),"onUpdate:modelValue":t[4]||(t[4]=e=>A(c)?c.value=e:null),round:"",mode:"center",borderRadius:"10",maskCloseAble:!1},{default:i((()=>[o(g,{class:"content bg-white w-[560rpx] p-[40rpx]"},{default:i((()=>[o(g,{class:"text-2xl font-medium text-center"},{default:i((()=>[p(" 支付确认 ")])),_:1}),o(g,{class:"pt-[30rpx] pb-[40rpx]"},{default:i((()=>[o(g,null,{default:i((()=>[p(" 请在微信内完成支付，如果您已支付成功，请点击`已完成支付`按钮 ")])),_:1})])),_:1}),o(g,{class:"flex"},{default:i((()=>[o(g,{class:"flex-1 mr-[20rpx]"},{default:i((()=>[o(C,{shape:"circle",type:"primary",plain:"",size:"medium","hover-class":"none",customStyle:{width:"100%"},onClick:t[2]||(t[2]=e=>B(!1))},{default:i((()=>[p(" 重新支付 ")])),_:1})])),_:1}),o(g,{class:"flex-1"},{default:i((()=>[o(C,{shape:"circle",type:"primary",size:"medium","hover-class":"none",customStyle:{width:"100%"},onClick:t[3]||(t[3]=e=>B())},{default:i((()=>[p(" 已完成支付 ")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"])],64)}}}),fe=H(me,[["__scopeId","data-v-909d81d9"]]),ye=t({__name:"recharge",setup(e){const t=v(""),a=U({orderId:"",from:"",showPay:!1,showCheck:!1,redirect:"/packages/pages/recharge/recharge"}),s=U({user_money:"",min_amount:0}),{isLock:r,lockFn:n}=ee((async()=>{const e=s.min_amount;if(!t.value)return uni.$u.toast("请输入充值金额");if(0==e&&Number(t.value)==e)return uni.$u.toast("充值金额必须大于0");if(Number(t.value)<e)return uni.$u.toast(`最低充值金额${e}`);const l=await ae({money:t.value});a.orderId=l.order_id,a.from=l.from,a.showPay=!0})),u=async()=>{a.showPay=!1,a.showCheck=!1,F({url:`/pages/payment_result/payment_result?id=${a.orderId}&from=${a.from}`})},d=async()=>{uni.$u.toast("支付失败")};return O((e=>{(null==e?void 0:e.checkPay)&&(a.orderId=e.id,a.from=e.from,a.showCheck=!0)})),R((()=>{(async()=>{const e=await te();Object.assign(s,e)})()})),(e,c)=>{const b=_(w("page-meta"),T),g=f,x=B,S=y,v=_(w("u-button"),q),C=W,D=_(w("payment"),fe);return l(),I(z,null,[o(b,{"page-style":e.$theme.pageStyle},null,8,["page-style"]),o(g,{class:"recharge p-[20rpx]"},{default:i((()=>[o(g,{class:"bg-white rounded-[14rpx] p-[40rpx]"},{default:i((()=>[o(g,{class:"text-content"},{default:i((()=>[p("充值金额")])),_:1}),o(g,{class:"border-0 border-b border-solid border-light"},{default:i((()=>[o(x,{modelValue:t.value,"onUpdate:modelValue":c[0]||(c[0]=e=>t.value=e),class:"text-[60rpx] h-[60rpx] py-[24rpx]",placeholder:"0.00",type:"digit"},null,8,["modelValue"])])),_:1}),o(g,{class:"mt-[20rpx] text-xs text-muted"},{default:i((()=>[p(" 当前可用余额 "),o(S,{class:"text-primary"},{default:i((()=>[p(h(s.user_money),1)])),_:1})])),_:1})])),_:1}),o(g,{class:"mt-[40rpx]"},{default:i((()=>[o(v,{loading:m(r),type:"primary",shape:"circle",onClick:m(n)},{default:i((()=>[p(" 立即充值 ")])),_:1},8,["loading","onClick"])])),_:1}),o(g,{class:"flex justify-center m-[60rpx]"},{default:i((()=>[o(C,{url:"/packages/pages/recharge_record/recharge_record","hover-class":"none"},{default:i((()=>[o(S,{class:"text-content text-sm"},{default:i((()=>[p("充值记录")])),_:1})])),_:1})])),_:1}),o(D,{show:a.showPay,"onUpdate:show":c[1]||(c[1]=e=>a.showPay=e),"show-check":a.showCheck,"onUpdate:showCheck":c[2]||(c[2]=e=>a.showCheck=e),"order-id":a.orderId,from:a.from,redirect:a.redirect,onSuccess:u,onFail:d},null,8,["show","show-check","order-id","from","redirect"])])),_:1})],64)}}});export{ye as default};
