import i from"./decoration-img-2F0tdl1c.js";import{d,c as f,o as t,a as s,F as p,r as m,as as u,m as _,b as x,t as y,e as b,x as h}from"./index-B2xNDy79.js";const g={class:"tabbar flex"},v={class:"leading-3 text-[12px] mt-[4px]"},k=d({__name:"content",props:{style:{type:Object,default:()=>({})},list:{type:Array,default:()=>[]}},setup(a){const c=a,n=f(()=>{var e;return((e=c.list)==null?void 0:e.filter(r=>r.is_show==1))||[]});return(e,r)=>(t(),s("div",g,[(t(!0),s(p,null,m(b(n),(o,l)=>(t(),s("div",{key:l,class:"tabbar-item flex flex-col justify-center items-center flex-1",style:u({color:a.style.default_color})},[_(i,{width:"22px",height:"22px",src:o.unselected,fit:"cover"},null,8,["src"]),x("div",v,y(o.name),1)],4))),128))]))}}),j=h(k,[["__scopeId","data-v-33d760f8"]]);export{j as default};
