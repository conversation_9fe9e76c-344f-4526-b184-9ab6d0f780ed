import{_ as w}from"./index-onOHNH0j.js";import{d as p,j as y,z as E,o as l,a as k,m as r,w as a,b as s,e as m,B,C,p as V,v as x}from"./index-B2xNDy79.js";import{E as D}from"./el-card-DpH4mUSc.js";import{E as S,a as j}from"./el-form-item-DlU85AZK.js";import{_ as N}from"./picker-Cd5l2hZ5.js";import{a as U,b as F}from"./user-BU48F4L7.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./index-BuNto3DN.js";import"./index-DSiy6YVt.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";const h={class:"user-setup"},z=p({name:"userSetup"}),it=p({...z,setup(I){const o=y({default_avatar:""}),n=async()=>{try{const e=await U();for(const t in o)o[t]=e[t]}catch(e){console.log("获取=>",e)}},_=async()=>{try{await F(o),n()}catch(e){console.log("保存=>",e)}};return n(),(e,t)=>{const u=N,i=S,c=j,d=D,f=x,v=w,g=E("perms");return l(),k("div",h,[r(d,{shadow:"never",class:"!border-none"},{default:a(()=>[t[2]||(t[2]=s("div",{class:"font-medium mb-7"},"基本设置",-1)),r(c,{ref:"formRef",model:m(o),"label-width":"120px"},{default:a(()=>[r(i,{label:"用户默认头像"},{default:a(()=>[s("div",null,[r(u,{modelValue:m(o).default_avatar,"onUpdate:modelValue":t[0]||(t[0]=b=>m(o).default_avatar=b),limit:1},null,8,["modelValue"])])]),_:1}),r(i,null,{default:a(()=>t[1]||(t[1]=[s("div",null,[s("div",{class:"form-tips"}," 用户注册时给的默认头像，建议尺寸：400*400像素，支持jpg，jpeg，png格式 ")],-1)])),_:1})]),_:1},8,["model"])]),_:1}),B((l(),C(v,null,{default:a(()=>[r(f,{type:"primary",onClick:_},{default:a(()=>t[3]||(t[3]=[V("保存")])),_:1})]),_:1})),[[g,["setting.user.user/setConfig"]]])])}}});export{it as default};
