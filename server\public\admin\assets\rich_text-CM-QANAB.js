import{d as l,j as n,o as s,a as d,m as t,w as i,e as r}from"./index-B2xNDy79.js";import{E as u}from"./el-card-DpH4mUSc.js";import{_}from"./index.vue_vue_type_style_index_0_lang-Dw4fui56.js";import"./picker-Cd5l2hZ5.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-BuNto3DN.js";import"./index-DSiy6YVt.js";import"./_baseClone-CdezRMKA.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";const J=l({__name:"rich_text",setup(c){const e=n({value1:"",value2:""});return(f,o)=>{const a=_,p=u;return s(),d("div",null,[t(p,{header:"基础使用",shadow:"never",class:"!border-none"},{default:i(()=>[t(a,{modelValue:r(e).value1,"onUpdate:modelValue":o[0]||(o[0]=m=>r(e).value1=m),height:"500px"},null,8,["modelValue"])]),_:1}),t(p,{header:"简洁模式",shadow:"never",class:"!border-none mt-4"},{default:i(()=>[t(a,{modelValue:r(e).value2,"onUpdate:modelValue":o[1]||(o[1]=m=>r(e).value2=m),height:"500px",mode:"simple"},null,8,["modelValue"])]),_:1})])}}});export{J as default};
