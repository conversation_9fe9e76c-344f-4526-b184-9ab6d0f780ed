import{_ as x}from"./index-onOHNH0j.js";import{d as _,z as w,o as n,a as b,m as e,w as o,b as i,B as l,C as m,e as p,p as c,v as C,x as k}from"./index-B2xNDy79.js";import{E as B}from"./el-card-DpH4mUSc.js";import{E}from"./el-alert-BUxHh72o.js";import g from"./oa-attr-C5W-LyEc.js";import y from"./oa-phone-BqzLkuVb.js";import{u as M}from"./useMenuOa-CrPjRcCC.js";import"./index-BuNto3DN.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./oa-menu-form.vue_vue_type_script_setup_true_lang-uZC5lSoG.js";import"./el-form-item-DlU85AZK.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";/* empty css                       */import"./el-radio-CKcO4hVq.js";import"./oa-menu-form-edit.vue_vue_type_script_setup_true_lang-Vx-dPtFc.js";import"./wx_oa-3-DCeMZg.js";const O={class:"menu-oa"},N={class:"lg:flex flex-1"},V={class:"mt-4 lg:mt-0 max-w-[400px]"},A=_({name:"wxOaMenu"}),D=_({...A,setup(P){const{getOaMenuFunc:d,handleSave:u,handlePublish:f}=M(void 0);return d(),(z,t)=>{const v=E,s=B,a=C,h=x,r=w("perms");return n(),b("div",O,[e(s,{class:"!border-none",shadow:"never"},{default:o(()=>[e(v,{type:"warning",title:"配置微信公众号菜单，点击确认，保存菜单并发布至微信公众号",closable:!1,"show-icon":""})]),_:1}),e(s,{class:"!border-none mt-4",shadow:"never"},{default:o(()=>[i("div",N,[e(y),i("div",V,[e(g)])])]),_:1}),e(h,null,{default:o(()=>[l((n(),m(a,{type:"primary",onClick:p(u)},{default:o(()=>t[0]||(t[0]=[c(" 保存 ")])),_:1},8,["onClick"])),[[r,["channel:oaMenu:save"]]]),l((n(),m(a,{type:"primary",onClick:p(f)},{default:o(()=>t[1]||(t[1]=[c(" 发布 ")])),_:1},8,["onClick"])),[[r,["channel:oaMenu:publish"]]])]),_:1})])}}}),ee=k(D,[["__scopeId","data-v-681915d2"]]);export{ee as default};
