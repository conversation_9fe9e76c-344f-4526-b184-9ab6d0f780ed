[www]
; 用户和组
user = www-data
group = www-data

; 监听设置
listen = 9000
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

; 进程管理
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

; 超时设置
request_terminate_timeout = 300s
request_slowlog_timeout = 10s

; 日志设置
access.log = /var/log/php/www-access.log
slowlog = /var/log/php/www-slow.log

; 环境变量
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; PHP 管理值
php_admin_value[sendmail_path] = /usr/sbin/sendmail -t -i -f <EMAIL>
php_flag[display_errors] = off
php_admin_value[error_log] = /var/log/php/www-error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 512M

; 会话设置
php_value[session.save_handler] = files
php_value[session.save_path] = /var/lib/php/sessions
php_value[soap.wsdl_cache_dir] = /var/lib/php/wsdlcache

; 安全设置
security.limit_extensions = .php .php3 .php4 .php5 .php7 .php8
