import{_ as e}from"./page-meta.438f2c32.js";import{d as t,z as a,L as s,M as l,c as i,A as o,o as n,b as u,w as r,E as d,f as c,j as f,n as h,N as v,p,C as b,O as m,e as g,G as y,P as x,r as _,a as w,g as k,F as C,h as S,x as T,l as z,t as F,Q as O,R as I,T as j,U as $,V as X,I as B,i as Y}from"./index-561dd99e.js";import{_ as N}from"./u-search.1f3b26b2.js";import{_ as R}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as H}from"./u-badge.45c73cfd.js";import{_ as W}from"./tabbar.vue_vue_type_script_setup_true_lang.b3a4c4a3.js";import{_ as M}from"./news-card.1749442f.js";import{_ as V}from"./z-paging.9764c1e2.js";import{g as U,a as A}from"./news.a3153aee.js";import"./u-icon.f1b72599.js";import"./u-image.e9ed38ca.js";import"./icon_visit.713e13e8.js";const E=R(t({__name:"tab",props:{dot:{type:[Boolean,String],default:!1},name:{type:[Boolean,String],default:""},info:null},setup(e){const t=e,b=a(!1),m=a(!1),g=a(!1),y=a(void 0),x=s("updateTabs"),_=s("handleChange"),w=()=>{x&&x()},k=v();console.log(k),_(null==k?void 0:k.props,(e=>{y.value=y.value||e,b.value=e,g.value=y.value,m.value=e})),l((()=>{w()}));const C=i((()=>{const{dot:e,info:a}=t;return{dot:e,info:a}}));return o((()=>C.value),(()=>{w()})),o((()=>t.name),(e=>{w()})),(e,t)=>{const a=p;return n(),u(a,{class:f({active:b.value,inactive:!b.value,tab:!0}),style:h(m.value?"":"display: none;")},{default:r((()=>[g.value?d(e.$slots,"default",{key:0},void 0,!0):c("v-if",!0)])),_:3},8,["class","style"])}}}),[["__scopeId","data-v-a5e2333b"]]);const G=R({name:"u-sticky",emits:["fixed","unfixed"],props:{offsetTop:{type:[Number,String],default:0},index:{type:[Number,String],default:""},enable:{type:Boolean,default:!0},h5NavHeight:{type:[Number,String],default:44},bgColor:{type:String,default:"#ffffff"},zIndex:{type:[Number,String],default:""}},data(){return{fixed:!1,height:"auto",stickyTop:0,elClass:this.$u.guid(),left:0,width:"auto"}},watch:{offsetTop(e){this.initObserver()},enable(e){0==e?(this.fixed=!1,this.disconnectObserver("contentObserver")):this.initObserver()}},computed:{uZIndex(){return this.zIndex?this.zIndex:this.$u.zIndex.sticky}},mounted(){this.initObserver()},methods:{initObserver(){this.enable&&(this.stickyTop=0!=this.offsetTop?b(this.offsetTop)+this.h5NavHeight:this.h5NavHeight,this.disconnectObserver("contentObserver"),this.$uGetRect("."+this.elClass).then((e=>{this.height=e.height,this.left=e.left,this.width=e.width,this.$nextTick((()=>{this.observeContent()}))})))},observeContent(){this.disconnectObserver("contentObserver");const e=m(this,{thresholds:[.95,.98,1]});e.relativeToViewport({top:-this.stickyTop}),e.observe("."+this.elClass,(e=>{this.enable&&this.setFixed(e.boundingClientRect.top)})),this.contentObserver=e},setFixed(e){const t=e<this.stickyTop;t?this.$emit("fixed",this.index):this.fixed&&this.$emit("unfixed",this.index),this.fixed=t},disconnectObserver(e){const t=this[e];t&&t.disconnect()}},beforeUnmount(){this.disconnectObserver("contentObserver")}},[["render",function(e,t,a,s,l,i){const o=p;return n(),u(o,{class:""},{default:r((()=>[g(o,{class:f(["u-sticky-wrap",[l.elClass]]),style:h({height:l.fixed?l.height+"px":"auto",backgroundColor:a.bgColor})},{default:r((()=>[g(o,{class:"u-sticky",style:h({position:l.fixed?"fixed":"static",top:l.stickyTop+"px",left:l.left+"px",width:"auto"==l.width?"auto":l.width+"px",zIndex:i.uZIndex})},{default:r((()=>[d(e.$slots,"default",{},void 0,!0)])),_:3},8,["style"])])),_:3},8,["class","style"])])),_:3})}],["__scopeId","data-v-74e1f11e"]]);const Q=R(t({__name:"tabs",props:{isScroll:{type:Boolean,default:!0},current:{default:0},height:{default:80},fontSize:{default:28},duration:{default:.3},activeColor:{default:"var(--color-primary)"},inactiveColor:{default:"#333"},barWidth:{default:40},barHeight:{default:4},gutter:{default:30},bgColor:{default:"#FFFFFF"},name:{default:"name"},count:{default:"count"},offset:{default:[5,20]},bold:{type:Boolean,default:!0},activeItemStyle:{default:{}},showBar:{type:Boolean,default:!0},barStyle:{default:{}},itemWidth:{default:"auto"},isFixed:{type:Boolean,default:!1},top:{default:0},stickyBgColor:{default:"#FFFFFF"},swipeable:{type:Boolean,default:!0}},emits:["change"],setup(e,{emit:t}){const s=e,{touch:m,resetTouchStatus:X,touchStart:B,touchMove:Y}=function(){const e=y({direction:"",deltaX:0,deltaY:0,offsetX:0,offsetY:0}),t=()=>{e.direction="",e.deltaX=0,e.deltaY=0,e.offsetX=0,e.offsetY=0};return{touch:e,resetTouchStatus:t,touchStart:a=>{t();const s=a.touches[0];e.startX=s.clientX,e.startY=s.clientY},touchMove:t=>{const a=t.touches[0];var s,l;e.deltaX=a.clientX-e.startX,e.deltaY=a.clientY-e.startY,e.offsetX=Math.abs(e.deltaX),e.offsetY=Math.abs(e.deltaY),e.direction=e.direction||((s=e.offsetX)>(l=e.offsetY)&&s>10?"horizontal":l>s&&l>10?"vertical":"")}}}(),N=a([]),R=a([]),W=a(0),M=a([]),V=a(0),U=a(0),A=a(0),E=a("cu-tab"),Q=a(s.current),Z=a(!0),L=a(!1),P=v();o((()=>N.value),(async(e,t)=>{Z.value||e.length===t.length||(Q.value=0),await O(),K()})),o((()=>s.current),((e,t)=>{O((()=>{Q.value=e,te()}))}),{immediate:!0});const q=i((()=>{const e={width:s.barWidth+"rpx",transform:`translate(${U.value}px, -100%)`,"transition-duration":`${Z.value?0:s.duration}s`,"background-color":s.activeColor,height:s.barHeight+"rpx",opacity:Z.value?0:1,"border-radius":s.barHeight/2+"px"};return Object.assign(e,s.barStyle),e})),D=i((()=>e=>{let t={height:s.height+"rpx","line-height":s.height+"rpx","font-size":s.fontSize+"rpx",padding:s.isScroll?`0 ${s.gutter}rpx`:"",flex:s.isScroll?"auto":"1",width:`${s.itemWidth}rpx`};return e==Q.value&&s.bold&&(t.fontWeight="bold"),e==Q.value?(t.color=s.activeColor,t=Object.assign(t,s.activeItemStyle)):t.color=s.inactiveColor,t})),J=()=>{N.value=R.value.map((e=>{const{name:t,dot:a,active:s,inited:l}=e.event,{updateRender:i}=e;return{name:t,dot:a,active:s,inited:l,updateRender:i}}))},K=async()=>{const e=await I("#"+E.value,!1,P);A.value=e.left,V.value=e.width,ee()},ee=()=>{const e=j().in(P);for(let t=0;t<N.value.length;t++)e.select(`#tab-item-${t}`).fields({size:!0,rect:!0});e.exec((e=>{M.value=e,te()}))},te=()=>{const e=M.value[Q.value];if(!e)return;const t=e.width,a=e.left-A.value-(V.value-t)/2;W.value=a<0?0:a;const l=e.left+e.width/2-A.value;U.value=l-b(s.barWidth)/2,1==Z.value&&setTimeout((()=>{Z.value=!1}),100),R.value.forEach(((e,t)=>{const a=t===Q.value;a===e.event.active&&e.event.inited||e.updateRender(a)}))},ae=e=>{s.swipeable&&(L.value=!0,B(e))},se=e=>{s.swipeable&&L.value&&Y(e)},le=()=>{if(!s.swipeable||!L.value)return;if("horizontal"===m.direction&&m.offsetX>=50){let e,a=N.value.length,s=Q.value;e=m.deltaX<=0?s>=a-1?0:s+1:s<=0?a-1:s-1,O((()=>{Q.value=e,te()})),t("change",e)}L.value=!1};return l((()=>{J()})),x("handleChange",((e,t)=>{R.value.push({event:e,updateRender:t})})),x("updateTabs",J),(a,s)=>{const l=_(w("u-badge"),H),i=p,o=$,v=_(w("u-sticky"),G);return n(),u(i,{class:"tabs"},{default:r((()=>[g(v,{enable:e.isFixed,"bg-color":e.stickyBgColor,"offset-top":e.top,"h5-nav-height":0},{default:r((()=>[g(i,{id:E.value,style:h({background:e.bgColor})},{default:r((()=>[g(o,{style:h({height:e.height+"rpx"}),"scroll-x":"",class:"scroll-view","scroll-left":W.value,"scroll-with-animation":""},{default:r((()=>[g(i,{class:f(["scroll-box",{"tabs-scorll-flex":!e.isScroll}])},{default:r((()=>[(n(!0),k(C,null,S(N.value,((a,s)=>(n(),u(i,{class:"tab-item line1",id:"tab-item-"+s,key:s,onClick:e=>(e=>{e!=Q.value&&(O((()=>{Q.value=e,te()})),t("change",e))})(s),style:h([T(D)(s)])},{default:r((()=>[g(l,{count:a[e.count]||a.dot||0,offset:e.offset,size:"mini"},null,8,["count","offset"]),z(" "+F(a[e.name]||a.name),1)])),_:2},1032,["id","onClick","style"])))),128)),e.showBar?(n(),u(i,{key:0,class:"tab-bar",style:h([T(q)])},null,8,["style"])):c("v-if",!0)])),_:1},8,["class"])])),_:1},8,["style","scroll-left"])])),_:1},8,["id","style"])])),_:1},8,["enable","bg-color","offset-top"]),g(i,{class:"tab-content",onTouchstart:ae,onTouchmove:se,onTouchcancel:le,onTouchend:le},{default:r((()=>[c(' <view class="tab-track" :class="{\'tab-animated\': animated}" :style="[trackStyle]"> '),g(i,null,{default:r((()=>[d(a.$slots,"default",{},void 0,!0)])),_:3}),c(" </view> ")])),_:3})])),_:3})}}}),[["__scopeId","data-v-a021632e"]]),Z=t({__name:"news-list",props:{cid:{default:0},i:null,index:null},setup(e){const t=e,s=X(null),l=a([]),i=a(!0);o((()=>t.index),(async()=>{var e;await O(),t.i==t.index&&i.value&&(i.value=!1,null==(e=s.value)||e.reload())}),{immediate:!0});const d=async(e,a)=>{try{const{lists:l}=await U({cid:t.cid,page_no:e,page_size:a});s.value.complete(l)}catch(l){console.log("报错=>",l),s.value.complete(!1)}};return(t,a)=>{const i=_(w("news-card"),M),o=_(w("z-paging"),V);return n(),u(o,{"auto-show-back-to-top":"",auto:e.i==e.index,ref_key:"paging",ref:s,modelValue:l.value,"onUpdate:modelValue":a[0]||(a[0]=e=>l.value=e),"data-key":e.i,onQuery:d,fixed:!1,height:"100%"},{default:r((()=>[(n(!0),k(C,null,S(l.value,((e,t)=>(n(),u(i,{key:t,item:e,newsId:e.id},null,8,["item","newsId"])))),128))])),_:1},8,["auto","modelValue","data-key"])}}}),L=R(t({__name:"news",setup(t){const s=a([]),l=a(0),i=e=>{console.log(e),l.value=Number(e)};return B((e=>{(async()=>{const e=await A();s.value=[{name:"全部",id:""}].concat(e)})()})),(t,a)=>{const o=_(w("page-meta"),e),d=_(w("u-search"),N),f=Y,h=p,v=_(w("tab"),E),b=_(w("tabs"),Q),m=_(w("tabbar"),W);return n(),k(C,null,[g(o,{"page-style":t.$theme.pageStyle},null,8,["page-style"]),g(h,{class:"news"},{default:r((()=>[c(" 搜索 "),g(f,{class:"news-search px-[24rpx] py-[14rpx] bg-white",url:"/pages/search/search"},{default:r((()=>[g(d,{placeholder:"请输入关键词搜索",disabled:"","show-action":!1})])),_:1}),c(" 内容 "),g(b,{current:l.value,onChange:i,height:"80","bar-width":"60",barStyle:{bottom:"0"}},{default:r((()=>[(n(!0),k(C,null,S(s.value,((e,t)=>(n(),u(v,{key:t,name:e.name},{default:r((()=>[g(h,{class:"news-list pt-[20rpx]"},{default:r((()=>[g(Z,{cid:e.id,i:t,index:l.value},null,8,["cid","i","index"])])),_:2},1024)])),_:2},1032,["name"])))),128))])),_:1},8,["current"]),g(m)])),_:1})],64)}}}),[["__scopeId","data-v-f3b641ed"]]);export{L as default};
