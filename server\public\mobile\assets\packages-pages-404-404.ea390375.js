import{_ as e}from"./page-meta.438f2c32.js";import{o as t,g as a,e as r,w as s,F as l,r as o,p,l as n,a as u}from"./index-561dd99e.js";import{_ as m}from"./u-empty.36fe4845.js";import{_ as f}from"./router-navigate.3c22c13e.js";import{_ as i}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.f1b72599.js";const x=i({},[["render",function(i,x){const c=o(u("page-meta"),e),d=o(u("u-empty"),m),g=p,_=o(u("router-navigate"),f);return t(),a(l,null,[r(c,{"page-style":i.$theme.pageStyle},null,8,["page-style"]),r(g,{class:"h-screen flex flex-col justify-center items-center"},{default:s((()=>[r(g,null,{default:s((()=>[r(d,{text:"对不起，您访问的页面不存在",mode:"data"})])),_:1}),r(g,{class:"w-full px-[100rpx] mt-[40rpx]"},{default:s((()=>[r(_,{class:"bg-primary rounded-full text-btn-text leading-[80rpx] text-center",to:"/","nav-type":"reLaunch"},{default:s((()=>[n(" 返回首页 ")])),_:1})])),_:1})])),_:1})],64)}]]);export{x as default};
