import{d as C,b9 as Y,ba as _,bb as G,a9 as B,bc as J,c as u,bd as H,o as i,a as S,b as m,U as r,Y as t,e,t as Q,m as y,w as n,C as F,aq as W,S as X,G as P,as as M,ar as N,be as Z,bf as x,bg as ee,bh as oe,am as se,al as ae,an as le,i as T,bi as te,av as ne,bj as re,B as ie,bk as de,bl as ce,bm as fe,b8 as ue,R as pe,bn as ge,bo as be,ay as ve}from"./index-B2xNDy79.js";const z=Symbol("dialogInjectionKey"),me=C({name:"ElDialogContent"}),ye=C({...me,props:Y,emits:_,setup(h,{expose:k}){const l=h,{t:w}=G(),{Close:d}=Z,{dialogRef:c,headerRef:p,bodyId:f,ns:s,style:g}=B(z),{focusTrapRef:b}=B(J),R=u(()=>[s.b(),s.is("fullscreen",l.fullscreen),s.is("draggable",l.draggable),s.is("align-center",l.alignCenter),{[s.m("center")]:l.center}]),E=x(b,c),v=u(()=>l.draggable),$=u(()=>l.overflow),{resetPosition:A}=H(c,p,v,$);return k({resetPosition:A}),(a,L)=>(i(),S("div",{ref:e(E),class:t(e(R)),style:M(e(g)),tabindex:"-1"},[m("header",{ref_key:"headerRef",ref:p,class:t([e(s).e("header"),{"show-close":a.showClose}])},[r(a.$slots,"header",{},()=>[m("span",{role:"heading","aria-level":a.ariaLevel,class:t(e(s).e("title"))},Q(a.title),11,["aria-level"])]),a.showClose?(i(),S("button",{key:0,"aria-label":e(w)("el.dialog.close"),class:t(e(s).e("headerbtn")),type:"button",onClick:D=>a.$emit("close")},[y(e(X),{class:t(e(s).e("close"))},{default:n(()=>[(i(),F(W(a.closeIcon||e(d))))]),_:1},8,["class"])],10,["aria-label","onClick"])):P("v-if",!0)],2),m("div",{id:e(f),class:t(e(s).e("body"))},[r(a.$slots,"default")],10,["id"]),a.$slots.footer?(i(),S("footer",{key:0,class:t(e(s).e("footer"))},[r(a.$slots,"footer")],2)):P("v-if",!0)],6))}});var Ce=N(ye,[["__file","dialog-content.vue"]]);const he=C({name:"ElDialog",inheritAttrs:!1}),ke=C({...he,props:ee,emits:oe,setup(h,{expose:k}){const l=h,w=se();ae({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},u(()=>!!w.title));const d=le("dialog"),c=T(),p=T(),f=T(),{visible:s,titleId:g,bodyId:b,style:R,overlayDialogStyle:E,rendered:v,zIndex:$,afterEnter:A,afterLeave:a,beforeLeave:L,handleClose:D,onModalClick:j,onOpenAutoFocus:q,onCloseAutoFocus:K,onCloseRequested:O,onFocusoutPrevented:U}=te(l,c);ne(z,{dialogRef:c,headerRef:p,bodyId:b,ns:d,rendered:v,style:R});const I=be(j),V=u(()=>l.draggable&&!l.fullscreen);return k({visible:s,dialogContentRef:f,resetPosition:()=>{var o;(o=f.value)==null||o.resetPosition()}}),(o,Ee)=>(i(),F(e(ge),{to:o.appendTo,disabled:o.appendTo!=="body"?!1:!o.appendToBody},{default:n(()=>[y(re,{name:"dialog-fade",onAfterEnter:e(A),onAfterLeave:e(a),onBeforeLeave:e(L),persisted:""},{default:n(()=>[ie(y(e(de),{"custom-mask-event":"",mask:o.modal,"overlay-class":o.modalClass,"z-index":e($)},{default:n(()=>[m("div",{role:"dialog","aria-modal":"true","aria-label":o.title||void 0,"aria-labelledby":o.title?void 0:e(g),"aria-describedby":e(b),class:t(`${e(d).namespace.value}-overlay-dialog`),style:M(e(E)),onClick:e(I).onClick,onMousedown:e(I).onMousedown,onMouseup:e(I).onMouseup},[y(e(ce),{loop:"",trapped:e(s),"focus-start-el":"container",onFocusAfterTrapped:e(q),onFocusAfterReleased:e(K),onFocusoutPrevented:e(U),onReleaseRequested:e(O)},{default:n(()=>[e(v)?(i(),F(Ce,fe({key:0,ref_key:"dialogContentRef",ref:f},o.$attrs,{center:o.center,"align-center":o.alignCenter,"close-icon":o.closeIcon,draggable:e(V),overflow:o.overflow,fullscreen:o.fullscreen,"show-close":o.showClose,title:o.title,"aria-level":o.headerAriaLevel,onClose:e(D)}),ue({header:n(()=>[o.$slots.title?r(o.$slots,"title",{key:1}):r(o.$slots,"header",{key:0,close:e(D),titleId:e(g),titleClass:e(d).e("title")})]),default:n(()=>[r(o.$slots,"default")]),_:2},[o.$slots.footer?{name:"footer",fn:n(()=>[r(o.$slots,"footer")])}:void 0]),1040,["center","align-center","close-icon","draggable","overflow","fullscreen","show-close","title","aria-level","onClose"])):P("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,["aria-label","aria-labelledby","aria-describedby","onClick","onMousedown","onMouseup"])]),_:3},8,["mask","overlay-class","z-index"]),[[pe,e(s)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])]),_:3},8,["to","disabled"]))}});var we=N(ke,[["__file","dialog.vue"]]);const Ae=ve(we);export{Ae as E};
