import{d as m,i as c,o as p,a as r,m as t,w as o,e as u,D as f,F as b,r as y,a1 as x,a2 as g,x as v}from"./index-B2xNDy79.js";import{E}from"./el-card-DpH4mUSc.js";import{_ as V}from"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-BuNto3DN.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-DSiy6YVt.js";import"./_baseClone-CdezRMKA.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";const k={class:"material-index"},w=m({name:"material"}),z=m({...w,setup(C){const s=[{type:"image",name:"图片"},{type:"video",name:"视频"},{type:"file",name:"文件"}],a=c("image");return(T,n)=>{const i=V,l=x,_=g,d=E;return p(),r("div",k,[t(d,{class:"!border-none",shadow:"never"},{default:o(()=>[t(_,{modelValue:u(a),"onUpdate:modelValue":n[0]||(n[0]=e=>f(a)?a.value=e:null)},{default:o(()=>[(p(),r(b,null,y(s,e=>t(l,{label:e.name,name:e.type,index:e.type,key:e.type,lazy:""},{default:o(()=>[t(i,{type:e.type,mode:"page","file-size":"120px",limit:-1,"page-size":20},null,8,["type"])]),_:2},1032,["label","name","index"])),64))]),_:1},8,["modelValue"])]),_:1})])}}}),X=v(z,[["__scopeId","data-v-f2b7db3b"]]);export{X as default};
