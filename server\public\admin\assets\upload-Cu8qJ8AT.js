import{d as _,o as u,a as c,m as o,w as e,b as r,p,v as f}from"./index-B2xNDy79.js";import{E as v}from"./el-card-DpH4mUSc.js";import{U as i}from"./index-DSiy6YVt.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./isEqual-CLGO95LP.js";import"./index-C6Cr8aHe.js";const y={class:"flex flex-wrap"},g={class:"m-4"},w={class:"m-4"},h={class:"m-4"},x={class:"m-4"},T=_({__name:"upload",setup(E){const a=s=>{console.log("上传文件的状态发生改变",s)},l=s=>{console.log("上传文件成功",s)},n=s=>{console.log("上传文件失败",s)};return(s,t)=>{const d=f,m=v;return u(),c("div",null,[o(m,{header:"基础使用",shadow:"never",class:"!border-none"},{default:e(()=>[r("div",y,[r("div",g,[o(i,{onChange:a,onSuccess:l,onError:n,"show-progress":!0},{default:e(()=>[o(d,{type:"primary"},{default:e(()=>t[0]||(t[0]=[p("上传图片")])),_:1})]),_:1})]),r("div",w,[o(i,{type:"video",onChange:a,onSuccess:l,onError:n,"show-progress":!0},{default:e(()=>[o(d,{type:"primary"},{default:e(()=>t[1]||(t[1]=[p("上传视频")])),_:1})]),_:1})]),r("div",h,[o(i,{multiple:!1,onChange:a,onSuccess:l,onError:n,"show-progress":!0},{default:e(()=>[o(d,{type:"primary"},{default:e(()=>t[2]||(t[2]=[p("取消多选")])),_:1})]),_:1})]),r("div",x,[o(i,{limit:2,onChange:a,onSuccess:l,onError:n,"show-progress":!0},{default:e(()=>[o(d,{type:"primary"},{default:e(()=>t[3]||(t[3]=[p("一次最多上传2张")])),_:1})]),_:1})])])]),_:1})])}}});export{T as default};
