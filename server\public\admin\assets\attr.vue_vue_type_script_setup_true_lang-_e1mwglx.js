import{d as F,c as O,o as d,a as v,m as o,w as s,b as n,e as u,C as _,G as r,p as S,I as b,E as q,J as z,q as A,v as G}from"./index-B2xNDy79.js";import{E as J,a as R}from"./el-form-item-DlU85AZK.js";import{E as T}from"./el-card-DpH4mUSc.js";import{_ as H}from"./index-BuNto3DN.js";import{_ as K}from"./picker-qQ9YEtJl.js";import{D as L,_ as M}from"./picker-Cd5l2hZ5.js";import{c as k}from"./index-DSiy6YVt.js";const P={class:"flex-1"},Q={class:"bg-fill-light w-full p-4 mt-4"},W={class:"flex-1"},X={class:"flex-1 flex items-center"},Y={class:"drag-move cursor-move ml-auto"},Z={key:0,class:"mt-4"},f=5,ce=F({__name:"attr",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})},type:{type:String,default:"mobile"}},emits:["update:content"],setup(m,{emit:y}){const p=y,c=m,g=O({get:()=>c.content,set:a=>{p("update:content",a)}}),w=()=>{var a;if(((a=c.content.data)==null?void 0:a.length)<f){const e=k(c.content);e.data.push({is_show:"1",image:"",name:"",link:{}}),p("update:content",e)}else b.msgError(`最多添加${f}张图片`)},E=a=>{var i;if(((i=c.content.data)==null?void 0:i.length)<=1)return b.msgError("最少保留一张图片");const e=k(c.content);e.data.splice(a,1),p("update:content",e)};return(a,e)=>{const i=M,U=K,C=q,h=J,B=z,D=A,N=H,$=G,I=T,j=R;return d(),v("div",null,[o(j,{"label-width":"70px"},{default:s(()=>[o(I,{shadow:"never",class:"!border-none flex mt-2"},{default:s(()=>{var x;return[e[2]||(e[2]=n("div",{class:"flex items-end"},[n("div",{class:"text-base text-[#101010] font-medium"},"图片设置"),n("div",{class:"text-xs text-tx-secondary ml-2"}," 最多添加5张，建议图片尺寸：750px*200px ")],-1)),n("div",P,[o(u(L),{class:"draggable",modelValue:u(g).data,"onUpdate:modelValue":e[0]||(e[0]=t=>u(g).data=t),animation:"300",handle:".drag-move"},{item:s(({element:t,index:V})=>[(d(),_(N,{key:V,onClose:l=>E(V),class:"w-full"},{default:s(()=>[n("div",Q,[o(i,{width:"396px",height:"196px",modelValue:t.image,"onUpdate:modelValue":l=>t.image=l,"upload-class":"bg-body","exclude-domain":""},null,8,["modelValue","onUpdate:modelValue"]),n("div",W,[o(h,{class:"mt-[18px]",label:"图片链接"},{default:s(()=>[m.type=="mobile"?(d(),_(U,{key:0,modelValue:t.link,"onUpdate:modelValue":l=>t.link=l},null,8,["modelValue","onUpdate:modelValue"])):r("",!0),m.type=="pc"?(d(),_(C,{key:1,placeholder:"请输入链接",modelValue:t.link.path,"onUpdate:modelValue":l=>t.link.path=l},null,8,["modelValue","onUpdate:modelValue"])):r("",!0)]),_:2},1024),o(h,{label:"是否显示",class:"mt-[18px] !mb-0"},{default:s(()=>[n("div",X,[o(B,{modelValue:t.is_show,"onUpdate:modelValue":l=>t.is_show=l,"active-value":"1","inactive-value":"0"},null,8,["modelValue","onUpdate:modelValue"]),n("div",Y,[o(D,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"])]),((x=m.content.data)==null?void 0:x.length)<f?(d(),v("div",Z,[o($,{class:"w-full",type:"primary",onClick:w},{default:s(()=>e[1]||(e[1]=[S("添加图片")])),_:1})])):r("",!0)]}),_:1})]),_:1})])}}});export{ce as _};
