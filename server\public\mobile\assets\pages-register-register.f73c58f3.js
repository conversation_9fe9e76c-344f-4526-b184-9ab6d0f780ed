import{d as e,z as a,u as t,c as r,G as l,o,g as s,e as u,w as p,l as d,x as n,b as c,k as m,f as i,F as f,ah as x,ai as _,r as g,a as h,p as b}from"./index-561dd99e.js";import{_ as y}from"./page-meta.438f2c32.js";import{_ as v}from"./u-input.adb6d3eb.js";import{_ as w}from"./router-navigate.3c22c13e.js";import{_ as V,a as j}from"./u-modal.208f51b9.js";import{_ as k}from"./u-button.e98befd5.js";import{_ as C}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.f1b72599.js";import"./emitter.1571a5d9.js";import"./u-loading.8620e4d6.js";import"./u-popup.6496bd54.js";const U=C(e({__name:"register",setup(e){const C=a(!1),U=t(),$=r((()=>1==U.getLoginConfig.login_agreement)),S=l({account:"",password:"",password_confirm:""}),F=a(!1),L=async()=>S.account?S.password?S.password_confirm?!C.value&&$.value?F.value=!0:S.password!=S.password_confirm?uni.$u.toast("两次输入的密码不一致"):(await x(S),void _()):uni.$u.toast("请输入确认密码"):uni.$u.toast("请输入密码"):uni.$u.toast("请输入账号");return(e,a)=>{const t=g(h("page-meta"),y),r=b,l=g(h("u-input"),v),x=g(h("router-navigate"),w),_=g(h("u-checkbox"),V),U=g(h("u-button"),k),z=g(h("u-modal"),j);return o(),s(f,null,[u(t,{"page-style":e.$theme.pageStyle},null,8,["page-style"]),u(r,{class:"register bg-white min-h-full flex flex-col items-center px-[40rpx] pt-[40rpx] box-border"},{default:p((()=>[u(r,{class:"w-full"},{default:p((()=>[u(r,{class:"text-2xl font-medium mb-[60rpx]"},{default:p((()=>[d("注册新账号")])),_:1}),u(r,{class:"px-[18rpx] border border-solid border-lightc border-light rounded-[10rpx] h-[100rpx] items-center flex"},{default:p((()=>[u(l,{class:"flex-1",modelValue:S.account,"onUpdate:modelValue":a[0]||(a[0]=e=>S.account=e),border:!1,placeholder:"请输入账号"},null,8,["modelValue"])])),_:1}),u(r,{class:"px-[18rpx] border border-solid border-lightc border-light rounded-[10rpx] h-[100rpx] items-center flex mt-[40rpx]"},{default:p((()=>[u(l,{class:"flex-1",type:"password",modelValue:S.password,"onUpdate:modelValue":a[1]||(a[1]=e=>S.password=e),placeholder:"请输入密码",border:!1},null,8,["modelValue"])])),_:1}),u(r,{class:"px-[18rpx] border border-solid border-lightc border-light rounded-[10rpx] h-[100rpx] items-center flex mt-[40rpx]"},{default:p((()=>[u(l,{class:"flex-1",type:"password",modelValue:S.password_confirm,"onUpdate:modelValue":a[2]||(a[2]=e=>S.password_confirm=e),placeholder:"请再次输入密码",border:!1},null,8,["modelValue"])])),_:1}),n($)?(o(),c(r,{key:0,class:"mt-[40rpx]"},{default:p((()=>[u(_,{modelValue:C.value,"onUpdate:modelValue":a[5]||(a[5]=e=>C.value=e),shape:"circle"},{default:p((()=>[u(r,{class:"text-xs flex"},{default:p((()=>[d(" 已阅读并同意 "),u(r,{onClick:a[3]||(a[3]=m((()=>{}),["stop"]))},{default:p((()=>[u(x,{class:"text-primary","hover-class":"none",to:"/pages/agreement/agreement?type=service"},{default:p((()=>[d(" 《服务协议》 ")])),_:1})])),_:1}),d(" 和 "),u(r,{onClick:a[4]||(a[4]=m((()=>{}),["stop"]))},{default:p((()=>[u(x,{class:"text-primary","hover-class":"none",to:"/pages/agreement/agreement?type=privacy"},{default:p((()=>[d(" 《隐私协议》 ")])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"])])),_:1})):i("v-if",!0),u(r,{class:"mt-[60rpx]"},{default:p((()=>[u(U,{type:"primary","hover-class":"none",onClick:L,customStyle:{height:"100rpx",opacity:S.account&&S.password&&S.password_confirm?"1":"0.5"}},{default:p((()=>[d(" 注册 ")])),_:1},8,["customStyle"])])),_:1})])),_:1})])),_:1}),i(" 协议弹框 "),u(z,{modelValue:F.value,"onUpdate:modelValue":a[6]||(a[6]=e=>F.value=e),"show-cancel-button":"","show-title":!1,onConfirm:a[7]||(a[7]=e=>{C.value=!0,F.value=!1}),onCancel:a[8]||(a[8]=e=>F.value=!1),"confirm-color":"var(--color-primary)"},{default:p((()=>[u(r,{class:"text-center px-[70rpx] py-[60rpx]"},{default:p((()=>[u(r,null,{default:p((()=>[d(" 请先阅读并同意")])),_:1}),u(r,{class:"flex justify-center"},{default:p((()=>[u(x,{"data-theme":"",to:"/pages/agreement/agreement?type=service"},{default:p((()=>[u(r,{class:"text-primary"},{default:p((()=>[d("《服务协议》")])),_:1})])),_:1}),d(" 和 "),u(x,{to:"/pages/agreement/agreement?type=privacy"},{default:p((()=>[u(r,{class:"text-primary"},{default:p((()=>[d("《隐私协议》")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-776b3c23"]]);export{U as default};
