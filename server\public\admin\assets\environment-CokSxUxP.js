import{d as u,j as f,k as b,o as n,a as k,m as e,w as a,b as l,e as _,C as c,f7 as h,q as w}from"./index-B2xNDy79.js";import{E as x}from"./el-card-DpH4mUSc.js";import{E as y,a as C}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./_initCloneObject-C-h6JGU9.js";const B={class:"system-environment"},E={class:"mt-4"},g={class:"mt-4"},q={class:"mt-4"},S=u({name:"environment"}),z=u({...S,setup(I){const o=f({server:[],env:[],auth:[]}),v=()=>{h().then(r=>{o.server=r.server,o.env=r.env,o.auth=r.auth})};return b(()=>{v()}),(r,s)=>{const t=y,m=C,i=x,d=w;return n(),k("div",B,[e(i,{class:"!border-none",shadow:"never"},{default:a(()=>[s[0]||(s[0]=l("div",null,"服务器信息",-1)),l("div",E,[e(m,{data:_(o).server},{default:a(()=>[e(t,{prop:"param",label:"参数"}),e(t,{prop:"value",label:"值"})]),_:1},8,["data"])])]),_:1}),e(i,{shadow:"never",class:"!border-none mt-4"},{default:a(()=>[s[1]||(s[1]=l("div",null,"PHP环境要求",-1)),l("div",g,[e(m,{data:_(o).env},{default:a(()=>[e(t,{prop:"option",label:"选项"}),e(t,{prop:"require",label:"要求"}),e(t,{label:"状态"},{default:a(p=>[p.row.status?(n(),c(d,{key:0,name:"el-icon-Select",class:"text-success"})):(n(),c(d,{key:1,name:"el-icon-CloseBold",class:"text-danger"}))]),_:1}),e(t,{prop:"remark",label:"说明及帮助"})]),_:1},8,["data"])])]),_:1}),e(i,{shadow:"never",class:"!border-none mt-4"},{default:a(()=>[s[2]||(s[2]=l("div",null,"目录权限",-1)),l("div",q,[e(m,{data:_(o).auth},{default:a(()=>[e(t,{prop:"dir",label:"选项"}),e(t,{prop:"require",label:"要求"}),e(t,{label:"状态"},{default:a(p=>[p.row.status?(n(),c(d,{key:0,name:"el-icon-Select",class:"text-success"})):(n(),c(d,{key:1,name:"el-icon-CloseBold",class:"text-danger"}))]),_:1}),e(t,{prop:"remark",label:"说明及帮助"})]),_:1},8,["data"])])]),_:1})])}}});export{z as default};
