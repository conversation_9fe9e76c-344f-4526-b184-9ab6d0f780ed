import{d as e,Z as n,c as a,as as o,at as c,au as d,av as m,r as t,a as r,o as u,b as i,w as l,E as s,Y as f,n as h,x as p,e as v,l as _,X as g,a8 as y,aw as b,a9 as w,m as x,ax as k,k as A,t as j,g as C,F as S,h as I,f as R,p as z,ay as E,az as O,a3 as T,z as $,aA as V,V as B,$ as N,aB as D,aC as U,ab as L,ac as W,aD as P,aE as M,aF as F,aG as q}from"./index-561dd99e.js";import{_ as Z}from"./page-meta.438f2c32.js";import{_ as Y}from"./u-icon.f1b72599.js";import{c as K}from"./_commonjsHelpers.02d3be64.js";import{_ as G}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as H}from"./u-button.e98befd5.js";import{_ as J}from"./u-form-item.3b95431b.js";import{_ as X}from"./u-popup.6496bd54.js";import{_ as Q}from"./u-input.adb6d3eb.js";import{_ as ee}from"./u-verification-code.f6f16d21.js";import"./emitter.1571a5d9.js";var ne,ae,oe={};
/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */
ne={get exports(){return oe},set exports(e){oe=e}},ae=oe,function(){var e,n="Expected a function",a="__lodash_hash_undefined__",o="__lodash_placeholder__",c=16,d=32,m=64,t=128,r=256,u=1/0,i=9007199254740991,l=NaN,s=**********,f=[["ary",t],["bind",1],["bindKey",2],["curry",8],["curryRight",c],["flip",512],["partial",d],["partialRight",m],["rearg",r]],h="[object Arguments]",p="[object Array]",v="[object Boolean]",_="[object Date]",g="[object Error]",y="[object Function]",b="[object GeneratorFunction]",w="[object Map]",x="[object Number]",k="[object Object]",A="[object Promise]",j="[object RegExp]",C="[object Set]",S="[object String]",I="[object Symbol]",R="[object WeakMap]",z="[object ArrayBuffer]",E="[object DataView]",O="[object Float32Array]",T="[object Float64Array]",$="[object Int8Array]",V="[object Int16Array]",B="[object Int32Array]",N="[object Uint8Array]",D="[object Uint8ClampedArray]",U="[object Uint16Array]",L="[object Uint32Array]",W=/\b__p \+= '';/g,P=/\b(__p \+=) '' \+/g,M=/(__e\(.*?\)|\b__t\)) \+\n'';/g,F=/&(?:amp|lt|gt|quot|#39);/g,q=/[&<>"']/g,Z=RegExp(F.source),Y=RegExp(q.source),G=/<%-([\s\S]+?)%>/g,H=/<%([\s\S]+?)%>/g,J=/<%=([\s\S]+?)%>/g,X=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Q=/^\w*$/,ee=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,oe=/[\\^$.*+?()[\]{}|]/g,ce=RegExp(oe.source),de=/^\s+/,me=/\s/,te=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,re=/\{\n\/\* \[wrapped with (.+)\] \*/,ue=/,? & /,ie=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,le=/[()=,{}\[\]\/\s]/,se=/\\(\\)?/g,fe=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,he=/\w*$/,pe=/^[-+]0x[0-9a-f]+$/i,ve=/^0b[01]+$/i,_e=/^\[object .+?Constructor\]$/,ge=/^0o[0-7]+$/i,ye=/^(?:0|[1-9]\d*)$/,be=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,we=/($^)/,xe=/['\n\r\u2028\u2029\\]/g,ke="\\ud800-\\udfff",Ae="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",je="\\u2700-\\u27bf",Ce="a-z\\xdf-\\xf6\\xf8-\\xff",Se="A-Z\\xc0-\\xd6\\xd8-\\xde",Ie="\\ufe0e\\ufe0f",Re="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",ze="['’]",Ee="["+ke+"]",Oe="["+Re+"]",Te="["+Ae+"]",$e="\\d+",Ve="["+je+"]",Be="["+Ce+"]",Ne="[^"+ke+Re+$e+je+Ce+Se+"]",De="\\ud83c[\\udffb-\\udfff]",Ue="[^"+ke+"]",Le="(?:\\ud83c[\\udde6-\\uddff]){2}",We="[\\ud800-\\udbff][\\udc00-\\udfff]",Pe="["+Se+"]",Me="\\u200d",Fe="(?:"+Be+"|"+Ne+")",qe="(?:"+Pe+"|"+Ne+")",Ze="(?:['’](?:d|ll|m|re|s|t|ve))?",Ye="(?:['’](?:D|LL|M|RE|S|T|VE))?",Ke="(?:"+Te+"|"+De+")?",Ge="["+Ie+"]?",He=Ge+Ke+"(?:"+Me+"(?:"+[Ue,Le,We].join("|")+")"+Ge+Ke+")*",Je="(?:"+[Ve,Le,We].join("|")+")"+He,Xe="(?:"+[Ue+Te+"?",Te,Le,We,Ee].join("|")+")",Qe=RegExp(ze,"g"),en=RegExp(Te,"g"),nn=RegExp(De+"(?="+De+")|"+Xe+He,"g"),an=RegExp([Pe+"?"+Be+"+"+Ze+"(?="+[Oe,Pe,"$"].join("|")+")",qe+"+"+Ye+"(?="+[Oe,Pe+Fe,"$"].join("|")+")",Pe+"?"+Fe+"+"+Ze,Pe+"+"+Ye,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",$e,Je].join("|"),"g"),on=RegExp("["+Me+ke+Ae+Ie+"]"),cn=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,dn=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],mn=-1,tn={};tn[O]=tn[T]=tn[$]=tn[V]=tn[B]=tn[N]=tn[D]=tn[U]=tn[L]=!0,tn[h]=tn[p]=tn[z]=tn[v]=tn[E]=tn[_]=tn[g]=tn[y]=tn[w]=tn[x]=tn[k]=tn[j]=tn[C]=tn[S]=tn[R]=!1;var rn={};rn[h]=rn[p]=rn[z]=rn[E]=rn[v]=rn[_]=rn[O]=rn[T]=rn[$]=rn[V]=rn[B]=rn[w]=rn[x]=rn[k]=rn[j]=rn[C]=rn[S]=rn[I]=rn[N]=rn[D]=rn[U]=rn[L]=!0,rn[g]=rn[y]=rn[R]=!1;var un={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},ln=parseFloat,sn=parseInt,fn="object"==typeof K&&K&&K.Object===Object&&K,hn="object"==typeof self&&self&&self.Object===Object&&self,pn=fn||hn||Function("return this")(),vn=ae&&!ae.nodeType&&ae,_n=vn&&ne&&!ne.nodeType&&ne,gn=_n&&_n.exports===vn,yn=gn&&fn.process,bn=function(){try{var e=_n&&_n.require&&_n.require("util").types;return e||yn&&yn.binding&&yn.binding("util")}catch(n){}}(),wn=bn&&bn.isArrayBuffer,xn=bn&&bn.isDate,kn=bn&&bn.isMap,An=bn&&bn.isRegExp,jn=bn&&bn.isSet,Cn=bn&&bn.isTypedArray;function Sn(e,n,a){switch(a.length){case 0:return e.call(n);case 1:return e.call(n,a[0]);case 2:return e.call(n,a[0],a[1]);case 3:return e.call(n,a[0],a[1],a[2])}return e.apply(n,a)}function In(e,n,a,o){for(var c=-1,d=null==e?0:e.length;++c<d;){var m=e[c];n(o,m,a(m),e)}return o}function Rn(e,n){for(var a=-1,o=null==e?0:e.length;++a<o&&!1!==n(e[a],a,e););return e}function zn(e,n){for(var a=null==e?0:e.length;a--&&!1!==n(e[a],a,e););return e}function En(e,n){for(var a=-1,o=null==e?0:e.length;++a<o;)if(!n(e[a],a,e))return!1;return!0}function On(e,n){for(var a=-1,o=null==e?0:e.length,c=0,d=[];++a<o;){var m=e[a];n(m,a,e)&&(d[c++]=m)}return d}function Tn(e,n){return!(null==e||!e.length)&&Mn(e,n,0)>-1}function $n(e,n,a){for(var o=-1,c=null==e?0:e.length;++o<c;)if(a(n,e[o]))return!0;return!1}function Vn(e,n){for(var a=-1,o=null==e?0:e.length,c=Array(o);++a<o;)c[a]=n(e[a],a,e);return c}function Bn(e,n){for(var a=-1,o=n.length,c=e.length;++a<o;)e[c+a]=n[a];return e}function Nn(e,n,a,o){var c=-1,d=null==e?0:e.length;for(o&&d&&(a=e[++c]);++c<d;)a=n(a,e[c],c,e);return a}function Dn(e,n,a,o){var c=null==e?0:e.length;for(o&&c&&(a=e[--c]);c--;)a=n(a,e[c],c,e);return a}function Un(e,n){for(var a=-1,o=null==e?0:e.length;++a<o;)if(n(e[a],a,e))return!0;return!1}var Ln=Yn("length");function Wn(e,n,a){var o;return a(e,(function(e,a,c){if(n(e,a,c))return o=a,!1})),o}function Pn(e,n,a,o){for(var c=e.length,d=a+(o?1:-1);o?d--:++d<c;)if(n(e[d],d,e))return d;return-1}function Mn(e,n,a){return n==n?function(e,n,a){for(var o=a-1,c=e.length;++o<c;)if(e[o]===n)return o;return-1}(e,n,a):Pn(e,qn,a)}function Fn(e,n,a,o){for(var c=a-1,d=e.length;++c<d;)if(o(e[c],n))return c;return-1}function qn(e){return e!=e}function Zn(e,n){var a=null==e?0:e.length;return a?Hn(e,n)/a:l}function Yn(n){return function(a){return null==a?e:a[n]}}function Kn(n){return function(a){return null==n?e:n[a]}}function Gn(e,n,a,o,c){return c(e,(function(e,c,d){a=o?(o=!1,e):n(a,e,c,d)})),a}function Hn(n,a){for(var o,c=-1,d=n.length;++c<d;){var m=a(n[c]);m!==e&&(o=o===e?m:o+m)}return o}function Jn(e,n){for(var a=-1,o=Array(e);++a<e;)o[a]=n(a);return o}function Xn(e){return e?e.slice(0,ha(e)+1).replace(de,""):e}function Qn(e){return function(n){return e(n)}}function ea(e,n){return Vn(n,(function(n){return e[n]}))}function na(e,n){return e.has(n)}function aa(e,n){for(var a=-1,o=e.length;++a<o&&Mn(n,e[a],0)>-1;);return a}function oa(e,n){for(var a=e.length;a--&&Mn(n,e[a],0)>-1;);return a}var ca=Kn({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),da=Kn({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function ma(e){return"\\"+un[e]}function ta(e){return on.test(e)}function ra(e){var n=-1,a=Array(e.size);return e.forEach((function(e,o){a[++n]=[o,e]})),a}function ua(e,n){return function(a){return e(n(a))}}function ia(e,n){for(var a=-1,c=e.length,d=0,m=[];++a<c;){var t=e[a];t!==n&&t!==o||(e[a]=o,m[d++]=a)}return m}function la(e){var n=-1,a=Array(e.size);return e.forEach((function(e){a[++n]=e})),a}function sa(e){return ta(e)?function(e){for(var n=nn.lastIndex=0;nn.test(e);)++n;return n}(e):Ln(e)}function fa(e){return ta(e)?function(e){return e.match(nn)||[]}(e):function(e){return e.split("")}(e)}function ha(e){for(var n=e.length;n--&&me.test(e.charAt(n)););return n}var pa=Kn({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),va=function K(ne){var ae,me=(ne=null==ne?pn:va.defaults(pn.Object(),ne,va.pick(pn,dn))).Array,ke=ne.Date,Ae=ne.Error,je=ne.Function,Ce=ne.Math,Se=ne.Object,Ie=ne.RegExp,Re=ne.String,ze=ne.TypeError,Ee=me.prototype,Oe=je.prototype,Te=Se.prototype,$e=ne["__core-js_shared__"],Ve=Oe.toString,Be=Te.hasOwnProperty,Ne=0,De=(ae=/[^.]+$/.exec($e&&$e.keys&&$e.keys.IE_PROTO||""))?"Symbol(src)_1."+ae:"",Ue=Te.toString,Le=Ve.call(Se),We=pn._,Pe=Ie("^"+Ve.call(Be).replace(oe,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Me=gn?ne.Buffer:e,Fe=ne.Symbol,qe=ne.Uint8Array,Ze=Me?Me.allocUnsafe:e,Ye=ua(Se.getPrototypeOf,Se),Ke=Se.create,Ge=Te.propertyIsEnumerable,He=Ee.splice,Je=Fe?Fe.isConcatSpreadable:e,Xe=Fe?Fe.iterator:e,nn=Fe?Fe.toStringTag:e,on=function(){try{var e=sd(Se,"defineProperty");return e({},"",{}),e}catch(n){}}(),un=ne.clearTimeout!==pn.clearTimeout&&ne.clearTimeout,fn=ke&&ke.now!==pn.Date.now&&ke.now,hn=ne.setTimeout!==pn.setTimeout&&ne.setTimeout,vn=Ce.ceil,_n=Ce.floor,yn=Se.getOwnPropertySymbols,bn=Me?Me.isBuffer:e,Ln=ne.isFinite,Kn=Ee.join,_a=ua(Se.keys,Se),ga=Ce.max,ya=Ce.min,ba=ke.now,wa=ne.parseInt,xa=Ce.random,ka=Ee.reverse,Aa=sd(ne,"DataView"),ja=sd(ne,"Map"),Ca=sd(ne,"Promise"),Sa=sd(ne,"Set"),Ia=sd(ne,"WeakMap"),Ra=sd(Se,"create"),za=Ia&&new Ia,Ea={},Oa=Wd(Aa),Ta=Wd(ja),$a=Wd(Ca),Va=Wd(Sa),Ba=Wd(Ia),Na=Fe?Fe.prototype:e,Da=Na?Na.valueOf:e,Ua=Na?Na.toString:e;function La(e){if(ct(e)&&!Ym(e)&&!(e instanceof Fa)){if(e instanceof Ma)return e;if(Be.call(e,"__wrapped__"))return Pd(e)}return new Ma(e)}var Wa=function(){function n(){}return function(a){if(!ot(a))return{};if(Ke)return Ke(a);n.prototype=a;var o=new n;return n.prototype=e,o}}();function Pa(){}function Ma(n,a){this.__wrapped__=n,this.__actions__=[],this.__chain__=!!a,this.__index__=0,this.__values__=e}function Fa(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=s,this.__views__=[]}function qa(e){var n=-1,a=null==e?0:e.length;for(this.clear();++n<a;){var o=e[n];this.set(o[0],o[1])}}function Za(e){var n=-1,a=null==e?0:e.length;for(this.clear();++n<a;){var o=e[n];this.set(o[0],o[1])}}function Ya(e){var n=-1,a=null==e?0:e.length;for(this.clear();++n<a;){var o=e[n];this.set(o[0],o[1])}}function Ka(e){var n=-1,a=null==e?0:e.length;for(this.__data__=new Ya;++n<a;)this.add(e[n])}function Ga(e){var n=this.__data__=new Za(e);this.size=n.size}function Ha(e,n){var a=Ym(e),o=!a&&Zm(e),c=!a&&!o&&Jm(e),d=!a&&!o&&!c&&st(e),m=a||o||c||d,t=m?Jn(e.length,Re):[],r=t.length;for(var u in e)!n&&!Be.call(e,u)||m&&("length"==u||c&&("offset"==u||"parent"==u)||d&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||yd(u,r))||t.push(u);return t}function Ja(n){var a=n.length;return a?n[Go(0,a-1)]:e}function Xa(e,n){return Vd(zc(e),ro(n,0,e.length))}function Qa(e){return Vd(zc(e))}function eo(n,a,o){(o!==e&&!Mm(n[a],o)||o===e&&!(a in n))&&mo(n,a,o)}function no(n,a,o){var c=n[a];Be.call(n,a)&&Mm(c,o)&&(o!==e||a in n)||mo(n,a,o)}function ao(e,n){for(var a=e.length;a--;)if(Mm(e[a][0],n))return a;return-1}function oo(e,n,a,o){return fo(e,(function(e,c,d){n(o,e,a(e),d)})),o}function co(e,n){return e&&Ec(n,$t(n),e)}function mo(e,n,a){"__proto__"==n&&on?on(e,n,{configurable:!0,enumerable:!0,value:a,writable:!0}):e[n]=a}function to(n,a){for(var o=-1,c=a.length,d=me(c),m=null==n;++o<c;)d[o]=m?e:Rt(n,a[o]);return d}function ro(n,a,o){return n==n&&(o!==e&&(n=n<=o?n:o),a!==e&&(n=n>=a?n:a)),n}function uo(n,a,o,c,d,m){var t,r=1&a,u=2&a,i=4&a;if(o&&(t=d?o(n,c,d,m):o(n)),t!==e)return t;if(!ot(n))return n;var l=Ym(n);if(l){if(t=function(e){var n=e.length,a=new e.constructor(n);return n&&"string"==typeof e[0]&&Be.call(e,"index")&&(a.index=e.index,a.input=e.input),a}(n),!r)return zc(n,t)}else{var s=pd(n),f=s==y||s==b;if(Jm(n))return Ac(n,r);if(s==k||s==h||f&&!d){if(t=u||f?{}:_d(n),!r)return u?function(e,n){return Ec(e,hd(e),n)}(n,function(e,n){return e&&Ec(n,Vt(n),e)}(t,n)):function(e,n){return Ec(e,fd(e),n)}(n,co(t,n))}else{if(!rn[s])return d?n:{};t=function(e,n,a){var o,c=e.constructor;switch(n){case z:return jc(e);case v:case _:return new c(+e);case E:return function(e,n){var a=n?jc(e.buffer):e.buffer;return new e.constructor(a,e.byteOffset,e.byteLength)}(e,a);case O:case T:case $:case V:case B:case N:case D:case U:case L:return Cc(e,a);case w:return new c;case x:case S:return new c(e);case j:return function(e){var n=new e.constructor(e.source,he.exec(e));return n.lastIndex=e.lastIndex,n}(e);case C:return new c;case I:return o=e,Da?Se(Da.call(o)):{}}}(n,s,r)}}m||(m=new Ga);var p=m.get(n);if(p)return p;m.set(n,t),ut(n)?n.forEach((function(e){t.add(uo(e,a,o,e,n,m))})):dt(n)&&n.forEach((function(e,c){t.set(c,uo(e,a,o,c,n,m))}));var g=l?e:(i?u?dd:cd:u?Vt:$t)(n);return Rn(g||n,(function(e,c){g&&(e=n[c=e]),no(t,c,uo(e,a,o,c,n,m))})),t}function io(n,a,o){var c=o.length;if(null==n)return!c;for(n=Se(n);c--;){var d=o[c],m=a[d],t=n[d];if(t===e&&!(d in n)||!m(t))return!1}return!0}function lo(a,o,c){if("function"!=typeof a)throw new ze(n);return Ed((function(){a.apply(e,c)}),o)}function so(e,n,a,o){var c=-1,d=Tn,m=!0,t=e.length,r=[],u=n.length;if(!t)return r;a&&(n=Vn(n,Qn(a))),o?(d=$n,m=!1):n.length>=200&&(d=na,m=!1,n=new Ka(n));e:for(;++c<t;){var i=e[c],l=null==a?i:a(i);if(i=o||0!==i?i:0,m&&l==l){for(var s=u;s--;)if(n[s]===l)continue e;r.push(i)}else d(n,l,o)||r.push(i)}return r}La.templateSettings={escape:G,evaluate:H,interpolate:J,variable:"",imports:{_:La}},La.prototype=Pa.prototype,La.prototype.constructor=La,Ma.prototype=Wa(Pa.prototype),Ma.prototype.constructor=Ma,Fa.prototype=Wa(Pa.prototype),Fa.prototype.constructor=Fa,qa.prototype.clear=function(){this.__data__=Ra?Ra(null):{},this.size=0},qa.prototype.delete=function(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n},qa.prototype.get=function(n){var o=this.__data__;if(Ra){var c=o[n];return c===a?e:c}return Be.call(o,n)?o[n]:e},qa.prototype.has=function(n){var a=this.__data__;return Ra?a[n]!==e:Be.call(a,n)},qa.prototype.set=function(n,o){var c=this.__data__;return this.size+=this.has(n)?0:1,c[n]=Ra&&o===e?a:o,this},Za.prototype.clear=function(){this.__data__=[],this.size=0},Za.prototype.delete=function(e){var n=this.__data__,a=ao(n,e);return!(a<0||(a==n.length-1?n.pop():He.call(n,a,1),--this.size,0))},Za.prototype.get=function(n){var a=this.__data__,o=ao(a,n);return o<0?e:a[o][1]},Za.prototype.has=function(e){return ao(this.__data__,e)>-1},Za.prototype.set=function(e,n){var a=this.__data__,o=ao(a,e);return o<0?(++this.size,a.push([e,n])):a[o][1]=n,this},Ya.prototype.clear=function(){this.size=0,this.__data__={hash:new qa,map:new(ja||Za),string:new qa}},Ya.prototype.delete=function(e){var n=id(this,e).delete(e);return this.size-=n?1:0,n},Ya.prototype.get=function(e){return id(this,e).get(e)},Ya.prototype.has=function(e){return id(this,e).has(e)},Ya.prototype.set=function(e,n){var a=id(this,e),o=a.size;return a.set(e,n),this.size+=a.size==o?0:1,this},Ka.prototype.add=Ka.prototype.push=function(e){return this.__data__.set(e,a),this},Ka.prototype.has=function(e){return this.__data__.has(e)},Ga.prototype.clear=function(){this.__data__=new Za,this.size=0},Ga.prototype.delete=function(e){var n=this.__data__,a=n.delete(e);return this.size=n.size,a},Ga.prototype.get=function(e){return this.__data__.get(e)},Ga.prototype.has=function(e){return this.__data__.has(e)},Ga.prototype.set=function(e,n){var a=this.__data__;if(a instanceof Za){var o=a.__data__;if(!ja||o.length<199)return o.push([e,n]),this.size=++a.size,this;a=this.__data__=new Ya(o)}return a.set(e,n),this.size=a.size,this};var fo=$c(wo),ho=$c(xo,!0);function po(e,n){var a=!0;return fo(e,(function(e,o,c){return a=!!n(e,o,c)})),a}function vo(n,a,o){for(var c=-1,d=n.length;++c<d;){var m=n[c],t=a(m);if(null!=t&&(r===e?t==t&&!lt(t):o(t,r)))var r=t,u=m}return u}function _o(e,n){var a=[];return fo(e,(function(e,o,c){n(e,o,c)&&a.push(e)})),a}function go(e,n,a,o,c){var d=-1,m=e.length;for(a||(a=gd),c||(c=[]);++d<m;){var t=e[d];n>0&&a(t)?n>1?go(t,n-1,a,o,c):Bn(c,t):o||(c[c.length]=t)}return c}var yo=Vc(),bo=Vc(!0);function wo(e,n){return e&&yo(e,n,$t)}function xo(e,n){return e&&bo(e,n,$t)}function ko(e,n){return On(n,(function(n){return et(e[n])}))}function Ao(n,a){for(var o=0,c=(a=bc(a,n)).length;null!=n&&o<c;)n=n[Ld(a[o++])];return o&&o==c?n:e}function jo(e,n,a){var o=n(e);return Ym(e)?o:Bn(o,a(e))}function Co(n){return null==n?n===e?"[object Undefined]":"[object Null]":nn&&nn in Se(n)?function(n){var a=Be.call(n,nn),o=n[nn];try{n[nn]=e;var c=!0}catch(m){}var d=Ue.call(n);return c&&(a?n[nn]=o:delete n[nn]),d}(n):function(e){return Ue.call(e)}(n)}function So(e,n){return e>n}function Io(e,n){return null!=e&&Be.call(e,n)}function Ro(e,n){return null!=e&&n in Se(e)}function zo(n,a,o){for(var c=o?$n:Tn,d=n[0].length,m=n.length,t=m,r=me(m),u=1/0,i=[];t--;){var l=n[t];t&&a&&(l=Vn(l,Qn(a))),u=ya(l.length,u),r[t]=!o&&(a||d>=120&&l.length>=120)?new Ka(t&&l):e}l=n[0];var s=-1,f=r[0];e:for(;++s<d&&i.length<u;){var h=l[s],p=a?a(h):h;if(h=o||0!==h?h:0,!(f?na(f,p):c(i,p,o))){for(t=m;--t;){var v=r[t];if(!(v?na(v,p):c(n[t],p,o)))continue e}f&&f.push(p),i.push(h)}}return i}function Eo(n,a,o){var c=null==(n=Id(n,a=bc(a,n)))?n:n[Ld(Qd(a))];return null==c?e:Sn(c,n,o)}function Oo(e){return ct(e)&&Co(e)==h}function To(n,a,o,c,d){return n===a||(null==n||null==a||!ct(n)&&!ct(a)?n!=n&&a!=a:function(n,a,o,c,d,m){var t=Ym(n),r=Ym(a),u=t?p:pd(n),i=r?p:pd(a),l=(u=u==h?k:u)==k,s=(i=i==h?k:i)==k,f=u==i;if(f&&Jm(n)){if(!Jm(a))return!1;t=!0,l=!1}if(f&&!l)return m||(m=new Ga),t||st(n)?ad(n,a,o,c,d,m):function(e,n,a,o,c,d,m){switch(a){case E:if(e.byteLength!=n.byteLength||e.byteOffset!=n.byteOffset)return!1;e=e.buffer,n=n.buffer;case z:return!(e.byteLength!=n.byteLength||!d(new qe(e),new qe(n)));case v:case _:case x:return Mm(+e,+n);case g:return e.name==n.name&&e.message==n.message;case j:case S:return e==n+"";case w:var t=ra;case C:var r=1&o;if(t||(t=la),e.size!=n.size&&!r)return!1;var u=m.get(e);if(u)return u==n;o|=2,m.set(e,n);var i=ad(t(e),t(n),o,c,d,m);return m.delete(e),i;case I:if(Da)return Da.call(e)==Da.call(n)}return!1}(n,a,u,o,c,d,m);if(!(1&o)){var y=l&&Be.call(n,"__wrapped__"),b=s&&Be.call(a,"__wrapped__");if(y||b){var A=y?n.value():n,R=b?a.value():a;return m||(m=new Ga),d(A,R,o,c,m)}}return!!f&&(m||(m=new Ga),function(n,a,o,c,d,m){var t=1&o,r=cd(n),u=r.length,i=cd(a),l=i.length;if(u!=l&&!t)return!1;for(var s=u;s--;){var f=r[s];if(!(t?f in a:Be.call(a,f)))return!1}var h=m.get(n),p=m.get(a);if(h&&p)return h==a&&p==n;var v=!0;m.set(n,a),m.set(a,n);for(var _=t;++s<u;){var g=n[f=r[s]],y=a[f];if(c)var b=t?c(y,g,f,a,n,m):c(g,y,f,n,a,m);if(!(b===e?g===y||d(g,y,o,c,m):b)){v=!1;break}_||(_="constructor"==f)}if(v&&!_){var w=n.constructor,x=a.constructor;w==x||!("constructor"in n)||!("constructor"in a)||"function"==typeof w&&w instanceof w&&"function"==typeof x&&x instanceof x||(v=!1)}return m.delete(n),m.delete(a),v}(n,a,o,c,d,m))}(n,a,o,c,To,d))}function $o(n,a,o,c){var d=o.length,m=d,t=!c;if(null==n)return!m;for(n=Se(n);d--;){var r=o[d];if(t&&r[2]?r[1]!==n[r[0]]:!(r[0]in n))return!1}for(;++d<m;){var u=(r=o[d])[0],i=n[u],l=r[1];if(t&&r[2]){if(i===e&&!(u in n))return!1}else{var s=new Ga;if(c)var f=c(i,l,u,n,a,s);if(!(f===e?To(l,i,3,c,s):f))return!1}}return!0}function Vo(e){return!(!ot(e)||(n=e,De&&De in n))&&(et(e)?Pe:_e).test(Wd(e));var n}function Bo(e){return"function"==typeof e?e:null==e?mr:"object"==typeof e?Ym(e)?Po(e[0],e[1]):Wo(e):pr(e)}function No(e){if(!Ad(e))return _a(e);var n=[];for(var a in Se(e))Be.call(e,a)&&"constructor"!=a&&n.push(a);return n}function Do(e){if(!ot(e))return function(e){var n=[];if(null!=e)for(var a in Se(e))n.push(a);return n}(e);var n=Ad(e),a=[];for(var o in e)("constructor"!=o||!n&&Be.call(e,o))&&a.push(o);return a}function Uo(e,n){return e<n}function Lo(e,n){var a=-1,o=Gm(e)?me(e.length):[];return fo(e,(function(e,c,d){o[++a]=n(e,c,d)})),o}function Wo(e){var n=ld(e);return 1==n.length&&n[0][2]?Cd(n[0][0],n[0][1]):function(a){return a===e||$o(a,e,n)}}function Po(n,a){return wd(n)&&jd(a)?Cd(Ld(n),a):function(o){var c=Rt(o,n);return c===e&&c===a?zt(o,n):To(a,c,3)}}function Mo(n,a,o,c,d){n!==a&&yo(a,(function(m,t){if(d||(d=new Ga),ot(m))!function(n,a,o,c,d,m,t){var r=Rd(n,o),u=Rd(a,o),i=t.get(u);if(i)eo(n,o,i);else{var l=m?m(r,u,o+"",n,a,t):e,s=l===e;if(s){var f=Ym(u),h=!f&&Jm(u),p=!f&&!h&&st(u);l=u,f||h||p?Ym(r)?l=r:Hm(r)?l=zc(r):h?(s=!1,l=Ac(u,!0)):p?(s=!1,l=Cc(u,!0)):l=[]:tt(u)||Zm(u)?(l=r,Zm(r)?l=bt(r):ot(r)&&!et(r)||(l=_d(u))):s=!1}s&&(t.set(u,l),d(l,u,c,m,t),t.delete(u)),eo(n,o,l)}}(n,a,t,o,Mo,c,d);else{var r=c?c(Rd(n,t),m,t+"",n,a,d):e;r===e&&(r=m),eo(n,t,r)}}),Vt)}function Fo(n,a){var o=n.length;if(o)return yd(a+=a<0?o:0,o)?n[a]:e}function qo(e,n,a){n=n.length?Vn(n,(function(e){return Ym(e)?function(n){return Ao(n,1===e.length?e[0]:e)}:e})):[mr];var o=-1;return n=Vn(n,Qn(ud())),function(e,n){var a=e.length;for(e.sort(n);a--;)e[a]=e[a].value;return e}(Lo(e,(function(e,a,c){return{criteria:Vn(n,(function(n){return n(e)})),index:++o,value:e}})),(function(e,n){return function(e,n,a){for(var o=-1,c=e.criteria,d=n.criteria,m=c.length,t=a.length;++o<m;){var r=Sc(c[o],d[o]);if(r)return o>=t?r:r*("desc"==a[o]?-1:1)}return e.index-n.index}(e,n,a)}))}function Zo(e,n,a){for(var o=-1,c=n.length,d={};++o<c;){var m=n[o],t=Ao(e,m);a(t,m)&&ec(d,bc(m,e),t)}return d}function Yo(e,n,a,o){var c=o?Fn:Mn,d=-1,m=n.length,t=e;for(e===n&&(n=zc(n)),a&&(t=Vn(e,Qn(a)));++d<m;)for(var r=0,u=n[d],i=a?a(u):u;(r=c(t,i,r,o))>-1;)t!==e&&He.call(t,r,1),He.call(e,r,1);return e}function Ko(e,n){for(var a=e?n.length:0,o=a-1;a--;){var c=n[a];if(a==o||c!==d){var d=c;yd(c)?He.call(e,c,1):sc(e,c)}}return e}function Go(e,n){return e+_n(xa()*(n-e+1))}function Ho(e,n){var a="";if(!e||n<1||n>i)return a;do{n%2&&(a+=e),(n=_n(n/2))&&(e+=e)}while(n);return a}function Jo(e,n){return Od(Sd(e,n,mr),e+"")}function Xo(e){return Ja(Mt(e))}function Qo(e,n){var a=Mt(e);return Vd(a,ro(n,0,a.length))}function ec(n,a,o,c){if(!ot(n))return n;for(var d=-1,m=(a=bc(a,n)).length,t=m-1,r=n;null!=r&&++d<m;){var u=Ld(a[d]),i=o;if("__proto__"===u||"constructor"===u||"prototype"===u)return n;if(d!=t){var l=r[u];(i=c?c(l,u,r):e)===e&&(i=ot(l)?l:yd(a[d+1])?[]:{})}no(r,u,i),r=r[u]}return n}var nc=za?function(e,n){return za.set(e,n),e}:mr,ac=on?function(e,n){return on(e,"toString",{configurable:!0,enumerable:!1,value:or(n),writable:!0})}:mr;function oc(e){return Vd(Mt(e))}function cc(e,n,a){var o=-1,c=e.length;n<0&&(n=-n>c?0:c+n),(a=a>c?c:a)<0&&(a+=c),c=n>a?0:a-n>>>0,n>>>=0;for(var d=me(c);++o<c;)d[o]=e[o+n];return d}function dc(e,n){var a;return fo(e,(function(e,o,c){return!(a=n(e,o,c))})),!!a}function mc(e,n,a){var o=0,c=null==e?o:e.length;if("number"==typeof n&&n==n&&c<=2147483647){for(;o<c;){var d=o+c>>>1,m=e[d];null!==m&&!lt(m)&&(a?m<=n:m<n)?o=d+1:c=d}return c}return tc(e,n,mr,a)}function tc(n,a,o,c){var d=0,m=null==n?0:n.length;if(0===m)return 0;for(var t=(a=o(a))!=a,r=null===a,u=lt(a),i=a===e;d<m;){var l=_n((d+m)/2),s=o(n[l]),f=s!==e,h=null===s,p=s==s,v=lt(s);if(t)var _=c||p;else _=i?p&&(c||f):r?p&&f&&(c||!h):u?p&&f&&!h&&(c||!v):!h&&!v&&(c?s<=a:s<a);_?d=l+1:m=l}return ya(m,4294967294)}function rc(e,n){for(var a=-1,o=e.length,c=0,d=[];++a<o;){var m=e[a],t=n?n(m):m;if(!a||!Mm(t,r)){var r=t;d[c++]=0===m?0:m}}return d}function uc(e){return"number"==typeof e?e:lt(e)?l:+e}function ic(e){if("string"==typeof e)return e;if(Ym(e))return Vn(e,ic)+"";if(lt(e))return Ua?Ua.call(e):"";var n=e+"";return"0"==n&&1/e==-1/0?"-0":n}function lc(e,n,a){var o=-1,c=Tn,d=e.length,m=!0,t=[],r=t;if(a)m=!1,c=$n;else if(d>=200){var u=n?null:Hc(e);if(u)return la(u);m=!1,c=na,r=new Ka}else r=n?[]:t;e:for(;++o<d;){var i=e[o],l=n?n(i):i;if(i=a||0!==i?i:0,m&&l==l){for(var s=r.length;s--;)if(r[s]===l)continue e;n&&r.push(l),t.push(i)}else c(r,l,a)||(r!==t&&r.push(l),t.push(i))}return t}function sc(e,n){return null==(e=Id(e,n=bc(n,e)))||delete e[Ld(Qd(n))]}function fc(e,n,a,o){return ec(e,n,a(Ao(e,n)),o)}function hc(e,n,a,o){for(var c=e.length,d=o?c:-1;(o?d--:++d<c)&&n(e[d],d,e););return a?cc(e,o?0:d,o?d+1:c):cc(e,o?d+1:0,o?c:d)}function pc(e,n){var a=e;return a instanceof Fa&&(a=a.value()),Nn(n,(function(e,n){return n.func.apply(n.thisArg,Bn([e],n.args))}),a)}function vc(e,n,a){var o=e.length;if(o<2)return o?lc(e[0]):[];for(var c=-1,d=me(o);++c<o;)for(var m=e[c],t=-1;++t<o;)t!=c&&(d[c]=so(d[c]||m,e[t],n,a));return lc(go(d,1),n,a)}function _c(n,a,o){for(var c=-1,d=n.length,m=a.length,t={};++c<d;){var r=c<m?a[c]:e;o(t,n[c],r)}return t}function gc(e){return Hm(e)?e:[]}function yc(e){return"function"==typeof e?e:mr}function bc(e,n){return Ym(e)?e:wd(e,n)?[e]:Ud(wt(e))}var wc=Jo;function xc(n,a,o){var c=n.length;return o=o===e?c:o,!a&&o>=c?n:cc(n,a,o)}var kc=un||function(e){return pn.clearTimeout(e)};function Ac(e,n){if(n)return e.slice();var a=e.length,o=Ze?Ze(a):new e.constructor(a);return e.copy(o),o}function jc(e){var n=new e.constructor(e.byteLength);return new qe(n).set(new qe(e)),n}function Cc(e,n){var a=n?jc(e.buffer):e.buffer;return new e.constructor(a,e.byteOffset,e.length)}function Sc(n,a){if(n!==a){var o=n!==e,c=null===n,d=n==n,m=lt(n),t=a!==e,r=null===a,u=a==a,i=lt(a);if(!r&&!i&&!m&&n>a||m&&t&&u&&!r&&!i||c&&t&&u||!o&&u||!d)return 1;if(!c&&!m&&!i&&n<a||i&&o&&d&&!c&&!m||r&&o&&d||!t&&d||!u)return-1}return 0}function Ic(e,n,a,o){for(var c=-1,d=e.length,m=a.length,t=-1,r=n.length,u=ga(d-m,0),i=me(r+u),l=!o;++t<r;)i[t]=n[t];for(;++c<m;)(l||c<d)&&(i[a[c]]=e[c]);for(;u--;)i[t++]=e[c++];return i}function Rc(e,n,a,o){for(var c=-1,d=e.length,m=-1,t=a.length,r=-1,u=n.length,i=ga(d-t,0),l=me(i+u),s=!o;++c<i;)l[c]=e[c];for(var f=c;++r<u;)l[f+r]=n[r];for(;++m<t;)(s||c<d)&&(l[f+a[m]]=e[c++]);return l}function zc(e,n){var a=-1,o=e.length;for(n||(n=me(o));++a<o;)n[a]=e[a];return n}function Ec(n,a,o,c){var d=!o;o||(o={});for(var m=-1,t=a.length;++m<t;){var r=a[m],u=c?c(o[r],n[r],r,o,n):e;u===e&&(u=n[r]),d?mo(o,r,u):no(o,r,u)}return o}function Oc(e,n){return function(a,o){var c=Ym(a)?In:oo,d=n?n():{};return c(a,e,ud(o,2),d)}}function Tc(n){return Jo((function(a,o){var c=-1,d=o.length,m=d>1?o[d-1]:e,t=d>2?o[2]:e;for(m=n.length>3&&"function"==typeof m?(d--,m):e,t&&bd(o[0],o[1],t)&&(m=d<3?e:m,d=1),a=Se(a);++c<d;){var r=o[c];r&&n(a,r,c,m)}return a}))}function $c(e,n){return function(a,o){if(null==a)return a;if(!Gm(a))return e(a,o);for(var c=a.length,d=n?c:-1,m=Se(a);(n?d--:++d<c)&&!1!==o(m[d],d,m););return a}}function Vc(e){return function(n,a,o){for(var c=-1,d=Se(n),m=o(n),t=m.length;t--;){var r=m[e?t:++c];if(!1===a(d[r],r,d))break}return n}}function Bc(n){return function(a){var o=ta(a=wt(a))?fa(a):e,c=o?o[0]:a.charAt(0),d=o?xc(o,1).join(""):a.slice(1);return c[n]()+d}}function Nc(e){return function(n){return Nn(er(Zt(n).replace(Qe,"")),e,"")}}function Dc(e){return function(){var n=arguments;switch(n.length){case 0:return new e;case 1:return new e(n[0]);case 2:return new e(n[0],n[1]);case 3:return new e(n[0],n[1],n[2]);case 4:return new e(n[0],n[1],n[2],n[3]);case 5:return new e(n[0],n[1],n[2],n[3],n[4]);case 6:return new e(n[0],n[1],n[2],n[3],n[4],n[5]);case 7:return new e(n[0],n[1],n[2],n[3],n[4],n[5],n[6])}var a=Wa(e.prototype),o=e.apply(a,n);return ot(o)?o:a}}function Uc(n){return function(a,o,c){var d=Se(a);if(!Gm(a)){var m=ud(o,3);a=$t(a),o=function(e){return m(d[e],e,d)}}var t=n(a,o,c);return t>-1?d[m?a[t]:t]:e}}function Lc(a){return od((function(o){var c=o.length,d=c,m=Ma.prototype.thru;for(a&&o.reverse();d--;){var t=o[d];if("function"!=typeof t)throw new ze(n);if(m&&!r&&"wrapper"==td(t))var r=new Ma([],!0)}for(d=r?d:c;++d<c;){var u=td(t=o[d]),i="wrapper"==u?md(t):e;r=i&&xd(i[0])&&424==i[1]&&!i[4].length&&1==i[9]?r[td(i[0])].apply(r,i[3]):1==t.length&&xd(t)?r[u]():r.thru(t)}return function(){var e=arguments,n=e[0];if(r&&1==e.length&&Ym(n))return r.plant(n).value();for(var a=0,d=c?o[a].apply(this,e):n;++a<c;)d=o[a].call(this,d);return d}}))}function Wc(n,a,o,c,d,m,r,u,i,l){var s=a&t,f=1&a,h=2&a,p=24&a,v=512&a,_=h?e:Dc(n);return function t(){for(var g=arguments.length,y=me(g),b=g;b--;)y[b]=arguments[b];if(p)var w=rd(t),x=function(e,n){for(var a=e.length,o=0;a--;)e[a]===n&&++o;return o}(y,w);if(c&&(y=Ic(y,c,d,p)),m&&(y=Rc(y,m,r,p)),g-=x,p&&g<l){var k=ia(y,w);return Kc(n,a,Wc,t.placeholder,o,y,k,u,i,l-g)}var A=f?o:this,j=h?A[n]:n;return g=y.length,u?y=function(n,a){for(var o=n.length,c=ya(a.length,o),d=zc(n);c--;){var m=a[c];n[c]=yd(m,o)?d[m]:e}return n}(y,u):v&&g>1&&y.reverse(),s&&i<g&&(y.length=i),this&&this!==pn&&this instanceof t&&(j=_||Dc(j)),j.apply(A,y)}}function Pc(e,n){return function(a,o){return function(e,n,a,o){return wo(e,(function(e,c,d){n(o,a(e),c,d)})),o}(a,e,n(o),{})}}function Mc(n,a){return function(o,c){var d;if(o===e&&c===e)return a;if(o!==e&&(d=o),c!==e){if(d===e)return c;"string"==typeof o||"string"==typeof c?(o=ic(o),c=ic(c)):(o=uc(o),c=uc(c)),d=n(o,c)}return d}}function Fc(e){return od((function(n){return n=Vn(n,Qn(ud())),Jo((function(a){var o=this;return e(n,(function(e){return Sn(e,o,a)}))}))}))}function qc(n,a){var o=(a=a===e?" ":ic(a)).length;if(o<2)return o?Ho(a,n):a;var c=Ho(a,vn(n/sa(a)));return ta(a)?xc(fa(c),0,n).join(""):c.slice(0,n)}function Zc(n){return function(a,o,c){return c&&"number"!=typeof c&&bd(a,o,c)&&(o=c=e),a=vt(a),o===e?(o=a,a=0):o=vt(o),function(e,n,a,o){for(var c=-1,d=ga(vn((n-e)/(a||1)),0),m=me(d);d--;)m[o?d:++c]=e,e+=a;return m}(a,o,c=c===e?a<o?1:-1:vt(c),n)}}function Yc(e){return function(n,a){return"string"==typeof n&&"string"==typeof a||(n=yt(n),a=yt(a)),e(n,a)}}function Kc(n,a,o,c,t,r,u,i,l,s){var f=8&a;a|=f?d:m,4&(a&=~(f?m:d))||(a&=-4);var h=[n,a,t,f?r:e,f?u:e,f?e:r,f?e:u,i,l,s],p=o.apply(e,h);return xd(n)&&zd(p,h),p.placeholder=c,Td(p,n,a)}function Gc(e){var n=Ce[e];return function(e,a){if(e=yt(e),(a=null==a?0:ya(_t(a),292))&&Ln(e)){var o=(wt(e)+"e").split("e");return+((o=(wt(n(o[0]+"e"+(+o[1]+a)))+"e").split("e"))[0]+"e"+(+o[1]-a))}return n(e)}}var Hc=Sa&&1/la(new Sa([,-0]))[1]==u?function(e){return new Sa(e)}:lr;function Jc(e){return function(n){var a=pd(n);return a==w?ra(n):a==C?function(e){var n=-1,a=Array(e.size);return e.forEach((function(e){a[++n]=[e,e]})),a}(n):function(e,n){return Vn(n,(function(n){return[n,e[n]]}))}(n,e(n))}}function Xc(a,u,i,l,s,f,h,p){var v=2&u;if(!v&&"function"!=typeof a)throw new ze(n);var _=l?l.length:0;if(_||(u&=-97,l=s=e),h=h===e?h:ga(_t(h),0),p=p===e?p:_t(p),_-=s?s.length:0,u&m){var g=l,y=s;l=s=e}var b=v?e:md(a),w=[a,u,i,l,s,g,y,f,h,p];if(b&&function(e,n){var a=e[1],c=n[1],d=a|c,m=d<131,u=c==t&&8==a||c==t&&a==r&&e[7].length<=n[8]||384==c&&n[7].length<=n[8]&&8==a;if(!m&&!u)return e;1&c&&(e[2]=n[2],d|=1&a?0:4);var i=n[3];if(i){var l=e[3];e[3]=l?Ic(l,i,n[4]):i,e[4]=l?ia(e[3],o):n[4]}(i=n[5])&&(l=e[5],e[5]=l?Rc(l,i,n[6]):i,e[6]=l?ia(e[5],o):n[6]),(i=n[7])&&(e[7]=i),c&t&&(e[8]=null==e[8]?n[8]:ya(e[8],n[8])),null==e[9]&&(e[9]=n[9]),e[0]=n[0],e[1]=d}(w,b),a=w[0],u=w[1],i=w[2],l=w[3],s=w[4],!(p=w[9]=w[9]===e?v?0:a.length:ga(w[9]-_,0))&&24&u&&(u&=-25),u&&1!=u)x=8==u||u==c?function(n,a,o){var c=Dc(n);return function d(){for(var m=arguments.length,t=me(m),r=m,u=rd(d);r--;)t[r]=arguments[r];var i=m<3&&t[0]!==u&&t[m-1]!==u?[]:ia(t,u);return(m-=i.length)<o?Kc(n,a,Wc,d.placeholder,e,t,i,e,e,o-m):Sn(this&&this!==pn&&this instanceof d?c:n,this,t)}}(a,u,p):u!=d&&33!=u||s.length?Wc.apply(e,w):function(e,n,a,o){var c=1&n,d=Dc(e);return function n(){for(var m=-1,t=arguments.length,r=-1,u=o.length,i=me(u+t),l=this&&this!==pn&&this instanceof n?d:e;++r<u;)i[r]=o[r];for(;t--;)i[r++]=arguments[++m];return Sn(l,c?a:this,i)}}(a,u,i,l);else var x=function(e,n,a){var o=1&n,c=Dc(e);return function n(){return(this&&this!==pn&&this instanceof n?c:e).apply(o?a:this,arguments)}}(a,u,i);return Td((b?nc:zd)(x,w),a,u)}function Qc(n,a,o,c){return n===e||Mm(n,Te[o])&&!Be.call(c,o)?a:n}function ed(n,a,o,c,d,m){return ot(n)&&ot(a)&&(m.set(a,n),Mo(n,a,e,ed,m),m.delete(a)),n}function nd(n){return tt(n)?e:n}function ad(n,a,o,c,d,m){var t=1&o,r=n.length,u=a.length;if(r!=u&&!(t&&u>r))return!1;var i=m.get(n),l=m.get(a);if(i&&l)return i==a&&l==n;var s=-1,f=!0,h=2&o?new Ka:e;for(m.set(n,a),m.set(a,n);++s<r;){var p=n[s],v=a[s];if(c)var _=t?c(v,p,s,a,n,m):c(p,v,s,n,a,m);if(_!==e){if(_)continue;f=!1;break}if(h){if(!Un(a,(function(e,n){if(!na(h,n)&&(p===e||d(p,e,o,c,m)))return h.push(n)}))){f=!1;break}}else if(p!==v&&!d(p,v,o,c,m)){f=!1;break}}return m.delete(n),m.delete(a),f}function od(n){return Od(Sd(n,e,Kd),n+"")}function cd(e){return jo(e,$t,fd)}function dd(e){return jo(e,Vt,hd)}var md=za?function(e){return za.get(e)}:lr;function td(e){for(var n=e.name+"",a=Ea[n],o=Be.call(Ea,n)?a.length:0;o--;){var c=a[o],d=c.func;if(null==d||d==e)return c.name}return n}function rd(e){return(Be.call(La,"placeholder")?La:e).placeholder}function ud(){var e=La.iteratee||tr;return e=e===tr?Bo:e,arguments.length?e(arguments[0],arguments[1]):e}function id(e,n){var a,o,c=e.__data__;return("string"==(o=typeof(a=n))||"number"==o||"symbol"==o||"boolean"==o?"__proto__"!==a:null===a)?c["string"==typeof n?"string":"hash"]:c.map}function ld(e){for(var n=$t(e),a=n.length;a--;){var o=n[a],c=e[o];n[a]=[o,c,jd(c)]}return n}function sd(n,a){var o=function(n,a){return null==n?e:n[a]}(n,a);return Vo(o)?o:e}var fd=yn?function(e){return null==e?[]:(e=Se(e),On(yn(e),(function(n){return Ge.call(e,n)})))}:gr,hd=yn?function(e){for(var n=[];e;)Bn(n,fd(e)),e=Ye(e);return n}:gr,pd=Co;function vd(e,n,a){for(var o=-1,c=(n=bc(n,e)).length,d=!1;++o<c;){var m=Ld(n[o]);if(!(d=null!=e&&a(e,m)))break;e=e[m]}return d||++o!=c?d:!!(c=null==e?0:e.length)&&at(c)&&yd(m,c)&&(Ym(e)||Zm(e))}function _d(e){return"function"!=typeof e.constructor||Ad(e)?{}:Wa(Ye(e))}function gd(e){return Ym(e)||Zm(e)||!!(Je&&e&&e[Je])}function yd(e,n){var a=typeof e;return!!(n=null==n?i:n)&&("number"==a||"symbol"!=a&&ye.test(e))&&e>-1&&e%1==0&&e<n}function bd(e,n,a){if(!ot(a))return!1;var o=typeof n;return!!("number"==o?Gm(a)&&yd(n,a.length):"string"==o&&n in a)&&Mm(a[n],e)}function wd(e,n){if(Ym(e))return!1;var a=typeof e;return!("number"!=a&&"symbol"!=a&&"boolean"!=a&&null!=e&&!lt(e))||Q.test(e)||!X.test(e)||null!=n&&e in Se(n)}function xd(e){var n=td(e),a=La[n];if("function"!=typeof a||!(n in Fa.prototype))return!1;if(e===a)return!0;var o=md(a);return!!o&&e===o[0]}(Aa&&pd(new Aa(new ArrayBuffer(1)))!=E||ja&&pd(new ja)!=w||Ca&&pd(Ca.resolve())!=A||Sa&&pd(new Sa)!=C||Ia&&pd(new Ia)!=R)&&(pd=function(n){var a=Co(n),o=a==k?n.constructor:e,c=o?Wd(o):"";if(c)switch(c){case Oa:return E;case Ta:return w;case $a:return A;case Va:return C;case Ba:return R}return a});var kd=$e?et:yr;function Ad(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||Te)}function jd(e){return e==e&&!ot(e)}function Cd(n,a){return function(o){return null!=o&&o[n]===a&&(a!==e||n in Se(o))}}function Sd(n,a,o){return a=ga(a===e?n.length-1:a,0),function(){for(var e=arguments,c=-1,d=ga(e.length-a,0),m=me(d);++c<d;)m[c]=e[a+c];c=-1;for(var t=me(a+1);++c<a;)t[c]=e[c];return t[a]=o(m),Sn(n,this,t)}}function Id(e,n){return n.length<2?e:Ao(e,cc(n,0,-1))}function Rd(e,n){if(("constructor"!==n||"function"!=typeof e[n])&&"__proto__"!=n)return e[n]}var zd=$d(nc),Ed=hn||function(e,n){return pn.setTimeout(e,n)},Od=$d(ac);function Td(e,n,a){var o=n+"";return Od(e,function(e,n){var a=n.length;if(!a)return e;var o=a-1;return n[o]=(a>1?"& ":"")+n[o],n=n.join(a>2?", ":" "),e.replace(te,"{\n/* [wrapped with "+n+"] */\n")}(o,function(e,n){return Rn(f,(function(a){var o="_."+a[0];n&a[1]&&!Tn(e,o)&&e.push(o)})),e.sort()}(function(e){var n=e.match(re);return n?n[1].split(ue):[]}(o),a)))}function $d(n){var a=0,o=0;return function(){var c=ba(),d=16-(c-o);if(o=c,d>0){if(++a>=800)return arguments[0]}else a=0;return n.apply(e,arguments)}}function Vd(n,a){var o=-1,c=n.length,d=c-1;for(a=a===e?c:a;++o<a;){var m=Go(o,d),t=n[m];n[m]=n[o],n[o]=t}return n.length=a,n}var Bd,Nd,Dd,Ud=(Bd=function(e){var n=[];return 46===e.charCodeAt(0)&&n.push(""),e.replace(ee,(function(e,a,o,c){n.push(o?c.replace(se,"$1"):a||e)})),n},Nd=Nm(Bd,(function(e){return 500===Dd.size&&Dd.clear(),e})),Dd=Nd.cache,Nd);function Ld(e){if("string"==typeof e||lt(e))return e;var n=e+"";return"0"==n&&1/e==-1/0?"-0":n}function Wd(e){if(null!=e){try{return Ve.call(e)}catch(n){}try{return e+""}catch(n){}}return""}function Pd(e){if(e instanceof Fa)return e.clone();var n=new Ma(e.__wrapped__,e.__chain__);return n.__actions__=zc(e.__actions__),n.__index__=e.__index__,n.__values__=e.__values__,n}var Md=Jo((function(e,n){return Hm(e)?so(e,go(n,1,Hm,!0)):[]})),Fd=Jo((function(n,a){var o=Qd(a);return Hm(o)&&(o=e),Hm(n)?so(n,go(a,1,Hm,!0),ud(o,2)):[]})),qd=Jo((function(n,a){var o=Qd(a);return Hm(o)&&(o=e),Hm(n)?so(n,go(a,1,Hm,!0),e,o):[]}));function Zd(e,n,a){var o=null==e?0:e.length;if(!o)return-1;var c=null==a?0:_t(a);return c<0&&(c=ga(o+c,0)),Pn(e,ud(n,3),c)}function Yd(n,a,o){var c=null==n?0:n.length;if(!c)return-1;var d=c-1;return o!==e&&(d=_t(o),d=o<0?ga(c+d,0):ya(d,c-1)),Pn(n,ud(a,3),d,!0)}function Kd(e){return null!=e&&e.length?go(e,1):[]}function Gd(n){return n&&n.length?n[0]:e}var Hd=Jo((function(e){var n=Vn(e,gc);return n.length&&n[0]===e[0]?zo(n):[]})),Jd=Jo((function(n){var a=Qd(n),o=Vn(n,gc);return a===Qd(o)?a=e:o.pop(),o.length&&o[0]===n[0]?zo(o,ud(a,2)):[]})),Xd=Jo((function(n){var a=Qd(n),o=Vn(n,gc);return(a="function"==typeof a?a:e)&&o.pop(),o.length&&o[0]===n[0]?zo(o,e,a):[]}));function Qd(n){var a=null==n?0:n.length;return a?n[a-1]:e}var em=Jo(nm);function nm(e,n){return e&&e.length&&n&&n.length?Yo(e,n):e}var am=od((function(e,n){var a=null==e?0:e.length,o=to(e,n);return Ko(e,Vn(n,(function(e){return yd(e,a)?+e:e})).sort(Sc)),o}));function om(e){return null==e?e:ka.call(e)}var cm=Jo((function(e){return lc(go(e,1,Hm,!0))})),dm=Jo((function(n){var a=Qd(n);return Hm(a)&&(a=e),lc(go(n,1,Hm,!0),ud(a,2))})),mm=Jo((function(n){var a=Qd(n);return a="function"==typeof a?a:e,lc(go(n,1,Hm,!0),e,a)}));function tm(e){if(!e||!e.length)return[];var n=0;return e=On(e,(function(e){if(Hm(e))return n=ga(e.length,n),!0})),Jn(n,(function(n){return Vn(e,Yn(n))}))}function rm(n,a){if(!n||!n.length)return[];var o=tm(n);return null==a?o:Vn(o,(function(n){return Sn(a,e,n)}))}var um=Jo((function(e,n){return Hm(e)?so(e,n):[]})),im=Jo((function(e){return vc(On(e,Hm))})),lm=Jo((function(n){var a=Qd(n);return Hm(a)&&(a=e),vc(On(n,Hm),ud(a,2))})),sm=Jo((function(n){var a=Qd(n);return a="function"==typeof a?a:e,vc(On(n,Hm),e,a)})),fm=Jo(tm),hm=Jo((function(n){var a=n.length,o=a>1?n[a-1]:e;return o="function"==typeof o?(n.pop(),o):e,rm(n,o)}));function pm(e){var n=La(e);return n.__chain__=!0,n}function vm(e,n){return n(e)}var _m=od((function(n){var a=n.length,o=a?n[0]:0,c=this.__wrapped__,d=function(e){return to(e,n)};return!(a>1||this.__actions__.length)&&c instanceof Fa&&yd(o)?((c=c.slice(o,+o+(a?1:0))).__actions__.push({func:vm,args:[d],thisArg:e}),new Ma(c,this.__chain__).thru((function(n){return a&&!n.length&&n.push(e),n}))):this.thru(d)})),gm=Oc((function(e,n,a){Be.call(e,a)?++e[a]:mo(e,a,1)})),ym=Uc(Zd),bm=Uc(Yd);function wm(e,n){return(Ym(e)?Rn:fo)(e,ud(n,3))}function xm(e,n){return(Ym(e)?zn:ho)(e,ud(n,3))}var km=Oc((function(e,n,a){Be.call(e,a)?e[a].push(n):mo(e,a,[n])})),Am=Jo((function(e,n,a){var o=-1,c="function"==typeof n,d=Gm(e)?me(e.length):[];return fo(e,(function(e){d[++o]=c?Sn(n,e,a):Eo(e,n,a)})),d})),jm=Oc((function(e,n,a){mo(e,a,n)}));function Cm(e,n){return(Ym(e)?Vn:Lo)(e,ud(n,3))}var Sm=Oc((function(e,n,a){e[a?0:1].push(n)}),(function(){return[[],[]]})),Im=Jo((function(e,n){if(null==e)return[];var a=n.length;return a>1&&bd(e,n[0],n[1])?n=[]:a>2&&bd(n[0],n[1],n[2])&&(n=[n[0]]),qo(e,go(n,1),[])})),Rm=fn||function(){return pn.Date.now()};function zm(n,a,o){return a=o?e:a,a=n&&null==a?n.length:a,Xc(n,t,e,e,e,e,a)}function Em(a,o){var c;if("function"!=typeof o)throw new ze(n);return a=_t(a),function(){return--a>0&&(c=o.apply(this,arguments)),a<=1&&(o=e),c}}var Om=Jo((function(e,n,a){var o=1;if(a.length){var c=ia(a,rd(Om));o|=d}return Xc(e,o,n,a,c)})),Tm=Jo((function(e,n,a){var o=3;if(a.length){var c=ia(a,rd(Tm));o|=d}return Xc(n,o,e,a,c)}));function $m(a,o,c){var d,m,t,r,u,i,l=0,s=!1,f=!1,h=!0;if("function"!=typeof a)throw new ze(n);function p(n){var o=d,c=m;return d=m=e,l=n,r=a.apply(c,o)}function v(n){var a=n-i;return i===e||a>=o||a<0||f&&n-l>=t}function _(){var e=Rm();if(v(e))return g(e);u=Ed(_,function(e){var n=o-(e-i);return f?ya(n,t-(e-l)):n}(e))}function g(n){return u=e,h&&d?p(n):(d=m=e,r)}function y(){var n=Rm(),a=v(n);if(d=arguments,m=this,i=n,a){if(u===e)return function(e){return l=e,u=Ed(_,o),s?p(e):r}(i);if(f)return kc(u),u=Ed(_,o),p(i)}return u===e&&(u=Ed(_,o)),r}return o=yt(o)||0,ot(c)&&(s=!!c.leading,t=(f="maxWait"in c)?ga(yt(c.maxWait)||0,o):t,h="trailing"in c?!!c.trailing:h),y.cancel=function(){u!==e&&kc(u),l=0,d=i=m=u=e},y.flush=function(){return u===e?r:g(Rm())},y}var Vm=Jo((function(e,n){return lo(e,1,n)})),Bm=Jo((function(e,n,a){return lo(e,yt(n)||0,a)}));function Nm(e,a){if("function"!=typeof e||null!=a&&"function"!=typeof a)throw new ze(n);var o=function(){var n=arguments,c=a?a.apply(this,n):n[0],d=o.cache;if(d.has(c))return d.get(c);var m=e.apply(this,n);return o.cache=d.set(c,m)||d,m};return o.cache=new(Nm.Cache||Ya),o}function Dm(e){if("function"!=typeof e)throw new ze(n);return function(){var n=arguments;switch(n.length){case 0:return!e.call(this);case 1:return!e.call(this,n[0]);case 2:return!e.call(this,n[0],n[1]);case 3:return!e.call(this,n[0],n[1],n[2])}return!e.apply(this,n)}}Nm.Cache=Ya;var Um=wc((function(e,n){var a=(n=1==n.length&&Ym(n[0])?Vn(n[0],Qn(ud())):Vn(go(n,1),Qn(ud()))).length;return Jo((function(o){for(var c=-1,d=ya(o.length,a);++c<d;)o[c]=n[c].call(this,o[c]);return Sn(e,this,o)}))})),Lm=Jo((function(n,a){var o=ia(a,rd(Lm));return Xc(n,d,e,a,o)})),Wm=Jo((function(n,a){var o=ia(a,rd(Wm));return Xc(n,m,e,a,o)})),Pm=od((function(n,a){return Xc(n,r,e,e,e,a)}));function Mm(e,n){return e===n||e!=e&&n!=n}var Fm=Yc(So),qm=Yc((function(e,n){return e>=n})),Zm=Oo(function(){return arguments}())?Oo:function(e){return ct(e)&&Be.call(e,"callee")&&!Ge.call(e,"callee")},Ym=me.isArray,Km=wn?Qn(wn):function(e){return ct(e)&&Co(e)==z};function Gm(e){return null!=e&&at(e.length)&&!et(e)}function Hm(e){return ct(e)&&Gm(e)}var Jm=bn||yr,Xm=xn?Qn(xn):function(e){return ct(e)&&Co(e)==_};function Qm(e){if(!ct(e))return!1;var n=Co(e);return n==g||"[object DOMException]"==n||"string"==typeof e.message&&"string"==typeof e.name&&!tt(e)}function et(e){if(!ot(e))return!1;var n=Co(e);return n==y||n==b||"[object AsyncFunction]"==n||"[object Proxy]"==n}function nt(e){return"number"==typeof e&&e==_t(e)}function at(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=i}function ot(e){var n=typeof e;return null!=e&&("object"==n||"function"==n)}function ct(e){return null!=e&&"object"==typeof e}var dt=kn?Qn(kn):function(e){return ct(e)&&pd(e)==w};function mt(e){return"number"==typeof e||ct(e)&&Co(e)==x}function tt(e){if(!ct(e)||Co(e)!=k)return!1;var n=Ye(e);if(null===n)return!0;var a=Be.call(n,"constructor")&&n.constructor;return"function"==typeof a&&a instanceof a&&Ve.call(a)==Le}var rt=An?Qn(An):function(e){return ct(e)&&Co(e)==j},ut=jn?Qn(jn):function(e){return ct(e)&&pd(e)==C};function it(e){return"string"==typeof e||!Ym(e)&&ct(e)&&Co(e)==S}function lt(e){return"symbol"==typeof e||ct(e)&&Co(e)==I}var st=Cn?Qn(Cn):function(e){return ct(e)&&at(e.length)&&!!tn[Co(e)]},ft=Yc(Uo),ht=Yc((function(e,n){return e<=n}));function pt(e){if(!e)return[];if(Gm(e))return it(e)?fa(e):zc(e);if(Xe&&e[Xe])return function(e){for(var n,a=[];!(n=e.next()).done;)a.push(n.value);return a}(e[Xe]());var n=pd(e);return(n==w?ra:n==C?la:Mt)(e)}function vt(e){return e?(e=yt(e))===u||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function _t(e){var n=vt(e),a=n%1;return n==n?a?n-a:n:0}function gt(e){return e?ro(_t(e),0,s):0}function yt(e){if("number"==typeof e)return e;if(lt(e))return l;if(ot(e)){var n="function"==typeof e.valueOf?e.valueOf():e;e=ot(n)?n+"":n}if("string"!=typeof e)return 0===e?e:+e;e=Xn(e);var a=ve.test(e);return a||ge.test(e)?sn(e.slice(2),a?2:8):pe.test(e)?l:+e}function bt(e){return Ec(e,Vt(e))}function wt(e){return null==e?"":ic(e)}var xt=Tc((function(e,n){if(Ad(n)||Gm(n))Ec(n,$t(n),e);else for(var a in n)Be.call(n,a)&&no(e,a,n[a])})),kt=Tc((function(e,n){Ec(n,Vt(n),e)})),At=Tc((function(e,n,a,o){Ec(n,Vt(n),e,o)})),jt=Tc((function(e,n,a,o){Ec(n,$t(n),e,o)})),Ct=od(to),St=Jo((function(n,a){n=Se(n);var o=-1,c=a.length,d=c>2?a[2]:e;for(d&&bd(a[0],a[1],d)&&(c=1);++o<c;)for(var m=a[o],t=Vt(m),r=-1,u=t.length;++r<u;){var i=t[r],l=n[i];(l===e||Mm(l,Te[i])&&!Be.call(n,i))&&(n[i]=m[i])}return n})),It=Jo((function(n){return n.push(e,ed),Sn(Nt,e,n)}));function Rt(n,a,o){var c=null==n?e:Ao(n,a);return c===e?o:c}function zt(e,n){return null!=e&&vd(e,n,Ro)}var Et=Pc((function(e,n,a){null!=n&&"function"!=typeof n.toString&&(n=Ue.call(n)),e[n]=a}),or(mr)),Ot=Pc((function(e,n,a){null!=n&&"function"!=typeof n.toString&&(n=Ue.call(n)),Be.call(e,n)?e[n].push(a):e[n]=[a]}),ud),Tt=Jo(Eo);function $t(e){return Gm(e)?Ha(e):No(e)}function Vt(e){return Gm(e)?Ha(e,!0):Do(e)}var Bt=Tc((function(e,n,a){Mo(e,n,a)})),Nt=Tc((function(e,n,a,o){Mo(e,n,a,o)})),Dt=od((function(e,n){var a={};if(null==e)return a;var o=!1;n=Vn(n,(function(n){return n=bc(n,e),o||(o=n.length>1),n})),Ec(e,dd(e),a),o&&(a=uo(a,7,nd));for(var c=n.length;c--;)sc(a,n[c]);return a})),Ut=od((function(e,n){return null==e?{}:function(e,n){return Zo(e,n,(function(n,a){return zt(e,a)}))}(e,n)}));function Lt(e,n){if(null==e)return{};var a=Vn(dd(e),(function(e){return[e]}));return n=ud(n),Zo(e,a,(function(e,a){return n(e,a[0])}))}var Wt=Jc($t),Pt=Jc(Vt);function Mt(e){return null==e?[]:ea(e,$t(e))}var Ft=Nc((function(e,n,a){return n=n.toLowerCase(),e+(a?qt(n):n)}));function qt(e){return Qt(wt(e).toLowerCase())}function Zt(e){return(e=wt(e))&&e.replace(be,ca).replace(en,"")}var Yt=Nc((function(e,n,a){return e+(a?"-":"")+n.toLowerCase()})),Kt=Nc((function(e,n,a){return e+(a?" ":"")+n.toLowerCase()})),Gt=Bc("toLowerCase"),Ht=Nc((function(e,n,a){return e+(a?"_":"")+n.toLowerCase()})),Jt=Nc((function(e,n,a){return e+(a?" ":"")+Qt(n)})),Xt=Nc((function(e,n,a){return e+(a?" ":"")+n.toUpperCase()})),Qt=Bc("toUpperCase");function er(n,a,o){return n=wt(n),(a=o?e:a)===e?function(e){return cn.test(e)}(n)?function(e){return e.match(an)||[]}(n):function(e){return e.match(ie)||[]}(n):n.match(a)||[]}var nr=Jo((function(n,a){try{return Sn(n,e,a)}catch(o){return Qm(o)?o:new Ae(o)}})),ar=od((function(e,n){return Rn(n,(function(n){n=Ld(n),mo(e,n,Om(e[n],e))})),e}));function or(e){return function(){return e}}var cr=Lc(),dr=Lc(!0);function mr(e){return e}function tr(e){return Bo("function"==typeof e?e:uo(e,1))}var rr=Jo((function(e,n){return function(a){return Eo(a,e,n)}})),ur=Jo((function(e,n){return function(a){return Eo(e,a,n)}}));function ir(e,n,a){var o=$t(n),c=ko(n,o);null!=a||ot(n)&&(c.length||!o.length)||(a=n,n=e,e=this,c=ko(n,$t(n)));var d=!(ot(a)&&"chain"in a&&!a.chain),m=et(e);return Rn(c,(function(a){var o=n[a];e[a]=o,m&&(e.prototype[a]=function(){var n=this.__chain__;if(d||n){var a=e(this.__wrapped__);return(a.__actions__=zc(this.__actions__)).push({func:o,args:arguments,thisArg:e}),a.__chain__=n,a}return o.apply(e,Bn([this.value()],arguments))})})),e}function lr(){}var sr=Fc(Vn),fr=Fc(En),hr=Fc(Un);function pr(e){return wd(e)?Yn(Ld(e)):function(e){return function(n){return Ao(n,e)}}(e)}var vr=Zc(),_r=Zc(!0);function gr(){return[]}function yr(){return!1}var br,wr=Mc((function(e,n){return e+n}),0),xr=Gc("ceil"),kr=Mc((function(e,n){return e/n}),1),Ar=Gc("floor"),jr=Mc((function(e,n){return e*n}),1),Cr=Gc("round"),Sr=Mc((function(e,n){return e-n}),0);return La.after=function(e,a){if("function"!=typeof a)throw new ze(n);return e=_t(e),function(){if(--e<1)return a.apply(this,arguments)}},La.ary=zm,La.assign=xt,La.assignIn=kt,La.assignInWith=At,La.assignWith=jt,La.at=Ct,La.before=Em,La.bind=Om,La.bindAll=ar,La.bindKey=Tm,La.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Ym(e)?e:[e]},La.chain=pm,La.chunk=function(n,a,o){a=(o?bd(n,a,o):a===e)?1:ga(_t(a),0);var c=null==n?0:n.length;if(!c||a<1)return[];for(var d=0,m=0,t=me(vn(c/a));d<c;)t[m++]=cc(n,d,d+=a);return t},La.compact=function(e){for(var n=-1,a=null==e?0:e.length,o=0,c=[];++n<a;){var d=e[n];d&&(c[o++]=d)}return c},La.concat=function(){var e=arguments.length;if(!e)return[];for(var n=me(e-1),a=arguments[0],o=e;o--;)n[o-1]=arguments[o];return Bn(Ym(a)?zc(a):[a],go(n,1))},La.cond=function(e){var a=null==e?0:e.length,o=ud();return e=a?Vn(e,(function(e){if("function"!=typeof e[1])throw new ze(n);return[o(e[0]),e[1]]})):[],Jo((function(n){for(var o=-1;++o<a;){var c=e[o];if(Sn(c[0],this,n))return Sn(c[1],this,n)}}))},La.conforms=function(e){return function(e){var n=$t(e);return function(a){return io(a,e,n)}}(uo(e,1))},La.constant=or,La.countBy=gm,La.create=function(e,n){var a=Wa(e);return null==n?a:co(a,n)},La.curry=function n(a,o,c){var d=Xc(a,8,e,e,e,e,e,o=c?e:o);return d.placeholder=n.placeholder,d},La.curryRight=function n(a,o,d){var m=Xc(a,c,e,e,e,e,e,o=d?e:o);return m.placeholder=n.placeholder,m},La.debounce=$m,La.defaults=St,La.defaultsDeep=It,La.defer=Vm,La.delay=Bm,La.difference=Md,La.differenceBy=Fd,La.differenceWith=qd,La.drop=function(n,a,o){var c=null==n?0:n.length;return c?cc(n,(a=o||a===e?1:_t(a))<0?0:a,c):[]},La.dropRight=function(n,a,o){var c=null==n?0:n.length;return c?cc(n,0,(a=c-(a=o||a===e?1:_t(a)))<0?0:a):[]},La.dropRightWhile=function(e,n){return e&&e.length?hc(e,ud(n,3),!0,!0):[]},La.dropWhile=function(e,n){return e&&e.length?hc(e,ud(n,3),!0):[]},La.fill=function(n,a,o,c){var d=null==n?0:n.length;return d?(o&&"number"!=typeof o&&bd(n,a,o)&&(o=0,c=d),function(n,a,o,c){var d=n.length;for((o=_t(o))<0&&(o=-o>d?0:d+o),(c=c===e||c>d?d:_t(c))<0&&(c+=d),c=o>c?0:gt(c);o<c;)n[o++]=a;return n}(n,a,o,c)):[]},La.filter=function(e,n){return(Ym(e)?On:_o)(e,ud(n,3))},La.flatMap=function(e,n){return go(Cm(e,n),1)},La.flatMapDeep=function(e,n){return go(Cm(e,n),u)},La.flatMapDepth=function(n,a,o){return o=o===e?1:_t(o),go(Cm(n,a),o)},La.flatten=Kd,La.flattenDeep=function(e){return null!=e&&e.length?go(e,u):[]},La.flattenDepth=function(n,a){return null!=n&&n.length?go(n,a=a===e?1:_t(a)):[]},La.flip=function(e){return Xc(e,512)},La.flow=cr,La.flowRight=dr,La.fromPairs=function(e){for(var n=-1,a=null==e?0:e.length,o={};++n<a;){var c=e[n];o[c[0]]=c[1]}return o},La.functions=function(e){return null==e?[]:ko(e,$t(e))},La.functionsIn=function(e){return null==e?[]:ko(e,Vt(e))},La.groupBy=km,La.initial=function(e){return null!=e&&e.length?cc(e,0,-1):[]},La.intersection=Hd,La.intersectionBy=Jd,La.intersectionWith=Xd,La.invert=Et,La.invertBy=Ot,La.invokeMap=Am,La.iteratee=tr,La.keyBy=jm,La.keys=$t,La.keysIn=Vt,La.map=Cm,La.mapKeys=function(e,n){var a={};return n=ud(n,3),wo(e,(function(e,o,c){mo(a,n(e,o,c),e)})),a},La.mapValues=function(e,n){var a={};return n=ud(n,3),wo(e,(function(e,o,c){mo(a,o,n(e,o,c))})),a},La.matches=function(e){return Wo(uo(e,1))},La.matchesProperty=function(e,n){return Po(e,uo(n,1))},La.memoize=Nm,La.merge=Bt,La.mergeWith=Nt,La.method=rr,La.methodOf=ur,La.mixin=ir,La.negate=Dm,La.nthArg=function(e){return e=_t(e),Jo((function(n){return Fo(n,e)}))},La.omit=Dt,La.omitBy=function(e,n){return Lt(e,Dm(ud(n)))},La.once=function(e){return Em(2,e)},La.orderBy=function(n,a,o,c){return null==n?[]:(Ym(a)||(a=null==a?[]:[a]),Ym(o=c?e:o)||(o=null==o?[]:[o]),qo(n,a,o))},La.over=sr,La.overArgs=Um,La.overEvery=fr,La.overSome=hr,La.partial=Lm,La.partialRight=Wm,La.partition=Sm,La.pick=Ut,La.pickBy=Lt,La.property=pr,La.propertyOf=function(n){return function(a){return null==n?e:Ao(n,a)}},La.pull=em,La.pullAll=nm,La.pullAllBy=function(e,n,a){return e&&e.length&&n&&n.length?Yo(e,n,ud(a,2)):e},La.pullAllWith=function(n,a,o){return n&&n.length&&a&&a.length?Yo(n,a,e,o):n},La.pullAt=am,La.range=vr,La.rangeRight=_r,La.rearg=Pm,La.reject=function(e,n){return(Ym(e)?On:_o)(e,Dm(ud(n,3)))},La.remove=function(e,n){var a=[];if(!e||!e.length)return a;var o=-1,c=[],d=e.length;for(n=ud(n,3);++o<d;){var m=e[o];n(m,o,e)&&(a.push(m),c.push(o))}return Ko(e,c),a},La.rest=function(a,o){if("function"!=typeof a)throw new ze(n);return Jo(a,o=o===e?o:_t(o))},La.reverse=om,La.sampleSize=function(n,a,o){return a=(o?bd(n,a,o):a===e)?1:_t(a),(Ym(n)?Xa:Qo)(n,a)},La.set=function(e,n,a){return null==e?e:ec(e,n,a)},La.setWith=function(n,a,o,c){return c="function"==typeof c?c:e,null==n?n:ec(n,a,o,c)},La.shuffle=function(e){return(Ym(e)?Qa:oc)(e)},La.slice=function(n,a,o){var c=null==n?0:n.length;return c?(o&&"number"!=typeof o&&bd(n,a,o)?(a=0,o=c):(a=null==a?0:_t(a),o=o===e?c:_t(o)),cc(n,a,o)):[]},La.sortBy=Im,La.sortedUniq=function(e){return e&&e.length?rc(e):[]},La.sortedUniqBy=function(e,n){return e&&e.length?rc(e,ud(n,2)):[]},La.split=function(n,a,o){return o&&"number"!=typeof o&&bd(n,a,o)&&(a=o=e),(o=o===e?s:o>>>0)?(n=wt(n))&&("string"==typeof a||null!=a&&!rt(a))&&!(a=ic(a))&&ta(n)?xc(fa(n),0,o):n.split(a,o):[]},La.spread=function(e,a){if("function"!=typeof e)throw new ze(n);return a=null==a?0:ga(_t(a),0),Jo((function(n){var o=n[a],c=xc(n,0,a);return o&&Bn(c,o),Sn(e,this,c)}))},La.tail=function(e){var n=null==e?0:e.length;return n?cc(e,1,n):[]},La.take=function(n,a,o){return n&&n.length?cc(n,0,(a=o||a===e?1:_t(a))<0?0:a):[]},La.takeRight=function(n,a,o){var c=null==n?0:n.length;return c?cc(n,(a=c-(a=o||a===e?1:_t(a)))<0?0:a,c):[]},La.takeRightWhile=function(e,n){return e&&e.length?hc(e,ud(n,3),!1,!0):[]},La.takeWhile=function(e,n){return e&&e.length?hc(e,ud(n,3)):[]},La.tap=function(e,n){return n(e),e},La.throttle=function(e,a,o){var c=!0,d=!0;if("function"!=typeof e)throw new ze(n);return ot(o)&&(c="leading"in o?!!o.leading:c,d="trailing"in o?!!o.trailing:d),$m(e,a,{leading:c,maxWait:a,trailing:d})},La.thru=vm,La.toArray=pt,La.toPairs=Wt,La.toPairsIn=Pt,La.toPath=function(e){return Ym(e)?Vn(e,Ld):lt(e)?[e]:zc(Ud(wt(e)))},La.toPlainObject=bt,La.transform=function(e,n,a){var o=Ym(e),c=o||Jm(e)||st(e);if(n=ud(n,4),null==a){var d=e&&e.constructor;a=c?o?new d:[]:ot(e)&&et(d)?Wa(Ye(e)):{}}return(c?Rn:wo)(e,(function(e,o,c){return n(a,e,o,c)})),a},La.unary=function(e){return zm(e,1)},La.union=cm,La.unionBy=dm,La.unionWith=mm,La.uniq=function(e){return e&&e.length?lc(e):[]},La.uniqBy=function(e,n){return e&&e.length?lc(e,ud(n,2)):[]},La.uniqWith=function(n,a){return a="function"==typeof a?a:e,n&&n.length?lc(n,e,a):[]},La.unset=function(e,n){return null==e||sc(e,n)},La.unzip=tm,La.unzipWith=rm,La.update=function(e,n,a){return null==e?e:fc(e,n,yc(a))},La.updateWith=function(n,a,o,c){return c="function"==typeof c?c:e,null==n?n:fc(n,a,yc(o),c)},La.values=Mt,La.valuesIn=function(e){return null==e?[]:ea(e,Vt(e))},La.without=um,La.words=er,La.wrap=function(e,n){return Lm(yc(n),e)},La.xor=im,La.xorBy=lm,La.xorWith=sm,La.zip=fm,La.zipObject=function(e,n){return _c(e||[],n||[],no)},La.zipObjectDeep=function(e,n){return _c(e||[],n||[],ec)},La.zipWith=hm,La.entries=Wt,La.entriesIn=Pt,La.extend=kt,La.extendWith=At,ir(La,La),La.add=wr,La.attempt=nr,La.camelCase=Ft,La.capitalize=qt,La.ceil=xr,La.clamp=function(n,a,o){return o===e&&(o=a,a=e),o!==e&&(o=(o=yt(o))==o?o:0),a!==e&&(a=(a=yt(a))==a?a:0),ro(yt(n),a,o)},La.clone=function(e){return uo(e,4)},La.cloneDeep=function(e){return uo(e,5)},La.cloneDeepWith=function(n,a){return uo(n,5,a="function"==typeof a?a:e)},La.cloneWith=function(n,a){return uo(n,4,a="function"==typeof a?a:e)},La.conformsTo=function(e,n){return null==n||io(e,n,$t(n))},La.deburr=Zt,La.defaultTo=function(e,n){return null==e||e!=e?n:e},La.divide=kr,La.endsWith=function(n,a,o){n=wt(n),a=ic(a);var c=n.length,d=o=o===e?c:ro(_t(o),0,c);return(o-=a.length)>=0&&n.slice(o,d)==a},La.eq=Mm,La.escape=function(e){return(e=wt(e))&&Y.test(e)?e.replace(q,da):e},La.escapeRegExp=function(e){return(e=wt(e))&&ce.test(e)?e.replace(oe,"\\$&"):e},La.every=function(n,a,o){var c=Ym(n)?En:po;return o&&bd(n,a,o)&&(a=e),c(n,ud(a,3))},La.find=ym,La.findIndex=Zd,La.findKey=function(e,n){return Wn(e,ud(n,3),wo)},La.findLast=bm,La.findLastIndex=Yd,La.findLastKey=function(e,n){return Wn(e,ud(n,3),xo)},La.floor=Ar,La.forEach=wm,La.forEachRight=xm,La.forIn=function(e,n){return null==e?e:yo(e,ud(n,3),Vt)},La.forInRight=function(e,n){return null==e?e:bo(e,ud(n,3),Vt)},La.forOwn=function(e,n){return e&&wo(e,ud(n,3))},La.forOwnRight=function(e,n){return e&&xo(e,ud(n,3))},La.get=Rt,La.gt=Fm,La.gte=qm,La.has=function(e,n){return null!=e&&vd(e,n,Io)},La.hasIn=zt,La.head=Gd,La.identity=mr,La.includes=function(e,n,a,o){e=Gm(e)?e:Mt(e),a=a&&!o?_t(a):0;var c=e.length;return a<0&&(a=ga(c+a,0)),it(e)?a<=c&&e.indexOf(n,a)>-1:!!c&&Mn(e,n,a)>-1},La.indexOf=function(e,n,a){var o=null==e?0:e.length;if(!o)return-1;var c=null==a?0:_t(a);return c<0&&(c=ga(o+c,0)),Mn(e,n,c)},La.inRange=function(n,a,o){return a=vt(a),o===e?(o=a,a=0):o=vt(o),function(e,n,a){return e>=ya(n,a)&&e<ga(n,a)}(n=yt(n),a,o)},La.invoke=Tt,La.isArguments=Zm,La.isArray=Ym,La.isArrayBuffer=Km,La.isArrayLike=Gm,La.isArrayLikeObject=Hm,La.isBoolean=function(e){return!0===e||!1===e||ct(e)&&Co(e)==v},La.isBuffer=Jm,La.isDate=Xm,La.isElement=function(e){return ct(e)&&1===e.nodeType&&!tt(e)},La.isEmpty=function(e){if(null==e)return!0;if(Gm(e)&&(Ym(e)||"string"==typeof e||"function"==typeof e.splice||Jm(e)||st(e)||Zm(e)))return!e.length;var n=pd(e);if(n==w||n==C)return!e.size;if(Ad(e))return!No(e).length;for(var a in e)if(Be.call(e,a))return!1;return!0},La.isEqual=function(e,n){return To(e,n)},La.isEqualWith=function(n,a,o){var c=(o="function"==typeof o?o:e)?o(n,a):e;return c===e?To(n,a,e,o):!!c},La.isError=Qm,La.isFinite=function(e){return"number"==typeof e&&Ln(e)},La.isFunction=et,La.isInteger=nt,La.isLength=at,La.isMap=dt,La.isMatch=function(e,n){return e===n||$o(e,n,ld(n))},La.isMatchWith=function(n,a,o){return o="function"==typeof o?o:e,$o(n,a,ld(a),o)},La.isNaN=function(e){return mt(e)&&e!=+e},La.isNative=function(e){if(kd(e))throw new Ae("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Vo(e)},La.isNil=function(e){return null==e},La.isNull=function(e){return null===e},La.isNumber=mt,La.isObject=ot,La.isObjectLike=ct,La.isPlainObject=tt,La.isRegExp=rt,La.isSafeInteger=function(e){return nt(e)&&e>=-9007199254740991&&e<=i},La.isSet=ut,La.isString=it,La.isSymbol=lt,La.isTypedArray=st,La.isUndefined=function(n){return n===e},La.isWeakMap=function(e){return ct(e)&&pd(e)==R},La.isWeakSet=function(e){return ct(e)&&"[object WeakSet]"==Co(e)},La.join=function(e,n){return null==e?"":Kn.call(e,n)},La.kebabCase=Yt,La.last=Qd,La.lastIndexOf=function(n,a,o){var c=null==n?0:n.length;if(!c)return-1;var d=c;return o!==e&&(d=(d=_t(o))<0?ga(c+d,0):ya(d,c-1)),a==a?function(e,n,a){for(var o=a+1;o--;)if(e[o]===n)return o;return o}(n,a,d):Pn(n,qn,d,!0)},La.lowerCase=Kt,La.lowerFirst=Gt,La.lt=ft,La.lte=ht,La.max=function(n){return n&&n.length?vo(n,mr,So):e},La.maxBy=function(n,a){return n&&n.length?vo(n,ud(a,2),So):e},La.mean=function(e){return Zn(e,mr)},La.meanBy=function(e,n){return Zn(e,ud(n,2))},La.min=function(n){return n&&n.length?vo(n,mr,Uo):e},La.minBy=function(n,a){return n&&n.length?vo(n,ud(a,2),Uo):e},La.stubArray=gr,La.stubFalse=yr,La.stubObject=function(){return{}},La.stubString=function(){return""},La.stubTrue=function(){return!0},La.multiply=jr,La.nth=function(n,a){return n&&n.length?Fo(n,_t(a)):e},La.noConflict=function(){return pn._===this&&(pn._=We),this},La.noop=lr,La.now=Rm,La.pad=function(e,n,a){e=wt(e);var o=(n=_t(n))?sa(e):0;if(!n||o>=n)return e;var c=(n-o)/2;return qc(_n(c),a)+e+qc(vn(c),a)},La.padEnd=function(e,n,a){e=wt(e);var o=(n=_t(n))?sa(e):0;return n&&o<n?e+qc(n-o,a):e},La.padStart=function(e,n,a){e=wt(e);var o=(n=_t(n))?sa(e):0;return n&&o<n?qc(n-o,a)+e:e},La.parseInt=function(e,n,a){return a||null==n?n=0:n&&(n=+n),wa(wt(e).replace(de,""),n||0)},La.random=function(n,a,o){if(o&&"boolean"!=typeof o&&bd(n,a,o)&&(a=o=e),o===e&&("boolean"==typeof a?(o=a,a=e):"boolean"==typeof n&&(o=n,n=e)),n===e&&a===e?(n=0,a=1):(n=vt(n),a===e?(a=n,n=0):a=vt(a)),n>a){var c=n;n=a,a=c}if(o||n%1||a%1){var d=xa();return ya(n+d*(a-n+ln("1e-"+((d+"").length-1))),a)}return Go(n,a)},La.reduce=function(e,n,a){var o=Ym(e)?Nn:Gn,c=arguments.length<3;return o(e,ud(n,4),a,c,fo)},La.reduceRight=function(e,n,a){var o=Ym(e)?Dn:Gn,c=arguments.length<3;return o(e,ud(n,4),a,c,ho)},La.repeat=function(n,a,o){return a=(o?bd(n,a,o):a===e)?1:_t(a),Ho(wt(n),a)},La.replace=function(){var e=arguments,n=wt(e[0]);return e.length<3?n:n.replace(e[1],e[2])},La.result=function(n,a,o){var c=-1,d=(a=bc(a,n)).length;for(d||(d=1,n=e);++c<d;){var m=null==n?e:n[Ld(a[c])];m===e&&(c=d,m=o),n=et(m)?m.call(n):m}return n},La.round=Cr,La.runInContext=K,La.sample=function(e){return(Ym(e)?Ja:Xo)(e)},La.size=function(e){if(null==e)return 0;if(Gm(e))return it(e)?sa(e):e.length;var n=pd(e);return n==w||n==C?e.size:No(e).length},La.snakeCase=Ht,La.some=function(n,a,o){var c=Ym(n)?Un:dc;return o&&bd(n,a,o)&&(a=e),c(n,ud(a,3))},La.sortedIndex=function(e,n){return mc(e,n)},La.sortedIndexBy=function(e,n,a){return tc(e,n,ud(a,2))},La.sortedIndexOf=function(e,n){var a=null==e?0:e.length;if(a){var o=mc(e,n);if(o<a&&Mm(e[o],n))return o}return-1},La.sortedLastIndex=function(e,n){return mc(e,n,!0)},La.sortedLastIndexBy=function(e,n,a){return tc(e,n,ud(a,2),!0)},La.sortedLastIndexOf=function(e,n){if(null!=e&&e.length){var a=mc(e,n,!0)-1;if(Mm(e[a],n))return a}return-1},La.startCase=Jt,La.startsWith=function(e,n,a){return e=wt(e),a=null==a?0:ro(_t(a),0,e.length),n=ic(n),e.slice(a,a+n.length)==n},La.subtract=Sr,La.sum=function(e){return e&&e.length?Hn(e,mr):0},La.sumBy=function(e,n){return e&&e.length?Hn(e,ud(n,2)):0},La.template=function(n,a,o){var c=La.templateSettings;o&&bd(n,a,o)&&(a=e),n=wt(n),a=At({},a,c,Qc);var d,m,t=At({},a.imports,c.imports,Qc),r=$t(t),u=ea(t,r),i=0,l=a.interpolate||we,s="__p += '",f=Ie((a.escape||we).source+"|"+l.source+"|"+(l===J?fe:we).source+"|"+(a.evaluate||we).source+"|$","g"),h="//# sourceURL="+(Be.call(a,"sourceURL")?(a.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++mn+"]")+"\n";n.replace(f,(function(e,a,o,c,t,r){return o||(o=c),s+=n.slice(i,r).replace(xe,ma),a&&(d=!0,s+="' +\n__e("+a+") +\n'"),t&&(m=!0,s+="';\n"+t+";\n__p += '"),o&&(s+="' +\n((__t = ("+o+")) == null ? '' : __t) +\n'"),i=r+e.length,e})),s+="';\n";var p=Be.call(a,"variable")&&a.variable;if(p){if(le.test(p))throw new Ae("Invalid `variable` option passed into `_.template`")}else s="with (obj) {\n"+s+"\n}\n";s=(m?s.replace(W,""):s).replace(P,"$1").replace(M,"$1;"),s="function("+(p||"obj")+") {\n"+(p?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(d?", __e = _.escape":"")+(m?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+s+"return __p\n}";var v=nr((function(){return je(r,h+"return "+s).apply(e,u)}));if(v.source=s,Qm(v))throw v;return v},La.times=function(e,n){if((e=_t(e))<1||e>i)return[];var a=s,o=ya(e,s);n=ud(n),e-=s;for(var c=Jn(o,n);++a<e;)n(a);return c},La.toFinite=vt,La.toInteger=_t,La.toLength=gt,La.toLower=function(e){return wt(e).toLowerCase()},La.toNumber=yt,La.toSafeInteger=function(e){return e?ro(_t(e),-9007199254740991,i):0===e?e:0},La.toString=wt,La.toUpper=function(e){return wt(e).toUpperCase()},La.trim=function(n,a,o){if((n=wt(n))&&(o||a===e))return Xn(n);if(!n||!(a=ic(a)))return n;var c=fa(n),d=fa(a);return xc(c,aa(c,d),oa(c,d)+1).join("")},La.trimEnd=function(n,a,o){if((n=wt(n))&&(o||a===e))return n.slice(0,ha(n)+1);if(!n||!(a=ic(a)))return n;var c=fa(n);return xc(c,0,oa(c,fa(a))+1).join("")},La.trimStart=function(n,a,o){if((n=wt(n))&&(o||a===e))return n.replace(de,"");if(!n||!(a=ic(a)))return n;var c=fa(n);return xc(c,aa(c,fa(a))).join("")},La.truncate=function(n,a){var o=30,c="...";if(ot(a)){var d="separator"in a?a.separator:d;o="length"in a?_t(a.length):o,c="omission"in a?ic(a.omission):c}var m=(n=wt(n)).length;if(ta(n)){var t=fa(n);m=t.length}if(o>=m)return n;var r=o-sa(c);if(r<1)return c;var u=t?xc(t,0,r).join(""):n.slice(0,r);if(d===e)return u+c;if(t&&(r+=u.length-r),rt(d)){if(n.slice(r).search(d)){var i,l=u;for(d.global||(d=Ie(d.source,wt(he.exec(d))+"g")),d.lastIndex=0;i=d.exec(l);)var s=i.index;u=u.slice(0,s===e?r:s)}}else if(n.indexOf(ic(d),r)!=r){var f=u.lastIndexOf(d);f>-1&&(u=u.slice(0,f))}return u+c},La.unescape=function(e){return(e=wt(e))&&Z.test(e)?e.replace(F,pa):e},La.uniqueId=function(e){var n=++Ne;return wt(e)+n},La.upperCase=Xt,La.upperFirst=Qt,La.each=wm,La.eachRight=xm,La.first=Gd,ir(La,(br={},wo(La,(function(e,n){Be.call(La.prototype,n)||(br[n]=e)})),br),{chain:!1}),La.VERSION="4.17.21",Rn(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){La[e].placeholder=La})),Rn(["drop","take"],(function(n,a){Fa.prototype[n]=function(o){o=o===e?1:ga(_t(o),0);var c=this.__filtered__&&!a?new Fa(this):this.clone();return c.__filtered__?c.__takeCount__=ya(o,c.__takeCount__):c.__views__.push({size:ya(o,s),type:n+(c.__dir__<0?"Right":"")}),c},Fa.prototype[n+"Right"]=function(e){return this.reverse()[n](e).reverse()}})),Rn(["filter","map","takeWhile"],(function(e,n){var a=n+1,o=1==a||3==a;Fa.prototype[e]=function(e){var n=this.clone();return n.__iteratees__.push({iteratee:ud(e,3),type:a}),n.__filtered__=n.__filtered__||o,n}})),Rn(["head","last"],(function(e,n){var a="take"+(n?"Right":"");Fa.prototype[e]=function(){return this[a](1).value()[0]}})),Rn(["initial","tail"],(function(e,n){var a="drop"+(n?"":"Right");Fa.prototype[e]=function(){return this.__filtered__?new Fa(this):this[a](1)}})),Fa.prototype.compact=function(){return this.filter(mr)},Fa.prototype.find=function(e){return this.filter(e).head()},Fa.prototype.findLast=function(e){return this.reverse().find(e)},Fa.prototype.invokeMap=Jo((function(e,n){return"function"==typeof e?new Fa(this):this.map((function(a){return Eo(a,e,n)}))})),Fa.prototype.reject=function(e){return this.filter(Dm(ud(e)))},Fa.prototype.slice=function(n,a){n=_t(n);var o=this;return o.__filtered__&&(n>0||a<0)?new Fa(o):(n<0?o=o.takeRight(-n):n&&(o=o.drop(n)),a!==e&&(o=(a=_t(a))<0?o.dropRight(-a):o.take(a-n)),o)},Fa.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},Fa.prototype.toArray=function(){return this.take(s)},wo(Fa.prototype,(function(n,a){var o=/^(?:filter|find|map|reject)|While$/.test(a),c=/^(?:head|last)$/.test(a),d=La[c?"take"+("last"==a?"Right":""):a],m=c||/^find/.test(a);d&&(La.prototype[a]=function(){var a=this.__wrapped__,t=c?[1]:arguments,r=a instanceof Fa,u=t[0],i=r||Ym(a),l=function(e){var n=d.apply(La,Bn([e],t));return c&&s?n[0]:n};i&&o&&"function"==typeof u&&1!=u.length&&(r=i=!1);var s=this.__chain__,f=!!this.__actions__.length,h=m&&!s,p=r&&!f;if(!m&&i){a=p?a:new Fa(this);var v=n.apply(a,t);return v.__actions__.push({func:vm,args:[l],thisArg:e}),new Ma(v,s)}return h&&p?n.apply(this,t):(v=this.thru(l),h?c?v.value()[0]:v.value():v)})})),Rn(["pop","push","shift","sort","splice","unshift"],(function(e){var n=Ee[e],a=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",o=/^(?:pop|shift)$/.test(e);La.prototype[e]=function(){var e=arguments;if(o&&!this.__chain__){var c=this.value();return n.apply(Ym(c)?c:[],e)}return this[a]((function(a){return n.apply(Ym(a)?a:[],e)}))}})),wo(Fa.prototype,(function(e,n){var a=La[n];if(a){var o=a.name+"";Be.call(Ea,o)||(Ea[o]=[]),Ea[o].push({name:n,func:a})}})),Ea[Wc(e,2).name]=[{name:"wrapper",func:e}],Fa.prototype.clone=function(){var e=new Fa(this.__wrapped__);return e.__actions__=zc(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=zc(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=zc(this.__views__),e},Fa.prototype.reverse=function(){if(this.__filtered__){var e=new Fa(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},Fa.prototype.value=function(){var e=this.__wrapped__.value(),n=this.__dir__,a=Ym(e),o=n<0,c=a?e.length:0,d=function(e,n,a){for(var o=-1,c=a.length;++o<c;){var d=a[o],m=d.size;switch(d.type){case"drop":e+=m;break;case"dropRight":n-=m;break;case"take":n=ya(n,e+m);break;case"takeRight":e=ga(e,n-m)}}return{start:e,end:n}}(0,c,this.__views__),m=d.start,t=d.end,r=t-m,u=o?t:m-1,i=this.__iteratees__,l=i.length,s=0,f=ya(r,this.__takeCount__);if(!a||!o&&c==r&&f==r)return pc(e,this.__actions__);var h=[];e:for(;r--&&s<f;){for(var p=-1,v=e[u+=n];++p<l;){var _=i[p],g=_.iteratee,y=_.type,b=g(v);if(2==y)v=b;else if(!b){if(1==y)continue e;break e}}h[s++]=v}return h},La.prototype.at=_m,La.prototype.chain=function(){return pm(this)},La.prototype.commit=function(){return new Ma(this.value(),this.__chain__)},La.prototype.next=function(){this.__values__===e&&(this.__values__=pt(this.value()));var n=this.__index__>=this.__values__.length;return{done:n,value:n?e:this.__values__[this.__index__++]}},La.prototype.plant=function(n){for(var a,o=this;o instanceof Pa;){var c=Pd(o);c.__index__=0,c.__values__=e,a?d.__wrapped__=c:a=c;var d=c;o=o.__wrapped__}return d.__wrapped__=n,a},La.prototype.reverse=function(){var n=this.__wrapped__;if(n instanceof Fa){var a=n;return this.__actions__.length&&(a=new Fa(this)),(a=a.reverse()).__actions__.push({func:vm,args:[om],thisArg:e}),new Ma(a,this.__chain__)}return this.thru(om)},La.prototype.toJSON=La.prototype.valueOf=La.prototype.value=function(){return pc(this.__wrapped__,this.__actions__)},La.prototype.first=La.prototype.head,Xe&&(La.prototype[Xe]=function(){return this}),La}();_n?((_n.exports=va)._=va,vn._=va):pn._=va}.call(K);const ce=G(e({__name:"avatar-upload",props:{modelValue:{type:String},fileKey:{type:String,default:"uri"},size:{type:[String,Number],default:120},round:{type:[Boolean,String,Number],default:!1},border:{type:Boolean,default:!0}},emits:["update:modelValue"],setup(e,{emit:A}){const j=e,C=n(),S=a((()=>{const e=o(j.size);return{width:e,height:e,borderRadius:oe.isBoolean(j.round)?j.round?"50%":"":o(j.round)}})),I=e=>{g({url:"/uni_modules/vk-uview-ui/components/u-avatar-cropper/u-avatar-cropper?destWidth=300&rectWidth=200&fileType=jpg"})};return c("uAvatarCropper",(e=>{(async e=>{y({title:"正在上传中..."});try{const n=await b(e,C.temToken);w(),console.log(n),A("update:modelValue",n[j.fileKey])}catch(n){w(),uni.$u.toast(n)}})(e)})),d((()=>{m("uAvatarCropper")})),(n,a)=>{const o=x,c=t(r("u-icon"),Y),d=k;return u(),i(d,{class:"avatar-upload p-0 m-0 rounded",style:h(p(S)),"hover-class":"none","open-type":"chooseAvatar",onClick:I,onChooseavatar:I},{default:l((()=>[e.modelValue?(u(),i(o,{key:0,class:"w-full h-full",mode:"heightFix",src:e.modelValue},null,8,["src"])):s(n.$slots,"default",{key:1},(()=>[f("div",{style:h(p(S)),class:"border border-dotted border-light flex w-full h-full flex-col items-center justify-center text-muted text-xs box-border rounded"},[v(c,{name:"plus",size:36}),_(" 添加图片 ")],4)]),!0)])),_:3},8,["style"])}}}),[["__scopeId","data-v-858fd1bb"]]),de=[{code:"110000",name:"北京市"},{code:"120000",name:"天津市"},{code:"130000",name:"河北省"},{code:"140000",name:"山西省"},{code:"150000",name:"内蒙古自治区"},{code:"210000",name:"辽宁省"},{code:"220000",name:"吉林省"},{code:"230000",name:"黑龙江省"},{code:"310000",name:"上海市"},{code:"320000",name:"江苏省"},{code:"330000",name:"浙江省"},{code:"340000",name:"安徽省"},{code:"350000",name:"福建省"},{code:"360000",name:"江西省"},{code:"370000",name:"山东省"},{code:"410000",name:"河南省"},{code:"420000",name:"湖北省"},{code:"430000",name:"湖南省"},{code:"440000",name:"广东省"},{code:"450000",name:"广西壮族自治区"},{code:"460000",name:"海南省"},{code:"500000",name:"重庆市"},{code:"510000",name:"四川省"},{code:"520000",name:"贵州省"},{code:"530000",name:"云南省"},{code:"540000",name:"西藏自治区"},{code:"610000",name:"陕西省"},{code:"620000",name:"甘肃省"},{code:"630000",name:"青海省"},{code:"640000",name:"宁夏回族自治区"},{code:"650000",name:"新疆维吾尔自治区"},{code:"710000",name:"台湾省"},{code:"810000",name:"香港特别行政区"},{code:"820000",name:"澳门特别行政区"}],me=[[{code:"110100",name:"北京市"}],[{code:"120100",name:"天津市"}],[{code:"130100",name:"石家庄市"},{code:"130200",name:"唐山市"},{code:"130300",name:"秦皇岛市"},{code:"130400",name:"邯郸市"},{code:"130500",name:"邢台市"},{code:"130600",name:"保定市"},{code:"130700",name:"张家口市"},{code:"130800",name:"承德市"},{code:"130900",name:"沧州市"},{code:"131000",name:"廊坊市"},{code:"131100",name:"衡水市"}],[{code:"140100",name:"太原市"},{code:"140200",name:"大同市"},{code:"140300",name:"阳泉市"},{code:"140400",name:"长治市"},{code:"140500",name:"晋城市"},{code:"140600",name:"朔州市"},{code:"140700",name:"晋中市"},{code:"140800",name:"运城市"},{code:"140900",name:"忻州市"},{code:"141000",name:"临汾市"},{code:"141100",name:"吕梁市"}],[{code:"150100",name:"呼和浩特市"},{code:"150200",name:"包头市"},{code:"150300",name:"乌海市"},{code:"150400",name:"赤峰市"},{code:"150500",name:"通辽市"},{code:"150600",name:"鄂尔多斯市"},{code:"150700",name:"呼伦贝尔市"},{code:"150800",name:"巴彦淖尔市"},{code:"150900",name:"乌兰察布市"},{code:"152200",name:"兴安盟"},{code:"152500",name:"锡林郭勒盟"},{code:"152900",name:"阿拉善盟"}],[{code:"210100",name:"沈阳市"},{code:"210200",name:"大连市"},{code:"210300",name:"鞍山市"},{code:"210400",name:"抚顺市"},{code:"210500",name:"本溪市"},{code:"210600",name:"丹东市"},{code:"210700",name:"锦州市"},{code:"210800",name:"营口市"},{code:"210900",name:"阜新市"},{code:"211000",name:"辽阳市"},{code:"211100",name:"盘锦市"},{code:"211200",name:"铁岭市"},{code:"211300",name:"朝阳市"},{code:"211400",name:"葫芦岛市"}],[{code:"220100",name:"长春市"},{code:"220200",name:"吉林市"},{code:"220300",name:"四平市"},{code:"220400",name:"辽源市"},{code:"220500",name:"通化市"},{code:"220600",name:"白山市"},{code:"220700",name:"松原市"},{code:"220800",name:"白城市"},{code:"222400",name:"延边朝鲜族自治州"}],[{code:"230100",name:"哈尔滨市"},{code:"230200",name:"齐齐哈尔市"},{code:"230300",name:"鸡西市"},{code:"230400",name:"鹤岗市"},{code:"230500",name:"双鸭山市"},{code:"230600",name:"大庆市"},{code:"230700",name:"伊春市"},{code:"230800",name:"佳木斯市"},{code:"230900",name:"七台河市"},{code:"231000",name:"牡丹江市"},{code:"231100",name:"黑河市"},{code:"231200",name:"绥化市"},{code:"232700",name:"大兴安岭地区"}],[{code:"310100",name:"上海市"}],[{code:"320100",name:"南京市"},{code:"320200",name:"无锡市"},{code:"320300",name:"徐州市"},{code:"320400",name:"常州市"},{code:"320500",name:"苏州市"},{code:"320600",name:"南通市"},{code:"320700",name:"连云港市"},{code:"320800",name:"淮安市"},{code:"320900",name:"盐城市"},{code:"321000",name:"扬州市"},{code:"321100",name:"镇江市"},{code:"321200",name:"泰州市"},{code:"321300",name:"宿迁市"}],[{code:"330100",name:"杭州市"},{code:"330200",name:"宁波市"},{code:"330300",name:"温州市"},{code:"330400",name:"嘉兴市"},{code:"330500",name:"湖州市"},{code:"330600",name:"绍兴市"},{code:"330700",name:"金华市"},{code:"330800",name:"衢州市"},{code:"330900",name:"舟山市"},{code:"331000",name:"台州市"},{code:"331100",name:"丽水市"}],[{code:"340100",name:"合肥市"},{code:"340200",name:"芜湖市"},{code:"340300",name:"蚌埠市"},{code:"340400",name:"淮南市"},{code:"340500",name:"马鞍山市"},{code:"340600",name:"淮北市"},{code:"340700",name:"铜陵市"},{code:"340800",name:"安庆市"},{code:"341000",name:"黄山市"},{code:"341100",name:"滁州市"},{code:"341200",name:"阜阳市"},{code:"341300",name:"宿州市"},{code:"341500",name:"六安市"},{code:"341600",name:"亳州市"},{code:"341700",name:"池州市"},{code:"341800",name:"宣城市"}],[{code:"350100",name:"福州市"},{code:"350200",name:"厦门市"},{code:"350300",name:"莆田市"},{code:"350400",name:"三明市"},{code:"350500",name:"泉州市"},{code:"350600",name:"漳州市"},{code:"350700",name:"南平市"},{code:"350800",name:"龙岩市"},{code:"350900",name:"宁德市"}],[{code:"360100",name:"南昌市"},{code:"360200",name:"景德镇市"},{code:"360300",name:"萍乡市"},{code:"360400",name:"九江市"},{code:"360500",name:"新余市"},{code:"360600",name:"鹰潭市"},{code:"360700",name:"赣州市"},{code:"360800",name:"吉安市"},{code:"360900",name:"宜春市"},{code:"361000",name:"抚州市"},{code:"361100",name:"上饶市"}],[{code:"370100",name:"济南市"},{code:"370200",name:"青岛市"},{code:"370300",name:"淄博市"},{code:"370400",name:"枣庄市"},{code:"370500",name:"东营市"},{code:"370600",name:"烟台市"},{code:"370700",name:"潍坊市"},{code:"370800",name:"济宁市"},{code:"370900",name:"泰安市"},{code:"371000",name:"威海市"},{code:"371100",name:"日照市"},{code:"371200",name:"莱芜市"},{code:"371300",name:"临沂市"},{code:"371400",name:"德州市"},{code:"371500",name:"聊城市"},{code:"371600",name:"滨州市"},{code:"371700",name:"菏泽市"}],[{code:"410100",name:"郑州市"},{code:"410200",name:"开封市"},{code:"410300",name:"洛阳市"},{code:"410400",name:"平顶山市"},{code:"410500",name:"安阳市"},{code:"410600",name:"鹤壁市"},{code:"410700",name:"新乡市"},{code:"410800",name:"焦作市"},{code:"410900",name:"濮阳市"},{code:"411000",name:"许昌市"},{code:"411100",name:"漯河市"},{code:"411200",name:"三门峡市"},{code:"411300",name:"南阳市"},{code:"411400",name:"商丘市"},{code:"411500",name:"信阳市"},{code:"411600",name:"周口市"},{code:"411700",name:"驻马店市"},{code:"419000",name:"省直辖县级行政区划"}],[{code:"420100",name:"武汉市"},{code:"420200",name:"黄石市"},{code:"420300",name:"十堰市"},{code:"420500",name:"宜昌市"},{code:"420600",name:"襄阳市"},{code:"420700",name:"鄂州市"},{code:"420800",name:"荆门市"},{code:"420900",name:"孝感市"},{code:"421000",name:"荆州市"},{code:"421100",name:"黄冈市"},{code:"421200",name:"咸宁市"},{code:"421300",name:"随州市"},{code:"422800",name:"恩施土家族苗族自治州"},{code:"429000",name:"省直辖县级行政区划"}],[{code:"430100",name:"长沙市"},{code:"430200",name:"株洲市"},{code:"430300",name:"湘潭市"},{code:"430400",name:"衡阳市"},{code:"430500",name:"邵阳市"},{code:"430600",name:"岳阳市"},{code:"430700",name:"常德市"},{code:"430800",name:"张家界市"},{code:"430900",name:"益阳市"},{code:"431000",name:"郴州市"},{code:"431100",name:"永州市"},{code:"431200",name:"怀化市"},{code:"431300",name:"娄底市"},{code:"433100",name:"湘西土家族苗族自治州"}],[{code:"440100",name:"广州市"},{code:"440200",name:"韶关市"},{code:"440300",name:"深圳市"},{code:"440400",name:"珠海市"},{code:"440500",name:"汕头市"},{code:"440600",name:"佛山市"},{code:"440700",name:"江门市"},{code:"440800",name:"湛江市"},{code:"440900",name:"茂名市"},{code:"441200",name:"肇庆市"},{code:"441300",name:"惠州市"},{code:"441400",name:"梅州市"},{code:"441500",name:"汕尾市"},{code:"441600",name:"河源市"},{code:"441700",name:"阳江市"},{code:"441800",name:"清远市"},{code:"441900",name:"东莞市"},{code:"442000",name:"中山市"},{code:"445100",name:"潮州市"},{code:"445200",name:"揭阳市"},{code:"445300",name:"云浮市"}],[{code:"450100",name:"南宁市"},{code:"450200",name:"柳州市"},{code:"450300",name:"桂林市"},{code:"450400",name:"梧州市"},{code:"450500",name:"北海市"},{code:"450600",name:"防城港市"},{code:"450700",name:"钦州市"},{code:"450800",name:"贵港市"},{code:"450900",name:"玉林市"},{code:"451000",name:"百色市"},{code:"451100",name:"贺州市"},{code:"451200",name:"河池市"},{code:"451300",name:"来宾市"},{code:"451400",name:"崇左市"}],[{code:"460100",name:"海口市"},{code:"460200",name:"三亚市"},{code:"460300",name:"三沙市"},{code:"460400",name:"儋州市"},{code:"469000",name:"省直辖县级行政区划"}],[{code:"500100",name:"重庆市"},{code:"500200",name:"县"}],[{code:"510100",name:"成都市"},{code:"510300",name:"自贡市"},{code:"510400",name:"攀枝花市"},{code:"510500",name:"泸州市"},{code:"510600",name:"德阳市"},{code:"510700",name:"绵阳市"},{code:"510800",name:"广元市"},{code:"510900",name:"遂宁市"},{code:"511000",name:"内江市"},{code:"511100",name:"乐山市"},{code:"511300",name:"南充市"},{code:"511400",name:"眉山市"},{code:"511500",name:"宜宾市"},{code:"511600",name:"广安市"},{code:"511700",name:"达州市"},{code:"511800",name:"雅安市"},{code:"511900",name:"巴中市"},{code:"512000",name:"资阳市"},{code:"513200",name:"阿坝藏族羌族自治州"},{code:"513300",name:"甘孜藏族自治州"},{code:"513400",name:"凉山彝族自治州"}],[{code:"520100",name:"贵阳市"},{code:"520200",name:"六盘水市"},{code:"520300",name:"遵义市"},{code:"520400",name:"安顺市"},{code:"520500",name:"毕节市"},{code:"520600",name:"铜仁市"},{code:"522300",name:"黔西南布依族苗族自治州"},{code:"522600",name:"黔东南苗族侗族自治州"},{code:"522700",name:"黔南布依族苗族自治州"}],[{code:"530100",name:"昆明市"},{code:"530300",name:"曲靖市"},{code:"530400",name:"玉溪市"},{code:"530500",name:"保山市"},{code:"530600",name:"昭通市"},{code:"530700",name:"丽江市"},{code:"530800",name:"普洱市"},{code:"530900",name:"临沧市"},{code:"532300",name:"楚雄彝族自治州"},{code:"532500",name:"红河哈尼族彝族自治州"},{code:"532600",name:"文山壮族苗族自治州"},{code:"532800",name:"西双版纳傣族自治州"},{code:"532900",name:"大理白族自治州"},{code:"533100",name:"德宏傣族景颇族自治州"},{code:"533300",name:"怒江傈僳族自治州"},{code:"533400",name:"迪庆藏族自治州"}],[{code:"540100",name:"拉萨市"},{code:"540200",name:"日喀则市"},{code:"540300",name:"昌都市"},{code:"540400",name:"林芝市"},{code:"540500",name:"山南市"},{code:"542400",name:"那曲地区"},{code:"542500",name:"阿里地区"}],[{code:"610100",name:"西安市"},{code:"610200",name:"铜川市"},{code:"610300",name:"宝鸡市"},{code:"610400",name:"咸阳市"},{code:"610500",name:"渭南市"},{code:"610600",name:"延安市"},{code:"610700",name:"汉中市"},{code:"610800",name:"榆林市"},{code:"610900",name:"安康市"},{code:"611000",name:"商洛市"}],[{code:"620100",name:"兰州市"},{code:"620200",name:"嘉峪关市"},{code:"620300",name:"金昌市"},{code:"620400",name:"白银市"},{code:"620500",name:"天水市"},{code:"620600",name:"武威市"},{code:"620700",name:"张掖市"},{code:"620800",name:"平凉市"},{code:"620900",name:"酒泉市"},{code:"621000",name:"庆阳市"},{code:"621100",name:"定西市"},{code:"621200",name:"陇南市"},{code:"622900",name:"临夏回族自治州"},{code:"623000",name:"甘南藏族自治州"}],[{code:"630100",name:"西宁市"},{code:"630200",name:"海东市"},{code:"632200",name:"海北藏族自治州"},{code:"632300",name:"黄南藏族自治州"},{code:"632500",name:"海南藏族自治州"},{code:"632600",name:"果洛藏族自治州"},{code:"632700",name:"玉树藏族自治州"},{code:"632800",name:"海西蒙古族藏族自治州"}],[{code:"640100",name:"银川市"},{code:"640200",name:"石嘴山市"},{code:"640300",name:"吴忠市"},{code:"640400",name:"固原市"},{code:"640500",name:"中卫市"}],[{code:"650100",name:"乌鲁木齐市"},{code:"650200",name:"克拉玛依市"},{code:"650400",name:"吐鲁番市"},{code:"650500",name:"哈密市"},{code:"652300",name:"昌吉回族自治州"},{code:"652700",name:"博尔塔拉蒙古自治州"},{code:"652800",name:"巴音郭楞蒙古自治州"},{code:"652900",name:"阿克苏地区"},{code:"653000",name:"克孜勒苏柯尔克孜自治州"},{code:"653100",name:"喀什地区"},{code:"653200",name:"和田地区"},{code:"654000",name:"伊犁哈萨克自治州"},{code:"654200",name:"塔城地区"},{code:"654300",name:"阿勒泰地区"},{code:"659000",name:"自治区直辖县级行政区划"}],[{code:"710100",name:"台北市"},{code:"710200",name:"高雄市"},{code:"710300",name:"台南市"},{code:"710400",name:"台中市"},{code:"710600",name:"南投县"},{code:"710700",name:"基隆市"},{code:"710800",name:"新竹市"},{code:"710900",name:"嘉义市"},{code:"711100",name:"新北市"},{code:"711200",name:"宜兰县"},{code:"711300",name:"新竹县"},{code:"711400",name:"桃园市"},{code:"711500",name:"苗栗县"},{code:"711700",name:"彰化县"},{code:"711900",name:"嘉义县"},{code:"712100",name:"云林县"},{code:"712400",name:"屏东县"},{code:"712500",name:"台东县"},{code:"712600",name:"花莲县"},{code:"712700",name:"澎湖县"}],[{code:"810100",name:"香港特别行政区"}],[{code:"820100",name:"澳门特别行政区"}]],te=[[[{code:"110101",name:"东城区"},{code:"110102",name:"西城区"},{code:"110105",name:"朝阳区"},{code:"110106",name:"丰台区"},{code:"110107",name:"石景山区"},{code:"110108",name:"海淀区"},{code:"110109",name:"门头沟区"},{code:"110111",name:"房山区"},{code:"110112",name:"通州区"},{code:"110113",name:"顺义区"},{code:"110114",name:"昌平区"},{code:"110115",name:"大兴区"},{code:"110116",name:"怀柔区"},{code:"110117",name:"平谷区"},{code:"110118",name:"密云区"},{code:"110119",name:"延庆区"}]],[[{code:"120101",name:"和平区"},{code:"120102",name:"河东区"},{code:"120103",name:"河西区"},{code:"120104",name:"南开区"},{code:"120105",name:"河北区"},{code:"120106",name:"红桥区"},{code:"120110",name:"东丽区"},{code:"120111",name:"西青区"},{code:"120112",name:"津南区"},{code:"120113",name:"北辰区"},{code:"120114",name:"武清区"},{code:"120115",name:"宝坻区"},{code:"120116",name:"滨海新区"},{code:"120117",name:"宁河区"},{code:"120118",name:"静海区"},{code:"120119",name:"蓟州区"}]],[[{code:"130102",name:"长安区"},{code:"130104",name:"桥西区"},{code:"130105",name:"新华区"},{code:"130107",name:"井陉矿区"},{code:"130108",name:"裕华区"},{code:"130109",name:"藁城区"},{code:"130110",name:"鹿泉区"},{code:"130111",name:"栾城区"},{code:"130121",name:"井陉县"},{code:"130123",name:"正定县"},{code:"130125",name:"行唐县"},{code:"130126",name:"灵寿县"},{code:"130127",name:"高邑县"},{code:"130128",name:"深泽县"},{code:"130129",name:"赞皇县"},{code:"130130",name:"无极县"},{code:"130131",name:"平山县"},{code:"130132",name:"元氏县"},{code:"130133",name:"赵县"},{code:"130181",name:"辛集市"},{code:"130183",name:"晋州市"},{code:"130184",name:"新乐市"}],[{code:"130202",name:"路南区"},{code:"130203",name:"路北区"},{code:"130204",name:"古冶区"},{code:"130205",name:"开平区"},{code:"130207",name:"丰南区"},{code:"130208",name:"丰润区"},{code:"130209",name:"曹妃甸区"},{code:"130223",name:"滦县"},{code:"130224",name:"滦南县"},{code:"130225",name:"乐亭县"},{code:"130227",name:"迁西县"},{code:"130229",name:"玉田县"},{code:"130281",name:"遵化市"},{code:"130283",name:"迁安市"}],[{code:"130302",name:"海港区"},{code:"130303",name:"山海关区"},{code:"130304",name:"北戴河区"},{code:"130306",name:"抚宁区"},{code:"130321",name:"青龙满族自治县"},{code:"130322",name:"昌黎县"},{code:"130324",name:"卢龙县"}],[{code:"130402",name:"邯山区"},{code:"130403",name:"丛台区"},{code:"130404",name:"复兴区"},{code:"130406",name:"峰峰矿区"},{code:"130407",name:"肥乡区"},{code:"130408",name:"永年区"},{code:"130423",name:"临漳县"},{code:"130424",name:"成安县"},{code:"130425",name:"大名县"},{code:"130426",name:"涉县"},{code:"130427",name:"磁县"},{code:"130430",name:"邱县"},{code:"130431",name:"鸡泽县"},{code:"130432",name:"广平县"},{code:"130433",name:"馆陶县"},{code:"130434",name:"魏县"},{code:"130435",name:"曲周县"},{code:"130481",name:"武安市"}],[{code:"130502",name:"桥东区"},{code:"130503",name:"桥西区"},{code:"130521",name:"邢台县"},{code:"130522",name:"临城县"},{code:"130523",name:"内丘县"},{code:"130524",name:"柏乡县"},{code:"130525",name:"隆尧县"},{code:"130526",name:"任县"},{code:"130527",name:"南和县"},{code:"130528",name:"宁晋县"},{code:"130529",name:"巨鹿县"},{code:"130530",name:"新河县"},{code:"130531",name:"广宗县"},{code:"130532",name:"平乡县"},{code:"130533",name:"威县"},{code:"130534",name:"清河县"},{code:"130535",name:"临西县"},{code:"130581",name:"南宫市"},{code:"130582",name:"沙河市"}],[{code:"130602",name:"竞秀区"},{code:"130606",name:"莲池区"},{code:"130607",name:"满城区"},{code:"130608",name:"清苑区"},{code:"130609",name:"徐水区"},{code:"130623",name:"涞水县"},{code:"130624",name:"阜平县"},{code:"130626",name:"定兴县"},{code:"130627",name:"唐县"},{code:"130628",name:"高阳县"},{code:"130629",name:"容城县"},{code:"130630",name:"涞源县"},{code:"130631",name:"望都县"},{code:"130632",name:"安新县"},{code:"130633",name:"易县"},{code:"130634",name:"曲阳县"},{code:"130635",name:"蠡县"},{code:"130636",name:"顺平县"},{code:"130637",name:"博野县"},{code:"130638",name:"雄县"},{code:"130681",name:"涿州市"},{code:"130682",name:"定州市"},{code:"130683",name:"安国市"},{code:"130684",name:"高碑店市"}],[{code:"130702",name:"桥东区"},{code:"130703",name:"桥西区"},{code:"130705",name:"宣化区"},{code:"130706",name:"下花园区"},{code:"130708",name:"万全区"},{code:"130709",name:"崇礼区"},{code:"130722",name:"张北县"},{code:"130723",name:"康保县"},{code:"130724",name:"沽源县"},{code:"130725",name:"尚义县"},{code:"130726",name:"蔚县"},{code:"130727",name:"阳原县"},{code:"130728",name:"怀安县"},{code:"130730",name:"怀来县"},{code:"130731",name:"涿鹿县"},{code:"130732",name:"赤城县"}],[{code:"130802",name:"双桥区"},{code:"130803",name:"双滦区"},{code:"130804",name:"鹰手营子矿区"},{code:"130821",name:"承德县"},{code:"130822",name:"兴隆县"},{code:"130824",name:"滦平县"},{code:"130825",name:"隆化县"},{code:"130826",name:"丰宁满族自治县"},{code:"130827",name:"宽城满族自治县"},{code:"130828",name:"围场满族蒙古族自治县"},{code:"130881",name:"平泉市"}],[{code:"130902",name:"新华区"},{code:"130903",name:"运河区"},{code:"130921",name:"沧县"},{code:"130922",name:"青县"},{code:"130923",name:"东光县"},{code:"130924",name:"海兴县"},{code:"130925",name:"盐山县"},{code:"130926",name:"肃宁县"},{code:"130927",name:"南皮县"},{code:"130928",name:"吴桥县"},{code:"130929",name:"献县"},{code:"130930",name:"孟村回族自治县"},{code:"130981",name:"泊头市"},{code:"130982",name:"任丘市"},{code:"130983",name:"黄骅市"},{code:"130984",name:"河间市"}],[{code:"131002",name:"安次区"},{code:"131003",name:"广阳区"},{code:"131022",name:"固安县"},{code:"131023",name:"永清县"},{code:"131024",name:"香河县"},{code:"131025",name:"大城县"},{code:"131026",name:"文安县"},{code:"131028",name:"大厂回族自治县"},{code:"131081",name:"霸州市"},{code:"131082",name:"三河市"}],[{code:"131102",name:"桃城区"},{code:"131103",name:"冀州区"},{code:"131121",name:"枣强县"},{code:"131122",name:"武邑县"},{code:"131123",name:"武强县"},{code:"131124",name:"饶阳县"},{code:"131125",name:"安平县"},{code:"131126",name:"故城县"},{code:"131127",name:"景县"},{code:"131128",name:"阜城县"},{code:"131182",name:"深州市"}]],[[{code:"140105",name:"小店区"},{code:"140106",name:"迎泽区"},{code:"140107",name:"杏花岭区"},{code:"140108",name:"尖草坪区"},{code:"140109",name:"万柏林区"},{code:"140110",name:"晋源区"},{code:"140121",name:"清徐县"},{code:"140122",name:"阳曲县"},{code:"140123",name:"娄烦县"},{code:"140181",name:"古交市"}],[{code:"140202",name:"城区"},{code:"140203",name:"矿区"},{code:"140211",name:"南郊区"},{code:"140212",name:"新荣区"},{code:"140221",name:"阳高县"},{code:"140222",name:"天镇县"},{code:"140223",name:"广灵县"},{code:"140224",name:"灵丘县"},{code:"140225",name:"浑源县"},{code:"140226",name:"左云县"},{code:"140227",name:"大同县"}],[{code:"140302",name:"城区"},{code:"140303",name:"矿区"},{code:"140311",name:"郊区"},{code:"140321",name:"平定县"},{code:"140322",name:"盂县"}],[{code:"140402",name:"城区"},{code:"140411",name:"郊区"},{code:"140421",name:"长治县"},{code:"140423",name:"襄垣县"},{code:"140424",name:"屯留县"},{code:"140425",name:"平顺县"},{code:"140426",name:"黎城县"},{code:"140427",name:"壶关县"},{code:"140428",name:"长子县"},{code:"140429",name:"武乡县"},{code:"140430",name:"沁县"},{code:"140431",name:"沁源县"},{code:"140481",name:"潞城市"}],[{code:"140502",name:"城区"},{code:"140521",name:"沁水县"},{code:"140522",name:"阳城县"},{code:"140524",name:"陵川县"},{code:"140525",name:"泽州县"},{code:"140581",name:"高平市"}],[{code:"140602",name:"朔城区"},{code:"140603",name:"平鲁区"},{code:"140621",name:"山阴县"},{code:"140622",name:"应县"},{code:"140623",name:"右玉县"},{code:"140624",name:"怀仁县"}],[{code:"140702",name:"榆次区"},{code:"140721",name:"榆社县"},{code:"140722",name:"左权县"},{code:"140723",name:"和顺县"},{code:"140724",name:"昔阳县"},{code:"140725",name:"寿阳县"},{code:"140726",name:"太谷县"},{code:"140727",name:"祁县"},{code:"140728",name:"平遥县"},{code:"140729",name:"灵石县"},{code:"140781",name:"介休市"}],[{code:"140802",name:"盐湖区"},{code:"140821",name:"临猗县"},{code:"140822",name:"万荣县"},{code:"140823",name:"闻喜县"},{code:"140824",name:"稷山县"},{code:"140825",name:"新绛县"},{code:"140826",name:"绛县"},{code:"140827",name:"垣曲县"},{code:"140828",name:"夏县"},{code:"140829",name:"平陆县"},{code:"140830",name:"芮城县"},{code:"140881",name:"永济市"},{code:"140882",name:"河津市"}],[{code:"140902",name:"忻府区"},{code:"140921",name:"定襄县"},{code:"140922",name:"五台县"},{code:"140923",name:"代县"},{code:"140924",name:"繁峙县"},{code:"140925",name:"宁武县"},{code:"140926",name:"静乐县"},{code:"140927",name:"神池县"},{code:"140928",name:"五寨县"},{code:"140929",name:"岢岚县"},{code:"140930",name:"河曲县"},{code:"140931",name:"保德县"},{code:"140932",name:"偏关县"},{code:"140981",name:"原平市"}],[{code:"141002",name:"尧都区"},{code:"141021",name:"曲沃县"},{code:"141022",name:"翼城县"},{code:"141023",name:"襄汾县"},{code:"141024",name:"洪洞县"},{code:"141025",name:"古县"},{code:"141026",name:"安泽县"},{code:"141027",name:"浮山县"},{code:"141028",name:"吉县"},{code:"141029",name:"乡宁县"},{code:"141030",name:"大宁县"},{code:"141031",name:"隰县"},{code:"141032",name:"永和县"},{code:"141033",name:"蒲县"},{code:"141034",name:"汾西县"},{code:"141081",name:"侯马市"},{code:"141082",name:"霍州市"}],[{code:"141102",name:"离石区"},{code:"141121",name:"文水县"},{code:"141122",name:"交城县"},{code:"141123",name:"兴县"},{code:"141124",name:"临县"},{code:"141125",name:"柳林县"},{code:"141126",name:"石楼县"},{code:"141127",name:"岚县"},{code:"141128",name:"方山县"},{code:"141129",name:"中阳县"},{code:"141130",name:"交口县"},{code:"141181",name:"孝义市"},{code:"141182",name:"汾阳市"}]],[[{code:"150102",name:"新城区"},{code:"150103",name:"回民区"},{code:"150104",name:"玉泉区"},{code:"150105",name:"赛罕区"},{code:"150121",name:"土默特左旗"},{code:"150122",name:"托克托县"},{code:"150123",name:"和林格尔县"},{code:"150124",name:"清水河县"},{code:"150125",name:"武川县"}],[{code:"150202",name:"东河区"},{code:"150203",name:"昆都仑区"},{code:"150204",name:"青山区"},{code:"150205",name:"石拐区"},{code:"150206",name:"白云鄂博矿区"},{code:"150207",name:"九原区"},{code:"150221",name:"土默特右旗"},{code:"150222",name:"固阳县"},{code:"150223",name:"达尔罕茂明安联合旗"}],[{code:"150302",name:"海勃湾区"},{code:"150303",name:"海南区"},{code:"150304",name:"乌达区"}],[{code:"150402",name:"红山区"},{code:"150403",name:"元宝山区"},{code:"150404",name:"松山区"},{code:"150421",name:"阿鲁科尔沁旗"},{code:"150422",name:"巴林左旗"},{code:"150423",name:"巴林右旗"},{code:"150424",name:"林西县"},{code:"150425",name:"克什克腾旗"},{code:"150426",name:"翁牛特旗"},{code:"150428",name:"喀喇沁旗"},{code:"150429",name:"宁城县"},{code:"150430",name:"敖汉旗"}],[{code:"150502",name:"科尔沁区"},{code:"150521",name:"科尔沁左翼中旗"},{code:"150522",name:"科尔沁左翼后旗"},{code:"150523",name:"开鲁县"},{code:"150524",name:"库伦旗"},{code:"150525",name:"奈曼旗"},{code:"150526",name:"扎鲁特旗"},{code:"150581",name:"霍林郭勒市"}],[{code:"150602",name:"东胜区"},{code:"150603",name:"康巴什区"},{code:"150621",name:"达拉特旗"},{code:"150622",name:"准格尔旗"},{code:"150623",name:"鄂托克前旗"},{code:"150624",name:"鄂托克旗"},{code:"150625",name:"杭锦旗"},{code:"150626",name:"乌审旗"},{code:"150627",name:"伊金霍洛旗"}],[{code:"150702",name:"海拉尔区"},{code:"150703",name:"扎赉诺尔区"},{code:"150721",name:"阿荣旗"},{code:"150722",name:"莫力达瓦达斡尔族自治旗"},{code:"150723",name:"鄂伦春自治旗"},{code:"150724",name:"鄂温克族自治旗"},{code:"150725",name:"陈巴尔虎旗"},{code:"150726",name:"新巴尔虎左旗"},{code:"150727",name:"新巴尔虎右旗"},{code:"150781",name:"满洲里市"},{code:"150782",name:"牙克石市"},{code:"150783",name:"扎兰屯市"},{code:"150784",name:"额尔古纳市"},{code:"150785",name:"根河市"}],[{code:"150802",name:"临河区"},{code:"150821",name:"五原县"},{code:"150822",name:"磴口县"},{code:"150823",name:"乌拉特前旗"},{code:"150824",name:"乌拉特中旗"},{code:"150825",name:"乌拉特后旗"},{code:"150826",name:"杭锦后旗"}],[{code:"150902",name:"集宁区"},{code:"150921",name:"卓资县"},{code:"150922",name:"化德县"},{code:"150923",name:"商都县"},{code:"150924",name:"兴和县"},{code:"150925",name:"凉城县"},{code:"150926",name:"察哈尔右翼前旗"},{code:"150927",name:"察哈尔右翼中旗"},{code:"150928",name:"察哈尔右翼后旗"},{code:"150929",name:"四子王旗"},{code:"150981",name:"丰镇市"}],[{code:"152201",name:"乌兰浩特市"},{code:"152202",name:"阿尔山市"},{code:"152221",name:"科尔沁右翼前旗"},{code:"152222",name:"科尔沁右翼中旗"},{code:"152223",name:"扎赉特旗"},{code:"152224",name:"突泉县"}],[{code:"152501",name:"二连浩特市"},{code:"152502",name:"锡林浩特市"},{code:"152522",name:"阿巴嘎旗"},{code:"152523",name:"苏尼特左旗"},{code:"152524",name:"苏尼特右旗"},{code:"152525",name:"东乌珠穆沁旗"},{code:"152526",name:"西乌珠穆沁旗"},{code:"152527",name:"太仆寺旗"},{code:"152528",name:"镶黄旗"},{code:"152529",name:"正镶白旗"},{code:"152530",name:"正蓝旗"},{code:"152531",name:"多伦县"}],[{code:"152921",name:"阿拉善左旗"},{code:"152922",name:"阿拉善右旗"},{code:"152923",name:"额济纳旗"}]],[[{code:"210102",name:"和平区"},{code:"210103",name:"沈河区"},{code:"210104",name:"大东区"},{code:"210105",name:"皇姑区"},{code:"210106",name:"铁西区"},{code:"210111",name:"苏家屯区"},{code:"210112",name:"浑南区"},{code:"210113",name:"沈北新区"},{code:"210114",name:"于洪区"},{code:"210115",name:"辽中区"},{code:"210123",name:"康平县"},{code:"210124",name:"法库县"},{code:"210181",name:"新民市"}],[{code:"210202",name:"中山区"},{code:"210203",name:"西岗区"},{code:"210204",name:"沙河口区"},{code:"210211",name:"甘井子区"},{code:"210212",name:"旅顺口区"},{code:"210213",name:"金州区"},{code:"210214",name:"普兰店区"},{code:"210224",name:"长海县"},{code:"210281",name:"瓦房店市"},{code:"210283",name:"庄河市"}],[{code:"210302",name:"铁东区"},{code:"210303",name:"铁西区"},{code:"210304",name:"立山区"},{code:"210311",name:"千山区"},{code:"210321",name:"台安县"},{code:"210323",name:"岫岩满族自治县"},{code:"210381",name:"海城市"}],[{code:"210402",name:"新抚区"},{code:"210403",name:"东洲区"},{code:"210404",name:"望花区"},{code:"210411",name:"顺城区"},{code:"210421",name:"抚顺县"},{code:"210422",name:"新宾满族自治县"},{code:"210423",name:"清原满族自治县"}],[{code:"210502",name:"平山区"},{code:"210503",name:"溪湖区"},{code:"210504",name:"明山区"},{code:"210505",name:"南芬区"},{code:"210521",name:"本溪满族自治县"},{code:"210522",name:"桓仁满族自治县"}],[{code:"210602",name:"元宝区"},{code:"210603",name:"振兴区"},{code:"210604",name:"振安区"},{code:"210624",name:"宽甸满族自治县"},{code:"210681",name:"东港市"},{code:"210682",name:"凤城市"}],[{code:"210702",name:"古塔区"},{code:"210703",name:"凌河区"},{code:"210711",name:"太和区"},{code:"210726",name:"黑山县"},{code:"210727",name:"义县"},{code:"210781",name:"凌海市"},{code:"210782",name:"北镇市"}],[{code:"210802",name:"站前区"},{code:"210803",name:"西市区"},{code:"210804",name:"鲅鱼圈区"},{code:"210811",name:"老边区"},{code:"210881",name:"盖州市"},{code:"210882",name:"大石桥市"}],[{code:"210902",name:"海州区"},{code:"210903",name:"新邱区"},{code:"210904",name:"太平区"},{code:"210905",name:"清河门区"},{code:"210911",name:"细河区"},{code:"210921",name:"阜新蒙古族自治县"},{code:"210922",name:"彰武县"}],[{code:"211002",name:"白塔区"},{code:"211003",name:"文圣区"},{code:"211004",name:"宏伟区"},{code:"211005",name:"弓长岭区"},{code:"211011",name:"太子河区"},{code:"211021",name:"辽阳县"},{code:"211081",name:"灯塔市"}],[{code:"211102",name:"双台子区"},{code:"211103",name:"兴隆台区"},{code:"211104",name:"大洼区"},{code:"211122",name:"盘山县"}],[{code:"211202",name:"银州区"},{code:"211204",name:"清河区"},{code:"211221",name:"铁岭县"},{code:"211223",name:"西丰县"},{code:"211224",name:"昌图县"},{code:"211281",name:"调兵山市"},{code:"211282",name:"开原市"}],[{code:"211302",name:"双塔区"},{code:"211303",name:"龙城区"},{code:"211321",name:"朝阳县"},{code:"211322",name:"建平县"},{code:"211324",name:"喀喇沁左翼蒙古族自治县"},{code:"211381",name:"北票市"},{code:"211382",name:"凌源市"}],[{code:"211402",name:"连山区"},{code:"211403",name:"龙港区"},{code:"211404",name:"南票区"},{code:"211421",name:"绥中县"},{code:"211422",name:"建昌县"},{code:"211481",name:"兴城市"}]],[[{code:"220102",name:"南关区"},{code:"220103",name:"宽城区"},{code:"220104",name:"朝阳区"},{code:"220105",name:"二道区"},{code:"220106",name:"绿园区"},{code:"220112",name:"双阳区"},{code:"220113",name:"九台区"},{code:"220122",name:"农安县"},{code:"220182",name:"榆树市"},{code:"220183",name:"德惠市"}],[{code:"220202",name:"昌邑区"},{code:"220203",name:"龙潭区"},{code:"220204",name:"船营区"},{code:"220211",name:"丰满区"},{code:"220221",name:"永吉县"},{code:"220281",name:"蛟河市"},{code:"220282",name:"桦甸市"},{code:"220283",name:"舒兰市"},{code:"220284",name:"磐石市"}],[{code:"220302",name:"铁西区"},{code:"220303",name:"铁东区"},{code:"220322",name:"梨树县"},{code:"220323",name:"伊通满族自治县"},{code:"220381",name:"公主岭市"},{code:"220382",name:"双辽市"}],[{code:"220402",name:"龙山区"},{code:"220403",name:"西安区"},{code:"220421",name:"东丰县"},{code:"220422",name:"东辽县"}],[{code:"220502",name:"东昌区"},{code:"220503",name:"二道江区"},{code:"220521",name:"通化县"},{code:"220523",name:"辉南县"},{code:"220524",name:"柳河县"},{code:"220581",name:"梅河口市"},{code:"220582",name:"集安市"}],[{code:"220602",name:"浑江区"},{code:"220605",name:"江源区"},{code:"220621",name:"抚松县"},{code:"220622",name:"靖宇县"},{code:"220623",name:"长白朝鲜族自治县"},{code:"220681",name:"临江市"}],[{code:"220702",name:"宁江区"},{code:"220721",name:"前郭尔罗斯蒙古族自治县"},{code:"220722",name:"长岭县"},{code:"220723",name:"乾安县"},{code:"220781",name:"扶余市"}],[{code:"220802",name:"洮北区"},{code:"220821",name:"镇赉县"},{code:"220822",name:"通榆县"},{code:"220881",name:"洮南市"},{code:"220882",name:"大安市"}],[{code:"222401",name:"延吉市"},{code:"222402",name:"图们市"},{code:"222403",name:"敦化市"},{code:"222404",name:"珲春市"},{code:"222405",name:"龙井市"},{code:"222406",name:"和龙市"},{code:"222424",name:"汪清县"},{code:"222426",name:"安图县"}]],[[{code:"230102",name:"道里区"},{code:"230103",name:"南岗区"},{code:"230104",name:"道外区"},{code:"230108",name:"平房区"},{code:"230109",name:"松北区"},{code:"230110",name:"香坊区"},{code:"230111",name:"呼兰区"},{code:"230112",name:"阿城区"},{code:"230113",name:"双城区"},{code:"230123",name:"依兰县"},{code:"230124",name:"方正县"},{code:"230125",name:"宾县"},{code:"230126",name:"巴彦县"},{code:"230127",name:"木兰县"},{code:"230128",name:"通河县"},{code:"230129",name:"延寿县"},{code:"230183",name:"尚志市"},{code:"230184",name:"五常市"}],[{code:"230202",name:"龙沙区"},{code:"230203",name:"建华区"},{code:"230204",name:"铁锋区"},{code:"230205",name:"昂昂溪区"},{code:"230206",name:"富拉尔基区"},{code:"230207",name:"碾子山区"},{code:"230208",name:"梅里斯达斡尔族区"},{code:"230221",name:"龙江县"},{code:"230223",name:"依安县"},{code:"230224",name:"泰来县"},{code:"230225",name:"甘南县"},{code:"230227",name:"富裕县"},{code:"230229",name:"克山县"},{code:"230230",name:"克东县"},{code:"230231",name:"拜泉县"},{code:"230281",name:"讷河市"}],[{code:"230302",name:"鸡冠区"},{code:"230303",name:"恒山区"},{code:"230304",name:"滴道区"},{code:"230305",name:"梨树区"},{code:"230306",name:"城子河区"},{code:"230307",name:"麻山区"},{code:"230321",name:"鸡东县"},{code:"230381",name:"虎林市"},{code:"230382",name:"密山市"}],[{code:"230402",name:"向阳区"},{code:"230403",name:"工农区"},{code:"230404",name:"南山区"},{code:"230405",name:"兴安区"},{code:"230406",name:"东山区"},{code:"230407",name:"兴山区"},{code:"230421",name:"萝北县"},{code:"230422",name:"绥滨县"}],[{code:"230502",name:"尖山区"},{code:"230503",name:"岭东区"},{code:"230505",name:"四方台区"},{code:"230506",name:"宝山区"},{code:"230521",name:"集贤县"},{code:"230522",name:"友谊县"},{code:"230523",name:"宝清县"},{code:"230524",name:"饶河县"}],[{code:"230602",name:"萨尔图区"},{code:"230603",name:"龙凤区"},{code:"230604",name:"让胡路区"},{code:"230605",name:"红岗区"},{code:"230606",name:"大同区"},{code:"230621",name:"肇州县"},{code:"230622",name:"肇源县"},{code:"230623",name:"林甸县"},{code:"230624",name:"杜尔伯特蒙古族自治县"}],[{code:"230702",name:"伊春区"},{code:"230703",name:"南岔区"},{code:"230704",name:"友好区"},{code:"230705",name:"西林区"},{code:"230706",name:"翠峦区"},{code:"230707",name:"新青区"},{code:"230708",name:"美溪区"},{code:"230709",name:"金山屯区"},{code:"230710",name:"五营区"},{code:"230711",name:"乌马河区"},{code:"230712",name:"汤旺河区"},{code:"230713",name:"带岭区"},{code:"230714",name:"乌伊岭区"},{code:"230715",name:"红星区"},{code:"230716",name:"上甘岭区"},{code:"230722",name:"嘉荫县"},{code:"230781",name:"铁力市"}],[{code:"230803",name:"向阳区"},{code:"230804",name:"前进区"},{code:"230805",name:"东风区"},{code:"230811",name:"郊区"},{code:"230822",name:"桦南县"},{code:"230826",name:"桦川县"},{code:"230828",name:"汤原县"},{code:"230881",name:"同江市"},{code:"230882",name:"富锦市"},{code:"230883",name:"抚远市"}],[{code:"230902",name:"新兴区"},{code:"230903",name:"桃山区"},{code:"230904",name:"茄子河区"},{code:"230921",name:"勃利县"}],[{code:"231002",name:"东安区"},{code:"231003",name:"阳明区"},{code:"231004",name:"爱民区"},{code:"231005",name:"西安区"},{code:"231025",name:"林口县"},{code:"231081",name:"绥芬河市"},{code:"231083",name:"海林市"},{code:"231084",name:"宁安市"},{code:"231085",name:"穆棱市"},{code:"231086",name:"东宁市"}],[{code:"231102",name:"爱辉区"},{code:"231121",name:"嫩江县"},{code:"231123",name:"逊克县"},{code:"231124",name:"孙吴县"},{code:"231181",name:"北安市"},{code:"231182",name:"五大连池市"}],[{code:"231202",name:"北林区"},{code:"231221",name:"望奎县"},{code:"231222",name:"兰西县"},{code:"231223",name:"青冈县"},{code:"231224",name:"庆安县"},{code:"231225",name:"明水县"},{code:"231226",name:"绥棱县"},{code:"231281",name:"安达市"},{code:"231282",name:"肇东市"},{code:"231283",name:"海伦市"}],[{code:"232701",name:"加格达奇区"},{code:"232702",name:"松岭区"},{code:"232703",name:"新林区"},{code:"232704",name:"呼中区"},{code:"232721",name:"呼玛县"},{code:"232722",name:"塔河县"},{code:"232723",name:"漠河县"}]],[[{code:"310101",name:"黄浦区"},{code:"310104",name:"徐汇区"},{code:"310105",name:"长宁区"},{code:"310106",name:"静安区"},{code:"310107",name:"普陀区"},{code:"310109",name:"虹口区"},{code:"310110",name:"杨浦区"},{code:"310112",name:"闵行区"},{code:"310113",name:"宝山区"},{code:"310114",name:"嘉定区"},{code:"310115",name:"浦东新区"},{code:"310116",name:"金山区"},{code:"310117",name:"松江区"},{code:"310118",name:"青浦区"},{code:"310120",name:"奉贤区"},{code:"310151",name:"崇明区"}]],[[{code:"320102",name:"玄武区"},{code:"320104",name:"秦淮区"},{code:"320105",name:"建邺区"},{code:"320106",name:"鼓楼区"},{code:"320111",name:"浦口区"},{code:"320113",name:"栖霞区"},{code:"320114",name:"雨花台区"},{code:"320115",name:"江宁区"},{code:"320116",name:"六合区"},{code:"320117",name:"溧水区"},{code:"320118",name:"高淳区"}],[{code:"320205",name:"锡山区"},{code:"320206",name:"惠山区"},{code:"320211",name:"滨湖区"},{code:"320213",name:"梁溪区"},{code:"320214",name:"新吴区"},{code:"320281",name:"江阴市"},{code:"320282",name:"宜兴市"}],[{code:"320302",name:"鼓楼区"},{code:"320303",name:"云龙区"},{code:"320305",name:"贾汪区"},{code:"320311",name:"泉山区"},{code:"320312",name:"铜山区"},{code:"320321",name:"丰县"},{code:"320322",name:"沛县"},{code:"320324",name:"睢宁县"},{code:"320381",name:"新沂市"},{code:"320382",name:"邳州市"}],[{code:"320402",name:"天宁区"},{code:"320404",name:"钟楼区"},{code:"320411",name:"新北区"},{code:"320412",name:"武进区"},{code:"320413",name:"金坛区"},{code:"320481",name:"溧阳市"}],[{code:"320505",name:"虎丘区"},{code:"320506",name:"吴中区"},{code:"320507",name:"相城区"},{code:"320508",name:"姑苏区"},{code:"320509",name:"吴江区"},{code:"320581",name:"常熟市"},{code:"320582",name:"张家港市"},{code:"320583",name:"昆山市"},{code:"320585",name:"太仓市"}],[{code:"320602",name:"崇川区"},{code:"320611",name:"港闸区"},{code:"320612",name:"通州区"},{code:"320621",name:"海安县"},{code:"320623",name:"如东县"},{code:"320681",name:"启东市"},{code:"320682",name:"如皋市"},{code:"320684",name:"海门市"}],[{code:"320703",name:"连云区"},{code:"320706",name:"海州区"},{code:"320707",name:"赣榆区"},{code:"320722",name:"东海县"},{code:"320723",name:"灌云县"},{code:"320724",name:"灌南县"}],[{code:"320803",name:"淮安区"},{code:"320804",name:"淮阴区"},{code:"320812",name:"清江浦区"},{code:"320813",name:"洪泽区"},{code:"320826",name:"涟水县"},{code:"320830",name:"盱眙县"},{code:"320831",name:"金湖县"}],[{code:"320902",name:"亭湖区"},{code:"320903",name:"盐都区"},{code:"320904",name:"大丰区"},{code:"320921",name:"响水县"},{code:"320922",name:"滨海县"},{code:"320923",name:"阜宁县"},{code:"320924",name:"射阳县"},{code:"320925",name:"建湖县"},{code:"320981",name:"东台市"}],[{code:"321002",name:"广陵区"},{code:"321003",name:"邗江区"},{code:"321012",name:"江都区"},{code:"321023",name:"宝应县"},{code:"321081",name:"仪征市"},{code:"321084",name:"高邮市"}],[{code:"321102",name:"京口区"},{code:"321111",name:"润州区"},{code:"321112",name:"丹徒区"},{code:"321181",name:"丹阳市"},{code:"321182",name:"扬中市"},{code:"321183",name:"句容市"}],[{code:"321202",name:"海陵区"},{code:"321203",name:"高港区"},{code:"321204",name:"姜堰区"},{code:"321281",name:"兴化市"},{code:"321282",name:"靖江市"},{code:"321283",name:"泰兴市"}],[{code:"321302",name:"宿城区"},{code:"321311",name:"宿豫区"},{code:"321322",name:"沭阳县"},{code:"321323",name:"泗阳县"},{code:"321324",name:"泗洪县"}]],[[{code:"330102",name:"上城区"},{code:"330105",name:"拱墅区"},{code:"330106",name:"西湖区"},{code:"330108",name:"滨江区"},{code:"330109",name:"萧山区"},{code:"330110",name:"余杭区"},{code:"330111",name:"富阳区"},{code:"330112",name:"临安区"},{code:"330113",name:"临平区"},{code:"330114",name:"钱塘区"},{code:"330122",name:"桐庐县"},{code:"330127",name:"淳安县"},{code:"330182",name:"建德市"}],[{code:"330203",name:"海曙区"},{code:"330205",name:"江北区"},{code:"330206",name:"北仑区"},{code:"330211",name:"镇海区"},{code:"330212",name:"鄞州区"},{code:"330213",name:"奉化区"},{code:"330225",name:"象山县"},{code:"330226",name:"宁海县"},{code:"330281",name:"余姚市"},{code:"330282",name:"慈溪市"}],[{code:"330302",name:"鹿城区"},{code:"330303",name:"龙湾区"},{code:"330304",name:"瓯海区"},{code:"330305",name:"洞头区"},{code:"330324",name:"永嘉县"},{code:"330326",name:"平阳县"},{code:"330327",name:"苍南县"},{code:"330328",name:"文成县"},{code:"330329",name:"泰顺县"},{code:"330381",name:"瑞安市"},{code:"330382",name:"乐清市"}],[{code:"330402",name:"南湖区"},{code:"330411",name:"秀洲区"},{code:"330421",name:"嘉善县"},{code:"330424",name:"海盐县"},{code:"330481",name:"海宁市"},{code:"330482",name:"平湖市"},{code:"330483",name:"桐乡市"}],[{code:"330502",name:"吴兴区"},{code:"330503",name:"南浔区"},{code:"330521",name:"德清县"},{code:"330522",name:"长兴县"},{code:"330523",name:"安吉县"}],[{code:"330602",name:"越城区"},{code:"330603",name:"柯桥区"},{code:"330604",name:"上虞区"},{code:"330624",name:"新昌县"},{code:"330681",name:"诸暨市"},{code:"330683",name:"嵊州市"}],[{code:"330702",name:"婺城区"},{code:"330703",name:"金东区"},{code:"330723",name:"武义县"},{code:"330726",name:"浦江县"},{code:"330727",name:"磐安县"},{code:"330781",name:"兰溪市"},{code:"330782",name:"义乌市"},{code:"330783",name:"东阳市"},{code:"330784",name:"永康市"}],[{code:"330802",name:"柯城区"},{code:"330803",name:"衢江区"},{code:"330822",name:"常山县"},{code:"330824",name:"开化县"},{code:"330825",name:"龙游县"},{code:"330881",name:"江山市"}],[{code:"330902",name:"定海区"},{code:"330903",name:"普陀区"},{code:"330921",name:"岱山县"},{code:"330922",name:"嵊泗县"}],[{code:"331002",name:"椒江区"},{code:"331003",name:"黄岩区"},{code:"331004",name:"路桥区"},{code:"331022",name:"三门县"},{code:"331023",name:"天台县"},{code:"331024",name:"仙居县"},{code:"331081",name:"温岭市"},{code:"331082",name:"临海市"},{code:"331083",name:"玉环市"}],[{code:"331102",name:"莲都区"},{code:"331121",name:"青田县"},{code:"331122",name:"缙云县"},{code:"331123",name:"遂昌县"},{code:"331124",name:"松阳县"},{code:"331125",name:"云和县"},{code:"331126",name:"庆元县"},{code:"331127",name:"景宁畲族自治县"},{code:"331181",name:"龙泉市"}]],[[{code:"340102",name:"瑶海区"},{code:"340103",name:"庐阳区"},{code:"340104",name:"蜀山区"},{code:"340111",name:"包河区"},{code:"340121",name:"长丰县"},{code:"340122",name:"肥东县"},{code:"340123",name:"肥西县"},{code:"340124",name:"庐江县"},{code:"340181",name:"巢湖市"}],[{code:"340202",name:"镜湖区"},{code:"340203",name:"弋江区"},{code:"340207",name:"鸠江区"},{code:"340208",name:"三山区"},{code:"340221",name:"芜湖县"},{code:"340222",name:"繁昌县"},{code:"340223",name:"南陵县"},{code:"340225",name:"无为县"}],[{code:"340302",name:"龙子湖区"},{code:"340303",name:"蚌山区"},{code:"340304",name:"禹会区"},{code:"340311",name:"淮上区"},{code:"340321",name:"怀远县"},{code:"340322",name:"五河县"},{code:"340323",name:"固镇县"}],[{code:"340402",name:"大通区"},{code:"340403",name:"田家庵区"},{code:"340404",name:"谢家集区"},{code:"340405",name:"八公山区"},{code:"340406",name:"潘集区"},{code:"340421",name:"凤台县"},{code:"340422",name:"寿县"}],[{code:"340503",name:"花山区"},{code:"340504",name:"雨山区"},{code:"340506",name:"博望区"},{code:"340521",name:"当涂县"},{code:"340522",name:"含山县"},{code:"340523",name:"和县"}],[{code:"340602",name:"杜集区"},{code:"340603",name:"相山区"},{code:"340604",name:"烈山区"},{code:"340621",name:"濉溪县"}],[{code:"340705",name:"铜官区"},{code:"340706",name:"义安区"},{code:"340711",name:"郊区"},{code:"340722",name:"枞阳县"}],[{code:"340802",name:"迎江区"},{code:"340803",name:"大观区"},{code:"340811",name:"宜秀区"},{code:"340822",name:"怀宁县"},{code:"340824",name:"潜山县"},{code:"340825",name:"太湖县"},{code:"340826",name:"宿松县"},{code:"340827",name:"望江县"},{code:"340828",name:"岳西县"},{code:"340881",name:"桐城市"}],[{code:"341002",name:"屯溪区"},{code:"341003",name:"黄山区"},{code:"341004",name:"徽州区"},{code:"341021",name:"歙县"},{code:"341022",name:"休宁县"},{code:"341023",name:"黟县"},{code:"341024",name:"祁门县"}],[{code:"341102",name:"琅琊区"},{code:"341103",name:"南谯区"},{code:"341122",name:"来安县"},{code:"341124",name:"全椒县"},{code:"341125",name:"定远县"},{code:"341126",name:"凤阳县"},{code:"341181",name:"天长市"},{code:"341182",name:"明光市"}],[{code:"341202",name:"颍州区"},{code:"341203",name:"颍东区"},{code:"341204",name:"颍泉区"},{code:"341221",name:"临泉县"},{code:"341222",name:"太和县"},{code:"341225",name:"阜南县"},{code:"341226",name:"颍上县"},{code:"341282",name:"界首市"}],[{code:"341302",name:"埇桥区"},{code:"341321",name:"砀山县"},{code:"341322",name:"萧县"},{code:"341323",name:"灵璧县"},{code:"341324",name:"泗县"}],[{code:"341502",name:"金安区"},{code:"341503",name:"裕安区"},{code:"341504",name:"叶集区"},{code:"341522",name:"霍邱县"},{code:"341523",name:"舒城县"},{code:"341524",name:"金寨县"},{code:"341525",name:"霍山县"}],[{code:"341602",name:"谯城区"},{code:"341621",name:"涡阳县"},{code:"341622",name:"蒙城县"},{code:"341623",name:"利辛县"}],[{code:"341702",name:"贵池区"},{code:"341721",name:"东至县"},{code:"341722",name:"石台县"},{code:"341723",name:"青阳县"}],[{code:"341802",name:"宣州区"},{code:"341821",name:"郎溪县"},{code:"341822",name:"广德县"},{code:"341823",name:"泾县"},{code:"341824",name:"绩溪县"},{code:"341825",name:"旌德县"},{code:"341881",name:"宁国市"}]],[[{code:"350102",name:"鼓楼区"},{code:"350103",name:"台江区"},{code:"350104",name:"仓山区"},{code:"350105",name:"马尾区"},{code:"350111",name:"晋安区"},{code:"350112",name:"长乐区"},{code:"350121",name:"闽侯县"},{code:"350122",name:"连江县"},{code:"350123",name:"罗源县"},{code:"350124",name:"闽清县"},{code:"350125",name:"永泰县"},{code:"350128",name:"平潭县"},{code:"350181",name:"福清市"}],[{code:"350203",name:"思明区"},{code:"350205",name:"海沧区"},{code:"350206",name:"湖里区"},{code:"350211",name:"集美区"},{code:"350212",name:"同安区"},{code:"350213",name:"翔安区"}],[{code:"350302",name:"城厢区"},{code:"350303",name:"涵江区"},{code:"350304",name:"荔城区"},{code:"350305",name:"秀屿区"},{code:"350322",name:"仙游县"}],[{code:"350404",name:"三元区"},{code:"350405",name:"沙县区"},{code:"350421",name:"明溪县"},{code:"350423",name:"清流县"},{code:"350424",name:"宁化县"},{code:"350425",name:"大田县"},{code:"350426",name:"尤溪县"},{code:"350428",name:"将乐县"},{code:"350429",name:"泰宁县"},{code:"350430",name:"建宁县"},{code:"350481",name:"永安市"}],[{code:"350502",name:"鲤城区"},{code:"350503",name:"丰泽区"},{code:"350504",name:"洛江区"},{code:"350505",name:"泉港区"},{code:"350521",name:"惠安县"},{code:"350524",name:"安溪县"},{code:"350525",name:"永春县"},{code:"350526",name:"德化县"},{code:"350527",name:"金门县"},{code:"350581",name:"石狮市"},{code:"350582",name:"晋江市"},{code:"350583",name:"南安市"}],[{code:"350602",name:"芗城区"},{code:"350603",name:"龙文区"},{code:"350604",name:"龙海区"},{code:"350605",name:"长泰区"},{code:"350622",name:"云霄县"},{code:"350623",name:"漳浦县"},{code:"350624",name:"诏安县"},{code:"350626",name:"东山县"},{code:"350627",name:"南靖县"},{code:"350628",name:"平和县"},{code:"350629",name:"华安县"}],[{code:"350702",name:"延平区"},{code:"350703",name:"建阳区"},{code:"350721",name:"顺昌县"},{code:"350722",name:"浦城县"},{code:"350723",name:"光泽县"},{code:"350724",name:"松溪县"},{code:"350725",name:"政和县"},{code:"350781",name:"邵武市"},{code:"350782",name:"武夷山市"},{code:"350783",name:"建瓯市"}],[{code:"350802",name:"新罗区"},{code:"350803",name:"永定区"},{code:"350821",name:"长汀县"},{code:"350823",name:"上杭县"},{code:"350824",name:"武平县"},{code:"350825",name:"连城县"},{code:"350881",name:"漳平市"}],[{code:"350902",name:"蕉城区"},{code:"350921",name:"霞浦县"},{code:"350922",name:"古田县"},{code:"350923",name:"屏南县"},{code:"350924",name:"寿宁县"},{code:"350925",name:"周宁县"},{code:"350926",name:"柘荣县"},{code:"350981",name:"福安市"},{code:"350982",name:"福鼎市"}]],[[{code:"360102",name:"东湖区"},{code:"360103",name:"西湖区"},{code:"360104",name:"青云谱区"},{code:"360105",name:"湾里区"},{code:"360111",name:"青山湖区"},{code:"360112",name:"新建区"},{code:"360121",name:"南昌县"},{code:"360123",name:"安义县"},{code:"360124",name:"进贤县"}],[{code:"360202",name:"昌江区"},{code:"360203",name:"珠山区"},{code:"360222",name:"浮梁县"},{code:"360281",name:"乐平市"}],[{code:"360302",name:"安源区"},{code:"360313",name:"湘东区"},{code:"360321",name:"莲花县"},{code:"360322",name:"上栗县"},{code:"360323",name:"芦溪县"}],[{code:"360402",name:"濂溪区"},{code:"360403",name:"浔阳区"},{code:"360404",name:"柴桑区"},{code:"360423",name:"武宁县"},{code:"360424",name:"修水县"},{code:"360425",name:"永修县"},{code:"360426",name:"德安县"},{code:"360428",name:"都昌县"},{code:"360429",name:"湖口县"},{code:"360430",name:"彭泽县"},{code:"360481",name:"瑞昌市"},{code:"360482",name:"共青城市"},{code:"360483",name:"庐山市"}],[{code:"360502",name:"渝水区"},{code:"360521",name:"分宜县"}],[{code:"360602",name:"月湖区"},{code:"360622",name:"余江区"},{code:"360681",name:"贵溪市"}],[{code:"360702",name:"章贡区"},{code:"360703",name:"南康区"},{code:"360704",name:"赣县区"},{code:"360722",name:"信丰县"},{code:"360723",name:"大余县"},{code:"360724",name:"上犹县"},{code:"360725",name:"崇义县"},{code:"360726",name:"安远县"},{code:"360727",name:"龙南县"},{code:"360728",name:"定南县"},{code:"360729",name:"全南县"},{code:"360730",name:"宁都县"},{code:"360731",name:"于都县"},{code:"360732",name:"兴国县"},{code:"360733",name:"会昌县"},{code:"360734",name:"寻乌县"},{code:"360735",name:"石城县"},{code:"360781",name:"瑞金市"}],[{code:"360802",name:"吉州区"},{code:"360803",name:"青原区"},{code:"360821",name:"吉安县"},{code:"360822",name:"吉水县"},{code:"360823",name:"峡江县"},{code:"360824",name:"新干县"},{code:"360825",name:"永丰县"},{code:"360826",name:"泰和县"},{code:"360827",name:"遂川县"},{code:"360828",name:"万安县"},{code:"360829",name:"安福县"},{code:"360830",name:"永新县"},{code:"360881",name:"井冈山市"}],[{code:"360902",name:"袁州区"},{code:"360921",name:"奉新县"},{code:"360922",name:"万载县"},{code:"360923",name:"上高县"},{code:"360924",name:"宜丰县"},{code:"360925",name:"靖安县"},{code:"360926",name:"铜鼓县"},{code:"360981",name:"丰城市"},{code:"360982",name:"樟树市"},{code:"360983",name:"高安市"}],[{code:"361002",name:"临川区"},{code:"361003",name:"东乡区"},{code:"361021",name:"南城县"},{code:"361022",name:"黎川县"},{code:"361023",name:"南丰县"},{code:"361024",name:"崇仁县"},{code:"361025",name:"乐安县"},{code:"361026",name:"宜黄县"},{code:"361027",name:"金溪县"},{code:"361028",name:"资溪县"},{code:"361030",name:"广昌县"}],[{code:"361102",name:"信州区"},{code:"361103",name:"广丰区"},{code:"361121",name:"上饶县"},{code:"361123",name:"玉山县"},{code:"361124",name:"铅山县"},{code:"361125",name:"横峰县"},{code:"361126",name:"弋阳县"},{code:"361127",name:"余干县"},{code:"361128",name:"鄱阳县"},{code:"361129",name:"万年县"},{code:"361130",name:"婺源县"},{code:"361181",name:"德兴市"}]],[[{code:"370102",name:"历下区"},{code:"370103",name:"市中区"},{code:"370104",name:"槐荫区"},{code:"370105",name:"天桥区"},{code:"370112",name:"历城区"},{code:"370113",name:"长清区"},{code:"370114",name:"章丘区"},{code:"370124",name:"平阴县"},{code:"370125",name:"济阳县"},{code:"370126",name:"商河县"}],[{code:"370202",name:"市南区"},{code:"370203",name:"市北区"},{code:"370211",name:"黄岛区"},{code:"370212",name:"崂山区"},{code:"370213",name:"李沧区"},{code:"370214",name:"城阳区"},{code:"370215",name:"即墨区"},{code:"370281",name:"胶州市"},{code:"370283",name:"平度市"},{code:"370285",name:"莱西市"}],[{code:"370302",name:"淄川区"},{code:"370303",name:"张店区"},{code:"370304",name:"博山区"},{code:"370305",name:"临淄区"},{code:"370306",name:"周村区"},{code:"370321",name:"桓台县"},{code:"370322",name:"高青县"},{code:"370323",name:"沂源县"}],[{code:"370402",name:"市中区"},{code:"370403",name:"薛城区"},{code:"370404",name:"峄城区"},{code:"370405",name:"台儿庄区"},{code:"370406",name:"山亭区"},{code:"370481",name:"滕州市"}],[{code:"370502",name:"东营区"},{code:"370503",name:"河口区"},{code:"370505",name:"垦利区"},{code:"370522",name:"利津县"},{code:"370523",name:"广饶县"}],[{code:"370602",name:"芝罘区"},{code:"370611",name:"福山区"},{code:"370612",name:"牟平区"},{code:"370613",name:"莱山区"},{code:"370634",name:"长岛县"},{code:"370681",name:"龙口市"},{code:"370682",name:"莱阳市"},{code:"370683",name:"莱州市"},{code:"370684",name:"蓬莱市"},{code:"370685",name:"招远市"},{code:"370686",name:"栖霞市"},{code:"370687",name:"海阳市"}],[{code:"370702",name:"潍城区"},{code:"370703",name:"寒亭区"},{code:"370704",name:"坊子区"},{code:"370705",name:"奎文区"},{code:"370724",name:"临朐县"},{code:"370725",name:"昌乐县"},{code:"370781",name:"青州市"},{code:"370782",name:"诸城市"},{code:"370783",name:"寿光市"},{code:"370784",name:"安丘市"},{code:"370785",name:"高密市"},{code:"370786",name:"昌邑市"}],[{code:"370811",name:"任城区"},{code:"370812",name:"兖州区"},{code:"370826",name:"微山县"},{code:"370827",name:"鱼台县"},{code:"370828",name:"金乡县"},{code:"370829",name:"嘉祥县"},{code:"370830",name:"汶上县"},{code:"370831",name:"泗水县"},{code:"370832",name:"梁山县"},{code:"370881",name:"曲阜市"},{code:"370883",name:"邹城市"}],[{code:"370902",name:"泰山区"},{code:"370911",name:"岱岳区"},{code:"370921",name:"宁阳县"},{code:"370923",name:"东平县"},{code:"370982",name:"新泰市"},{code:"370983",name:"肥城市"}],[{code:"371002",name:"环翠区"},{code:"371003",name:"文登区"},{code:"371082",name:"荣成市"},{code:"371083",name:"乳山市"}],[{code:"371102",name:"东港区"},{code:"371103",name:"岚山区"},{code:"371121",name:"五莲县"},{code:"371122",name:"莒县"}],[{code:"371202",name:"莱城区"},{code:"371203",name:"钢城区"}],[{code:"371302",name:"兰山区"},{code:"371311",name:"罗庄区"},{code:"371312",name:"河东区"},{code:"371321",name:"沂南县"},{code:"371322",name:"郯城县"},{code:"371323",name:"沂水县"},{code:"371324",name:"兰陵县"},{code:"371325",name:"费县"},{code:"371326",name:"平邑县"},{code:"371327",name:"莒南县"},{code:"371328",name:"蒙阴县"},{code:"371329",name:"临沭县"}],[{code:"371402",name:"德城区"},{code:"371403",name:"陵城区"},{code:"371422",name:"宁津县"},{code:"371423",name:"庆云县"},{code:"371424",name:"临邑县"},{code:"371425",name:"齐河县"},{code:"371426",name:"平原县"},{code:"371427",name:"夏津县"},{code:"371428",name:"武城县"},{code:"371481",name:"乐陵市"},{code:"371482",name:"禹城市"}],[{code:"371502",name:"东昌府区"},{code:"371521",name:"阳谷县"},{code:"371522",name:"莘县"},{code:"371523",name:"茌平县"},{code:"371524",name:"东阿县"},{code:"371525",name:"冠县"},{code:"371526",name:"高唐县"},{code:"371581",name:"临清市"}],[{code:"371602",name:"滨城区"},{code:"371603",name:"沾化区"},{code:"371621",name:"惠民县"},{code:"371622",name:"阳信县"},{code:"371623",name:"无棣县"},{code:"371625",name:"博兴县"},{code:"371626",name:"邹平县"}],[{code:"371702",name:"牡丹区"},{code:"371703",name:"定陶区"},{code:"371721",name:"曹县"},{code:"371722",name:"单县"},{code:"371723",name:"成武县"},{code:"371724",name:"巨野县"},{code:"371725",name:"郓城县"},{code:"371726",name:"鄄城县"},{code:"371728",name:"东明县"}]],[[{code:"410102",name:"中原区"},{code:"410103",name:"二七区"},{code:"410104",name:"管城回族区"},{code:"410105",name:"金水区"},{code:"410106",name:"上街区"},{code:"410108",name:"惠济区"},{code:"410122",name:"中牟县"},{code:"410181",name:"巩义市"},{code:"410182",name:"荥阳市"},{code:"410183",name:"新密市"},{code:"410184",name:"新郑市"},{code:"410185",name:"登封市"}],[{code:"410202",name:"龙亭区"},{code:"410203",name:"顺河回族区"},{code:"410204",name:"鼓楼区"},{code:"410205",name:"禹王台区"},{code:"410212",name:"祥符区"},{code:"410221",name:"杞县"},{code:"410222",name:"通许县"},{code:"410223",name:"尉氏县"},{code:"410225",name:"兰考县"}],[{code:"410302",name:"老城区"},{code:"410303",name:"西工区"},{code:"410304",name:"瀍河回族区"},{code:"410305",name:"涧西区"},{code:"410307",name:"偃师区"},{code:"410308",name:"孟津区"},{code:"410311",name:"洛龙区"},{code:"410323",name:"新安县"},{code:"410324",name:"栾川县"},{code:"410325",name:"嵩县"},{code:"410326",name:"汝阳县"},{code:"410327",name:"宜阳县"},{code:"410328",name:"洛宁县"},{code:"410329",name:"伊川县"}],[{code:"410402",name:"新华区"},{code:"410403",name:"卫东区"},{code:"410404",name:"石龙区"},{code:"410411",name:"湛河区"},{code:"410421",name:"宝丰县"},{code:"410422",name:"叶县"},{code:"410423",name:"鲁山县"},{code:"410425",name:"郏县"},{code:"410481",name:"舞钢市"},{code:"410482",name:"汝州市"}],[{code:"410502",name:"文峰区"},{code:"410503",name:"北关区"},{code:"410505",name:"殷都区"},{code:"410506",name:"龙安区"},{code:"410522",name:"安阳县"},{code:"410523",name:"汤阴县"},{code:"410526",name:"滑县"},{code:"410527",name:"内黄县"},{code:"410581",name:"林州市"}],[{code:"410602",name:"鹤山区"},{code:"410603",name:"山城区"},{code:"410611",name:"淇滨区"},{code:"410621",name:"浚县"},{code:"410622",name:"淇县"}],[{code:"410702",name:"红旗区"},{code:"410703",name:"卫滨区"},{code:"410704",name:"凤泉区"},{code:"410711",name:"牧野区"},{code:"410721",name:"新乡县"},{code:"410724",name:"获嘉县"},{code:"410725",name:"原阳县"},{code:"410726",name:"延津县"},{code:"410727",name:"封丘县"},{code:"410728",name:"长垣县"},{code:"410781",name:"卫辉市"},{code:"410782",name:"辉县市"}],[{code:"410802",name:"解放区"},{code:"410803",name:"中站区"},{code:"410804",name:"马村区"},{code:"410811",name:"山阳区"},{code:"410821",name:"修武县"},{code:"410822",name:"博爱县"},{code:"410823",name:"武陟县"},{code:"410825",name:"温县"},{code:"410882",name:"沁阳市"},{code:"410883",name:"孟州市"}],[{code:"410902",name:"华龙区"},{code:"410922",name:"清丰县"},{code:"410923",name:"南乐县"},{code:"410926",name:"范县"},{code:"410927",name:"台前县"},{code:"410928",name:"濮阳县"}],[{code:"411002",name:"魏都区"},{code:"411003",name:"建安区"},{code:"411024",name:"鄢陵县"},{code:"411025",name:"襄城县"},{code:"411081",name:"禹州市"},{code:"411082",name:"长葛市"}],[{code:"411102",name:"源汇区"},{code:"411103",name:"郾城区"},{code:"411104",name:"召陵区"},{code:"411121",name:"舞阳县"},{code:"411122",name:"临颍县"}],[{code:"411202",name:"湖滨区"},{code:"411203",name:"陕州区"},{code:"411221",name:"渑池县"},{code:"411224",name:"卢氏县"},{code:"411281",name:"义马市"},{code:"411282",name:"灵宝市"}],[{code:"411302",name:"宛城区"},{code:"411303",name:"卧龙区"},{code:"411321",name:"南召县"},{code:"411322",name:"方城县"},{code:"411323",name:"西峡县"},{code:"411324",name:"镇平县"},{code:"411325",name:"内乡县"},{code:"411326",name:"淅川县"},{code:"411327",name:"社旗县"},{code:"411328",name:"唐河县"},{code:"411329",name:"新野县"},{code:"411330",name:"桐柏县"},{code:"411381",name:"邓州市"}],[{code:"411402",name:"梁园区"},{code:"411403",name:"睢阳区"},{code:"411421",name:"民权县"},{code:"411422",name:"睢县"},{code:"411423",name:"宁陵县"},{code:"411424",name:"柘城县"},{code:"411425",name:"虞城县"},{code:"411426",name:"夏邑县"},{code:"411481",name:"永城市"}],[{code:"411502",name:"浉河区"},{code:"411503",name:"平桥区"},{code:"411521",name:"罗山县"},{code:"411522",name:"光山县"},{code:"411523",name:"新县"},{code:"411524",name:"商城县"},{code:"411525",name:"固始县"},{code:"411526",name:"潢川县"},{code:"411527",name:"淮滨县"},{code:"411528",name:"息县"}],[{code:"411602",name:"川汇区"},{code:"411621",name:"扶沟县"},{code:"411622",name:"西华县"},{code:"411623",name:"商水县"},{code:"411624",name:"沈丘县"},{code:"411625",name:"郸城县"},{code:"411626",name:"淮阳县"},{code:"411627",name:"太康县"},{code:"411628",name:"鹿邑县"},{code:"411681",name:"项城市"}],[{code:"411702",name:"驿城区"},{code:"411721",name:"西平县"},{code:"411722",name:"上蔡县"},{code:"411723",name:"平舆县"},{code:"411724",name:"正阳县"},{code:"411725",name:"确山县"},{code:"411726",name:"泌阳县"},{code:"411727",name:"汝南县"},{code:"411728",name:"遂平县"},{code:"411729",name:"新蔡县"}],[{code:"419001",name:"济源市"}]],[[{code:"420102",name:"江岸区"},{code:"420103",name:"江汉区"},{code:"420104",name:"硚口区"},{code:"420105",name:"汉阳区"},{code:"420106",name:"武昌区"},{code:"420107",name:"青山区"},{code:"420111",name:"洪山区"},{code:"420112",name:"东西湖区"},{code:"420113",name:"汉南区"},{code:"420114",name:"蔡甸区"},{code:"420115",name:"江夏区"},{code:"420116",name:"黄陂区"},{code:"420117",name:"新洲区"}],[{code:"420202",name:"黄石港区"},{code:"420203",name:"西塞山区"},{code:"420204",name:"下陆区"},{code:"420205",name:"铁山区"},{code:"420222",name:"阳新县"},{code:"420281",name:"大冶市"}],[{code:"420302",name:"茅箭区"},{code:"420303",name:"张湾区"},{code:"420304",name:"郧阳区"},{code:"420322",name:"郧西县"},{code:"420323",name:"竹山县"},{code:"420324",name:"竹溪县"},{code:"420325",name:"房县"},{code:"420381",name:"丹江口市"}],[{code:"420502",name:"西陵区"},{code:"420503",name:"伍家岗区"},{code:"420504",name:"点军区"},{code:"420505",name:"猇亭区"},{code:"420506",name:"夷陵区"},{code:"420525",name:"远安县"},{code:"420526",name:"兴山县"},{code:"420527",name:"秭归县"},{code:"420528",name:"长阳土家族自治县"},{code:"420529",name:"五峰土家族自治县"},{code:"420581",name:"宜都市"},{code:"420582",name:"当阳市"},{code:"420583",name:"枝江市"}],[{code:"420602",name:"襄城区"},{code:"420606",name:"樊城区"},{code:"420607",name:"襄州区"},{code:"420624",name:"南漳县"},{code:"420625",name:"谷城县"},{code:"420626",name:"保康县"},{code:"420682",name:"老河口市"},{code:"420683",name:"枣阳市"},{code:"420684",name:"宜城市"}],[{code:"420702",name:"梁子湖区"},{code:"420703",name:"华容区"},{code:"420704",name:"鄂城区"}],[{code:"420802",name:"东宝区"},{code:"420804",name:"掇刀区"},{code:"420821",name:"京山县"},{code:"420822",name:"沙洋县"},{code:"420881",name:"钟祥市"}],[{code:"420902",name:"孝南区"},{code:"420921",name:"孝昌县"},{code:"420922",name:"大悟县"},{code:"420923",name:"云梦县"},{code:"420981",name:"应城市"},{code:"420982",name:"安陆市"},{code:"420984",name:"汉川市"}],[{code:"421002",name:"沙市区"},{code:"421003",name:"荆州区"},{code:"421022",name:"公安县"},{code:"421023",name:"监利县"},{code:"421024",name:"江陵县"},{code:"421081",name:"石首市"},{code:"421083",name:"洪湖市"},{code:"421087",name:"松滋市"}],[{code:"421102",name:"黄州区"},{code:"421121",name:"团风县"},{code:"421122",name:"红安县"},{code:"421123",name:"罗田县"},{code:"421124",name:"英山县"},{code:"421125",name:"浠水县"},{code:"421126",name:"蕲春县"},{code:"421127",name:"黄梅县"},{code:"421181",name:"麻城市"},{code:"421182",name:"武穴市"}],[{code:"421202",name:"咸安区"},{code:"421221",name:"嘉鱼县"},{code:"421222",name:"通城县"},{code:"421223",name:"崇阳县"},{code:"421224",name:"通山县"},{code:"421281",name:"赤壁市"}],[{code:"421303",name:"曾都区"},{code:"421321",name:"随县"},{code:"421381",name:"广水市"}],[{code:"422801",name:"恩施市"},{code:"422802",name:"利川市"},{code:"422822",name:"建始县"},{code:"422823",name:"巴东县"},{code:"422825",name:"宣恩县"},{code:"422826",name:"咸丰县"},{code:"422827",name:"来凤县"},{code:"422828",name:"鹤峰县"}],[{code:"429004",name:"仙桃市"},{code:"429005",name:"潜江市"},{code:"429006",name:"天门市"},{code:"429021",name:"神农架林区"}]],[[{code:"430102",name:"芙蓉区"},{code:"430103",name:"天心区"},{code:"430104",name:"岳麓区"},{code:"430105",name:"开福区"},{code:"430111",name:"雨花区"},{code:"430112",name:"望城区"},{code:"430121",name:"长沙县"},{code:"430181",name:"浏阳市"},{code:"430182",name:"宁乡市"}],[{code:"430202",name:"荷塘区"},{code:"430203",name:"芦淞区"},{code:"430204",name:"石峰区"},{code:"430211",name:"天元区"},{code:"430221",name:"株洲县"},{code:"430223",name:"攸县"},{code:"430224",name:"茶陵县"},{code:"430225",name:"炎陵县"},{code:"430281",name:"醴陵市"}],[{code:"430302",name:"雨湖区"},{code:"430304",name:"岳塘区"},{code:"430321",name:"湘潭县"},{code:"430381",name:"湘乡市"},{code:"430382",name:"韶山市"}],[{code:"430405",name:"珠晖区"},{code:"430406",name:"雁峰区"},{code:"430407",name:"石鼓区"},{code:"430408",name:"蒸湘区"},{code:"430412",name:"南岳区"},{code:"430421",name:"衡阳县"},{code:"430422",name:"衡南县"},{code:"430423",name:"衡山县"},{code:"430424",name:"衡东县"},{code:"430426",name:"祁东县"},{code:"430481",name:"耒阳市"},{code:"430482",name:"常宁市"}],[{code:"430502",name:"双清区"},{code:"430503",name:"大祥区"},{code:"430511",name:"北塔区"},{code:"430521",name:"邵东县"},{code:"430522",name:"新邵县"},{code:"430523",name:"邵阳县"},{code:"430524",name:"隆回县"},{code:"430525",name:"洞口县"},{code:"430527",name:"绥宁县"},{code:"430528",name:"新宁县"},{code:"430529",name:"城步苗族自治县"},{code:"430581",name:"武冈市"}],[{code:"430602",name:"岳阳楼区"},{code:"430603",name:"云溪区"},{code:"430611",name:"君山区"},{code:"430621",name:"岳阳县"},{code:"430623",name:"华容县"},{code:"430624",name:"湘阴县"},{code:"430626",name:"平江县"},{code:"430681",name:"汨罗市"},{code:"430682",name:"临湘市"}],[{code:"430702",name:"武陵区"},{code:"430703",name:"鼎城区"},{code:"430721",name:"安乡县"},{code:"430722",name:"汉寿县"},{code:"430723",name:"澧县"},{code:"430724",name:"临澧县"},{code:"430725",name:"桃源县"},{code:"430726",name:"石门县"},{code:"430781",name:"津市市"}],[{code:"430802",name:"永定区"},{code:"430811",name:"武陵源区"},{code:"430821",name:"慈利县"},{code:"430822",name:"桑植县"}],[{code:"430902",name:"资阳区"},{code:"430903",name:"赫山区"},{code:"430921",name:"南县"},{code:"430922",name:"桃江县"},{code:"430923",name:"安化县"},{code:"430981",name:"沅江市"}],[{code:"431002",name:"北湖区"},{code:"431003",name:"苏仙区"},{code:"431021",name:"桂阳县"},{code:"431022",name:"宜章县"},{code:"431023",name:"永兴县"},{code:"431024",name:"嘉禾县"},{code:"431025",name:"临武县"},{code:"431026",name:"汝城县"},{code:"431027",name:"桂东县"},{code:"431028",name:"安仁县"},{code:"431081",name:"资兴市"}],[{code:"431102",name:"零陵区"},{code:"431103",name:"冷水滩区"},{code:"431122",name:"东安县"},{code:"431123",name:"双牌县"},{code:"431124",name:"道县"},{code:"431125",name:"江永县"},{code:"431126",name:"宁远县"},{code:"431127",name:"蓝山县"},{code:"431128",name:"新田县"},{code:"431129",name:"江华瑶族自治县"},{code:"431181",name:"祁阳市"}],[{code:"431202",name:"鹤城区"},{code:"431221",name:"中方县"},{code:"431222",name:"沅陵县"},{code:"431223",name:"辰溪县"},{code:"431224",name:"溆浦县"},{code:"431225",name:"会同县"},{code:"431226",name:"麻阳苗族自治县"},{code:"431227",name:"新晃侗族自治县"},{code:"431228",name:"芷江侗族自治县"},{code:"431229",name:"靖州苗族侗族自治县"},{code:"431230",name:"通道侗族自治县"},{code:"431281",name:"洪江市"}],[{code:"431302",name:"娄星区"},{code:"431321",name:"双峰县"},{code:"431322",name:"新化县"},{code:"431381",name:"冷水江市"},{code:"431382",name:"涟源市"}],[{code:"433101",name:"吉首市"},{code:"433122",name:"泸溪县"},{code:"433123",name:"凤凰县"},{code:"433124",name:"花垣县"},{code:"433125",name:"保靖县"},{code:"433126",name:"古丈县"},{code:"433127",name:"永顺县"},{code:"433130",name:"龙山县"}]],[[{code:"440103",name:"荔湾区"},{code:"440104",name:"越秀区"},{code:"440105",name:"海珠区"},{code:"440106",name:"天河区"},{code:"440111",name:"白云区"},{code:"440112",name:"黄埔区"},{code:"440113",name:"番禺区"},{code:"440114",name:"花都区"},{code:"440115",name:"南沙区"},{code:"440117",name:"从化区"},{code:"440118",name:"增城区"}],[{code:"440203",name:"武江区"},{code:"440204",name:"浈江区"},{code:"440205",name:"曲江区"},{code:"440222",name:"始兴县"},{code:"440224",name:"仁化县"},{code:"440229",name:"翁源县"},{code:"440232",name:"乳源瑶族自治县"},{code:"440233",name:"新丰县"},{code:"440281",name:"乐昌市"},{code:"440282",name:"南雄市"}],[{code:"440303",name:"罗湖区"},{code:"440304",name:"福田区"},{code:"440305",name:"南山区"},{code:"440306",name:"宝安区"},{code:"440307",name:"龙岗区"},{code:"440308",name:"盐田区"},{code:"440309",name:"龙华区"},{code:"440310",name:"坪山区"}],[{code:"440402",name:"香洲区"},{code:"440403",name:"斗门区"},{code:"440404",name:"金湾区"}],[{code:"440507",name:"龙湖区"},{code:"440511",name:"金平区"},{code:"440512",name:"濠江区"},{code:"440513",name:"潮阳区"},{code:"440514",name:"潮南区"},{code:"440515",name:"澄海区"},{code:"440523",name:"南澳县"}],[{code:"440604",name:"禅城区"},{code:"440605",name:"南海区"},{code:"440606",name:"顺德区"},{code:"440607",name:"三水区"},{code:"440608",name:"高明区"}],[{code:"440703",name:"蓬江区"},{code:"440704",name:"江海区"},{code:"440705",name:"新会区"},{code:"440781",name:"台山市"},{code:"440783",name:"开平市"},{code:"440784",name:"鹤山市"},{code:"440785",name:"恩平市"}],[{code:"440802",name:"赤坎区"},{code:"440803",name:"霞山区"},{code:"440804",name:"坡头区"},{code:"440811",name:"麻章区"},{code:"440823",name:"遂溪县"},{code:"440825",name:"徐闻县"},{code:"440881",name:"廉江市"},{code:"440882",name:"雷州市"},{code:"440883",name:"吴川市"}],[{code:"440902",name:"茂南区"},{code:"440904",name:"电白区"},{code:"440981",name:"高州市"},{code:"440982",name:"化州市"},{code:"440983",name:"信宜市"}],[{code:"441202",name:"端州区"},{code:"441203",name:"鼎湖区"},{code:"441204",name:"高要区"},{code:"441223",name:"广宁县"},{code:"441224",name:"怀集县"},{code:"441225",name:"封开县"},{code:"441226",name:"德庆县"},{code:"441284",name:"四会市"}],[{code:"441302",name:"惠城区"},{code:"441303",name:"惠阳区"},{code:"441322",name:"博罗县"},{code:"441323",name:"惠东县"},{code:"441324",name:"龙门县"}],[{code:"441402",name:"梅江区"},{code:"441403",name:"梅县区"},{code:"441422",name:"大埔县"},{code:"441423",name:"丰顺县"},{code:"441424",name:"五华县"},{code:"441426",name:"平远县"},{code:"441427",name:"蕉岭县"},{code:"441481",name:"兴宁市"}],[{code:"441502",name:"城区"},{code:"441521",name:"海丰县"},{code:"441523",name:"陆河县"},{code:"441581",name:"陆丰市"}],[{code:"441602",name:"源城区"},{code:"441621",name:"紫金县"},{code:"441622",name:"龙川县"},{code:"441623",name:"连平县"},{code:"441624",name:"和平县"},{code:"441625",name:"东源县"}],[{code:"441702",name:"江城区"},{code:"441704",name:"阳东区"},{code:"441721",name:"阳西县"},{code:"441781",name:"阳春市"}],[{code:"441802",name:"清城区"},{code:"441803",name:"清新区"},{code:"441821",name:"佛冈县"},{code:"441823",name:"阳山县"},{code:"441825",name:"连山壮族瑶族自治县"},{code:"441826",name:"连南瑶族自治县"},{code:"441881",name:"英德市"},{code:"441882",name:"连州市"}],[{code:"441901",name:"东城街道"},{code:"441902",name:"南城街道"},{code:"441903",name:"万江街道"},{code:"441904",name:"莞城街道"},{code:"441905",name:"石碣镇"},{code:"441906",name:"石龙镇"},{code:"441907",name:"茶山镇"},{code:"441908",name:"石排镇"},{code:"441909",name:"企石镇"},{code:"441910",name:"横沥镇"},{code:"441911",name:"桥头镇"},{code:"441912",name:"谢岗镇"},{code:"441913",name:"东坑镇"},{code:"441914",name:"常平镇"},{code:"441915",name:"寮步镇"},{code:"441916",name:"樟木头镇"},{code:"441917",name:"大朗镇"},{code:"441918",name:"黄江镇"},{code:"441919",name:"清溪镇"},{code:"441920",name:"塘厦镇"},{code:"441921",name:"凤岗镇"},{code:"441922",name:"大岭山镇"},{code:"441923",name:"长安镇"},{code:"441924",name:"虎门镇"},{code:"441925",name:"厚街镇"},{code:"441926",name:"沙田镇"},{code:"441927",name:"道滘镇"},{code:"441928",name:"洪梅镇"},{code:"441929",name:"麻涌镇"},{code:"441930",name:"望牛墩镇"},{code:"441931",name:"中堂镇"},{code:"441932",name:"高埗镇"},{code:"441933",name:"松山湖管委会"},{code:"441934",name:"虎门港管委会"},{code:"441935",name:"东莞生态园"}],[{code:"442001",name:"石岐区街道"},{code:"442002",name:"东区街道"},{code:"442003",name:"火炬开发区"},{code:"442004",name:"西区街道"},{code:"442005",name:"南区街道"},{code:"442006",name:"五桂山街道"},{code:"442007",name:"小榄镇"},{code:"442008",name:"黄圃镇"},{code:"442009",name:"民众镇"},{code:"442010",name:"东凤镇"},{code:"442011",name:"东升镇"},{code:"442012",name:"古镇镇"},{code:"442013",name:"沙溪镇"},{code:"442014",name:"坦洲镇"},{code:"442015",name:"港口镇"},{code:"442016",name:"三角镇"},{code:"442017",name:"横栏镇"},{code:"442018",name:"南头镇"},{code:"442019",name:"阜沙镇"},{code:"442020",name:"南朗镇"},{code:"442021",name:"三乡镇"},{code:"442022",name:"板芙镇"},{code:"442023",name:"大涌镇"},{code:"442024",name:"神湾镇"}],[{code:"445102",name:"湘桥区"},{code:"445103",name:"潮安区"},{code:"445122",name:"饶平县"}],[{code:"445202",name:"榕城区"},{code:"445203",name:"揭东区"},{code:"445222",name:"揭西县"},{code:"445224",name:"惠来县"},{code:"445281",name:"普宁市"}],[{code:"445302",name:"云城区"},{code:"445303",name:"云安区"},{code:"445321",name:"新兴县"},{code:"445322",name:"郁南县"},{code:"445381",name:"罗定市"}]],[[{code:"450102",name:"兴宁区"},{code:"450103",name:"青秀区"},{code:"450105",name:"江南区"},{code:"450107",name:"西乡塘区"},{code:"450108",name:"良庆区"},{code:"450109",name:"邕宁区"},{code:"450110",name:"武鸣区"},{code:"450123",name:"隆安县"},{code:"450124",name:"马山县"},{code:"450125",name:"上林县"},{code:"450126",name:"宾阳县"},{code:"450181",name:"横州市"}],[{code:"450202",name:"城中区"},{code:"450203",name:"鱼峰区"},{code:"450204",name:"柳南区"},{code:"450205",name:"柳北区"},{code:"450206",name:"柳江区"},{code:"450222",name:"柳城县"},{code:"450223",name:"鹿寨县"},{code:"450224",name:"融安县"},{code:"450225",name:"融水苗族自治县"},{code:"450226",name:"三江侗族自治县"}],[{code:"450302",name:"秀峰区"},{code:"450303",name:"叠彩区"},{code:"450304",name:"象山区"},{code:"450305",name:"七星区"},{code:"450311",name:"雁山区"},{code:"450312",name:"临桂区"},{code:"450321",name:"阳朔县"},{code:"450323",name:"灵川县"},{code:"450324",name:"全州县"},{code:"450325",name:"兴安县"},{code:"450326",name:"永福县"},{code:"450327",name:"灌阳县"},{code:"450328",name:"龙胜各族自治县"},{code:"450329",name:"资源县"},{code:"450330",name:"平乐县"},{code:"450331",name:"荔浦县"},{code:"450332",name:"恭城瑶族自治县"}],[{code:"450403",name:"万秀区"},{code:"450405",name:"长洲区"},{code:"450406",name:"龙圩区"},{code:"450421",name:"苍梧县"},{code:"450422",name:"藤县"},{code:"450423",name:"蒙山县"},{code:"450481",name:"岑溪市"}],[{code:"450502",name:"海城区"},{code:"450503",name:"银海区"},{code:"450512",name:"铁山港区"},{code:"450521",name:"合浦县"}],[{code:"450602",name:"港口区"},{code:"450603",name:"防城区"},{code:"450621",name:"上思县"},{code:"450681",name:"东兴市"}],[{code:"450702",name:"钦南区"},{code:"450703",name:"钦北区"},{code:"450721",name:"灵山县"},{code:"450722",name:"浦北县"}],[{code:"450802",name:"港北区"},{code:"450803",name:"港南区"},{code:"450804",name:"覃塘区"},{code:"450821",name:"平南县"},{code:"450881",name:"桂平市"}],[{code:"450902",name:"玉州区"},{code:"450903",name:"福绵区"},{code:"450921",name:"容县"},{code:"450922",name:"陆川县"},{code:"450923",name:"博白县"},{code:"450924",name:"兴业县"},{code:"450981",name:"北流市"}],[{code:"451002",name:"右江区"},{code:"451021",name:"田阳县"},{code:"451022",name:"田东县"},{code:"451023",name:"平果县"},{code:"451024",name:"德保县"},{code:"451026",name:"那坡县"},{code:"451027",name:"凌云县"},{code:"451028",name:"乐业县"},{code:"451029",name:"田林县"},{code:"451030",name:"西林县"},{code:"451031",name:"隆林各族自治县"},{code:"451081",name:"靖西市"}],[{code:"451102",name:"八步区"},{code:"451103",name:"平桂区"},{code:"451121",name:"昭平县"},{code:"451122",name:"钟山县"},{code:"451123",name:"富川瑶族自治县"}],[{code:"451202",name:"金城江区"},{code:"451203",name:"宜州区"},{code:"451221",name:"南丹县"},{code:"451222",name:"天峨县"},{code:"451223",name:"凤山县"},{code:"451224",name:"东兰县"},{code:"451225",name:"罗城仫佬族自治县"},{code:"451226",name:"环江毛南族自治县"},{code:"451227",name:"巴马瑶族自治县"},{code:"451228",name:"都安瑶族自治县"},{code:"451229",name:"大化瑶族自治县"}],[{code:"451302",name:"兴宾区"},{code:"451321",name:"忻城县"},{code:"451322",name:"象州县"},{code:"451323",name:"武宣县"},{code:"451324",name:"金秀瑶族自治县"},{code:"451381",name:"合山市"}],[{code:"451402",name:"江州区"},{code:"451421",name:"扶绥县"},{code:"451422",name:"宁明县"},{code:"451423",name:"龙州县"},{code:"451424",name:"大新县"},{code:"451425",name:"天等县"},{code:"451481",name:"凭祥市"}]],[[{code:"460105",name:"秀英区"},{code:"460106",name:"龙华区"},{code:"460107",name:"琼山区"},{code:"460108",name:"美兰区"}],[{code:"460202",name:"海棠区"},{code:"460203",name:"吉阳区"},{code:"460204",name:"天涯区"},{code:"460205",name:"崖州区"}],[{code:"460321",name:"西沙群岛"},{code:"460322",name:"南沙群岛"},{code:"460323",name:"中沙群岛的岛礁及其海域"}],[{code:"460401",name:"那大镇"},{code:"460402",name:"和庆镇"},{code:"460403",name:"南丰镇"},{code:"460404",name:"大成镇"},{code:"460405",name:"雅星镇"},{code:"460406",name:"兰洋镇"},{code:"460407",name:"光村镇"},{code:"460408",name:"木棠镇"},{code:"460409",name:"海头镇"},{code:"460410",name:"峨蔓镇"},{code:"460411",name:"三都镇"},{code:"460412",name:"王五镇"},{code:"460413",name:"白马井镇"},{code:"460414",name:"中和镇"},{code:"460415",name:"排浦镇"},{code:"460416",name:"东成镇"},{code:"460417",name:"新州镇"},{code:"460418",name:"国营西培农场"},{code:"460419",name:"国营西联农场"},{code:"460420",name:"国营蓝洋农场"},{code:"460421",name:"国营八一农场"},{code:"460422",name:"洋浦经济开发区"},{code:"460423",name:"华南热作学院"},{code:"460424",name:"红岭农场"}],[{code:"469001",name:"五指山市"},{code:"469002",name:"琼海市"},{code:"469005",name:"文昌市"},{code:"469006",name:"万宁市"},{code:"469007",name:"东方市"},{code:"469021",name:"定安县"},{code:"469022",name:"屯昌县"},{code:"469023",name:"澄迈县"},{code:"469024",name:"临高县"},{code:"469025",name:"白沙黎族自治县"},{code:"469026",name:"昌江黎族自治县"},{code:"469027",name:"乐东黎族自治县"},{code:"469028",name:"陵水黎族自治县"},{code:"469029",name:"保亭黎族苗族自治县"},{code:"469030",name:"琼中黎族苗族自治县"}]],[[{code:"500101",name:"万州区"},{code:"500102",name:"涪陵区"},{code:"500103",name:"渝中区"},{code:"500104",name:"大渡口区"},{code:"500105",name:"江北区"},{code:"500106",name:"沙坪坝区"},{code:"500107",name:"九龙坡区"},{code:"500108",name:"南岸区"},{code:"500109",name:"北碚区"},{code:"500110",name:"綦江区"},{code:"500111",name:"大足区"},{code:"500112",name:"渝北区"},{code:"500113",name:"巴南区"},{code:"500114",name:"黔江区"},{code:"500115",name:"长寿区"},{code:"500116",name:"江津区"},{code:"500117",name:"合川区"},{code:"500118",name:"永川区"},{code:"500119",name:"南川区"},{code:"500120",name:"璧山区"},{code:"500151",name:"铜梁区"},{code:"500152",name:"潼南区"},{code:"500153",name:"荣昌区"},{code:"500154",name:"开州区"},{code:"500155",name:"梁平区"},{code:"500156",name:"武隆区"}],[{code:"500229",name:"城口县"},{code:"500230",name:"丰都县"},{code:"500231",name:"垫江县"},{code:"500233",name:"忠县"},{code:"500235",name:"云阳县"},{code:"500236",name:"奉节县"},{code:"500237",name:"巫山县"},{code:"500238",name:"巫溪县"},{code:"500240",name:"石柱土家族自治县"},{code:"500241",name:"秀山土家族苗族自治县"},{code:"500242",name:"酉阳土家族苗族自治县"},{code:"500243",name:"彭水苗族土家族自治县"}]],[[{code:"510104",name:"锦江区"},{code:"510105",name:"青羊区"},{code:"510106",name:"金牛区"},{code:"510107",name:"武侯区"},{code:"510108",name:"成华区"},{code:"510112",name:"龙泉驿区"},{code:"510113",name:"青白江区"},{code:"510114",name:"新都区"},{code:"510115",name:"温江区"},{code:"510116",name:"双流区"},{code:"510117",name:"郫都区"},{code:"510121",name:"金堂县"},{code:"510129",name:"大邑县"},{code:"510131",name:"蒲江县"},{code:"510132",name:"新津县"},{code:"510181",name:"都江堰市"},{code:"510182",name:"彭州市"},{code:"510183",name:"邛崃市"},{code:"510184",name:"崇州市"},{code:"510185",name:"简阳市"}],[{code:"510302",name:"自流井区"},{code:"510303",name:"贡井区"},{code:"510304",name:"大安区"},{code:"510311",name:"沿滩区"},{code:"510321",name:"荣县"},{code:"510322",name:"富顺县"}],[{code:"510402",name:"东区"},{code:"510403",name:"西区"},{code:"510411",name:"仁和区"},{code:"510421",name:"米易县"},{code:"510422",name:"盐边县"}],[{code:"510502",name:"江阳区"},{code:"510503",name:"纳溪区"},{code:"510504",name:"龙马潭区"},{code:"510521",name:"泸县"},{code:"510522",name:"合江县"},{code:"510524",name:"叙永县"},{code:"510525",name:"古蔺县"}],[{code:"510603",name:"旌阳区"},{code:"510604",name:"罗江区"},{code:"510623",name:"中江县"},{code:"510681",name:"广汉市"},{code:"510682",name:"什邡市"},{code:"510683",name:"绵竹市"}],[{code:"510703",name:"涪城区"},{code:"510704",name:"游仙区"},{code:"510705",name:"安州区"},{code:"510722",name:"三台县"},{code:"510723",name:"盐亭县"},{code:"510725",name:"梓潼县"},{code:"510726",name:"北川羌族自治县"},{code:"510727",name:"平武县"},{code:"510781",name:"江油市"}],[{code:"510802",name:"利州区"},{code:"510811",name:"昭化区"},{code:"510812",name:"朝天区"},{code:"510821",name:"旺苍县"},{code:"510822",name:"青川县"},{code:"510823",name:"剑阁县"},{code:"510824",name:"苍溪县"}],[{code:"510903",name:"船山区"},{code:"510904",name:"安居区"},{code:"510921",name:"蓬溪县"},{code:"510922",name:"射洪县"},{code:"510923",name:"大英县"}],[{code:"511002",name:"市中区"},{code:"511011",name:"东兴区"},{code:"511024",name:"威远县"},{code:"511025",name:"资中县"},{code:"511083",name:"隆昌市"}],[{code:"511102",name:"市中区"},{code:"511111",name:"沙湾区"},{code:"511112",name:"五通桥区"},{code:"511113",name:"金口河区"},{code:"511123",name:"犍为县"},{code:"511124",name:"井研县"},{code:"511126",name:"夹江县"},{code:"511129",name:"沐川县"},{code:"511132",name:"峨边彝族自治县"},{code:"511133",name:"马边彝族自治县"},{code:"511181",name:"峨眉山市"}],[{code:"511302",name:"顺庆区"},{code:"511303",name:"高坪区"},{code:"511304",name:"嘉陵区"},{code:"511321",name:"南部县"},{code:"511322",name:"营山县"},{code:"511323",name:"蓬安县"},{code:"511324",name:"仪陇县"},{code:"511325",name:"西充县"},{code:"511381",name:"阆中市"}],[{code:"511402",name:"东坡区"},{code:"511403",name:"彭山区"},{code:"511421",name:"仁寿县"},{code:"511423",name:"洪雅县"},{code:"511424",name:"丹棱县"},{code:"511425",name:"青神县"}],[{code:"511502",name:"翠屏区"},{code:"511503",name:"南溪区"},{code:"511521",name:"宜宾县"},{code:"511523",name:"江安县"},{code:"511524",name:"长宁县"},{code:"511525",name:"高县"},{code:"511526",name:"珙县"},{code:"511527",name:"筠连县"},{code:"511528",name:"兴文县"},{code:"511529",name:"屏山县"}],[{code:"511602",name:"广安区"},{code:"511603",name:"前锋区"},{code:"511621",name:"岳池县"},{code:"511622",name:"武胜县"},{code:"511623",name:"邻水县"},{code:"511681",name:"华蓥市"}],[{code:"511702",name:"通川区"},{code:"511703",name:"达川区"},{code:"511722",name:"宣汉县"},{code:"511723",name:"开江县"},{code:"511724",name:"大竹县"},{code:"511725",name:"渠县"},{code:"511781",name:"万源市"}],[{code:"511802",name:"雨城区"},{code:"511803",name:"名山区"},{code:"511822",name:"荥经县"},{code:"511823",name:"汉源县"},{code:"511824",name:"石棉县"},{code:"511825",name:"天全县"},{code:"511826",name:"芦山县"},{code:"511827",name:"宝兴县"}],[{code:"511902",name:"巴州区"},{code:"511903",name:"恩阳区"},{code:"511921",name:"通江县"},{code:"511922",name:"南江县"},{code:"511923",name:"平昌县"}],[{code:"512002",name:"雁江区"},{code:"512021",name:"安岳县"},{code:"512022",name:"乐至县"}],[{code:"513201",name:"马尔康市"},{code:"513221",name:"汶川县"},{code:"513222",name:"理县"},{code:"513223",name:"茂县"},{code:"513224",name:"松潘县"},{code:"513225",name:"九寨沟县"},{code:"513226",name:"金川县"},{code:"513227",name:"小金县"},{code:"513228",name:"黑水县"},{code:"513230",name:"壤塘县"},{code:"513231",name:"阿坝县"},{code:"513232",name:"若尔盖县"},{code:"513233",name:"红原县"}],[{code:"513301",name:"康定市"},{code:"513322",name:"泸定县"},{code:"513323",name:"丹巴县"},{code:"513324",name:"九龙县"},{code:"513325",name:"雅江县"},{code:"513326",name:"道孚县"},{code:"513327",name:"炉霍县"},{code:"513328",name:"甘孜县"},{code:"513329",name:"新龙县"},{code:"513330",name:"德格县"},{code:"513331",name:"白玉县"},{code:"513332",name:"石渠县"},{code:"513333",name:"色达县"},{code:"513334",name:"理塘县"},{code:"513335",name:"巴塘县"},{code:"513336",name:"乡城县"},{code:"513337",name:"稻城县"},{code:"513338",name:"得荣县"}],[{code:"513401",name:"西昌市"},{code:"513402",name:"会理市"},{code:"513422",name:"木里藏族自治县"},{code:"513423",name:"盐源县"},{code:"513424",name:"德昌县"},{code:"513426",name:"会东县"},{code:"513427",name:"宁南县"},{code:"513428",name:"普格县"},{code:"513429",name:"布拖县"},{code:"513430",name:"金阳县"},{code:"513431",name:"昭觉县"},{code:"513432",name:"喜德县"},{code:"513433",name:"冕宁县"},{code:"513434",name:"越西县"},{code:"513435",name:"甘洛县"},{code:"513436",name:"美姑县"},{code:"513437",name:"雷波县"}]],[[{code:"520102",name:"南明区"},{code:"520103",name:"云岩区"},{code:"520111",name:"花溪区"},{code:"520112",name:"乌当区"},{code:"520113",name:"白云区"},{code:"520115",name:"观山湖区"},{code:"520121",name:"开阳县"},{code:"520122",name:"息烽县"},{code:"520123",name:"修文县"},{code:"520181",name:"清镇市"}],[{code:"520201",name:"钟山区"},{code:"520203",name:"六枝特区"},{code:"520221",name:"水城县"},{code:"520281",name:"盘州市"}],[{code:"520302",name:"红花岗区"},{code:"520303",name:"汇川区"},{code:"520304",name:"播州区"},{code:"520322",name:"桐梓县"},{code:"520323",name:"绥阳县"},{code:"520324",name:"正安县"},{code:"520325",name:"道真仡佬族苗族自治县"},{code:"520326",name:"务川仡佬族苗族自治县"},{code:"520327",name:"凤冈县"},{code:"520328",name:"湄潭县"},{code:"520329",name:"余庆县"},{code:"520330",name:"习水县"},{code:"520381",name:"赤水市"},{code:"520382",name:"仁怀市"}],[{code:"520402",name:"西秀区"},{code:"520403",name:"平坝区"},{code:"520422",name:"普定县"},{code:"520423",name:"镇宁布依族苗族自治县"},{code:"520424",name:"关岭布依族苗族自治县"},{code:"520425",name:"紫云苗族布依族自治县"}],[{code:"520502",name:"七星关区"},{code:"520521",name:"大方县"},{code:"520523",name:"金沙县"},{code:"520524",name:"织金县"},{code:"520525",name:"纳雍县"},{code:"520526",name:"威宁彝族回族苗族自治县"},{code:"520527",name:"赫章县"},{code:"520581",name:"黔西市"}],[{code:"520602",name:"碧江区"},{code:"520603",name:"万山区"},{code:"520621",name:"江口县"},{code:"520622",name:"玉屏侗族自治县"},{code:"520623",name:"石阡县"},{code:"520624",name:"思南县"},{code:"520625",name:"印江土家族苗族自治县"},{code:"520626",name:"德江县"},{code:"520627",name:"沿河土家族自治县"},{code:"520628",name:"松桃苗族自治县"}],[{code:"522301",name:"兴义市"},{code:"522322",name:"兴仁县"},{code:"522323",name:"普安县"},{code:"522324",name:"晴隆县"},{code:"522325",name:"贞丰县"},{code:"522326",name:"望谟县"},{code:"522327",name:"册亨县"},{code:"522328",name:"安龙县"}],[{code:"522601",name:"凯里市"},{code:"522622",name:"黄平县"},{code:"522623",name:"施秉县"},{code:"522624",name:"三穗县"},{code:"522625",name:"镇远县"},{code:"522626",name:"岑巩县"},{code:"522627",name:"天柱县"},{code:"522628",name:"锦屏县"},{code:"522629",name:"剑河县"},{code:"522630",name:"台江县"},{code:"522631",name:"黎平县"},{code:"522632",name:"榕江县"},{code:"522633",name:"从江县"},{code:"522634",name:"雷山县"},{code:"522635",name:"麻江县"},{code:"522636",name:"丹寨县"}],[{code:"522701",name:"都匀市"},{code:"522702",name:"福泉市"},{code:"522722",name:"荔波县"},{code:"522723",name:"贵定县"},{code:"522725",name:"瓮安县"},{code:"522726",name:"独山县"},{code:"522727",name:"平塘县"},{code:"522728",name:"罗甸县"},{code:"522729",name:"长顺县"},{code:"522730",name:"龙里县"},{code:"522731",name:"惠水县"},{code:"522732",name:"三都水族自治县"}]],[[{code:"530102",name:"五华区"},{code:"530103",name:"盘龙区"},{code:"530111",name:"官渡区"},{code:"530112",name:"西山区"},{code:"530113",name:"东川区"},{code:"530114",name:"呈贡区"},{code:"530115",name:"晋宁区"},{code:"530124",name:"富民县"},{code:"530125",name:"宜良县"},{code:"530126",name:"石林彝族自治县"},{code:"530127",name:"嵩明县"},{code:"530128",name:"禄劝彝族苗族自治县"},{code:"530129",name:"寻甸回族彝族自治县"},{code:"530181",name:"安宁市"}],[{code:"530302",name:"麒麟区"},{code:"530303",name:"沾益区"},{code:"530321",name:"马龙县"},{code:"530322",name:"陆良县"},{code:"530323",name:"师宗县"},{code:"530324",name:"罗平县"},{code:"530325",name:"富源县"},{code:"530326",name:"会泽县"},{code:"530381",name:"宣威市"}],[{code:"530402",name:"红塔区"},{code:"530403",name:"江川区"},{code:"530422",name:"澄江县"},{code:"530423",name:"通海县"},{code:"530424",name:"华宁县"},{code:"530425",name:"易门县"},{code:"530426",name:"峨山彝族自治县"},{code:"530427",name:"新平彝族傣族自治县"},{code:"530428",name:"元江哈尼族彝族傣族自治县"}],[{code:"530502",name:"隆阳区"},{code:"530521",name:"施甸县"},{code:"530523",name:"龙陵县"},{code:"530524",name:"昌宁县"},{code:"530581",name:"腾冲市"}],[{code:"530602",name:"昭阳区"},{code:"530621",name:"鲁甸县"},{code:"530622",name:"巧家县"},{code:"530623",name:"盐津县"},{code:"530624",name:"大关县"},{code:"530625",name:"永善县"},{code:"530626",name:"绥江县"},{code:"530627",name:"镇雄县"},{code:"530628",name:"彝良县"},{code:"530629",name:"威信县"},{code:"530630",name:"水富县"}],[{code:"530702",name:"古城区"},{code:"530721",name:"玉龙纳西族自治县"},{code:"530722",name:"永胜县"},{code:"530723",name:"华坪县"},{code:"530724",name:"宁蒗彝族自治县"}],[{code:"530802",name:"思茅区"},{code:"530821",name:"宁洱哈尼族彝族自治县"},{code:"530822",name:"墨江哈尼族自治县"},{code:"530823",name:"景东彝族自治县"},{code:"530824",name:"景谷傣族彝族自治县"},{code:"530825",name:"镇沅彝族哈尼族拉祜族自治县"},{code:"530826",name:"江城哈尼族彝族自治县"},{code:"530827",name:"孟连傣族拉祜族佤族自治县"},{code:"530828",name:"澜沧拉祜族自治县"},{code:"530829",name:"西盟佤族自治县"}],[{code:"530902",name:"临翔区"},{code:"530921",name:"凤庆县"},{code:"530922",name:"云县"},{code:"530923",name:"永德县"},{code:"530924",name:"镇康县"},{code:"530925",name:"双江拉祜族佤族布朗族傣族自治县"},{code:"530926",name:"耿马傣族佤族自治县"},{code:"530927",name:"沧源佤族自治县"}],[{code:"532301",name:"楚雄市"},{code:"532302",name:"禄丰市"},{code:"532322",name:"双柏县"},{code:"532323",name:"牟定县"},{code:"532324",name:"南华县"},{code:"532325",name:"姚安县"},{code:"532326",name:"大姚县"},{code:"532327",name:"永仁县"},{code:"532328",name:"元谋县"},{code:"532329",name:"武定县"}],[{code:"532501",name:"个旧市"},{code:"532502",name:"开远市"},{code:"532503",name:"蒙自市"},{code:"532504",name:"弥勒市"},{code:"532523",name:"屏边苗族自治县"},{code:"532524",name:"建水县"},{code:"532525",name:"石屏县"},{code:"532527",name:"泸西县"},{code:"532528",name:"元阳县"},{code:"532529",name:"红河县"},{code:"532530",name:"金平苗族瑶族傣族自治县"},{code:"532531",name:"绿春县"},{code:"532532",name:"河口瑶族自治县"}],[{code:"532601",name:"文山市"},{code:"532622",name:"砚山县"},{code:"532623",name:"西畴县"},{code:"532624",name:"麻栗坡县"},{code:"532625",name:"马关县"},{code:"532626",name:"丘北县"},{code:"532627",name:"广南县"},{code:"532628",name:"富宁县"}],[{code:"532801",name:"景洪市"},{code:"532822",name:"勐海县"},{code:"532823",name:"勐腊县"}],[{code:"532901",name:"大理市"},{code:"532922",name:"漾濞彝族自治县"},{code:"532923",name:"祥云县"},{code:"532924",name:"宾川县"},{code:"532925",name:"弥渡县"},{code:"532926",name:"南涧彝族自治县"},{code:"532927",name:"巍山彝族回族自治县"},{code:"532928",name:"永平县"},{code:"532929",name:"云龙县"},{code:"532930",name:"洱源县"},{code:"532931",name:"剑川县"},{code:"532932",name:"鹤庆县"}],[{code:"533102",name:"瑞丽市"},{code:"533103",name:"芒市"},{code:"533122",name:"梁河县"},{code:"533123",name:"盈江县"},{code:"533124",name:"陇川县"}],[{code:"533301",name:"泸水市"},{code:"533323",name:"福贡县"},{code:"533324",name:"贡山独龙族怒族自治县"},{code:"533325",name:"兰坪白族普米族自治县"}],[{code:"533401",name:"香格里拉市"},{code:"533422",name:"德钦县"},{code:"533423",name:"维西傈僳族自治县"}]],[[{code:"540102",name:"城关区"},{code:"540103",name:"堆龙德庆区"},{code:"540104",name:"达孜区"},{code:"540121",name:"林周县"},{code:"540122",name:"当雄县"},{code:"540123",name:"尼木县"},{code:"540124",name:"曲水县"},{code:"540127",name:"墨竹工卡县"}],[{code:"540202",name:"桑珠孜区"},{code:"540221",name:"南木林县"},{code:"540222",name:"江孜县"},{code:"540223",name:"定日县"},{code:"540224",name:"萨迦县"},{code:"540225",name:"拉孜县"},{code:"540226",name:"昂仁县"},{code:"540227",name:"谢通门县"},{code:"540228",name:"白朗县"},{code:"540229",name:"仁布县"},{code:"540230",name:"康马县"},{code:"540231",name:"定结县"},{code:"540232",name:"仲巴县"},{code:"540233",name:"亚东县"},{code:"540234",name:"吉隆县"},{code:"540235",name:"聂拉木县"},{code:"540236",name:"萨嘎县"},{code:"540237",name:"岗巴县"}],[{code:"540302",name:"卡若区"},{code:"540321",name:"江达县"},{code:"540322",name:"贡觉县"},{code:"540323",name:"类乌齐县"},{code:"540324",name:"丁青县"},{code:"540325",name:"察雅县"},{code:"540326",name:"八宿县"},{code:"540327",name:"左贡县"},{code:"540328",name:"芒康县"},{code:"540329",name:"洛隆县"},{code:"540330",name:"边坝县"}],[{code:"540402",name:"巴宜区"},{code:"540421",name:"工布江达县"},{code:"540422",name:"米林县"},{code:"540423",name:"墨脱县"},{code:"540424",name:"波密县"},{code:"540425",name:"察隅县"},{code:"540426",name:"朗县"}],[{code:"540502",name:"乃东区"},{code:"540521",name:"扎囊县"},{code:"540522",name:"贡嘎县"},{code:"540523",name:"桑日县"},{code:"540524",name:"琼结县"},{code:"540525",name:"曲松县"},{code:"540526",name:"措美县"},{code:"540527",name:"洛扎县"},{code:"540528",name:"加查县"},{code:"540529",name:"隆子县"},{code:"540530",name:"错那县"},{code:"540531",name:"浪卡子县"}],[{code:"542421",name:"那曲县"},{code:"542422",name:"嘉黎县"},{code:"542423",name:"比如县"},{code:"542424",name:"聂荣县"},{code:"542425",name:"安多县"},{code:"542426",name:"申扎县"},{code:"542427",name:"索县"},{code:"542428",name:"班戈县"},{code:"542429",name:"巴青县"},{code:"542430",name:"尼玛县"},{code:"542431",name:"双湖县"}],[{code:"542521",name:"普兰县"},{code:"542522",name:"札达县"},{code:"542523",name:"噶尔县"},{code:"542524",name:"日土县"},{code:"542525",name:"革吉县"},{code:"542526",name:"改则县"},{code:"542527",name:"措勤县"}]],[[{code:"610102",name:"新城区"},{code:"610103",name:"碑林区"},{code:"610104",name:"莲湖区"},{code:"610111",name:"灞桥区"},{code:"610112",name:"未央区"},{code:"610113",name:"雁塔区"},{code:"610114",name:"阎良区"},{code:"610115",name:"临潼区"},{code:"610116",name:"长安区"},{code:"610117",name:"高陵区"},{code:"610118",name:"鄠邑区"},{code:"610122",name:"蓝田县"},{code:"610124",name:"周至县"}],[{code:"610202",name:"王益区"},{code:"610203",name:"印台区"},{code:"610204",name:"耀州区"},{code:"610222",name:"宜君县"}],[{code:"610302",name:"渭滨区"},{code:"610303",name:"金台区"},{code:"610304",name:"陈仓区"},{code:"610305",name:"凤翔区"},{code:"610323",name:"岐山县"},{code:"610324",name:"扶风县"},{code:"610326",name:"眉县"},{code:"610327",name:"陇县"},{code:"610328",name:"千阳县"},{code:"610329",name:"麟游县"},{code:"610330",name:"凤县"},{code:"610331",name:"太白县"}],[{code:"610402",name:"秦都区"},{code:"610403",name:"杨陵区"},{code:"610404",name:"渭城区"},{code:"610422",name:"三原县"},{code:"610423",name:"泾阳县"},{code:"610424",name:"乾县"},{code:"610425",name:"礼泉县"},{code:"610426",name:"永寿县"},{code:"610427",name:"彬州市"},{code:"610428",name:"长武县"},{code:"610429",name:"旬邑县"},{code:"610430",name:"淳化县"},{code:"610431",name:"武功县"},{code:"610481",name:"兴平市"}],[{code:"610502",name:"临渭区"},{code:"610503",name:"华州区"},{code:"610522",name:"潼关县"},{code:"610523",name:"大荔县"},{code:"610524",name:"合阳县"},{code:"610525",name:"澄城县"},{code:"610526",name:"蒲城县"},{code:"610527",name:"白水县"},{code:"610528",name:"富平县"},{code:"610581",name:"韩城市"},{code:"610582",name:"华阴市"}],[{code:"610602",name:"宝塔区"},{code:"610603",name:"安塞区"},{code:"610621",name:"延长县"},{code:"610622",name:"延川县"},{code:"610623",name:"子长县"},{code:"610625",name:"志丹县"},{code:"610626",name:"吴起县"},{code:"610627",name:"甘泉县"},{code:"610628",name:"富县"},{code:"610629",name:"洛川县"},{code:"610630",name:"宜川县"},{code:"610631",name:"黄龙县"},{code:"610632",name:"黄陵县"}],[{code:"610702",name:"汉台区"},{code:"610703",name:"南郑区"},{code:"610722",name:"城固县"},{code:"610723",name:"洋县"},{code:"610724",name:"西乡县"},{code:"610725",name:"勉县"},{code:"610726",name:"宁强县"},{code:"610727",name:"略阳县"},{code:"610728",name:"镇巴县"},{code:"610729",name:"留坝县"},{code:"610730",name:"佛坪县"}],[{code:"610802",name:"榆阳区"},{code:"610803",name:"横山区"},{code:"610822",name:"府谷县"},{code:"610824",name:"靖边县"},{code:"610825",name:"定边县"},{code:"610826",name:"绥德县"},{code:"610827",name:"米脂县"},{code:"610828",name:"佳县"},{code:"610829",name:"吴堡县"},{code:"610830",name:"清涧县"},{code:"610831",name:"子洲县"},{code:"610881",name:"神木市"}],[{code:"610902",name:"汉滨区"},{code:"610921",name:"汉阴县"},{code:"610922",name:"石泉县"},{code:"610923",name:"宁陕县"},{code:"610924",name:"紫阳县"},{code:"610925",name:"岚皋县"},{code:"610926",name:"平利县"},{code:"610927",name:"镇坪县"},{code:"610929",name:"白河县"},{code:"610981",name:"旬阳市"}],[{code:"611002",name:"商州区"},{code:"611021",name:"洛南县"},{code:"611022",name:"丹凤县"},{code:"611023",name:"商南县"},{code:"611024",name:"山阳县"},{code:"611025",name:"镇安县"},{code:"611026",name:"柞水县"}]],[[{code:"620102",name:"城关区"},{code:"620103",name:"七里河区"},{code:"620104",name:"西固区"},{code:"620105",name:"安宁区"},{code:"620111",name:"红古区"},{code:"620121",name:"永登县"},{code:"620122",name:"皋兰县"},{code:"620123",name:"榆中县"}],[{code:"620201",name:"雄关区"},{code:"620202",name:"镜铁区"},{code:"620203",name:"长城区"}],[{code:"620302",name:"金川区"},{code:"620321",name:"永昌县"}],[{code:"620402",name:"白银区"},{code:"620403",name:"平川区"},{code:"620421",name:"靖远县"},{code:"620422",name:"会宁县"},{code:"620423",name:"景泰县"}],[{code:"620502",name:"秦州区"},{code:"620503",name:"麦积区"},{code:"620521",name:"清水县"},{code:"620522",name:"秦安县"},{code:"620523",name:"甘谷县"},{code:"620524",name:"武山县"},{code:"620525",name:"张家川回族自治县"}],[{code:"620602",name:"凉州区"},{code:"620621",name:"民勤县"},{code:"620622",name:"古浪县"},{code:"620623",name:"天祝藏族自治县"}],[{code:"620702",name:"甘州区"},{code:"620721",name:"肃南裕固族自治县"},{code:"620722",name:"民乐县"},{code:"620723",name:"临泽县"},{code:"620724",name:"高台县"},{code:"620725",name:"山丹县"}],[{code:"620802",name:"崆峒区"},{code:"620821",name:"泾川县"},{code:"620822",name:"灵台县"},{code:"620823",name:"崇信县"},{code:"620824",name:"华亭县"},{code:"620825",name:"庄浪县"},{code:"620826",name:"静宁县"}],[{code:"620902",name:"肃州区"},{code:"620921",name:"金塔县"},{code:"620922",name:"瓜州县"},{code:"620923",name:"肃北蒙古族自治县"},{code:"620924",name:"阿克塞哈萨克族自治县"},{code:"620981",name:"玉门市"},{code:"620982",name:"敦煌市"}],[{code:"621002",name:"西峰区"},{code:"621021",name:"庆城县"},{code:"621022",name:"环县"},{code:"621023",name:"华池县"},{code:"621024",name:"合水县"},{code:"621025",name:"正宁县"},{code:"621026",name:"宁县"},{code:"621027",name:"镇原县"}],[{code:"621102",name:"安定区"},{code:"621121",name:"通渭县"},{code:"621122",name:"陇西县"},{code:"621123",name:"渭源县"},{code:"621124",name:"临洮县"},{code:"621125",name:"漳县"},{code:"621126",name:"岷县"}],[{code:"621202",name:"武都区"},{code:"621221",name:"成县"},{code:"621222",name:"文县"},{code:"621223",name:"宕昌县"},{code:"621224",name:"康县"},{code:"621225",name:"西和县"},{code:"621226",name:"礼县"},{code:"621227",name:"徽县"},{code:"621228",name:"两当县"}],[{code:"622901",name:"临夏市"},{code:"622921",name:"临夏县"},{code:"622922",name:"康乐县"},{code:"622923",name:"永靖县"},{code:"622924",name:"广河县"},{code:"622925",name:"和政县"},{code:"622926",name:"东乡族自治县"},{code:"622927",name:"积石山保安族东乡族撒拉族自治县"}],[{code:"623001",name:"合作市"},{code:"623021",name:"临潭县"},{code:"623022",name:"卓尼县"},{code:"623023",name:"舟曲县"},{code:"623024",name:"迭部县"},{code:"623025",name:"玛曲县"},{code:"623026",name:"碌曲县"},{code:"623027",name:"夏河县"}]],[[{code:"630102",name:"城东区"},{code:"630103",name:"城中区"},{code:"630104",name:"城西区"},{code:"630105",name:"城北区"},{code:"630121",name:"大通回族土族自治县"},{code:"630122",name:"湟中县"},{code:"630123",name:"湟源县"}],[{code:"630202",name:"乐都区"},{code:"630203",name:"平安区"},{code:"630222",name:"民和回族土族自治县"},{code:"630223",name:"互助土族自治县"},{code:"630224",name:"化隆回族自治县"},{code:"630225",name:"循化撒拉族自治县"}],[{code:"632221",name:"门源回族自治县"},{code:"632222",name:"祁连县"},{code:"632223",name:"海晏县"},{code:"632224",name:"刚察县"}],[{code:"632321",name:"同仁县"},{code:"632322",name:"尖扎县"},{code:"632323",name:"泽库县"},{code:"632324",name:"河南蒙古族自治县"}],[{code:"632521",name:"共和县"},{code:"632522",name:"同德县"},{code:"632523",name:"贵德县"},{code:"632524",name:"兴海县"},{code:"632525",name:"贵南县"}],[{code:"632621",name:"玛沁县"},{code:"632622",name:"班玛县"},{code:"632623",name:"甘德县"},{code:"632624",name:"达日县"},{code:"632625",name:"久治县"},{code:"632626",name:"玛多县"}],[{code:"632701",name:"玉树市"},{code:"632722",name:"杂多县"},{code:"632723",name:"称多县"},{code:"632724",name:"治多县"},{code:"632725",name:"囊谦县"},{code:"632726",name:"曲麻莱县"}],[{code:"632801",name:"格尔木市"},{code:"632802",name:"德令哈市"},{code:"632821",name:"乌兰县"},{code:"632822",name:"都兰县"},{code:"632823",name:"天峻县"},{code:"632824",name:"冷湖行政委员会"},{code:"632825",name:"大柴旦行政委员会"},{code:"632826",name:"茫崖行政委员会"}]],[[{code:"640104",name:"兴庆区"},{code:"640105",name:"西夏区"},{code:"640106",name:"金凤区"},{code:"640121",name:"永宁县"},{code:"640122",name:"贺兰县"},{code:"640181",name:"灵武市"}],[{code:"640202",name:"大武口区"},{code:"640205",name:"惠农区"},{code:"640221",name:"平罗县"}],[{code:"640302",name:"利通区"},{code:"640303",name:"红寺堡区"},{code:"640323",name:"盐池县"},{code:"640324",name:"同心县"},{code:"640381",name:"青铜峡市"}],[{code:"640402",name:"原州区"},{code:"640422",name:"西吉县"},{code:"640423",name:"隆德县"},{code:"640424",name:"泾源县"},{code:"640425",name:"彭阳县"}],[{code:"640502",name:"沙坡头区"},{code:"640521",name:"中宁县"},{code:"640522",name:"海原县"}]],[[{code:"650102",name:"天山区"},{code:"650103",name:"沙依巴克区"},{code:"650104",name:"新市区"},{code:"650105",name:"水磨沟区"},{code:"650106",name:"头屯河区"},{code:"650107",name:"达坂城区"},{code:"650109",name:"米东区"},{code:"650121",name:"乌鲁木齐县"}],[{code:"650202",name:"独山子区"},{code:"650203",name:"克拉玛依区"},{code:"650204",name:"白碱滩区"},{code:"650205",name:"乌尔禾区"}],[{code:"650402",name:"高昌区"},{code:"650421",name:"鄯善县"},{code:"650422",name:"托克逊县"}],[{code:"650502",name:"伊州区"},{code:"650521",name:"巴里坤哈萨克自治县"},{code:"650522",name:"伊吾县"}],[{code:"652301",name:"昌吉市"},{code:"652302",name:"阜康市"},{code:"652323",name:"呼图壁县"},{code:"652324",name:"玛纳斯县"},{code:"652325",name:"奇台县"},{code:"652327",name:"吉木萨尔县"},{code:"652328",name:"木垒哈萨克自治县"}],[{code:"652701",name:"博乐市"},{code:"652702",name:"阿拉山口市"},{code:"652722",name:"精河县"},{code:"652723",name:"温泉县"}],[{code:"652801",name:"库尔勒市"},{code:"652822",name:"轮台县"},{code:"652823",name:"尉犁县"},{code:"652824",name:"若羌县"},{code:"652825",name:"且末县"},{code:"652826",name:"焉耆回族自治县"},{code:"652827",name:"和静县"},{code:"652828",name:"和硕县"},{code:"652829",name:"博湖县"}],[{code:"652901",name:"阿克苏市"},{code:"652922",name:"温宿县"},{code:"652923",name:"库车县"},{code:"652924",name:"沙雅县"},{code:"652925",name:"新和县"},{code:"652926",name:"拜城县"},{code:"652927",name:"乌什县"},{code:"652928",name:"阿瓦提县"},{code:"652929",name:"柯坪县"}],[{code:"653001",name:"阿图什市"},{code:"653022",name:"阿克陶县"},{code:"653023",name:"阿合奇县"},{code:"653024",name:"乌恰县"}],[{code:"653101",name:"喀什市"},{code:"653121",name:"疏附县"},{code:"653122",name:"疏勒县"},{code:"653123",name:"英吉沙县"},{code:"653124",name:"泽普县"},{code:"653125",name:"莎车县"},{code:"653126",name:"叶城县"},{code:"653127",name:"麦盖提县"},{code:"653128",name:"岳普湖县"},{code:"653129",name:"伽师县"},{code:"653130",name:"巴楚县"},{code:"653131",name:"塔什库尔干塔吉克自治县"}],[{code:"653201",name:"和田市"},{code:"653221",name:"和田县"},{code:"653222",name:"墨玉县"},{code:"653223",name:"皮山县"},{code:"653224",name:"洛浦县"},{code:"653225",name:"策勒县"},{code:"653226",name:"于田县"},{code:"653227",name:"民丰县"}],[{code:"654002",name:"伊宁市"},{code:"654003",name:"奎屯市"},{code:"654004",name:"霍尔果斯市"},{code:"654021",name:"伊宁县"},{code:"654022",name:"察布查尔锡伯自治县"},{code:"654023",name:"霍城县"},{code:"654024",name:"巩留县"},{code:"654025",name:"新源县"},{code:"654026",name:"昭苏县"},{code:"654027",name:"特克斯县"},{code:"654028",name:"尼勒克县"}],[{code:"654201",name:"塔城市"},{code:"654202",name:"乌苏市"},{code:"654203",name:"沙湾市"},{code:"654221",name:"额敏县"},{code:"654224",name:"托里县"},{code:"654225",name:"裕民县"},{code:"654226",name:"和布克赛尔蒙古自治县"}],[{code:"654301",name:"阿勒泰市"},{code:"654321",name:"布尔津县"},{code:"654322",name:"富蕴县"},{code:"654323",name:"福海县"},{code:"654324",name:"哈巴河县"},{code:"654325",name:"青河县"},{code:"654326",name:"吉木乃县"}],[{code:"659001",name:"石河子市"},{code:"659002",name:"阿拉尔市"},{code:"659003",name:"图木舒克市"},{code:"659004",name:"五家渠市"},{code:"659005",name:"北屯市"},{code:"659006",name:"铁门关市"},{code:"659007",name:"双河市"},{code:"659008",name:"可克达拉市"},{code:"659009",name:"昆玉市"}]],[[{code:"710101",name:"中正区"},{code:"710102",name:"大同区"},{code:"710103",name:"中山区"},{code:"710104",name:"松山区"},{code:"710105",name:"大安区"},{code:"710106",name:"万华区"},{code:"710107",name:"信义区"},{code:"710108",name:"士林区"},{code:"710109",name:"北投区"},{code:"710110",name:"内湖区"},{code:"710111",name:"南港区"},{code:"710112",name:"文山区"}],[{code:"710201",name:"新兴区"},{code:"710202",name:"前金区"},{code:"710203",name:"苓雅区"},{code:"710204",name:"盐埕区"},{code:"710205",name:"鼓山区"},{code:"710206",name:"旗津区"},{code:"710207",name:"前镇区"},{code:"710208",name:"三民区"},{code:"710209",name:"左营区"},{code:"710210",name:"楠梓区"},{code:"710211",name:"小港区"},{code:"710242",name:"仁武区"},{code:"710243",name:"大社区"},{code:"710244",name:"冈山区"},{code:"710245",name:"路竹区"},{code:"710246",name:"阿莲区"},{code:"710247",name:"田寮区"},{code:"710248",name:"燕巢区"},{code:"710249",name:"桥头区"},{code:"710250",name:"梓官区"},{code:"710251",name:"弥陀区"},{code:"710252",name:"永安区"},{code:"710253",name:"湖内区"},{code:"710254",name:"凤山区"},{code:"710255",name:"大寮区"},{code:"710256",name:"林园区"},{code:"710257",name:"鸟松区"},{code:"710258",name:"大树区"},{code:"710259",name:"旗山区"},{code:"710260",name:"美浓区"},{code:"710261",name:"六龟区"},{code:"710262",name:"内门区"},{code:"710263",name:"杉林区"},{code:"710264",name:"甲仙区"},{code:"710265",name:"桃源区"},{code:"710266",name:"那玛夏区"},{code:"710267",name:"茂林区"},{code:"710268",name:"茄萣区"}],[{code:"710301",name:"中西区"},{code:"710302",name:"东区"},{code:"710303",name:"南区"},{code:"710304",name:"北区"},{code:"710305",name:"安平区"},{code:"710306",name:"安南区"},{code:"710339",name:"永康区"},{code:"710340",name:"归仁区"},{code:"710341",name:"新化区"},{code:"710342",name:"左镇区"},{code:"710343",name:"玉井区"},{code:"710344",name:"楠西区"},{code:"710345",name:"南化区"},{code:"710346",name:"仁德区"},{code:"710347",name:"关庙区"},{code:"710348",name:"龙崎区"},{code:"710349",name:"官田区"},{code:"710350",name:"麻豆区"},{code:"710351",name:"佳里区"},{code:"710352",name:"西港区"},{code:"710353",name:"七股区"},{code:"710354",name:"将军区"},{code:"710355",name:"学甲区"},{code:"710356",name:"北门区"},{code:"710357",name:"新营区"},{code:"710358",name:"后壁区"},{code:"710359",name:"白河区"},{code:"710360",name:"东山区"},{code:"710361",name:"六甲区"},{code:"710362",name:"下营区"},{code:"710363",name:"柳营区"},{code:"710364",name:"盐水区"},{code:"710365",name:"善化区"},{code:"710366",name:"大内区"},{code:"710367",name:"山上区"},{code:"710368",name:"新市区"},{code:"710369",name:"安定区"}],[{code:"710401",name:"中区"},{code:"710402",name:"东区"},{code:"710403",name:"南区"},{code:"710404",name:"西区"},{code:"710405",name:"北区"},{code:"710406",name:"北屯区"},{code:"710407",name:"西屯区"},{code:"710408",name:"南屯区"},{code:"710431",name:"太平区"},{code:"710432",name:"大里区"},{code:"710433",name:"雾峰区"},{code:"710434",name:"乌日区"},{code:"710435",name:"丰原区"},{code:"710436",name:"后里区"},{code:"710437",name:"石冈区"},{code:"710438",name:"东势区"},{code:"710439",name:"和平区"},{code:"710440",name:"新社区"},{code:"710441",name:"潭子区"},{code:"710442",name:"大雅区"},{code:"710443",name:"神冈区"},{code:"710444",name:"大肚区"},{code:"710445",name:"沙鹿区"},{code:"710446",name:"龙井区"},{code:"710447",name:"梧栖区"},{code:"710448",name:"清水区"},{code:"710449",name:"大甲区"},{code:"710450",name:"外埔区"},{code:"710451",name:"大安区"}],[{code:"710614",name:"南投市"},{code:"710615",name:"中寮乡"},{code:"710616",name:"草屯镇"},{code:"710617",name:"国姓乡"},{code:"710618",name:"埔里镇"},{code:"710619",name:"仁爱乡"},{code:"710620",name:"名间乡"},{code:"710621",name:"集集镇"},{code:"710622",name:"水里乡"},{code:"710623",name:"鱼池乡"},{code:"710624",name:"信义乡"},{code:"710625",name:"竹山镇"},{code:"710626",name:"鹿谷乡"}],[{code:"710701",name:"仁爱区"},{code:"710702",name:"信义区"},{code:"710703",name:"中正区"},{code:"710704",name:"中山区"},{code:"710705",name:"安乐区"},{code:"710706",name:"暖暖区"},{code:"710707",name:"七堵区"}],[{code:"710801",name:"东区"},{code:"710802",name:"北区"},{code:"710803",name:"香山区"}],[{code:"710901",name:"东区"},{code:"710902",name:"西区"}],[{code:"711130",name:"万里区"},{code:"711131",name:"金山区"},{code:"711132",name:"板桥区"},{code:"711133",name:"汐止区"},{code:"711134",name:"深坑区"},{code:"711135",name:"石碇区"},{code:"711136",name:"瑞芳区"},{code:"711137",name:"平溪区"},{code:"711138",name:"双溪区"},{code:"711139",name:"贡寮区"},{code:"711140",name:"新店区"},{code:"711141",name:"坪林区"},{code:"711142",name:"乌来区"},{code:"711143",name:"永和区"},{code:"711144",name:"中和区"},{code:"711145",name:"土城区"},{code:"711146",name:"三峡区"},{code:"711147",name:"树林区"},{code:"711148",name:"莺歌区"},{code:"711149",name:"三重区"},{code:"711150",name:"新庄区"},{code:"711151",name:"泰山区"},{code:"711152",name:"林口区"},{code:"711153",name:"芦洲区"},{code:"711154",name:"五股区"},{code:"711155",name:"八里区"},{code:"711156",name:"淡水区"},{code:"711157",name:"三芝区"},{code:"711158",name:"石门区"}],[{code:"711214",name:"宜兰市"},{code:"711215",name:"头城镇"},{code:"711216",name:"礁溪乡"},{code:"711217",name:"壮围乡"},{code:"711218",name:"员山乡"},{code:"711219",name:"罗东镇"},{code:"711220",name:"三星乡"},{code:"711221",name:"大同乡"},{code:"711222",name:"五结乡"},{code:"711223",name:"冬山乡"},{code:"711224",name:"苏澳镇"},{code:"711225",name:"南澳乡"}],[{code:"711314",name:"竹北市"},{code:"711315",name:"湖口乡"},{code:"711316",name:"新丰乡"},{code:"711317",name:"新埔镇"},{code:"711318",name:"关西镇"},{code:"711319",name:"芎林乡"},{code:"711320",name:"宝山乡"},{code:"711321",name:"竹东镇"},{code:"711322",name:"五峰乡"},{code:"711323",name:"横山乡"},{code:"711324",name:"尖石乡"},{code:"711325",name:"北埔乡"},{code:"711326",name:"峨眉乡"}],[{code:"711414",name:"中坜区"},{code:"711415",name:"平镇区"},{code:"711416",name:"龙潭区"},{code:"711417",name:"杨梅区"},{code:"711418",name:"新屋区"},{code:"711419",name:"观音区"},{code:"711420",name:"桃园区"},{code:"711421",name:"龟山区"},{code:"711422",name:"八德区"},{code:"711423",name:"大溪区"},{code:"711424",name:"复兴区"},{code:"711425",name:"大园区"},{code:"711426",name:"芦竹区"}],[{code:"711519",name:"竹南镇"},{code:"711520",name:"头份市"},{code:"711521",name:"三湾乡"},{code:"711522",name:"南庄乡"},{code:"711523",name:"狮潭乡"},{code:"711524",name:"后龙镇"},{code:"711525",name:"通霄镇"},{code:"711526",name:"苑里镇"},{code:"711527",name:"苗栗市"},{code:"711528",name:"造桥乡"},{code:"711529",name:"头屋乡"},{code:"711530",name:"公馆乡"},{code:"711531",name:"大湖乡"},{code:"711532",name:"泰安乡"},{code:"711533",name:"铜锣乡"},{code:"711534",name:"三义乡"},{code:"711535",name:"西湖乡"},{code:"711536",name:"卓兰镇"}],[{code:"711727",name:"彰化市"},{code:"711728",name:"芬园乡"},{code:"711729",name:"花坛乡"},{code:"711730",name:"秀水乡"},{code:"711731",name:"鹿港镇"},{code:"711732",name:"福兴乡"},{code:"711733",name:"线西乡"},{code:"711734",name:"和美镇"},{code:"711735",name:"伸港乡"},{code:"711736",name:"员林市"},{code:"711737",name:"社头乡"},{code:"711738",name:"永靖乡"},{code:"711739",name:"埔心乡"},{code:"711740",name:"溪湖镇"},{code:"711741",name:"大村乡"},{code:"711742",name:"埔盐乡"},{code:"711743",name:"田中镇"},{code:"711744",name:"北斗镇"},{code:"711745",name:"田尾乡"},{code:"711746",name:"埤头乡"},{code:"711747",name:"溪州乡"},{code:"711748",name:"竹塘乡"},{code:"711749",name:"二林镇"},{code:"711750",name:"大城乡"},{code:"711751",name:"芳苑乡"},{code:"711752",name:"二水乡"}],[{code:"711919",name:"番路乡"},{code:"711920",name:"梅山乡"},{code:"711921",name:"竹崎乡"},{code:"711922",name:"阿里山乡"},{code:"711923",name:"中埔乡"},{code:"711924",name:"大埔乡"},{code:"711925",name:"水上乡"},{code:"711926",name:"鹿草乡"},{code:"711927",name:"太保市"},{code:"711928",name:"朴子市"},{code:"711929",name:"东石乡"},{code:"711930",name:"六脚乡"},{code:"711931",name:"新港乡"},{code:"711932",name:"民雄乡"},{code:"711933",name:"大林镇"},{code:"711934",name:"溪口乡"},{code:"711935",name:"义竹乡"},{code:"711936",name:"布袋镇"}],[{code:"712121",name:"斗南镇"},{code:"712122",name:"大埤乡"},{code:"712123",name:"虎尾镇"},{code:"712124",name:"土库镇"},{code:"712125",name:"褒忠乡"},{code:"712126",name:"东势乡"},{code:"712127",name:"台西乡"},{code:"712128",name:"仑背乡"},{code:"712129",name:"麦寮乡"},{code:"712130",name:"斗六市"},{code:"712131",name:"林内乡"},{code:"712132",name:"古坑乡"},{code:"712133",name:"莿桐乡"},{code:"712134",name:"西螺镇"},{code:"712135",name:"二仑乡"},{code:"712136",name:"北港镇"},{code:"712137",name:"水林乡"},{code:"712138",name:"口湖乡"},{code:"712139",name:"四湖乡"},{code:"712140",name:"元长乡"}],[{code:"712434",name:"屏东市"},{code:"712435",name:"三地门乡"},{code:"712436",name:"雾台乡"},{code:"712437",name:"玛家乡"},{code:"712438",name:"九如乡"},{code:"712439",name:"里港乡"},{code:"712440",name:"高树乡"},{code:"712441",name:"盐埔乡"},{code:"712442",name:"长治乡"},{code:"712443",name:"麟洛乡"},{code:"712444",name:"竹田乡"},{code:"712445",name:"内埔乡"},{code:"712446",name:"万丹乡"},{code:"712447",name:"潮州镇"},{code:"712448",name:"泰武乡"},{code:"712449",name:"来义乡"},{code:"712450",name:"万峦乡"},{code:"712451",name:"崁顶乡"},{code:"712452",name:"新埤乡"},{code:"712453",name:"南州乡"},{code:"712454",name:"林边乡"},{code:"712455",name:"东港镇"},{code:"712456",name:"琉球乡"},{code:"712457",name:"佳冬乡"},{code:"712458",name:"新园乡"},{code:"712459",name:"枋寮乡"},{code:"712460",name:"枋山乡"},{code:"712461",name:"春日乡"},{code:"712462",name:"狮子乡"},{code:"712463",name:"车城乡"},{code:"712464",name:"牡丹乡"},{code:"712465",name:"恒春镇"},{code:"712466",name:"满州乡"}],[{code:"712517",name:"台东市"},{code:"712518",name:"绿岛乡"},{code:"712519",name:"兰屿乡"},{code:"712520",name:"延平乡"},{code:"712521",name:"卑南乡"},{code:"712522",name:"鹿野乡"},{code:"712523",name:"关山镇"},{code:"712524",name:"海端乡"},{code:"712525",name:"池上乡"},{code:"712526",name:"东河乡"},{code:"712527",name:"成功镇"},{code:"712528",name:"长滨乡"},{code:"712529",name:"金峰乡"},{code:"712530",name:"大武乡"},{code:"712531",name:"达仁乡"},{code:"712532",name:"太麻里乡"}],[{code:"712615",name:"花莲市"},{code:"712616",name:"新城乡"},{code:"712618",name:"秀林乡"},{code:"712619",name:"吉安乡"},{code:"712620",name:"寿丰乡"},{code:"712621",name:"凤林镇"},{code:"712622",name:"光复乡"},{code:"712623",name:"丰滨乡"},{code:"712624",name:"瑞穗乡"},{code:"712625",name:"万荣乡"},{code:"712626",name:"玉里镇"},{code:"712627",name:"卓溪乡"},{code:"712628",name:"富里乡"}],[{code:"712707",name:"马公市"},{code:"712708",name:"西屿乡"},{code:"712709",name:"望安乡"},{code:"712710",name:"七美乡"},{code:"712711",name:"白沙乡"},{code:"712712",name:"湖西乡"}]],[[{code:"810101",name:"中西区"},{code:"810102",name:"东区"},{code:"810103",name:"九龙城区"},{code:"810104",name:"观塘区"},{code:"810105",name:"南区"},{code:"810106",name:"深水埗区"},{code:"810107",name:"湾仔区"},{code:"810108",name:"黄大仙区"},{code:"810109",name:"油尖旺区"},{code:"810110",name:"离岛区"},{code:"810111",name:"葵青区"},{code:"810112",name:"北区"},{code:"810113",name:"西贡区"},{code:"810114",name:"沙田区"},{code:"810115",name:"屯门区"},{code:"810116",name:"大埔区"},{code:"810117",name:"荃湾区"},{code:"810118",name:"元朗区"}]],[[{code:"820101",name:"澳门半岛"},{code:"820102",name:"凼仔"},{code:"820103",name:"路凼城"},{code:"820104",name:"路环"}]]];const re=G({name:"u-picker",emits:["update:modelValue","input","confirm","cancel"],props:{value:{type:Boolean,default:!1},modelValue:{type:Boolean,default:!1},params:{type:Object,default:()=>({year:!0,month:!0,day:!0,hour:!1,minute:!1,second:!1,province:!0,city:!0,area:!0,timestamp:!0})},range:{type:Array,default:()=>[]},defaultSelector:{type:Array,default:()=>[0]},rangeKey:{type:String,default:""},mode:{type:String,default:"time"},startYear:{type:[String,Number],default:1950},endYear:{type:[String,Number],default:2050},cancelColor:{type:String,default:"#606266"},confirmColor:{type:String,default:"#2979ff"},defaultTime:{type:String,default:""},defaultRegion:{type:Array,default:()=>[]},showTimeTag:{type:Boolean,default:!0},areaCode:{type:Array,default:()=>[]},safeAreaInsetBottom:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:0},title:{type:String,default:""},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认"},blur:{type:[String,Number],default:0}},data:()=>({popupValue:!1,years:[],months:[],days:[],hours:[],minutes:[],seconds:[],year:0,month:0,day:0,hour:0,minute:0,second:0,reset:!1,startDate:"",endDate:"",valueArr:[],provinces:de,citys:me[0],areas:te[0][0],province:0,city:0,area:0,moving:!1}),mounted(){this.init()},computed:{valueCom(){return this.modelValue},propsChange(){return`${this.mode}-${this.defaultTime}-${this.startYear}-${this.endYear}-${this.defaultRegion}-${this.areaCode}`},regionChange(){return`${this.province}-${this.city}`},yearAndMonth(){return`${this.year}-${this.month}`},uZIndex(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},watch:{propsChange(){this.reset=!0,setTimeout((()=>this.init()),10)},regionChange(e){this.citys=me[this.province],this.areas=te[this.province][this.city]},yearAndMonth(e){this.params.year&&this.setDays()},valueCom(e){e&&(this.reset=!0,setTimeout((()=>this.init()),10)),this.popupValue=e}},methods:{pickstart(){},pickend(){},getItemValue(e,n){if(this.mode==n)return"object"==typeof e?e[this.rangeKey]:e},formatNumber:e=>+e<10?"0"+e:String(e),generateArray:function(e,n){return e=Number(e),n=(n=Number(n))>e?n:e,[...Array(n+1).keys()].slice(e)},getIndex:function(e,n){let a=e.indexOf(n);return~a?a:0},initTimeValue(){let e=this.defaultTime.replace(/\-/g,"/");e=e&&-1==e.indexOf("/")?`2020/01/01 ${e}`:e;let n=null;n=e?new Date(e):new Date,this.year=n.getFullYear(),this.month=Number(n.getMonth())+1,this.day=n.getDate(),this.hour=n.getHours(),this.minute=n.getMinutes(),this.second=n.getSeconds()},init(){this.valueArr=[],this.reset=!1,"time"==this.mode?(this.initTimeValue(),this.params.year&&(this.valueArr.push(0),this.setYears()),this.params.month&&(this.valueArr.push(0),this.setMonths()),this.params.day&&(this.valueArr.push(0),this.setDays()),this.params.hour&&(this.valueArr.push(0),this.setHours()),this.params.minute&&(this.valueArr.push(0),this.setMinutes()),this.params.second&&(this.valueArr.push(0),this.setSeconds())):"region"==this.mode?(this.params.province&&(this.valueArr.push(0),this.setProvinces()),this.params.city&&(this.valueArr.push(0),this.setCitys()),this.params.area&&(this.valueArr.push(0),this.setAreas())):"selector"==this.mode?this.valueArr=this.defaultSelector:"multiSelector"==this.mode&&(this.valueArr=this.defaultSelector,this.multiSelectorValue=this.defaultSelector),this.$forceUpdate()},setYears(){this.years=this.generateArray(this.startYear,this.endYear),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.years,this.year))},setMonths(){this.months=this.generateArray(1,12),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.months,this.month))},setDays(){let e=new Date(this.year,this.month,0).getDate();this.days=this.generateArray(1,e);let n=0;n=this.params.year&&this.params.month?2:this.params.month||this.params.year?1:0,this.day>this.days.length&&(this.day=this.days.length),this.valueArr.splice(n,1,this.getIndex(this.days,this.day))},setHours(){this.hours=this.generateArray(0,23),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.hours,this.hour))},setMinutes(){this.minutes=this.generateArray(0,59),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.minutes,this.minute))},setSeconds(){this.seconds=this.generateArray(0,59),this.valueArr.splice(this.valueArr.length-1,1,this.getIndex(this.seconds,this.second))},setProvinces(){if(!this.params.province)return;let e="",n=!1;this.areaCode.length?(e=this.areaCode[0],n=!0):e=this.defaultRegion.length?this.defaultRegion[0]:0,de.map(((a,o)=>{(n?a.code==e:a.name==e)&&(e=o)})),this.province=e,this.provinces=de,this.valueArr.splice(0,1,this.province)},setCitys(){if(!this.params.city)return;let e="",n=!1;this.areaCode.length?(e=this.areaCode[1],n=!0):e=this.defaultRegion.length?this.defaultRegion[1]:0,me[this.province].map(((a,o)=>{(n?a.code==e:a.name==e)&&(e=o)})),this.city=e,this.citys=me[this.province],this.valueArr.splice(1,1,this.city)},setAreas(){if(!this.params.area)return;let e="",n=!1;this.areaCode.length?(e=this.areaCode[2],n=!0):e=this.defaultRegion.length?this.defaultRegion[2]:0,te[this.province][this.city].map(((a,o)=>{(n?a.code==e:a.name==e)&&(e=o)})),this.area=e,this.areas=te[this.province][this.city],this.valueArr.splice(2,1,this.area)},close(){this.$emit("input",!1),this.$emit("update:modelValue",!1)},change(e){this.valueArr=e.detail.value;let n=0;if("time"==this.mode)this.params.year&&(this.year=this.years[this.valueArr[n++]]),this.params.month&&(this.month=this.months[this.valueArr[n++]]),this.params.day&&(this.day=this.days[this.valueArr[n++]]),this.params.hour&&(this.hour=this.hours[this.valueArr[n++]]),this.params.minute&&(this.minute=this.minutes[this.valueArr[n++]]),this.params.second&&(this.second=this.seconds[this.valueArr[n++]]);else if("region"==this.mode)this.params.province&&(this.province=this.valueArr[n++]),this.params.city&&(this.city=this.valueArr[n++]),this.params.area&&(this.area=this.valueArr[n++]);else if("multiSelector"==this.mode){let n=null;this.defaultSelector.map(((a,o)=>{a!=e.detail.value[o]&&(n=o)})),null!=n&&this.$emit("columnchange",{column:n,index:e.detail.value[n]})}},getResult(e=null){let n={};"time"==this.mode?(this.params.year&&(n.year=this.formatNumber(this.year||0)),this.params.month&&(n.month=this.formatNumber(this.month||0)),this.params.day&&(n.day=this.formatNumber(this.day||0)),this.params.hour&&(n.hour=this.formatNumber(this.hour||0)),this.params.minute&&(n.minute=this.formatNumber(this.minute||0)),this.params.second&&(n.second=this.formatNumber(this.second||0)),this.params.timestamp&&(n.timestamp=this.getTimestamp())):"region"==this.mode?(this.params.province&&(n.province=de[this.province]),this.params.city&&(n.city=me[this.province][this.city]),this.params.area&&(n.area=te[this.province][this.city][this.area])):("selector"==this.mode||"multiSelector"==this.mode)&&(n=this.valueArr),e&&this.$emit(e,n),this.close()},getTimestamp(){let e=this.year+"/"+this.month+"/"+this.day+" "+this.hour+":"+this.minute+":"+this.second;return new Date(e).getTime()/1e3},getDateSource:()=>({provinces:de,citys:me,areas:te}),regionDiscern(e){let n="",a={},o={},c={};if(!e)return{code:-1,msg:"地址文本不能为空"};e.trim();let d=e.substring(0,2),m=-1;for(let l=0;l<de.length;l++){let{code:e,name:n}=de[l];if(0==n.indexOf(d)){a={code:e,name:n},m=l;break}}if(-1==m)return{code:-1,msg:`省份【${d}】没有找到，请输入正确的地址`};let t=me[m],r=-1;for(let l=0;l<t.length;l++){let{name:n,code:a}=t[l],c=n.substr(0,n.length-1);if(e.indexOf(c)>-1){o={code:a,name:n},r=l;break}}if(-1==r)return{code:-1,msg:"地级市没有找到，请输入正确的地址"};let u=te[m][r],i=-1;for(let l=0;l<u.length;l++){let{code:a,name:o}=u[l],d=o;o.length>2&&(d+=`|${o.substr(0,o.length-1)}`);let m=new RegExp(d);if(e.search(m)>-1){c={code:a,name:o},n=e.replace(new RegExp(d),"{{~}}").split("{{~}}")[1],i=l;break}}return-1==i?{code:-1,msg:"县级市没有找到，请输入正确的地址"}:{code:0,msg:"ok",data:{province:a,city:o,area:c,address:n,formatted_address:`${a.name}${o.name}${c.name}${n}`}}},addressDiscern(e){let n="",a="";if(!e)return{code:-1,msg:"地址文本不能为空"};let o,c,d=e.split(/[^\u4e00-\u9fa5a-zA-Z0-9+-（）()]+/g).filter((e=>e.length));if(3!=d.length)return{code:-1,msg:"地址格式不正确，请按姓名 手机号 收货地址格式。"};for(let[t,r]of d.entries())/^1[3,4,5,6,7,8,9][0-9]{9}$/.test(r)?a=r:o?o.length>r.length?(c=o,n=r):(c=r,n=o):o=r;let m=this.regionDiscern(c);return 0!==m.code?m:{code:0,msg:"ok",data:{name:n,mobile:a,position:m.data}}}}},[["render",function(e,n,a,o,c,d){const m=z,s=E,f=O,p=T,g=t(r("u-popup"),X);return u(),i(g,{maskCloseAble:a.maskCloseAble,mode:"bottom",popup:!1,modelValue:c.popupValue,"onUpdate:modelValue":n[4]||(n[4]=e=>c.popupValue=e),length:"auto",safeAreaInsetBottom:a.safeAreaInsetBottom,onClose:d.close,"z-index":d.uZIndex,blur:a.blur},{default:l((()=>[v(m,{class:"u-datetime-picker"},{default:l((()=>[v(m,{class:"u-picker-header",onTouchmove:n[3]||(n[3]=A((()=>{}),["stop","prevent"]))},{default:l((()=>[v(m,{class:"u-btn-picker u-btn-picker--tips",style:h({color:a.cancelColor}),"hover-class":"u-opacity","hover-stay-time":150,onClick:n[0]||(n[0]=e=>d.getResult("cancel"))},{default:l((()=>[_(j(a.cancelText),1)])),_:1},8,["style"]),v(m,{class:"u-picker__title"},{default:l((()=>[_(j(a.title),1)])),_:1}),v(m,{class:"u-btn-picker u-btn-picker--primary",style:h({color:c.moving?a.cancelColor:a.confirmColor}),"hover-class":"u-opacity","hover-stay-time":150,onTouchmove:n[1]||(n[1]=A((()=>{}),["stop"])),onClick:n[2]||(n[2]=A((e=>d.getResult("confirm")),["stop"]))},{default:l((()=>[_(j(a.confirmText),1)])),_:1},8,["style"])])),_:1}),v(m,{class:"u-picker-body"},{default:l((()=>["region"==a.mode?(u(),i(f,{key:0,value:c.valueArr,onChange:d.change,class:"u-picker-view",onPickstart:d.pickstart,onPickend:d.pickend},{default:l((()=>[!c.reset&&a.params.province?(u(),i(s,{key:0},{default:l((()=>[(u(!0),C(S,null,I(c.provinces,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[v(m,{class:"u-line-1"},{default:l((()=>[_(j(e.name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):R("v-if",!0),!c.reset&&a.params.city?(u(),i(s,{key:1},{default:l((()=>[(u(!0),C(S,null,I(c.citys,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[v(m,{class:"u-line-1"},{default:l((()=>[_(j(e.name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):R("v-if",!0),!c.reset&&a.params.area?(u(),i(s,{key:2},{default:l((()=>[(u(!0),C(S,null,I(c.areas,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[v(m,{class:"u-line-1"},{default:l((()=>[_(j(e.name),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):R("v-if",!0)])),_:1},8,["value","onChange","onPickstart","onPickend"])):"time"==a.mode?(u(),i(f,{key:1,value:c.valueArr,onChange:d.change,class:"u-picker-view",onPickstart:d.pickstart,onPickend:d.pickend},{default:l((()=>[!c.reset&&a.params.year?(u(),i(s,{key:0},{default:l((()=>[(u(!0),C(S,null,I(c.years,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[_(j(e)+" ",1),a.showTimeTag?(u(),i(p,{key:0,class:"u-text"},{default:l((()=>[_("年")])),_:1})):R("v-if",!0)])),_:2},1024)))),128))])),_:1})):R("v-if",!0),!c.reset&&a.params.month?(u(),i(s,{key:1},{default:l((()=>[(u(!0),C(S,null,I(c.months,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[_(j(d.formatNumber(e))+" ",1),a.showTimeTag?(u(),i(p,{key:0,class:"u-text"},{default:l((()=>[_("月")])),_:1})):R("v-if",!0)])),_:2},1024)))),128))])),_:1})):R("v-if",!0),!c.reset&&a.params.day?(u(),i(s,{key:2},{default:l((()=>[(u(!0),C(S,null,I(c.days,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[_(j(d.formatNumber(e))+" ",1),a.showTimeTag?(u(),i(p,{key:0,class:"u-text"},{default:l((()=>[_("日")])),_:1})):R("v-if",!0)])),_:2},1024)))),128))])),_:1})):R("v-if",!0),!c.reset&&a.params.hour?(u(),i(s,{key:3},{default:l((()=>[(u(!0),C(S,null,I(c.hours,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[_(j(d.formatNumber(e))+" ",1),a.showTimeTag?(u(),i(p,{key:0,class:"u-text"},{default:l((()=>[_("时")])),_:1})):R("v-if",!0)])),_:2},1024)))),128))])),_:1})):R("v-if",!0),!c.reset&&a.params.minute?(u(),i(s,{key:4},{default:l((()=>[(u(!0),C(S,null,I(c.minutes,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[_(j(d.formatNumber(e))+" ",1),a.showTimeTag?(u(),i(p,{key:0,class:"u-text"},{default:l((()=>[_("分")])),_:1})):R("v-if",!0)])),_:2},1024)))),128))])),_:1})):R("v-if",!0),!c.reset&&a.params.second?(u(),i(s,{key:5},{default:l((()=>[(u(!0),C(S,null,I(c.seconds,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[_(j(d.formatNumber(e))+" ",1),a.showTimeTag?(u(),i(p,{key:0,class:"u-text"},{default:l((()=>[_("秒")])),_:1})):R("v-if",!0)])),_:2},1024)))),128))])),_:1})):R("v-if",!0)])),_:1},8,["value","onChange","onPickstart","onPickend"])):"selector"==a.mode?(u(),i(f,{key:2,value:c.valueArr,onChange:d.change,class:"u-picker-view",onPickstart:d.pickstart,onPickend:d.pickend},{default:l((()=>[c.reset?R("v-if",!0):(u(),i(s,{key:0},{default:l((()=>[(u(!0),C(S,null,I(a.range,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[v(m,{class:"u-line-1"},{default:l((()=>[_(j(d.getItemValue(e,"selector")),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1}))])),_:1},8,["value","onChange","onPickstart","onPickend"])):"multiSelector"==a.mode?(u(),i(f,{key:3,value:c.valueArr,onChange:d.change,class:"u-picker-view",onPickstart:d.pickstart,onPickend:d.pickend},{default:l((()=>[c.reset?R("v-if",!0):(u(!0),C(S,{key:0},I(a.range,((e,n)=>(u(),i(s,{key:n},{default:l((()=>[(u(!0),C(S,null,I(e,((e,n)=>(u(),i(m,{class:"u-column-item",key:n},{default:l((()=>[v(m,{class:"u-line-1"},{default:l((()=>[_(j(d.getItemValue(e,"multiSelector")),1)])),_:2},1024)])),_:2},1024)))),128))])),_:2},1024)))),128))])),_:1},8,["value","onChange","onPickstart","onPickend"])):R("v-if",!0)])),_:1})])),_:1})])),_:1},8,["maskCloseAble","modelValue","safeAreaInsetBottom","onClose","z-index","blur"])}],["__scopeId","data-v-7c341487"]]),ue=G(e({__name:"user_data",setup(e){const n=$({}),a=$(V.NONE),o=$(["男","女"]),c=$(!1),d=$(!1),m=$(!1),i=$(!1),s=$(""),f=$(""),h=$(""),p=$(""),g=$(""),y=B(),b=async()=>{n.value=await U()},w=e=>{g.value=e},x=async()=>{var e,a;if(!h.value)return uni.$u.toast("请输入新的手机号码");(null==(e=y.value)?void 0:e.canGetCode)&&(await L({scene:n.value.mobile?W.CHANGE_MOBILE:W.BIND_MOBILE,mobile:h.value}),uni.$u.toast("发送成功"),null==(a=y.value)||a.start())},A=e=>{a.value=V.AVATAR,E(e)},I=async()=>{await P({type:n.value.mobile?"change":"bind",mobile:h.value,code:p.value}),uni.$u.toast("操作成功"),i.value=!1,b()},E=async e=>{await M({field:a.value,value:e}),uni.$u.toast("操作成功"),b()},O=()=>{m.value=!0,a.value=V.SEX},T=e=>{E(e[0]+1),m.value=!1},K=()=>""==f.value?uni.$u.toast("账号不能为空"):f.value.length>10?uni.$u.toast("账号长度不得超过十位数"):(a.value=V.USERNAME,E(f.value),void(d.value=!1)),G=async e=>(s.value=e.detail.value.nickname,""==s.value?uni.$u.toast("昵称不能为空"):s.value.length>10?uni.$u.toast("昵称长度不得超过十位数"):(a.value=V.NICKNAME,await E(s.value),void(c.value=!1)));return N((async()=>{b()})),D((()=>{})),(e,a)=>{const b=t(r("page-meta"),Z),E=t(r("avatar-upload"),ce),$=z,V=t(r("u-icon"),Y),B=t(r("u-button"),H),N=F,D=t(r("u-form-item"),J),U=k,L=q,W=t(r("u-popup"),X),P=t(r("u-input"),Q),M=t(r("u-picker"),re),ne=t(r("u-verification-code"),ee);return u(),C(S,null,[v(b,{"page-style":e.$theme.pageStyle},null,8,["page-style"]),R(" Main Start "),R(" 头部修改头像 "),v($,{class:"header bg-white pt-[30rpx]"},{default:l((()=>[v($,{class:"flex justify-center pb-5"},{default:l((()=>{var e;return[v(E,{modelValue:null==(e=n.value)?void 0:e.avatar,"file-key":"url",round:!0,"onUpdate:modelValue":A},null,8,["modelValue"])]})),_:1})])),_:1}),R(" 用户ID "),v($,{class:"item text-nr flex justify-between",onClick:a[0]||(a[0]=e=>{var a;d.value=!0,f.value=null==(a=n.value)?void 0:a.username})},{default:l((()=>[v($,{class:"label"},{default:l((()=>[_("账号")])),_:1}),v($,{class:"content"},{default:l((()=>{var e;return[_(j(null==(e=n.value)?void 0:e.account),1)]})),_:1}),v(V,{name:"arrow-right",size:"22",color:"#666"})])),_:1}),R(" 昵称 "),v($,{class:"item text-nr flex justify-between",onClick:a[1]||(a[1]=e=>{var a;c.value=!0,s.value=null==(a=n.value)?void 0:a.nickname})},{default:l((()=>[v($,{class:"label"},{default:l((()=>[_("昵称")])),_:1}),v($,{class:"content"},{default:l((()=>{var e;return[_(j(null==(e=n.value)?void 0:e.nickname),1)]})),_:1}),v(V,{name:"arrow-right",size:"22",color:"#666"})])),_:1}),R(" 性别 "),v($,{class:"item text-nr flex justify-between",onClick:O},{default:l((()=>[v($,{class:"label"},{default:l((()=>[_("性别")])),_:1}),v($,{class:"content"},{default:l((()=>{var e;return[_(j(null==(e=n.value)?void 0:e.sex),1)]})),_:1}),v(V,{name:"arrow-right",size:"22",color:"#666"})])),_:1}),R(" 手机号 "),v($,{class:"item text-nr flex justify-between"},{default:l((()=>[v($,{class:"label"},{default:l((()=>[_("手机号")])),_:1}),v($,{class:"content"},{default:l((()=>{var e,a;return[_(j(""==(null==(e=n.value)?void 0:e.mobile)?"未绑定手机号":null==(a=n.value)?void 0:a.mobile),1)]})),_:1}),v(B,{onClick:a[2]||(a[2]=e=>i.value=!0),size:"mini",type:"primary",shape:"circle",plain:!0},{default:l((()=>{var e;return[_(j(""==(null==(e=n.value)?void 0:e.mobile)?"绑定手机号":"更换手机号"),1)]})),_:1})])),_:1}),R(" 注册时间 "),v($,{class:"item text-nr flex justify-between"},{default:l((()=>[v($,{class:"label"},{default:l((()=>[_("注册时间")])),_:1}),v($,{class:"content"},{default:l((()=>{var e;return[_(j(null==(e=n.value)?void 0:e.create_time),1)]})),_:1})])),_:1}),R(" 昵称修改组件 "),v(W,{modelValue:c.value,"onUpdate:modelValue":a[3]||(a[3]=e=>c.value=e),closeable:!0,mode:"center",maskCloseAble:!1,"border-radius":"20"},{default:l((()=>[v($,{class:"px-[50rpx] py-[40rpx] bg-white",style:{width:"85vw"}},{default:l((()=>[v(L,{onSubmit:G},{default:l((()=>[v($,{class:"mb-[70rpx] text-xl text-center"},{default:l((()=>[_("修改昵称")])),_:1}),v(D,{borderBottom:""},{default:l((()=>[v(N,{class:"nr h-[60rpx] w-full",value:n.value.nickname,name:"nickname",type:"nickname",placeholder:"请输入昵称"},null,8,["value"])])),_:1}),v($,{class:"mt-[80rpx]"},{default:l((()=>[v(U,{class:"bg-primary text-white w-full h-[80rpx] !text-lg !leading-[80rpx] rounded-full","form-type":"submit",size:"mini","hover-class":"none"},{default:l((()=>[_(" 确定 ")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"]),R(" 账号修改组件 "),v(W,{modelValue:d.value,"onUpdate:modelValue":a[5]||(a[5]=e=>d.value=e),closeable:!0,mode:"center","border-radius":"20"},{default:l((()=>[v($,{class:"px-[50rpx] py-[40rpx] bg-white",style:{width:"85vw"}},{default:l((()=>[v($,{class:"mb-[70rpx] text-xl text-center"},{default:l((()=>[_("修改账号")])),_:1}),v(D,{borderBottom:""},{default:l((()=>[v(P,{class:"flex-1",modelValue:f.value,"onUpdate:modelValue":a[4]||(a[4]=e=>f.value=e),placeholder:"请输入账号",border:!1},null,8,["modelValue"])])),_:1}),v($,{class:"mt-[80rpx]"},{default:l((()=>[v(B,{onClick:K,type:"primary",shape:"circle"},{default:l((()=>[_(" 确定 ")])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"]),R(" 性别修改组件 "),v(M,{mode:"selector",modelValue:m.value,"onUpdate:modelValue":a[6]||(a[6]=e=>m.value=e),"confirm-color":"#4173FF","default-selector":[0],range:o.value,onConfirm:T},null,8,["modelValue","range"]),R(" 账号修改组件 "),v(W,{modelValue:i.value,"onUpdate:modelValue":a[9]||(a[9]=e=>i.value=e),closeable:!0,mode:"center","border-radius":"20"},{default:l((()=>[v($,{class:"px-[50rpx] py-[40rpx] bg-white",style:{width:"85vw"}},{default:l((()=>[v($,{class:"mb-[70rpx] text-xl text-center"},{default:l((()=>{var e;return[_(j(""==(null==(e=n.value)?void 0:e.mobile)?"绑定手机号":"更换手机号"),1)]})),_:1}),v(D,{borderBottom:""},{default:l((()=>[v(P,{class:"flex-1",modelValue:h.value,"onUpdate:modelValue":a[7]||(a[7]=e=>h.value=e),placeholder:"请输入新的手机号码",border:!1},null,8,["modelValue"])])),_:1}),v(D,{borderBottom:""},{default:l((()=>[v(P,{class:"flex-1",modelValue:p.value,"onUpdate:modelValue":a[8]||(a[8]=e=>p.value=e),placeholder:"请输入验证码",border:!1},null,8,["modelValue"]),v($,{class:"border-l border-solid border-0 border-light pl-3 text-muted leading-4 ml-3 w-[180rpx]",onClick:x},{default:l((()=>[v(ne,{ref_key:"uCodeRef",ref:y,seconds:60,onChange:w,"change-text":"x秒"},null,512),_(" "+j(g.value),1)])),_:1})])),_:1}),v($,{class:"mt-[80rpx]"},{default:l((()=>[v(B,{onClick:I,type:"primary",shape:"circle"},{default:l((()=>[_(" 确定 ")])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-91ffed17"]]);export{ue as default};
