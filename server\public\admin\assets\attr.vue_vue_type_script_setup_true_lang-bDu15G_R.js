import{d as U,c as C,o as r,C as s,w as t,m as l,e as a,p,G as i,b as g,E as F}from"./index-B2xNDy79.js";import{E as N,a as z}from"./el-form-item-DlU85AZK.js";import{E as B}from"./el-card-DpH4mUSc.js";import{_ as j}from"./index.vue_vue_type_script_setup_true_lang-C-xtr9xT.js";import{_ as G}from"./picker-Cd5l2hZ5.js";/* empty css                       */import{E as I,a as O}from"./el-radio-CKcO4hVq.js";const H=U({__name:"attr",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},emits:["update:content"],setup(d,{emit:y}){const b=y,x=d,o=C({get:()=>x.content,set:_=>{b("update:content",_)}});return(_,e)=>{const u=I,f=O,m=N,E=F,V=G,k=j,v=B,w=z;return r(),s(w,{ref:"form","label-width":"80px",size:"large"},{default:t(()=>[l(v,{shadow:"never",class:"!border-none flex mt-2"},{default:t(()=>[l(m,{label:"页面标题"},{default:t(()=>[l(f,{modelValue:a(o).title_type,"onUpdate:modelValue":e[0]||(e[0]=n=>a(o).title_type=n)},{default:t(()=>[l(u,{value:"1"},{default:t(()=>e[7]||(e[7]=[p("文字")])),_:1}),l(u,{value:"2"},{default:t(()=>e[8]||(e[8]=[p("图片")])),_:1})]),_:1},8,["modelValue"])]),_:1}),d.content.title_type==1?(r(),s(m,{key:0},{default:t(()=>[l(E,{modelValue:a(o).title,"onUpdate:modelValue":e[1]||(e[1]=n=>a(o).title=n),maxlength:"8","show-word-limit":"",class:"w-[300px]",placeholder:"请输入页面标题"},null,8,["modelValue"])]),_:1})):i("",!0),d.content.title_type==2?(r(),s(m,{key:1},{default:t(()=>[l(V,{modelValue:a(o).title_img,"onUpdate:modelValue":e[2]||(e[2]=n=>a(o).title_img=n),limit:1,size:"100px"},null,8,["modelValue"]),e[9]||(e[9]=g("div",{class:"form-tips"},"建议图片尺寸：300px*40px",-1))]),_:1})):i("",!0),d.content.title_type==1?(r(),s(m,{key:2,label:"文字颜色"},{default:t(()=>[l(f,{modelValue:a(o).text_color,"onUpdate:modelValue":e[3]||(e[3]=n=>a(o).text_color=n)},{default:t(()=>[l(u,{value:"1"},{default:t(()=>e[10]||(e[10]=[p("白色")])),_:1}),l(u,{value:"2"},{default:t(()=>e[11]||(e[11]=[p("黑色")])),_:1})]),_:1},8,["modelValue"])]),_:1})):i("",!0),l(m,{label:"页面背景"},{default:t(()=>[l(f,{modelValue:a(o).bg_type,"onUpdate:modelValue":e[4]||(e[4]=n=>a(o).bg_type=n)},{default:t(()=>[l(u,{value:"1"},{default:t(()=>e[12]||(e[12]=[p("背景颜色")])),_:1}),l(u,{value:"2"},{default:t(()=>e[13]||(e[13]=[p("背景图片")])),_:1})]),_:1},8,["modelValue"])]),_:1}),d.content.bg_type==1?(r(),s(m,{key:3},{default:t(()=>[l(k,{modelValue:a(o).bg_color,"onUpdate:modelValue":e[5]||(e[5]=n=>a(o).bg_color=n),"reset-color":"#F5F5F5"},null,8,["modelValue"])]),_:1})):i("",!0),d.content.bg_type==2?(r(),s(m,{key:4},{default:t(()=>[l(V,{modelValue:a(o).bg_image,"onUpdate:modelValue":e[6]||(e[6]=n=>a(o).bg_image=n),limit:1,size:"100px"},null,8,["modelValue"]),e[14]||(e[14]=g("div",{class:"form-tips"},"建议图片尺寸：750px*高度不限",-1))]),_:1})):i("",!0)]),_:1})]),_:1},512)}}});export{H as _};
