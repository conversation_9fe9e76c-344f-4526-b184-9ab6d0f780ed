import{at as wt,bp as xt,d as ke,an as Ot,c as Ie,o as z,a as Z,C as ve,w as $,aq as Pt,e as B,S as Ft,G as ee,Y as _e,U as lt,ar as Mt,ay as Nt,x as Tt,b as V,m as W,as as Me,ea as Bt,q as St,y as Ue,s as nt,i as he,j as Qe,I as ut,eb as bt,dv as We,dt as Ye,p as re,B as ye,R as Te,t as ce,ao as et,V as Xe,k as ft,bO as Lt,F as Ae,r as Fe,bj as dt,dO as Ut,bm as At,n as ze,W as gt,H as it,ae as Gt,D as Ke,ec as jt,ax as Vt,a9 as Kt,ed as Ht,ee as Wt,ef as Yt,eg as qt,bJ as zt,v as Xt,E as $t,bF as Qt,K as Zt}from"./index-B2xNDy79.js";import{_ as Jt}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as er,a as tr}from"./el-table-column-DG3vRCd5.js";import{E as rr}from"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{_ as ir}from"./index-BuNto3DN.js";import{P as nr}from"./index-DFOp_83R.js";import{E as ar,a as sr}from"./el-select-BRdnbwTl.js";import{U as or}from"./index-DSiy6YVt.js";import{E as lr}from"./el-tree-8o9N7gsQ.js";import{_ as ur}from"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import{u as dr}from"./usePaging-Dm2wALfy.js";import{E as cr}from"./index-C6Cr8aHe.js";const hr=wt({type:{type:String,values:["primary","success","warning","info","danger","default"],default:"default"},underline:{type:Boolean,default:!0},disabled:Boolean,href:{type:String,default:""},target:{type:String,default:"_self"},icon:{type:xt}}),fr={click:M=>M instanceof MouseEvent},gr=ke({name:"ElLink"}),vr=ke({...gr,props:hr,emits:fr,setup(M,{emit:I}){const g=M,R=Ot("link"),E=Ie(()=>[R.b(),R.m(g.type),R.is("disabled",g.disabled),R.is("underline",g.underline&&!g.disabled)]);function A(k){g.disabled||I("click",k)}return(k,C)=>(z(),Z("a",{class:_e(B(E)),href:k.disabled||!k.href?void 0:k.href,target:k.disabled||!k.href?void 0:k.target,onClick:A},[k.icon?(z(),ve(B(Ft),{key:0},{default:$(()=>[(z(),ve(Pt(k.icon)))]),_:1})):ee("v-if",!0),k.$slots.default?(z(),Z("span",{key:1,class:_e(B(R).e("inner"))},[lt(k.$slots,"default")],2)):ee("v-if",!0),k.$slots.icon?lt(k.$slots,"icon",{key:2}):ee("v-if",!0)],10,["href","target"]))}});var pr=Mt(vr,[["__file","link.vue"]]);const mr=Nt(pr),yr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAgCAYAAABgrToAAAACJElEQVRYR+2YMWsUURSFz3m7s+nskjUIQSutbMRi7WzUVjSadMHCbVLkByjmLygaCVYWRqMEUhkFS9Gg0cJfYCPZjYUQFbPs+I7c2R1Q2ZjZfRNYYS4MAzPv3vnmvDvL3kMA2Hl5/CjLI9ckf4ZwY3Zt15C+gfwIao3So0rt3XsJtPUk9M/cAW6y9ap2DIyfAjgCwANwGeoYiEFtk/5e5CvXeer1D2neATcGgiTZM4+t9RNLEKcBtAFEGeBsiRWzl7EoSXo+8rV9gWc/fDc1B1VSEoEnDpj0KTB33tS26DGaEezvZQZpRxmODyoT5+vwBwS3zeTcT4yjTdZNJEiPSykk1bjZX6HeD/WQJ1zUApgq2w+etcsniBuAVlH9vELOx6Yo1VywgkmTB4X1kEGGhyAtg/Ecq3NNqnknDwVTrNBaactEts88OHs5b8Bw/Tof4M+kr4WrwwhoL9n5uRPWhxWwsxPEl+EGNMacP5I8evCPGgVgqKSFgoWCoQqE5hc9WCgYqkBoftGDeSiYz1/+UJLe+foftvh2A2B1fwQIrapkaFoDcK4PVyH0qVnyU4fjGdW4NQ2WlgDE5hLkMoJmQdh9zW9Dk59K5lhtLjyE01TX/jDILP5MGEbvbFPOJroIXvc5PjvTBbx7GM4vAjjd9WdSc2g/IPaqaTv5Aq58haP1TSb2Au20GGErvgTxIqiTAA7tVSnn+2Z9vAXdCsa4bD6Nsf0C/gYA5PMzcW0AAAAASUVORK5CYII=",Er=ke({props:{uri:{type:String},fileSize:{type:String,default:"100px"},width:{type:String,default:""},height:{type:String,default:""},type:{type:String,default:"image"}},emits:["close"]}),Tr=["src"],Sr={key:3,class:"absolute left-1/2 top-1/2 translate-x-[-50%] translate-y-[-50%] rounded-full w-5 h-5 flex justify-center items-center bg-[rgba(0,0,0,0.3)]"};function br(M,I,g,R,E,A){const k=Bt,C=St;return z(),Z("div",null,[V("div",{class:"file-item relative",style:Me({height:M.height||M.fileSize,width:M.width||M.fileSize})},[M.type=="image"?(z(),ve(k,{key:0,class:"image",fit:"contain",src:M.uri},null,8,["src"])):M.type=="video"?(z(),Z("video",{key:1,class:"video",src:M.uri},null,8,Tr)):(z(),ve(k,{key:2,class:"image",src:"https://img95.699pic.com/element/40103/3946.png_860.png"})),M.type=="video"?(z(),Z("div",Sr,[W(C,{name:"el-icon-CaretRight",size:18,color:"#fff"})])):ee("",!0),lt(M.$slots,"default",{},void 0,!0)],4)])}const at=Tt(Er,[["render",br],["__scopeId","data-v-acf6f94d"]]);function vt(M){return Ue.post({url:"/file/addCate",params:M})}function Lr(M){return Ue.post({url:"/file/editCate",params:M})}function Ar(M){return Ue.post({url:"/file/delCate",params:M})}function kr(M){return Ue.get({url:"/file/listCate",params:M})}function Dr(M){return Ue.get({url:"/file/lists",params:M},{ignoreCancelToken:!0,isOpenRetry:!1})}function _r(M){return Ue.post({url:"/file/delete",params:M})}function Rr(M){return Ue.post({url:"/file/move",params:M})}function Cr(M){return Ue.post({url:"/file/rename",params:M})}function Ir(M){const I=nt(),g=he([]),R=he(""),E=async()=>{const b=await kr({page_type:0,type:M}),m=[{name:"全部",id:""},{name:"未分组",id:0}];g.value=b.lists,g.value.unshift(...m),setTimeout(()=>{var h;(h=I.value)==null||h.setCurrentKey(R.value)},0)};return{treeRef:I,cateId:R,cateLists:g,handleAddCate:async b=>{await vt({type:M,name:b,pid:0}),E()},handleAddChildCate:async(b,m)=>{await vt({type:M,name:b,pid:m}),E()},handleEditCate:async(b,m)=>{await Lr({id:m,name:b}),E()},handleDeleteCate:async(b,m)=>{m?await ut.confirm("删除文件夹将会永久删除文件夹及其所有内容。您确定要继续吗？"):await ut.confirm("确定要删除？"),await Ar({id:b}),R.value="",E()},getCateLists:E,handleCatSelect:b=>{R.value=b.id}}}function wr(M,I,g,R){const E=nt(),A=he("normal"),k=he(0),C=he([]),D=he(!1),s=he(!1),b=Qe({name:"",type:I,cid:M,source:""}),{pager:m,getLists:h,resetPage:T}=dr({fetchFun:Dr,params:b,firstLoading:!0,size:R}),y=()=>{h()},d=()=>{T()},e=f=>!!C.value.find(v=>v.id==f),a=async f=>{await ut.confirm("确认删除后，本地或云存储文件也将同步删除，如文件已被使用，请谨慎操作！");const v=f||C.value.map(r=>r.id);await _r({ids:v}),y(),u()},t=async()=>{const f=C.value.map(v=>v.id);await Rr({ids:f,cid:k.value}),k.value=0,y(),u()},o=f=>{const v=C.value.findIndex(r=>r.id==f.id);if(v!=-1){C.value.splice(v,1);return}if(C.value.length==g.value){if(g.value==1){C.value=[],C.value.push(f);return}bt.warning("已达到选择上限");return}C.value.push(f)},u=()=>{C.value=[]};return{listShowType:A,tableRef:E,moveId:k,pager:m,fileParams:b,select:C,isCheckAll:D,isIndeterminate:s,getFileList:y,refresh:d,batchFileDelete:a,batchFileMove:t,selectFile:o,isSelect:e,clearSelect:u,cancelSelete:f=>{C.value=C.value.filter(v=>v.id!=f)},selectAll:f=>{var v;if(s.value=!1,(v=E.value)==null||v.toggleAllSelection(),f){C.value=[...m.lists];return}u()},handleFileRename:async(f,v)=>{await Cr({id:v,name:f}),y()}}}var xr=Object.defineProperty,Or=Object.defineProperties,Pr=Object.getOwnPropertyDescriptors,pt=Object.getOwnPropertySymbols,Fr=Object.prototype.hasOwnProperty,Mr=Object.prototype.propertyIsEnumerable,mt=(M,I,g)=>I in M?xr(M,I,{enumerable:!0,configurable:!0,writable:!0,value:g}):M[I]=g,ct=(M,I)=>{for(var g in I||(I={}))Fr.call(I,g)&&mt(M,g,I[g]);if(pt)for(var g of pt(I))Mr.call(I,g)&&mt(M,g,I[g]);return M},ht=(M,I)=>Or(M,Pr(I));function Nr(M,I,g,R){var E,A=!1,k=0;function C(){E&&clearTimeout(E)}function D(){for(var s=arguments.length,b=new Array(s),m=0;m<s;m++)b[m]=arguments[m];var h=this,T=Date.now()-k;function y(){k=Date.now(),g.apply(h,b)}function d(){E=void 0}A||(R&&!E&&y(),C(),R===void 0&&T>M?y():I!==!0&&(E=setTimeout(R?d:y,R===void 0?M-T:M)))}return typeof I!="boolean"&&(R=g,g=I,I=void 0),D.cancel=function(){C(),A=!0},D}function yt(M,I,g){return Nr(M,I,!1)}function Br(M){return M&&M.__esModule&&Object.prototype.hasOwnProperty.call(M,"default")?M.default:M}var kt={exports:{}};typeof window<"u"&&(kt.exports=function(M){var I={};function g(R){if(I[R])return I[R].exports;var E=I[R]={i:R,l:!1,exports:{}};return M[R].call(E.exports,E,E.exports,g),E.l=!0,E.exports}return g.m=M,g.c=I,g.d=function(R,E,A){g.o(R,E)||Object.defineProperty(R,E,{enumerable:!0,get:A})},g.r=function(R){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(R,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(R,"__esModule",{value:!0})},g.t=function(R,E){if(1&E&&(R=g(R)),8&E||4&E&&typeof R=="object"&&R&&R.__esModule)return R;var A=Object.create(null);if(g.r(A),Object.defineProperty(A,"default",{enumerable:!0,value:R}),2&E&&typeof R!="string")for(var k in R)g.d(A,k,(function(C){return R[C]}).bind(null,k));return A},g.n=function(R){var E=R&&R.__esModule?function(){return R.default}:function(){return R};return g.d(E,"a",E),E},g.o=function(R,E){return Object.prototype.hasOwnProperty.call(R,E)},g.p="/dist/",g(g.s="./src/hls.ts")}({"./node_modules/eventemitter3/index.js":function(M,I,g){var R=Object.prototype.hasOwnProperty,E="~";function A(){}function k(b,m,h){this.fn=b,this.context=m,this.once=h||!1}function C(b,m,h,T,y){if(typeof h!="function")throw new TypeError("The listener must be a function");var d=new k(h,T||b,y),e=E?E+m:m;return b._events[e]?b._events[e].fn?b._events[e]=[b._events[e],d]:b._events[e].push(d):(b._events[e]=d,b._eventsCount++),b}function D(b,m){--b._eventsCount==0?b._events=new A:delete b._events[m]}function s(){this._events=new A,this._eventsCount=0}Object.create&&(A.prototype=Object.create(null),new A().__proto__||(E=!1)),s.prototype.eventNames=function(){var b,m,h=[];if(this._eventsCount===0)return h;for(m in b=this._events)R.call(b,m)&&h.push(E?m.slice(1):m);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(b)):h},s.prototype.listeners=function(b){var m=E?E+b:b,h=this._events[m];if(!h)return[];if(h.fn)return[h.fn];for(var T=0,y=h.length,d=new Array(y);T<y;T++)d[T]=h[T].fn;return d},s.prototype.listenerCount=function(b){var m=E?E+b:b,h=this._events[m];return h?h.fn?1:h.length:0},s.prototype.emit=function(b,m,h,T,y,d){var e=E?E+b:b;if(!this._events[e])return!1;var a,t,o=this._events[e],u=arguments.length;if(o.fn){switch(o.once&&this.removeListener(b,o.fn,void 0,!0),u){case 1:return o.fn.call(o.context),!0;case 2:return o.fn.call(o.context,m),!0;case 3:return o.fn.call(o.context,m,h),!0;case 4:return o.fn.call(o.context,m,h,T),!0;case 5:return o.fn.call(o.context,m,h,T,y),!0;case 6:return o.fn.call(o.context,m,h,T,y,d),!0}for(t=1,a=new Array(u-1);t<u;t++)a[t-1]=arguments[t];o.fn.apply(o.context,a)}else{var n,l=o.length;for(t=0;t<l;t++)switch(o[t].once&&this.removeListener(b,o[t].fn,void 0,!0),u){case 1:o[t].fn.call(o[t].context);break;case 2:o[t].fn.call(o[t].context,m);break;case 3:o[t].fn.call(o[t].context,m,h);break;case 4:o[t].fn.call(o[t].context,m,h,T);break;default:if(!a)for(n=1,a=new Array(u-1);n<u;n++)a[n-1]=arguments[n];o[t].fn.apply(o[t].context,a)}}return!0},s.prototype.on=function(b,m,h){return C(this,b,m,h,!1)},s.prototype.once=function(b,m,h){return C(this,b,m,h,!0)},s.prototype.removeListener=function(b,m,h,T){var y=E?E+b:b;if(!this._events[y])return this;if(!m)return D(this,y),this;var d=this._events[y];if(d.fn)d.fn!==m||T&&!d.once||h&&d.context!==h||D(this,y);else{for(var e=0,a=[],t=d.length;e<t;e++)(d[e].fn!==m||T&&!d[e].once||h&&d[e].context!==h)&&a.push(d[e]);a.length?this._events[y]=a.length===1?a[0]:a:D(this,y)}return this},s.prototype.removeAllListeners=function(b){var m;return b?(m=E?E+b:b,this._events[m]&&D(this,m)):(this._events=new A,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=E,s.EventEmitter=s,M.exports=s},"./node_modules/url-toolkit/src/url-toolkit.js":function(M,I,g){var R,E,A,k,C;R=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/?#]*)?((?:[^\/?#]*\/)*[^;?#]*)?(;[^?#]*)?(\?[^#]*)?(#[^]*)?$/,E=/^([^\/?#]*)([^]*)$/,A=/(?:\/|^)\.(?=\/)/g,k=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,C={buildAbsoluteURL:function(D,s,b){if(b=b||{},D=D.trim(),!(s=s.trim())){if(!b.alwaysNormalize)return D;var m=C.parseURL(D);if(!m)throw new Error("Error trying to parse base URL.");return m.path=C.normalizePath(m.path),C.buildURLFromParts(m)}var h=C.parseURL(s);if(!h)throw new Error("Error trying to parse relative URL.");if(h.scheme)return b.alwaysNormalize?(h.path=C.normalizePath(h.path),C.buildURLFromParts(h)):s;var T=C.parseURL(D);if(!T)throw new Error("Error trying to parse base URL.");if(!T.netLoc&&T.path&&T.path[0]!=="/"){var y=E.exec(T.path);T.netLoc=y[1],T.path=y[2]}T.netLoc&&!T.path&&(T.path="/");var d={scheme:T.scheme,netLoc:h.netLoc,path:null,params:h.params,query:h.query,fragment:h.fragment};if(!h.netLoc&&(d.netLoc=T.netLoc,h.path[0]!=="/"))if(h.path){var e=T.path,a=e.substring(0,e.lastIndexOf("/")+1)+h.path;d.path=C.normalizePath(a)}else d.path=T.path,h.params||(d.params=T.params,h.query||(d.query=T.query));return d.path===null&&(d.path=b.alwaysNormalize?C.normalizePath(h.path):h.path),C.buildURLFromParts(d)},parseURL:function(D){var s=R.exec(D);return s?{scheme:s[1]||"",netLoc:s[2]||"",path:s[3]||"",params:s[4]||"",query:s[5]||"",fragment:s[6]||""}:null},normalizePath:function(D){for(D=D.split("").reverse().join("").replace(A,"");D.length!==(D=D.replace(k,"")).length;);return D.split("").reverse().join("")},buildURLFromParts:function(D){return D.scheme+D.netLoc+D.path+D.params+D.query+D.fragment}},M.exports=C},"./node_modules/webworkify-webpack/index.js":function(M,I,g){function R(s){var b={};function m(T){if(b[T])return b[T].exports;var y=b[T]={i:T,l:!1,exports:{}};return s[T].call(y.exports,y,y.exports,m),y.l=!0,y.exports}m.m=s,m.c=b,m.i=function(T){return T},m.d=function(T,y,d){m.o(T,y)||Object.defineProperty(T,y,{configurable:!1,enumerable:!0,get:d})},m.r=function(T){Object.defineProperty(T,"__esModule",{value:!0})},m.n=function(T){var y=T&&T.__esModule?function(){return T.default}:function(){return T};return m.d(y,"a",y),y},m.o=function(T,y){return Object.prototype.hasOwnProperty.call(T,y)},m.p="/",m.oe=function(T){throw console.error(T),T};var h=m(m.s=ENTRY_MODULE);return h.default||h}var E="[\\.|\\-|\\+|\\w|/|@]+",A="\\(\\s*(/\\*.*?\\*/)?\\s*.*?([\\.|\\-|\\+|\\w|/|@]+).*?\\)";function k(s){return(s+"").replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}function C(s,b,m){var h={};h[m]=[];var T=b.toString(),y=T.match(/^function\s?\w*\(\w+,\s*\w+,\s*(\w+)\)/);if(!y)return h;for(var d,e=y[1],a=new RegExp("(\\\\n|\\W)"+k(e)+A,"g");d=a.exec(T);)d[3]!=="dll-reference"&&h[m].push(d[3]);for(a=new RegExp("\\("+k(e)+'\\("(dll-reference\\s('+E+'))"\\)\\)'+A,"g");d=a.exec(T);)s[d[2]]||(h[m].push(d[1]),s[d[2]]=g(d[1]).m),h[d[2]]=h[d[2]]||[],h[d[2]].push(d[4]);for(var t,o=Object.keys(h),u=0;u<o.length;u++)for(var n=0;n<h[o[u]].length;n++)t=h[o[u]][n],isNaN(1*t)||(h[o[u]][n]=1*h[o[u]][n]);return h}function D(s){return Object.keys(s).reduce(function(b,m){return b||s[m].length>0},!1)}M.exports=function(s,b){b=b||{};var m={main:g.m},h=b.all?{main:Object.keys(m.main)}:function(a,t){for(var o={main:[t]},u={main:[]},n={main:{}};D(o);)for(var l=Object.keys(o),p=0;p<l.length;p++){var f=l[p],v=o[f].pop();if(n[f]=n[f]||{},!n[f][v]&&a[f][v]){n[f][v]=!0,u[f]=u[f]||[],u[f].push(v);for(var r=C(a,a[f][v],f),i=Object.keys(r),c=0;c<i.length;c++)o[i[c]]=o[i[c]]||[],o[i[c]]=o[i[c]].concat(r[i[c]])}}return u}(m,s),T="";Object.keys(h).filter(function(a){return a!=="main"}).forEach(function(a){for(var t=0;h[a][t];)t++;h[a].push(t),m[a][t]="(function(module, exports, __webpack_require__) { module.exports = __webpack_require__; })",T=T+"var "+a+" = ("+R.toString().replace("ENTRY_MODULE",JSON.stringify(t))+")({"+h[a].map(function(o){return JSON.stringify(o)+": "+m[a][o].toString()}).join(",")+`});
`}),T=T+"new (("+R.toString().replace("ENTRY_MODULE",JSON.stringify(s))+")({"+h.main.map(function(a){return JSON.stringify(a)+": "+m.main[a].toString()}).join(",")+"}))(self);";var y=new window.Blob([T],{type:"text/javascript"});if(b.bare)return y;var d=(window.URL||window.webkitURL||window.mozURL||window.msURL).createObjectURL(y),e=new window.Worker(d);return e.objectURL=d,e}},"./src/config.ts":function(M,I,g){g.r(I),g.d(I,"hlsDefaultConfig",function(){return l}),g.d(I,"mergeConfig",function(){return p}),g.d(I,"enableStreamingMode",function(){return f});var R=g("./src/controller/abr-controller.ts"),E=g("./src/controller/audio-stream-controller.ts"),A=g("./src/controller/audio-track-controller.ts"),k=g("./src/controller/subtitle-stream-controller.ts"),C=g("./src/controller/subtitle-track-controller.ts"),D=g("./src/controller/buffer-controller.ts"),s=g("./src/controller/timeline-controller.ts"),b=g("./src/controller/cap-level-controller.ts"),m=g("./src/controller/fps-controller.ts"),h=g("./src/controller/eme-controller.ts"),T=g("./src/utils/xhr-loader.ts"),y=g("./src/utils/fetch-loader.ts"),d=g("./src/utils/cues.ts"),e=g("./src/utils/mediakeys-helper.ts"),a=g("./src/utils/logger.ts");function t(){return(t=Object.assign||function(v){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var c in i)Object.prototype.hasOwnProperty.call(i,c)&&(v[c]=i[c])}return v}).apply(this,arguments)}function o(v,r){var i=Object.keys(v);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(v);r&&(c=c.filter(function(S){return Object.getOwnPropertyDescriptor(v,S).enumerable})),i.push.apply(i,c)}return i}function u(v){for(var r=1;r<arguments.length;r++){var i=arguments[r]!=null?arguments[r]:{};r%2?o(Object(i),!0).forEach(function(c){n(v,c,i[c])}):Object.getOwnPropertyDescriptors?Object.defineProperties(v,Object.getOwnPropertyDescriptors(i)):o(Object(i)).forEach(function(c){Object.defineProperty(v,c,Object.getOwnPropertyDescriptor(i,c))})}return v}function n(v,r,i){return r in v?Object.defineProperty(v,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):v[r]=i,v}var l=u(u({autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,initialLiveManifestSize:1,maxBufferLength:30,backBufferLength:1/0,maxBufferSize:6e7,maxBufferHole:.1,highBufferWatchdogPeriod:2,nudgeOffset:.1,nudgeMaxRetry:3,maxFragLookUpTolerance:.25,liveSyncDurationCount:3,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,maxLiveSyncPlaybackRate:1,liveDurationInfinity:!1,liveBackBufferLength:null,maxMaxBufferLength:600,enableWorker:!0,enableSoftwareAES:!0,manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,startLevel:void 0,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,loader:T.default,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,licenseXhrSetup:void 0,licenseResponseCallback:void 0,abrController:R.default,bufferController:D.default,capLevelController:b.default,fpsController:m.default,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0,emeEnabled:!1,widevineLicenseUrl:void 0,drmSystemOptions:{},requestMediaKeySystemAccessFunc:e.requestMediaKeySystemAccess,testBandwidth:!0,progressive:!1,lowLatencyMode:!0},{cueHandler:d.default,enableCEA708Captions:!0,enableWebVTT:!0,enableIMSC1:!0,captionsTextTrack1Label:"English",captionsTextTrack1LanguageCode:"en",captionsTextTrack2Label:"Spanish",captionsTextTrack2LanguageCode:"es",captionsTextTrack3Label:"Unknown CC",captionsTextTrack3LanguageCode:"",captionsTextTrack4Label:"Unknown CC",captionsTextTrack4LanguageCode:"",renderTextTracksNatively:!0}),{},{subtitleStreamController:k.SubtitleStreamController,subtitleTrackController:C.default,timelineController:s.TimelineController,audioStreamController:E.default,audioTrackController:A.default,emeController:h.default});function p(v,r){if((r.liveSyncDurationCount||r.liveMaxLatencyDurationCount)&&(r.liveSyncDuration||r.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");if(r.liveMaxLatencyDurationCount!==void 0&&(r.liveSyncDurationCount===void 0||r.liveMaxLatencyDurationCount<=r.liveSyncDurationCount))throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be greater than "liveSyncDurationCount"');if(r.liveMaxLatencyDuration!==void 0&&(r.liveSyncDuration===void 0||r.liveMaxLatencyDuration<=r.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be greater than "liveSyncDuration"');return t({},v,r)}function f(v){var r=v.loader;r!==y.default&&r!==T.default?(a.logger.log("[config]: Custom loader detected, cannot enable progressive streaming"),v.progressive=!1):Object(y.fetchSupported)()&&(v.loader=y.default,v.progressive=!0,v.enableSoftwareAES=!0,a.logger.log("[config]: Progressive streaming enabled, using FetchLoader"))}},"./src/controller/abr-controller.ts":function(M,I,g){g.r(I);var R=g("./src/polyfills/number.ts"),E=g("./src/utils/ewma-bandwidth-estimator.ts"),A=g("./src/events.ts"),k=g("./src/utils/buffer-helper.ts"),C=g("./src/errors.ts"),D=g("./src/types/loader.ts"),s=g("./src/utils/logger.ts");function b(h,T){for(var y=0;y<T.length;y++){var d=T[y];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(h,d.key,d)}}var m=function(){function h(e){this.hls=void 0,this.lastLoadedFragLevel=0,this._nextAutoLevel=-1,this.timer=void 0,this.onCheck=this._abandonRulesCheck.bind(this),this.fragCurrent=null,this.partCurrent=null,this.bitrateTestDelay=0,this.bwEstimator=void 0,this.hls=e;var a=e.config;this.bwEstimator=new E.default(a.abrEwmaSlowVoD,a.abrEwmaFastVoD,a.abrEwmaDefaultEstimate),this.registerListeners()}var T,y,d=h.prototype;return d.registerListeners=function(){var e=this.hls;e.on(A.Events.FRAG_LOADING,this.onFragLoading,this),e.on(A.Events.FRAG_LOADED,this.onFragLoaded,this),e.on(A.Events.FRAG_BUFFERED,this.onFragBuffered,this),e.on(A.Events.LEVEL_LOADED,this.onLevelLoaded,this),e.on(A.Events.ERROR,this.onError,this)},d.unregisterListeners=function(){var e=this.hls;e.off(A.Events.FRAG_LOADING,this.onFragLoading,this),e.off(A.Events.FRAG_LOADED,this.onFragLoaded,this),e.off(A.Events.FRAG_BUFFERED,this.onFragBuffered,this),e.off(A.Events.LEVEL_LOADED,this.onLevelLoaded,this),e.off(A.Events.ERROR,this.onError,this)},d.destroy=function(){this.unregisterListeners(),this.clearTimer(),this.hls=this.onCheck=null,this.fragCurrent=this.partCurrent=null},d.onFragLoading=function(e,a){var t,o=a.frag;o.type===D.PlaylistLevelType.MAIN&&(this.timer||(this.fragCurrent=o,this.partCurrent=(t=a.part)!=null?t:null,this.timer=self.setInterval(this.onCheck,100)))},d.onLevelLoaded=function(e,a){var t=this.hls.config;a.details.live?this.bwEstimator.update(t.abrEwmaSlowLive,t.abrEwmaFastLive):this.bwEstimator.update(t.abrEwmaSlowVoD,t.abrEwmaFastVoD)},d._abandonRulesCheck=function(){var e=this.fragCurrent,a=this.partCurrent,t=this.hls,o=t.autoLevelEnabled,u=t.config,n=t.media;if(e&&n){var l=a?a.stats:e.stats,p=a?a.duration:e.duration;if(l.aborted)return s.logger.warn("frag loader destroy or aborted, disarm abandonRules"),this.clearTimer(),void(this._nextAutoLevel=-1);if(o&&!n.paused&&n.playbackRate&&n.readyState){var f=performance.now()-l.loading.start,v=Math.abs(n.playbackRate);if(!(f<=500*p/v)){var r=t.levels,i=t.minAutoLevel,c=r[e.level],S=l.total||Math.max(l.loaded,Math.round(p*c.maxBitrate/8)),L=Math.max(1,l.bwEstimate?l.bwEstimate/8:1e3*l.loaded/f),_=(S-l.loaded)/L,x=n.currentTime,w=(k.BufferHelper.bufferInfo(n,x,u.maxBufferHole).end-x)/v;if(!(w>=2*p/v||_<=w)){var O,P=Number.POSITIVE_INFINITY;for(O=e.level-1;O>i&&!((P=p*r[O].maxBitrate/(6.4*L))<w);O--);if(!(P>=_)){var F=this.bwEstimator.getEstimate();s.logger.warn("Fragment "+e.sn+(a?" part "+a.index:"")+" of level "+e.level+" is loading too slowly and will cause an underbuffer; aborting and switching to level "+O+`
      Current BW estimate: `+(Object(R.isFiniteNumber)(F)?(F/1024).toFixed(3):"Unknown")+` Kb/s
      Estimated load time for current fragment: `+_.toFixed(3)+` s
      Estimated load time for the next fragment: `+P.toFixed(3)+` s
      Time to underbuffer: `+w.toFixed(3)+" s"),t.nextLoadLevel=O,this.bwEstimator.sample(f,l.loaded),this.clearTimer(),e.loader&&(this.fragCurrent=this.partCurrent=null,e.loader.abort()),t.trigger(A.Events.FRAG_LOAD_EMERGENCY_ABORTED,{frag:e,part:a,stats:l})}}}}}},d.onFragLoaded=function(e,a){var t=a.frag,o=a.part;if(t.type===D.PlaylistLevelType.MAIN&&Object(R.isFiniteNumber)(t.sn)){var u=o?o.stats:t.stats,n=o?o.duration:t.duration;if(this.clearTimer(),this.lastLoadedFragLevel=t.level,this._nextAutoLevel=-1,this.hls.config.abrMaxWithRealBitrate){var l=this.hls.levels[t.level],p=(l.loaded?l.loaded.bytes:0)+u.loaded,f=(l.loaded?l.loaded.duration:0)+n;l.loaded={bytes:p,duration:f},l.realBitrate=Math.round(8*p/f)}if(t.bitrateTest){var v={stats:u,frag:t,part:o,id:t.type};this.onFragBuffered(A.Events.FRAG_BUFFERED,v),t.bitrateTest=!1}}},d.onFragBuffered=function(e,a){var t=a.frag,o=a.part,u=o?o.stats:t.stats;if(!u.aborted&&t.type===D.PlaylistLevelType.MAIN&&t.sn!=="initSegment"){var n=u.parsing.end-u.loading.start;this.bwEstimator.sample(n,u.loaded),u.bwEstimate=this.bwEstimator.getEstimate(),t.bitrateTest?this.bitrateTestDelay=n/1e3:this.bitrateTestDelay=0}},d.onError=function(e,a){switch(a.details){case C.ErrorDetails.FRAG_LOAD_ERROR:case C.ErrorDetails.FRAG_LOAD_TIMEOUT:this.clearTimer()}},d.clearTimer=function(){self.clearInterval(this.timer),this.timer=void 0},d.getNextABRAutoLevel=function(){var e=this.fragCurrent,a=this.partCurrent,t=this.hls,o=t.maxAutoLevel,u=t.config,n=t.minAutoLevel,l=t.media,p=a?a.duration:e?e.duration:0,f=l?l.currentTime:0,v=l&&l.playbackRate!==0?Math.abs(l.playbackRate):1,r=this.bwEstimator?this.bwEstimator.getEstimate():u.abrEwmaDefaultEstimate,i=(k.BufferHelper.bufferInfo(l,f,u.maxBufferHole).end-f)/v,c=this.findBestLevel(r,n,o,i,u.abrBandWidthFactor,u.abrBandWidthUpFactor);if(c>=0)return c;s.logger.trace((i?"rebuffering expected":"buffer is empty")+", finding optimal quality level");var S=p?Math.min(p,u.maxStarvationDelay):u.maxStarvationDelay,L=u.abrBandWidthFactor,_=u.abrBandWidthUpFactor;if(!i){var x=this.bitrateTestDelay;x&&(S=(p?Math.min(p,u.maxLoadingDelay):u.maxLoadingDelay)-x,s.logger.trace("bitrate test took "+Math.round(1e3*x)+"ms, set first fragment max fetchDuration to "+Math.round(1e3*S)+" ms"),L=_=1)}return c=this.findBestLevel(r,n,o,i+S,L,_),Math.max(c,0)},d.findBestLevel=function(e,a,t,o,u,n){for(var l,p=this.fragCurrent,f=this.partCurrent,v=this.lastLoadedFragLevel,r=this.hls.levels,i=r[v],c=!(i==null||(l=i.details)===null||l===void 0||!l.live),S=i==null?void 0:i.codecSet,L=f?f.duration:p?p.duration:0,_=t;_>=a;_--){var x=r[_];if(x&&(!S||x.codecSet===S)){var w=x.details,O=(f?w==null?void 0:w.partTarget:w==null?void 0:w.averagetargetduration)||L,P=void 0;P=_<=v?u*e:n*e;var F=r[_].maxBitrate,N=F*O/P;if(s.logger.trace("level/adjustedbw/bitrate/avgDuration/maxFetchDuration/fetchDuration: "+_+"/"+Math.round(P)+"/"+F+"/"+O+"/"+o+"/"+N),P>F&&(!N||c&&!this.bitrateTestDelay||N<o))return _}}return-1},T=h,(y=[{key:"nextAutoLevel",get:function(){var e=this._nextAutoLevel,a=this.bwEstimator;if(!(e===-1||a&&a.canEstimate()))return e;var t=this.getNextABRAutoLevel();return e!==-1&&(t=Math.min(e,t)),t},set:function(e){this._nextAutoLevel=e}}])&&b(T.prototype,y),h}();I.default=m},"./src/controller/audio-stream-controller.ts":function(M,I,g){g.r(I);var R=g("./src/polyfills/number.ts"),E=g("./src/controller/base-stream-controller.ts"),A=g("./src/events.ts"),k=g("./src/utils/buffer-helper.ts"),C=g("./src/controller/fragment-tracker.ts"),D=g("./src/types/level.ts"),s=g("./src/types/loader.ts"),b=g("./src/loader/fragment.ts"),m=g("./src/demux/chunk-cache.ts"),h=g("./src/demux/transmuxer-interface.ts"),T=g("./src/types/transmuxer.ts"),y=g("./src/controller/fragment-finders.ts"),d=g("./src/utils/discontinuities.ts"),e=g("./src/errors.ts"),a=g("./src/utils/logger.ts");function t(){return(t=Object.assign||function(n){for(var l=1;l<arguments.length;l++){var p=arguments[l];for(var f in p)Object.prototype.hasOwnProperty.call(p,f)&&(n[f]=p[f])}return n}).apply(this,arguments)}function o(n,l){return(o=Object.setPrototypeOf||function(p,f){return p.__proto__=f,p})(n,l)}var u=function(n){var l,p;function f(r,i){var c;return(c=n.call(this,r,i,"[audio-stream-controller]")||this).videoBuffer=null,c.videoTrackCC=-1,c.waitingVideoCC=-1,c.audioSwitch=!1,c.trackId=-1,c.waitingData=null,c.mainDetails=null,c.bufferFlushed=!1,c._registerListeners(),c}p=n,(l=f).prototype=Object.create(p.prototype),l.prototype.constructor=l,o(l,p);var v=f.prototype;return v.onHandlerDestroying=function(){this._unregisterListeners(),this.mainDetails=null},v._registerListeners=function(){var r=this.hls;r.on(A.Events.MEDIA_ATTACHED,this.onMediaAttached,this),r.on(A.Events.MEDIA_DETACHING,this.onMediaDetaching,this),r.on(A.Events.MANIFEST_LOADING,this.onManifestLoading,this),r.on(A.Events.LEVEL_LOADED,this.onLevelLoaded,this),r.on(A.Events.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),r.on(A.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),r.on(A.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),r.on(A.Events.ERROR,this.onError,this),r.on(A.Events.BUFFER_RESET,this.onBufferReset,this),r.on(A.Events.BUFFER_CREATED,this.onBufferCreated,this),r.on(A.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),r.on(A.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),r.on(A.Events.FRAG_BUFFERED,this.onFragBuffered,this)},v._unregisterListeners=function(){var r=this.hls;r.off(A.Events.MEDIA_ATTACHED,this.onMediaAttached,this),r.off(A.Events.MEDIA_DETACHING,this.onMediaDetaching,this),r.off(A.Events.MANIFEST_LOADING,this.onManifestLoading,this),r.off(A.Events.LEVEL_LOADED,this.onLevelLoaded,this),r.off(A.Events.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),r.off(A.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),r.off(A.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),r.off(A.Events.ERROR,this.onError,this),r.off(A.Events.BUFFER_RESET,this.onBufferReset,this),r.off(A.Events.BUFFER_CREATED,this.onBufferCreated,this),r.off(A.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),r.off(A.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),r.off(A.Events.FRAG_BUFFERED,this.onFragBuffered,this)},v.onInitPtsFound=function(r,i){var c=i.frag,S=i.id,L=i.initPTS;if(S==="main"){var _=c.cc;this.initPTS[c.cc]=L,this.log("InitPTS for cc: "+_+" found from main: "+L),this.videoTrackCC=_,this.state===E.State.WAITING_INIT_PTS&&this.tick()}},v.startLoad=function(r){if(!this.levels)return this.startPosition=r,void(this.state=E.State.STOPPED);var i=this.lastCurrentTime;this.stopLoad(),this.setInterval(100),this.fragLoadError=0,i>0&&r===-1?(this.log("Override startPosition with lastCurrentTime @"+i.toFixed(3)),this.state=E.State.IDLE):(this.loadedmetadata=!1,this.state=E.State.WAITING_TRACK),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=r,this.tick()},v.doTick=function(){switch(this.state){case E.State.IDLE:this.doTickIdle();break;case E.State.WAITING_TRACK:var r,i=this.levels,c=this.trackId,S=i==null||(r=i[c])===null||r===void 0?void 0:r.details;if(S){if(this.waitForCdnTuneIn(S))break;this.state=E.State.WAITING_INIT_PTS}break;case E.State.FRAG_LOADING_WAITING_RETRY:var L,_=performance.now(),x=this.retryDate;(!x||_>=x||(L=this.media)!==null&&L!==void 0&&L.seeking)&&(this.log("RetryDate reached, switch back to IDLE state"),this.state=E.State.IDLE);break;case E.State.WAITING_INIT_PTS:var w=this.waitingData;if(w){var O=w.frag,P=w.part,F=w.cache,N=w.complete;if(this.initPTS[O.cc]!==void 0){this.waitingData=null,this.waitingVideoCC=-1,this.state=E.State.FRAG_LOADING;var U={frag:O,part:P,payload:F.flush(),networkDetails:null};this._handleFragmentLoadProgress(U),N&&n.prototype._handleFragmentLoadComplete.call(this,U)}else if(this.videoTrackCC!==this.waitingVideoCC)a.logger.log("Waiting fragment cc ("+O.cc+") cancelled because video is at cc "+this.videoTrackCC),this.clearWaitingFragment();else{var G=this.getLoadPosition(),K=k.BufferHelper.bufferInfo(this.mediaBuffer,G,this.config.maxBufferHole);Object(y.fragmentWithinToleranceTest)(K.end,this.config.maxFragLookUpTolerance,O)<0&&(a.logger.log("Waiting fragment cc ("+O.cc+") @ "+O.start+" cancelled because another fragment at "+K.end+" is needed"),this.clearWaitingFragment())}}else this.state=E.State.IDLE}this.onTickEnd()},v.clearWaitingFragment=function(){var r=this.waitingData;r&&(this.fragmentTracker.removeFragment(r.frag),this.waitingData=null,this.waitingVideoCC=-1,this.state=E.State.IDLE)},v.onTickEnd=function(){var r=this.media;if(r&&r.readyState){var i=(this.mediaBuffer?this.mediaBuffer:r).buffered;!this.loadedmetadata&&i.length&&(this.loadedmetadata=!0),this.lastCurrentTime=r.currentTime}},v.doTickIdle=function(){var r,i,c=this.hls,S=this.levels,L=this.media,_=this.trackId,x=c.config;if(S&&S[_]&&(L||!this.startFragRequested&&x.startFragPrefetch)){var w=S[_].details;if(!w||w.live&&this.levelLastLoaded!==_||this.waitForCdnTuneIn(w))this.state=E.State.WAITING_TRACK;else{this.bufferFlushed&&(this.bufferFlushed=!1,this.afterBufferFlushed(this.mediaBuffer?this.mediaBuffer:this.media,b.ElementaryStreamTypes.AUDIO,s.PlaylistLevelType.AUDIO));var O=this.getFwdBufferInfo(this.mediaBuffer?this.mediaBuffer:this.media,s.PlaylistLevelType.AUDIO);if(O!==null){var P=O.len,F=this.getMaxBufferLength(),N=this.audioSwitch;if(!(P>=F)||N){if(!N&&this._streamEnded(O,w))return c.trigger(A.Events.BUFFER_EOS,{type:"audio"}),void(this.state=E.State.ENDED);var U=w.fragments[0].start,G=O.end;if(N){var K=this.getLoadPosition();G=K,w.PTSKnown&&K<U&&(O.end>U||O.nextStart)&&(this.log("Alt audio track ahead of main track, seek to start of alt audio track"),L.currentTime=U+.05)}var q=this.getNextFragment(G,w);q?((r=q.decryptdata)===null||r===void 0?void 0:r.keyFormat)!=="identity"||(i=q.decryptdata)!==null&&i!==void 0&&i.key?this.loadFragment(q,w,G):this.loadKey(q,w):this.bufferFlushed=!0}}}}},v.getMaxBufferLength=function(){var r=n.prototype.getMaxBufferLength.call(this),i=this.getFwdBufferInfo(this.videoBuffer?this.videoBuffer:this.media,s.PlaylistLevelType.MAIN);return i===null?r:Math.max(r,i.len)},v.onMediaDetaching=function(){this.videoBuffer=null,n.prototype.onMediaDetaching.call(this)},v.onAudioTracksUpdated=function(r,i){var c=i.audioTracks;this.resetTransmuxer(),this.levels=c.map(function(S){return new D.Level(S)})},v.onAudioTrackSwitching=function(r,i){var c=!!i.url;this.trackId=i.id;var S=this.fragCurrent;S!=null&&S.loader&&S.loader.abort(),this.fragCurrent=null,this.clearWaitingFragment(),c?this.setInterval(100):this.resetTransmuxer(),c?(this.audioSwitch=!0,this.state=E.State.IDLE):this.state=E.State.STOPPED,this.tick()},v.onManifestLoading=function(){this.mainDetails=null,this.fragmentTracker.removeAllFragments(),this.startPosition=this.lastCurrentTime=0,this.bufferFlushed=!1},v.onLevelLoaded=function(r,i){this.mainDetails=i.details},v.onAudioTrackLoaded=function(r,i){var c,S=this.levels,L=i.details,_=i.id;if(S){this.log("Track "+_+" loaded ["+L.startSN+","+L.endSN+"],duration:"+L.totalduration);var x=S[_],w=0;if(L.live||(c=x.details)!==null&&c!==void 0&&c.live){var O=this.mainDetails;if(L.fragments[0]||(L.deltaUpdateFailed=!0),L.deltaUpdateFailed||!O)return;!x.details&&L.hasProgramDateTime&&O.hasProgramDateTime?(Object(d.alignPDT)(L,O),w=L.fragments[0].start):w=this.alignPlaylists(L,x.details)}x.details=L,this.levelLastLoaded=_,this.startFragRequested||!this.mainDetails&&L.live||this.setStartPosition(x.details,w),this.state!==E.State.WAITING_TRACK||this.waitForCdnTuneIn(L)||(this.state=E.State.IDLE),this.tick()}else this.warn("Audio tracks were reset while loading level "+_)},v._handleFragmentLoadProgress=function(r){var i,c=r.frag,S=r.part,L=r.payload,_=this.config,x=this.trackId,w=this.levels;if(w){var O=w[x];console.assert(O,"Audio track is defined on fragment load progress");var P=O.details;console.assert(P,"Audio track details are defined on fragment load progress");var F=_.defaultAudioCodec||O.audioCodec||"mp4a.40.2",N=this.transmuxer;N||(N=this.transmuxer=new h.default(this.hls,s.PlaylistLevelType.AUDIO,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)));var U=this.initPTS[c.cc],G=(i=c.initSegment)===null||i===void 0?void 0:i.data;if(U!==void 0){var K=S?S.index:-1,q=K!==-1,Y=new T.ChunkMetadata(c.level,c.sn,c.stats.chunkCount,L.byteLength,K,q);N.push(L,G,F,"",c,S,P.totalduration,!1,Y,U)}else a.logger.log("Unknown video PTS for cc "+c.cc+", waiting for video PTS before demuxing audio frag "+c.sn+" of ["+P.startSN+" ,"+P.endSN+"],track "+x),(this.waitingData=this.waitingData||{frag:c,part:S,cache:new m.default,complete:!1}).cache.push(new Uint8Array(L)),this.waitingVideoCC=this.videoTrackCC,this.state=E.State.WAITING_INIT_PTS}else this.warn("Audio tracks were reset while fragment load was in progress. Fragment "+c.sn+" of level "+c.level+" will not be buffered")},v._handleFragmentLoadComplete=function(r){this.waitingData?this.waitingData.complete=!0:n.prototype._handleFragmentLoadComplete.call(this,r)},v.onBufferReset=function(){this.mediaBuffer=this.videoBuffer=null,this.loadedmetadata=!1},v.onBufferCreated=function(r,i){var c=i.tracks.audio;c&&(this.mediaBuffer=c.buffer),i.tracks.video&&(this.videoBuffer=i.tracks.video.buffer)},v.onFragBuffered=function(r,i){var c=i.frag,S=i.part;c.type===s.PlaylistLevelType.AUDIO&&(this.fragContextChanged(c)?this.warn("Fragment "+c.sn+(S?" p: "+S.index:"")+" of level "+c.level+" finished buffering, but was aborted. state: "+this.state+", audioSwitch: "+this.audioSwitch):(c.sn!=="initSegment"&&(this.fragPrevious=c,this.audioSwitch&&(this.audioSwitch=!1,this.hls.trigger(A.Events.AUDIO_TRACK_SWITCHED,{id:this.trackId}))),this.fragBufferedComplete(c,S)))},v.onError=function(r,i){switch(i.details){case e.ErrorDetails.FRAG_LOAD_ERROR:case e.ErrorDetails.FRAG_LOAD_TIMEOUT:case e.ErrorDetails.KEY_LOAD_ERROR:case e.ErrorDetails.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(s.PlaylistLevelType.AUDIO,i);break;case e.ErrorDetails.AUDIO_TRACK_LOAD_ERROR:case e.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT:this.state!==E.State.ERROR&&this.state!==E.State.STOPPED&&(this.state=i.fatal?E.State.ERROR:E.State.IDLE,this.warn(i.details+" while loading frag, switching to "+this.state+" state"));break;case e.ErrorDetails.BUFFER_FULL_ERROR:if(i.parent==="audio"&&(this.state===E.State.PARSING||this.state===E.State.PARSED)){var c=!0,S=this.getFwdBufferInfo(this.mediaBuffer,s.PlaylistLevelType.AUDIO);S&&S.len>.5&&(c=!this.reduceMaxBufferLength(S.len)),c&&(this.warn("Buffer full error also media.currentTime is not buffered, flush audio buffer"),this.fragCurrent=null,n.prototype.flushMainBuffer.call(this,0,Number.POSITIVE_INFINITY,"audio")),this.resetLoadingState()}}},v.onBufferFlushed=function(r,i){i.type===b.ElementaryStreamTypes.AUDIO&&(this.bufferFlushed=!0)},v._handleTransmuxComplete=function(r){var i,c="audio",S=this.hls,L=r.remuxResult,_=r.chunkMeta,x=this.getCurrentContext(_);if(!x)return this.warn("The loading context changed while buffering fragment "+_.sn+" of level "+_.level+". This chunk will not be buffered."),void this.resetLiveStartWhenNotLoaded(_.level);var w=x.frag,O=x.part,P=L.audio,F=L.text,N=L.id3,U=L.initSegment;if(!this.fragContextChanged(w)){if(this.state=E.State.PARSING,this.audioSwitch&&P&&this.completeAudioSwitch(),U!=null&&U.tracks&&(this._bufferInitSegment(U.tracks,w,_),S.trigger(A.Events.FRAG_PARSING_INIT_SEGMENT,{frag:w,id:c,tracks:U.tracks})),P){var G=P.startPTS,K=P.endPTS,q=P.startDTS,Y=P.endDTS;O&&(O.elementaryStreams[b.ElementaryStreamTypes.AUDIO]={startPTS:G,endPTS:K,startDTS:q,endDTS:Y}),w.setElementaryStreamInfo(b.ElementaryStreamTypes.AUDIO,G,K,q,Y),this.bufferFragmentData(P,w,O,_)}if(N!=null&&(i=N.samples)!==null&&i!==void 0&&i.length){var j=t({frag:w,id:c},N);S.trigger(A.Events.FRAG_PARSING_METADATA,j)}if(F){var X=t({frag:w,id:c},F);S.trigger(A.Events.FRAG_PARSING_USERDATA,X)}}},v._bufferInitSegment=function(r,i,c){if(this.state===E.State.PARSING){r.video&&delete r.video;var S=r.audio;if(S){S.levelCodec=S.codec,S.id="audio",this.log("Init audio buffer, container:"+S.container+", codecs[parsed]=["+S.codec+"]"),this.hls.trigger(A.Events.BUFFER_CODECS,r);var L=S.initSegment;if(L!=null&&L.byteLength){var _={type:"audio",frag:i,part:null,chunkMeta:c,parent:i.type,data:L};this.hls.trigger(A.Events.BUFFER_APPENDING,_)}this.tick()}}},v.loadFragment=function(r,i,c){var S=this.fragmentTracker.getState(r);this.fragCurrent=r,(this.audioSwitch||S===C.FragmentState.NOT_LOADED||S===C.FragmentState.PARTIAL)&&(r.sn==="initSegment"?this._loadInitSegment(r):i.live&&!Object(R.isFiniteNumber)(this.initPTS[r.cc])?(this.log("Waiting for video PTS in continuity counter "+r.cc+" of live stream before loading audio fragment "+r.sn+" of level "+this.trackId),this.state=E.State.WAITING_INIT_PTS):(this.startFragRequested=!0,n.prototype.loadFragment.call(this,r,i,c)))},v.completeAudioSwitch=function(){var r=this.hls,i=this.media,c=this.trackId;i&&(this.log("Switching audio track : flushing all audio"),n.prototype.flushMainBuffer.call(this,0,Number.POSITIVE_INFINITY,"audio")),this.audioSwitch=!1,r.trigger(A.Events.AUDIO_TRACK_SWITCHED,{id:c})},f}(E.default);I.default=u},"./src/controller/audio-track-controller.ts":function(M,I,g){g.r(I);var R=g("./src/events.ts"),E=g("./src/errors.ts"),A=g("./src/controller/base-playlist-controller.ts"),k=g("./src/types/loader.ts");function C(b,m){for(var h=0;h<m.length;h++){var T=m[h];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(b,T.key,T)}}function D(b,m){return(D=Object.setPrototypeOf||function(h,T){return h.__proto__=T,h})(b,m)}var s=function(b){var m,h;function T(a){var t;return(t=b.call(this,a,"[audio-track-controller]")||this).tracks=[],t.groupId=null,t.tracksInGroup=[],t.trackId=-1,t.trackName="",t.selectDefaultTrack=!0,t.registerListeners(),t}h=b,(m=T).prototype=Object.create(h.prototype),m.prototype.constructor=m,D(m,h);var y,d,e=T.prototype;return e.registerListeners=function(){var a=this.hls;a.on(R.Events.MANIFEST_LOADING,this.onManifestLoading,this),a.on(R.Events.MANIFEST_PARSED,this.onManifestParsed,this),a.on(R.Events.LEVEL_LOADING,this.onLevelLoading,this),a.on(R.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),a.on(R.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),a.on(R.Events.ERROR,this.onError,this)},e.unregisterListeners=function(){var a=this.hls;a.off(R.Events.MANIFEST_LOADING,this.onManifestLoading,this),a.off(R.Events.MANIFEST_PARSED,this.onManifestParsed,this),a.off(R.Events.LEVEL_LOADING,this.onLevelLoading,this),a.off(R.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),a.off(R.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),a.off(R.Events.ERROR,this.onError,this)},e.destroy=function(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,b.prototype.destroy.call(this)},e.onManifestLoading=function(){this.tracks=[],this.groupId=null,this.tracksInGroup=[],this.trackId=-1,this.trackName="",this.selectDefaultTrack=!0},e.onManifestParsed=function(a,t){this.tracks=t.audioTracks||[]},e.onAudioTrackLoaded=function(a,t){var o=t.id,u=t.details,n=this.tracksInGroup[o];if(n){var l=n.details;n.details=t.details,this.log("audioTrack "+o+" loaded ["+u.startSN+"-"+u.endSN+"]"),o===this.trackId&&(this.retryCount=0,this.playlistLoaded(o,t,l))}else this.warn("Invalid audio track id "+o)},e.onLevelLoading=function(a,t){this.switchLevel(t.level)},e.onLevelSwitching=function(a,t){this.switchLevel(t.level)},e.switchLevel=function(a){var t=this.hls.levels[a];if(t!=null&&t.audioGroupIds){var o=t.audioGroupIds[t.urlId];if(this.groupId!==o){this.groupId=o;var u=this.tracks.filter(function(l){return!o||l.groupId===o});this.selectDefaultTrack&&!u.some(function(l){return l.default})&&(this.selectDefaultTrack=!1),this.tracksInGroup=u;var n={audioTracks:u};this.log("Updating audio tracks, "+u.length+' track(s) found in "'+o+'" group-id'),this.hls.trigger(R.Events.AUDIO_TRACKS_UPDATED,n),this.selectInitialTrack()}}},e.onError=function(a,t){b.prototype.onError.call(this,a,t),!t.fatal&&t.context&&t.context.type===k.PlaylistContextType.AUDIO_TRACK&&t.context.id===this.trackId&&t.context.groupId===this.groupId&&this.retryLoadingOrFail(t)},e.setAudioTrack=function(a){var t=this.tracksInGroup;if(a<0||a>=t.length)this.warn("Invalid id passed to audio-track controller");else{this.clearTimer();var o=t[this.trackId];this.log("Now switching to audio-track index "+a);var u=t[a],n=u.id,l=u.groupId,p=l===void 0?"":l,f=u.name,v=u.type,r=u.url;if(this.trackId=a,this.trackName=f,this.selectDefaultTrack=!1,this.hls.trigger(R.Events.AUDIO_TRACK_SWITCHING,{id:n,groupId:p,name:f,type:v,url:r}),!u.details||u.details.live){var i=this.switchParams(u.url,o==null?void 0:o.details);this.loadPlaylist(i)}}},e.selectInitialTrack=function(){var a=this.tracksInGroup;console.assert(a.length,"Initial audio track should be selected when tracks are known");var t=this.trackName,o=this.findTrackId(t)||this.findTrackId();o!==-1?this.setAudioTrack(o):(this.warn("No track found for running audio group-ID: "+this.groupId),this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.MEDIA_ERROR,details:E.ErrorDetails.AUDIO_TRACK_LOAD_ERROR,fatal:!0}))},e.findTrackId=function(a){for(var t=this.tracksInGroup,o=0;o<t.length;o++){var u=t[o];if((!this.selectDefaultTrack||u.default)&&(!a||a===u.name))return u.id}return-1},e.loadPlaylist=function(a){var t=this.tracksInGroup[this.trackId];if(this.shouldLoadTrack(t)){var o=t.id,u=t.groupId,n=t.url;if(a)try{n=a.addDirectives(n)}catch(l){this.warn("Could not construct new URL with HLS Delivery Directives: "+l)}this.log("loading audio-track playlist for id: "+o),this.clearTimer(),this.hls.trigger(R.Events.AUDIO_TRACK_LOADING,{url:n,id:o,groupId:u,deliveryDirectives:a||null})}},y=T,(d=[{key:"audioTracks",get:function(){return this.tracksInGroup}},{key:"audioTrack",get:function(){return this.trackId},set:function(a){this.selectDefaultTrack=!1,this.setAudioTrack(a)}}])&&C(y.prototype,d),T}(A.default);I.default=s},"./src/controller/base-playlist-controller.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return D});var R=g("./src/polyfills/number.ts"),E=g("./src/types/level.ts"),A=g("./src/controller/level-helper.ts"),k=g("./src/utils/logger.ts"),C=g("./src/errors.ts"),D=function(){function s(m,h){this.hls=void 0,this.timer=-1,this.canLoad=!1,this.retryCount=0,this.log=void 0,this.warn=void 0,this.log=k.logger.log.bind(k.logger,h+":"),this.warn=k.logger.warn.bind(k.logger,h+":"),this.hls=m}var b=s.prototype;return b.destroy=function(){this.clearTimer(),this.hls=this.log=this.warn=null},b.onError=function(m,h){h.fatal&&h.type===C.ErrorTypes.NETWORK_ERROR&&this.clearTimer()},b.clearTimer=function(){clearTimeout(this.timer),this.timer=-1},b.startLoad=function(){this.canLoad=!0,this.retryCount=0,this.loadPlaylist()},b.stopLoad=function(){this.canLoad=!1,this.clearTimer()},b.switchParams=function(m,h){var T=h==null?void 0:h.renditionReports;if(T)for(var y=0;y<T.length;y++){var d=T[y],e=""+d.URI;if(e===m.substr(-e.length)){var a=parseInt(d["LAST-MSN"]),t=parseInt(d["LAST-PART"]);if(h&&this.hls.config.lowLatencyMode){var o=Math.min(h.age-h.partTarget,h.targetduration);t!==void 0&&o>h.partTarget&&(t+=1)}if(Object(R.isFiniteNumber)(a))return new E.HlsUrlParameters(a,Object(R.isFiniteNumber)(t)?t:void 0,E.HlsSkip.No)}}},b.loadPlaylist=function(m){},b.shouldLoadTrack=function(m){return this.canLoad&&m&&!!m.url&&(!m.details||m.details.live)},b.playlistLoaded=function(m,h,T){var y=this,d=h.details,e=h.stats,a=e.loading.end?Math.max(0,self.performance.now()-e.loading.end):0;if(d.advancedDateTime=Date.now()-a,d.live||T!=null&&T.live){if(d.reloaded(T),T&&this.log("live playlist "+m+" "+(d.advanced?"REFRESHED "+d.lastPartSn+"-"+d.lastPartIndex:"MISSED")),T&&d.fragments.length>0&&Object(A.mergeDetails)(T,d),!this.canLoad||!d.live)return;var t,o=void 0,u=void 0;if(d.canBlockReload&&d.endSN&&d.advanced){var n=this.hls.config.lowLatencyMode,l=d.lastPartSn,p=d.endSN,f=d.lastPartIndex,v=l===p;f!==-1?(o=v?p+1:l,u=v?n?0:f:f+1):o=p+1;var r=d.age,i=r+d.ageHeader,c=Math.min(i-d.partTarget,1.5*d.targetduration);if(c>0){if(T&&c>T.tuneInGoal)this.warn("CDN Tune-in goal increased from: "+T.tuneInGoal+" to: "+c+" with playlist age: "+d.age),c=0;else{var S=Math.floor(c/d.targetduration);o+=S,u!==void 0&&(u+=Math.round(c%d.targetduration/d.partTarget)),this.log("CDN Tune-in age: "+d.ageHeader+"s last advanced "+r.toFixed(2)+"s goal: "+c+" skip sn "+S+" to part "+u)}d.tuneInGoal=c}if(t=this.getDeliveryDirectives(d,h.deliveryDirectives,o,u),n||!v)return void this.loadPlaylist(t)}else t=this.getDeliveryDirectives(d,h.deliveryDirectives,o,u);var L=Object(A.computeReloadInterval)(d,e);o!==void 0&&d.canBlockReload&&(L-=d.partTarget||1),this.log("reload live playlist "+m+" in "+Math.round(L)+" ms"),this.timer=self.setTimeout(function(){return y.loadPlaylist(t)},L)}else this.clearTimer()},b.getDeliveryDirectives=function(m,h,T,y){var d=Object(E.getSkipValue)(m,T);return h!=null&&h.skip&&m.deltaUpdateFailed&&(T=h.msn,y=h.part,d=E.HlsSkip.No),new E.HlsUrlParameters(T,y,d)},b.retryLoadingOrFail=function(m){var h,T=this,y=this.hls.config,d=this.retryCount<y.levelLoadingMaxRetry;if(d)if(this.retryCount++,m.details.indexOf("LoadTimeOut")>-1&&(h=m.context)!==null&&h!==void 0&&h.deliveryDirectives)this.warn("retry playlist loading #"+this.retryCount+' after "'+m.details+'"'),this.loadPlaylist();else{var e=Math.min(Math.pow(2,this.retryCount)*y.levelLoadingRetryDelay,y.levelLoadingMaxRetryTimeout);this.timer=self.setTimeout(function(){return T.loadPlaylist()},e),this.warn("retry playlist loading #"+this.retryCount+" in "+e+' ms after "'+m.details+'"')}else this.warn('cannot recover from error "'+m.details+'"'),this.clearTimer(),m.fatal=!0;return d},s}()},"./src/controller/base-stream-controller.ts":function(M,I,g){g.r(I),g.d(I,"State",function(){return n}),g.d(I,"default",function(){return l});var R=g("./src/polyfills/number.ts"),E=g("./src/task-loop.ts"),A=g("./src/controller/fragment-tracker.ts"),k=g("./src/utils/buffer-helper.ts"),C=g("./src/utils/logger.ts"),D=g("./src/events.ts"),s=g("./src/errors.ts"),b=g("./src/types/transmuxer.ts"),m=g("./src/utils/mp4-tools.ts"),h=g("./src/utils/discontinuities.ts"),T=g("./src/controller/fragment-finders.ts"),y=g("./src/controller/level-helper.ts"),d=g("./src/loader/fragment-loader.ts"),e=g("./src/crypt/decrypter.ts"),a=g("./src/utils/time-ranges.ts"),t=g("./src/types/loader.ts");function o(p,f){for(var v=0;v<f.length;v++){var r=f[v];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(p,r.key,r)}}function u(p,f){return(u=Object.setPrototypeOf||function(v,r){return v.__proto__=r,v})(p,f)}var n={STOPPED:"STOPPED",IDLE:"IDLE",KEY_LOADING:"KEY_LOADING",FRAG_LOADING:"FRAG_LOADING",FRAG_LOADING_WAITING_RETRY:"FRAG_LOADING_WAITING_RETRY",WAITING_TRACK:"WAITING_TRACK",PARSING:"PARSING",PARSED:"PARSED",BACKTRACKING:"BACKTRACKING",ENDED:"ENDED",ERROR:"ERROR",WAITING_INIT_PTS:"WAITING_INIT_PTS",WAITING_LEVEL:"WAITING_LEVEL"},l=function(p){var f,v;function r(L,_,x){var w;return(w=p.call(this)||this).hls=void 0,w.fragPrevious=null,w.fragCurrent=null,w.fragmentTracker=void 0,w.transmuxer=null,w._state=n.STOPPED,w.media=void 0,w.mediaBuffer=void 0,w.config=void 0,w.bitrateTest=!1,w.lastCurrentTime=0,w.nextLoadPosition=0,w.startPosition=0,w.loadedmetadata=!1,w.fragLoadError=0,w.retryDate=0,w.levels=null,w.fragmentLoader=void 0,w.levelLastLoaded=null,w.startFragRequested=!1,w.decrypter=void 0,w.initPTS=[],w.onvseeking=null,w.onvended=null,w.logPrefix="",w.log=void 0,w.warn=void 0,w.logPrefix=x,w.log=C.logger.log.bind(C.logger,x+":"),w.warn=C.logger.warn.bind(C.logger,x+":"),w.hls=L,w.fragmentLoader=new d.default(L.config),w.fragmentTracker=_,w.config=L.config,w.decrypter=new e.default(L,L.config),L.on(D.Events.KEY_LOADED,w.onKeyLoaded,function(O){if(O===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O}(w)),w}v=p,(f=r).prototype=Object.create(v.prototype),f.prototype.constructor=f,u(f,v);var i,c,S=r.prototype;return S.doTick=function(){this.onTickEnd()},S.onTickEnd=function(){},S.startLoad=function(L){},S.stopLoad=function(){this.fragmentLoader.abort();var L=this.fragCurrent;L&&this.fragmentTracker.removeFragment(L),this.resetTransmuxer(),this.fragCurrent=null,this.fragPrevious=null,this.clearInterval(),this.clearNextTick(),this.state=n.STOPPED},S._streamEnded=function(L,_){var x=this.fragCurrent,w=this.fragmentTracker;if(!_.live&&x&&x.sn===_.endSN&&!L.nextStart){var O=w.getState(x);return O===A.FragmentState.PARTIAL||O===A.FragmentState.OK}return!1},S.onMediaAttached=function(L,_){var x=this.media=this.mediaBuffer=_.media;this.onvseeking=this.onMediaSeeking.bind(this),this.onvended=this.onMediaEnded.bind(this),x.addEventListener("seeking",this.onvseeking),x.addEventListener("ended",this.onvended);var w=this.config;this.levels&&w.autoStartLoad&&this.state===n.STOPPED&&this.startLoad(w.startPosition)},S.onMediaDetaching=function(){var L=this.media;L!=null&&L.ended&&(this.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0),L&&(L.removeEventListener("seeking",this.onvseeking),L.removeEventListener("ended",this.onvended),this.onvseeking=this.onvended=null),this.media=this.mediaBuffer=null,this.loadedmetadata=!1,this.fragmentTracker.removeAllFragments(),this.stopLoad()},S.onMediaSeeking=function(){var L=this.config,_=this.fragCurrent,x=this.media,w=this.mediaBuffer,O=this.state,P=x?x.currentTime:0,F=k.BufferHelper.bufferInfo(w||x,P,L.maxBufferHole);if(this.log("media seeking to "+(Object(R.isFiniteNumber)(P)?P.toFixed(3):P)+", state: "+O),O===n.ENDED)this.resetLoadingState();else if(_&&!F.len){var N=L.maxFragLookUpTolerance,U=_.start-N,G=P>_.start+_.duration+N;(P<U||G)&&(G&&_.loader&&(this.log("seeking outside of buffer while fragment load in progress, cancel fragment load"),_.loader.abort()),this.resetLoadingState())}x&&(this.lastCurrentTime=P),this.loadedmetadata||F.len||(this.nextLoadPosition=this.startPosition=P),this.tickImmediate()},S.onMediaEnded=function(){this.startPosition=this.lastCurrentTime=0},S.onKeyLoaded=function(L,_){if(this.state===n.KEY_LOADING&&_.frag===this.fragCurrent&&this.levels){this.state=n.IDLE;var x=this.levels[_.frag.level].details;x&&this.loadFragment(_.frag,x,_.frag.start)}},S.onHandlerDestroying=function(){this.stopLoad(),p.prototype.onHandlerDestroying.call(this)},S.onHandlerDestroyed=function(){this.state=n.STOPPED,this.hls.off(D.Events.KEY_LOADED,this.onKeyLoaded,this),this.fragmentLoader&&this.fragmentLoader.destroy(),this.decrypter&&this.decrypter.destroy(),this.hls=this.log=this.warn=this.decrypter=this.fragmentLoader=this.fragmentTracker=null,p.prototype.onHandlerDestroyed.call(this)},S.loadKey=function(L,_){this.log("Loading key for "+L.sn+" of ["+_.startSN+"-"+_.endSN+"], "+(this.logPrefix==="[stream-controller]"?"level":"track")+" "+L.level),this.state=n.KEY_LOADING,this.fragCurrent=L,this.hls.trigger(D.Events.KEY_LOADING,{frag:L})},S.loadFragment=function(L,_,x){this._loadFragForPlayback(L,_,x)},S._loadFragForPlayback=function(L,_,x){var w=this;this._doFragLoad(L,_,x,function(O){if(w.fragContextChanged(L))return w.warn("Fragment "+L.sn+(O.part?" p: "+O.part.index:"")+" of level "+L.level+" was dropped during download."),void w.fragmentTracker.removeFragment(L);L.stats.chunkCount++,w._handleFragmentLoadProgress(O)}).then(function(O){if(O){w.fragLoadError=0;var P=w.state;if(!w.fragContextChanged(L))return"payload"in O&&(w.log("Loaded fragment "+L.sn+" of level "+L.level),w.hls.trigger(D.Events.FRAG_LOADED,O),w.state===n.BACKTRACKING)?(w.fragmentTracker.backtrack(L,O),void w.resetFragmentLoading(L)):void w._handleFragmentLoadComplete(O);(P===n.FRAG_LOADING||P===n.BACKTRACKING||!w.fragCurrent&&P===n.PARSING)&&(w.fragmentTracker.removeFragment(L),w.state=n.IDLE)}}).catch(function(O){w.warn(O),w.resetFragmentLoading(L)})},S.flushMainBuffer=function(L,_,x){if(x===void 0&&(x=null),L-_){var w={startOffset:L,endOffset:_,type:x};this.fragLoadError=0,this.hls.trigger(D.Events.BUFFER_FLUSHING,w)}},S._loadInitSegment=function(L){var _=this;this._doFragLoad(L).then(function(x){if(!x||_.fragContextChanged(L)||!_.levels)throw new Error("init load aborted");return x}).then(function(x){var w=_.hls,O=x.payload,P=L.decryptdata;if(O&&O.byteLength>0&&P&&P.key&&P.iv&&P.method==="AES-128"){var F=self.performance.now();return _.decrypter.webCryptoDecrypt(new Uint8Array(O),P.key.buffer,P.iv.buffer).then(function(N){var U=self.performance.now();return w.trigger(D.Events.FRAG_DECRYPTED,{frag:L,payload:N,stats:{tstart:F,tdecrypt:U}}),x.payload=N,x})}return x}).then(function(x){var w=_.fragCurrent,O=_.hls,P=_.levels;if(!P)throw new Error("init load aborted, missing levels");var F=P[L.level].details;console.assert(F,"Level details are defined when init segment is loaded");var N=L.stats;_.state=n.IDLE,_.fragLoadError=0,L.data=new Uint8Array(x.payload),N.parsing.start=N.buffering.start=self.performance.now(),N.parsing.end=N.buffering.end=self.performance.now(),x.frag===w&&O.trigger(D.Events.FRAG_BUFFERED,{stats:N,frag:w,part:null,id:L.type}),_.tick()}).catch(function(x){_.warn(x),_.resetFragmentLoading(L)})},S.fragContextChanged=function(L){var _=this.fragCurrent;return!L||!_||L.level!==_.level||L.sn!==_.sn||L.urlId!==_.urlId},S.fragBufferedComplete=function(L,_){var x=this.mediaBuffer?this.mediaBuffer:this.media;this.log("Buffered "+L.type+" sn: "+L.sn+(_?" part: "+_.index:"")+" of "+(this.logPrefix==="[stream-controller]"?"level":"track")+" "+L.level+" "+a.default.toString(k.BufferHelper.getBuffered(x))),this.state=n.IDLE,this.tick()},S._handleFragmentLoadComplete=function(L){var _=this.transmuxer;if(_){var x=L.frag,w=L.part,O=L.partsLoaded,P=!O||O.length===0||O.some(function(N){return!N}),F=new b.ChunkMetadata(x.level,x.sn,x.stats.chunkCount+1,0,w?w.index:-1,!P);_.flush(F)}},S._handleFragmentLoadProgress=function(L){},S._doFragLoad=function(L,_,x,w){var O=this;if(x===void 0&&(x=null),!this.levels)throw new Error("frag load aborted, missing levels");if(x=Math.max(L.start,x||0),this.config.lowLatencyMode&&_){var P=_.partList;if(P&&w){x>L.end&&_.fragmentHint&&(L=_.fragmentHint);var F=this.getNextPart(P,L,x);if(F>-1){var N=P[F];return this.log("Loading part sn: "+L.sn+" p: "+N.index+" cc: "+L.cc+" of playlist ["+_.startSN+"-"+_.endSN+"] parts [0-"+F+"-"+(P.length-1)+"] "+(this.logPrefix==="[stream-controller]"?"level":"track")+": "+L.level+", target: "+parseFloat(x.toFixed(3))),this.nextLoadPosition=N.start+N.duration,this.state=n.FRAG_LOADING,this.hls.trigger(D.Events.FRAG_LOADING,{frag:L,part:P[F],targetBufferTime:x}),this.doFragPartsLoad(L,P,F,w).catch(function(U){return O.handleFragLoadError(U)})}if(!L.url||this.loadedEndOfParts(P,x))return Promise.resolve(null)}}return this.log("Loading fragment "+L.sn+" cc: "+L.cc+" "+(_?"of ["+_.startSN+"-"+_.endSN+"] ":"")+(this.logPrefix==="[stream-controller]"?"level":"track")+": "+L.level+", target: "+parseFloat(x.toFixed(3))),Object(R.isFiniteNumber)(L.sn)&&!this.bitrateTest&&(this.nextLoadPosition=L.start+L.duration),this.state=n.FRAG_LOADING,this.hls.trigger(D.Events.FRAG_LOADING,{frag:L,targetBufferTime:x}),this.fragmentLoader.load(L,w).catch(function(U){return O.handleFragLoadError(U)})},S.doFragPartsLoad=function(L,_,x,w){var O=this;return new Promise(function(P,F){var N=[];(function U(G){var K=_[G];O.fragmentLoader.loadPart(L,K,w).then(function(q){N[K.index]=q;var Y=q.part;O.hls.trigger(D.Events.FRAG_LOADED,q);var j=_[G+1];if(!j||j.fragment!==L)return P({frag:L,part:Y,partsLoaded:N});U(G+1)}).catch(F)})(x)})},S.handleFragLoadError=function(L){var _=L.data;return _&&_.details===s.ErrorDetails.INTERNAL_ABORTED?this.handleFragLoadAborted(_.frag,_.part):this.hls.trigger(D.Events.ERROR,_),null},S._handleTransmuxerFlush=function(L){var _=this.getCurrentContext(L);if(_&&this.state===n.PARSING){var x=_.frag,w=_.part,O=_.level,P=self.performance.now();x.stats.parsing.end=P,w&&(w.stats.parsing.end=P),this.updateLevelTiming(x,w,O,L.partial)}else this.fragCurrent||(this.state=n.IDLE)},S.getCurrentContext=function(L){var _=this.levels,x=L.level,w=L.sn,O=L.part;if(!_||!_[x])return this.warn("Levels object was unset while buffering fragment "+w+" of level "+x+". The current chunk will not be buffered."),null;var P=_[x],F=O>-1?Object(y.getPartWith)(P,w,O):null,N=F?F.fragment:Object(y.getFragmentWithSN)(P,w,this.fragCurrent);return N?{frag:N,part:F,level:P}:null},S.bufferFragmentData=function(L,_,x,w){if(L&&this.state===n.PARSING){var O=L.data1,P=L.data2,F=O;if(O&&P&&(F=Object(m.appendUint8Array)(O,P)),F&&F.length){var N={type:L.type,frag:_,part:x,chunkMeta:w,parent:_.type,data:F};this.hls.trigger(D.Events.BUFFER_APPENDING,N),L.dropped&&L.independent&&!x&&this.flushBufferGap(_)}}},S.flushBufferGap=function(L){var _=this.media;if(_)if(k.BufferHelper.isBuffered(_,_.currentTime)){var x=_.currentTime,w=k.BufferHelper.bufferInfo(_,x,0),O=L.duration,P=Math.min(2*this.config.maxFragLookUpTolerance,.25*O),F=Math.max(Math.min(L.start-P,w.end-P),x+P);L.start-F>P&&this.flushMainBuffer(F,L.start)}else this.flushMainBuffer(0,L.start)},S.getFwdBufferInfo=function(L,_){var x=this.config,w=this.getLoadPosition();if(!Object(R.isFiniteNumber)(w))return null;var O=k.BufferHelper.bufferInfo(L,w,x.maxBufferHole);if(O.len===0&&O.nextStart!==void 0){var P=this.fragmentTracker.getBufferedFrag(w,_);if(P&&O.nextStart<P.end)return k.BufferHelper.bufferInfo(L,w,Math.max(O.nextStart,x.maxBufferHole))}return O},S.getMaxBufferLength=function(L){var _,x=this.config;return _=L?Math.max(8*x.maxBufferSize/L,x.maxBufferLength):x.maxBufferLength,Math.min(_,x.maxMaxBufferLength)},S.reduceMaxBufferLength=function(L){var _=this.config,x=L||_.maxBufferLength;return _.maxMaxBufferLength>=x&&(_.maxMaxBufferLength/=2,this.warn("Reduce max buffer length to "+_.maxMaxBufferLength+"s"),!0)},S.getNextFragment=function(L,_){var x,w,O=_.fragments,P=O.length;if(!P)return null;var F,N=this.config,U=O[0].start;if(_.live){var G=N.initialLiveManifestSize;if(P<G)return this.warn("Not enough fragments to start playback (have: "+P+", need: "+G+")"),null;_.PTSKnown||this.startFragRequested||this.startPosition!==-1||(F=this.getInitialLiveFragment(_,O),this.startPosition=F?this.hls.liveSyncPosition||F.start:L)}else L<=U&&(F=O[0]);if(!F){var K=N.lowLatencyMode?_.partEnd:_.fragmentEnd;F=this.getFragmentAtPosition(L,K,_)}return(x=F)===null||x===void 0||!x.initSegment||(w=F)!==null&&w!==void 0&&w.initSegment.data||this.bitrateTest||(F=F.initSegment),F},S.getNextPart=function(L,_,x){for(var w=-1,O=!1,P=!0,F=0,N=L.length;F<N;F++){var U=L[F];if(P=P&&!U.independent,w>-1&&x<U.start)break;var G=U.loaded;!G&&(O||U.independent||P)&&U.fragment===_&&(w=F),O=G}return w},S.loadedEndOfParts=function(L,_){var x=L[L.length-1];return x&&_>x.start&&x.loaded},S.getInitialLiveFragment=function(L,_){var x=this.fragPrevious,w=null;if(x){if(L.hasProgramDateTime&&(this.log("Live playlist, switching playlist, load frag with same PDT: "+x.programDateTime),w=Object(T.findFragmentByPDT)(_,x.endProgramDateTime,this.config.maxFragLookUpTolerance)),!w){var O=x.sn+1;if(O>=L.startSN&&O<=L.endSN){var P=_[O-L.startSN];x.cc===P.cc&&(w=P,this.log("Live playlist, switching playlist, load frag with next SN: "+w.sn))}w||(w=Object(T.findFragWithCC)(_,x.cc))&&this.log("Live playlist, switching playlist, load frag with same CC: "+w.sn)}}else{var F=this.hls.liveSyncPosition;F!==null&&(w=this.getFragmentAtPosition(F,this.bitrateTest?L.fragmentEnd:L.edge,L))}return w},S.getFragmentAtPosition=function(L,_,x){var w,O=this.config,P=this.fragPrevious,F=x.fragments,N=x.endSN,U=x.fragmentHint,G=O.maxFragLookUpTolerance,K=!!(O.lowLatencyMode&&x.partList&&U);if(K&&U&&!this.bitrateTest&&(F=F.concat(U),N=U.sn),L<_){var q=L>_-G?0:G;w=Object(T.findFragmentByPTS)(P,F,L,q)}else w=F[F.length-1];if(w){var Y=w.sn-x.startSN,j=P&&w.level===P.level,X=F[Y+1];if(this.fragmentTracker.getState(w)===A.FragmentState.BACKTRACKED){w=null;for(var Q=Y;F[Q]&&this.fragmentTracker.getState(F[Q])===A.FragmentState.BACKTRACKED;)w=P?F[Q--]:F[--Q];w||(w=X)}else P&&w.sn===P.sn&&!K&&j&&(w.sn<N&&this.fragmentTracker.getState(X)!==A.FragmentState.OK?(this.log("SN "+w.sn+" just loaded, load next one: "+X.sn),w=X):w=null)}return w},S.synchronizeToLiveEdge=function(L){var _=this.config,x=this.media;if(x){var w=this.hls.liveSyncPosition,O=x.currentTime,P=L.fragments[0].start,F=L.edge,N=O>=P-_.maxFragLookUpTolerance&&O<=F;if(w!==null&&x.duration>w&&(O<w||!N)){var U=_.liveMaxLatencyDuration!==void 0?_.liveMaxLatencyDuration:_.liveMaxLatencyDurationCount*L.targetduration;(!N&&x.readyState<4||O<F-U)&&(this.loadedmetadata||(this.nextLoadPosition=w),x.readyState&&(this.warn("Playback: "+O.toFixed(3)+" is located too far from the end of live sliding playlist: "+F+", reset currentTime to : "+w.toFixed(3)),x.currentTime=w))}}},S.alignPlaylists=function(L,_){var x=this.levels,w=this.levelLastLoaded,O=this.fragPrevious,P=w!==null?x[w]:null,F=L.fragments.length;if(!F)return this.warn("No fragments in live playlist"),0;var N=L.fragments[0].start,U=!_,G=L.alignedSliding&&Object(R.isFiniteNumber)(N);if(U||!G&&!N){Object(h.alignStream)(O,P,L);var K=L.fragments[0].start;return this.log("Live playlist sliding: "+K.toFixed(2)+" start-sn: "+(_?_.startSN:"na")+"->"+L.startSN+" prev-sn: "+(O?O.sn:"na")+" fragments: "+F),K}return N},S.waitForCdnTuneIn=function(L){return L.live&&L.canBlockReload&&L.tuneInGoal>Math.max(L.partHoldBack,3*L.partTarget)},S.setStartPosition=function(L,_){var x=this.startPosition;if(x<_&&(x=-1),x===-1||this.lastCurrentTime===-1){var w=L.startTimeOffset;Object(R.isFiniteNumber)(w)?(x=_+w,w<0&&(x+=L.totalduration),x=Math.min(Math.max(_,x),_+L.totalduration),this.log("Start time offset "+w+" found in playlist, adjust startPosition to "+x),this.startPosition=x):L.live?x=this.hls.liveSyncPosition||_:this.startPosition=x=0,this.lastCurrentTime=x}this.nextLoadPosition=x},S.getLoadPosition=function(){var L=this.media,_=0;return this.loadedmetadata&&L?_=L.currentTime:this.nextLoadPosition&&(_=this.nextLoadPosition),_},S.handleFragLoadAborted=function(L,_){this.transmuxer&&L.sn!=="initSegment"&&L.stats.aborted&&(this.warn("Fragment "+L.sn+(_?" part"+_.index:"")+" of level "+L.level+" was aborted"),this.resetFragmentLoading(L))},S.resetFragmentLoading=function(L){this.fragCurrent&&this.fragContextChanged(L)||(this.state=n.IDLE)},S.onFragmentOrKeyLoadError=function(L,_){if(!_.fatal){var x=_.frag;if(x&&x.type===L){var w=this.fragCurrent;console.assert(w&&x.sn===w.sn&&x.level===w.level&&x.urlId===w.urlId,"Frag load error must match current frag to retry");var O=this.config;if(this.fragLoadError+1<=O.fragLoadingMaxRetry){if(this.resetLiveStartWhenNotLoaded(x.level))return;var P=Math.min(Math.pow(2,this.fragLoadError)*O.fragLoadingRetryDelay,O.fragLoadingMaxRetryTimeout);this.warn("Fragment "+x.sn+" of "+L+" "+x.level+" failed to load, retrying in "+P+"ms"),this.retryDate=self.performance.now()+P,this.fragLoadError++,this.state=n.FRAG_LOADING_WAITING_RETRY}else _.levelRetry?(L===t.PlaylistLevelType.AUDIO&&(this.fragCurrent=null),this.fragLoadError=0,this.state=n.IDLE):(C.logger.error(_.details+" reaches max retry, redispatch as fatal ..."),_.fatal=!0,this.hls.stopLoad(),this.state=n.ERROR)}}},S.afterBufferFlushed=function(L,_,x){if(L){var w=k.BufferHelper.getBuffered(L);this.fragmentTracker.detectEvictedFragments(_,w,x),this.state===n.ENDED&&this.resetLoadingState()}},S.resetLoadingState=function(){this.fragCurrent=null,this.fragPrevious=null,this.state=n.IDLE},S.resetLiveStartWhenNotLoaded=function(L){if(!this.loadedmetadata){this.startFragRequested=!1;var _=this.levels?this.levels[L].details:null;if(_!=null&&_.live)return this.startPosition=-1,this.setStartPosition(_,0),this.resetLoadingState(),!0;this.nextLoadPosition=this.startPosition}return!1},S.updateLevelTiming=function(L,_,x,w){var O=this,P=x.details;console.assert(!!P,"level.details must be defined"),Object.keys(L.elementaryStreams).reduce(function(F,N){var U=L.elementaryStreams[N];if(U){var G=U.endPTS-U.startPTS;if(G<=0)return O.warn("Could not parse fragment "+L.sn+" "+N+" duration reliably ("+G+") resetting transmuxer to fallback to playlist timing"),O.resetTransmuxer(),F||!1;var K=w?0:Object(y.updateFragPTSDTS)(P,L,U.startPTS,U.endPTS,U.startDTS,U.endDTS);return O.hls.trigger(D.Events.LEVEL_PTS_UPDATED,{details:P,level:x,drift:K,type:N,frag:L,start:U.startPTS,end:U.endPTS}),!0}return F},!1)?(this.state=n.PARSED,this.hls.trigger(D.Events.FRAG_PARSED,{frag:L,part:_})):this.resetLoadingState()},S.resetTransmuxer=function(){this.transmuxer&&(this.transmuxer.destroy(),this.transmuxer=null)},i=r,(c=[{key:"state",get:function(){return this._state},set:function(L){var _=this._state;_!==L&&(this._state=L,this.log(_+"->"+L))}}])&&o(i.prototype,c),r}(E.default)},"./src/controller/buffer-controller.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return T});var R=g("./src/polyfills/number.ts"),E=g("./src/events.ts"),A=g("./src/utils/logger.ts"),k=g("./src/errors.ts"),C=g("./src/utils/buffer-helper.ts"),D=g("./src/utils/mediasource-helper.ts"),s=g("./src/loader/fragment.ts"),b=g("./src/controller/buffer-operation-queue.ts"),m=Object(D.getMediaSource)(),h=/([ha]vc.)(?:\.[^.,]+)+/,T=function(){function y(e){var a=this;this.details=null,this._objectUrl=null,this.operationQueue=void 0,this.listeners=void 0,this.hls=void 0,this.bufferCodecEventsExpected=0,this._bufferCodecEventsTotal=0,this.media=null,this.mediaSource=null,this.appendError=0,this.tracks={},this.pendingTracks={},this.sourceBuffer=void 0,this._onMediaSourceOpen=function(){var t=a.hls,o=a.media,u=a.mediaSource;A.logger.log("[buffer-controller]: Media source opened"),o&&(a.updateMediaElementDuration(),t.trigger(E.Events.MEDIA_ATTACHED,{media:o})),u&&u.removeEventListener("sourceopen",a._onMediaSourceOpen),a.checkPendingTracks()},this._onMediaSourceClose=function(){A.logger.log("[buffer-controller]: Media source closed")},this._onMediaSourceEnded=function(){A.logger.log("[buffer-controller]: Media source ended")},this.hls=e,this._initSourceBuffer(),this.registerListeners()}var d=y.prototype;return d.hasSourceTypes=function(){return this.getSourceBufferTypes().length>0||Object.keys(this.pendingTracks).length>0},d.destroy=function(){this.unregisterListeners(),this.details=null},d.registerListeners=function(){var e=this.hls;e.on(E.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(E.Events.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(E.Events.MANIFEST_PARSED,this.onManifestParsed,this),e.on(E.Events.BUFFER_RESET,this.onBufferReset,this),e.on(E.Events.BUFFER_APPENDING,this.onBufferAppending,this),e.on(E.Events.BUFFER_CODECS,this.onBufferCodecs,this),e.on(E.Events.BUFFER_EOS,this.onBufferEos,this),e.on(E.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(E.Events.LEVEL_UPDATED,this.onLevelUpdated,this),e.on(E.Events.FRAG_PARSED,this.onFragParsed,this),e.on(E.Events.FRAG_CHANGED,this.onFragChanged,this)},d.unregisterListeners=function(){var e=this.hls;e.off(E.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(E.Events.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(E.Events.MANIFEST_PARSED,this.onManifestParsed,this),e.off(E.Events.BUFFER_RESET,this.onBufferReset,this),e.off(E.Events.BUFFER_APPENDING,this.onBufferAppending,this),e.off(E.Events.BUFFER_CODECS,this.onBufferCodecs,this),e.off(E.Events.BUFFER_EOS,this.onBufferEos,this),e.off(E.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(E.Events.LEVEL_UPDATED,this.onLevelUpdated,this),e.off(E.Events.FRAG_PARSED,this.onFragParsed,this),e.off(E.Events.FRAG_CHANGED,this.onFragChanged,this)},d._initSourceBuffer=function(){this.sourceBuffer={},this.operationQueue=new b.default(this.sourceBuffer),this.listeners={audio:[],video:[],audiovideo:[]}},d.onManifestParsed=function(e,a){var t=2;(a.audio&&!a.video||!a.altAudio)&&(t=1),this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=t,this.details=null,A.logger.log(this.bufferCodecEventsExpected+" bufferCodec event(s) expected")},d.onMediaAttaching=function(e,a){var t=this.media=a.media;if(t&&m){var o=this.mediaSource=new m;o.addEventListener("sourceopen",this._onMediaSourceOpen),o.addEventListener("sourceended",this._onMediaSourceEnded),o.addEventListener("sourceclose",this._onMediaSourceClose),t.src=self.URL.createObjectURL(o),this._objectUrl=t.src}},d.onMediaDetaching=function(){var e=this.media,a=this.mediaSource,t=this._objectUrl;if(a){if(A.logger.log("[buffer-controller]: media source detaching"),a.readyState==="open")try{a.endOfStream()}catch(o){A.logger.warn("[buffer-controller]: onMediaDetaching: "+o.message+" while calling endOfStream")}this.onBufferReset(),a.removeEventListener("sourceopen",this._onMediaSourceOpen),a.removeEventListener("sourceended",this._onMediaSourceEnded),a.removeEventListener("sourceclose",this._onMediaSourceClose),e&&(t&&self.URL.revokeObjectURL(t),e.src===t?(e.removeAttribute("src"),e.load()):A.logger.warn("[buffer-controller]: media.src was changed by a third party - skip cleanup")),this.mediaSource=null,this.media=null,this._objectUrl=null,this.bufferCodecEventsExpected=this._bufferCodecEventsTotal,this.pendingTracks={},this.tracks={}}this.hls.trigger(E.Events.MEDIA_DETACHED,void 0)},d.onBufferReset=function(){var e=this;this.getSourceBufferTypes().forEach(function(a){var t=e.sourceBuffer[a];try{t&&(e.removeBufferListeners(a),e.mediaSource&&e.mediaSource.removeSourceBuffer(t),e.sourceBuffer[a]=void 0)}catch(o){A.logger.warn("[buffer-controller]: Failed to reset the "+a+" buffer",o)}}),this._initSourceBuffer()},d.onBufferCodecs=function(e,a){var t=this,o=this.getSourceBufferTypes().length;Object.keys(a).forEach(function(u){if(o){var n=t.tracks[u];if(n&&typeof n.buffer.changeType=="function"){var l=a[u],p=l.codec,f=l.levelCodec,v=l.container;if((n.levelCodec||n.codec).replace(h,"$1")!==(f||p).replace(h,"$1")){var r=v+";codecs="+(f||p);t.appendChangeType(u,r)}}}else t.pendingTracks[u]=a[u]}),o||(this.bufferCodecEventsExpected=Math.max(this.bufferCodecEventsExpected-1,0),this.mediaSource&&this.mediaSource.readyState==="open"&&this.checkPendingTracks())},d.appendChangeType=function(e,a){var t=this,o=this.operationQueue,u={execute:function(){var n=t.sourceBuffer[e];n&&(A.logger.log("[buffer-controller]: changing "+e+" sourceBuffer type to "+a),n.changeType(a)),o.shiftAndExecuteNext(e)},onStart:function(){},onComplete:function(){},onError:function(n){A.logger.warn("[buffer-controller]: Failed to change "+e+" SourceBuffer type",n)}};o.append(u,e)},d.onBufferAppending=function(e,a){var t=this,o=this.hls,u=this.operationQueue,n=this.tracks,l=a.data,p=a.type,f=a.frag,v=a.part,r=a.chunkMeta,i=r.buffering[p],c=self.performance.now();i.start=c;var S=f.stats.buffering,L=v?v.stats.buffering:null;S.start===0&&(S.start=c),L&&L.start===0&&(L.start=c);var _=n.audio,x=p==="audio"&&r.id===1&&(_==null?void 0:_.container)==="audio/mpeg",w={execute:function(){if(i.executeStart=self.performance.now(),x){var O=t.sourceBuffer[p];if(O){var P=f.start-O.timestampOffset;Math.abs(P)>=.1&&(A.logger.log("[buffer-controller]: Updating audio SourceBuffer timestampOffset to "+f.start+" (delta: "+P+") sn: "+f.sn+")"),O.timestampOffset=f.start)}}t.appendExecutor(l,p)},onStart:function(){},onComplete:function(){var O=self.performance.now();i.executeEnd=i.end=O,S.first===0&&(S.first=O),L&&L.first===0&&(L.first=O);var P=t.sourceBuffer,F={};for(var N in P)F[N]=C.BufferHelper.getBuffered(P[N]);t.appendError=0,t.hls.trigger(E.Events.BUFFER_APPENDED,{type:p,frag:f,part:v,chunkMeta:r,parent:f.type,timeRanges:F})},onError:function(O){A.logger.error("[buffer-controller]: Error encountered while trying to append to the "+p+" SourceBuffer",O);var P={type:k.ErrorTypes.MEDIA_ERROR,parent:f.type,details:k.ErrorDetails.BUFFER_APPEND_ERROR,err:O,fatal:!1};O.code===DOMException.QUOTA_EXCEEDED_ERR?P.details=k.ErrorDetails.BUFFER_FULL_ERROR:(t.appendError++,P.details=k.ErrorDetails.BUFFER_APPEND_ERROR,t.appendError>o.config.appendErrorMaxRetry&&(A.logger.error("[buffer-controller]: Failed "+o.config.appendErrorMaxRetry+" times to append segment in sourceBuffer"),P.fatal=!0)),o.trigger(E.Events.ERROR,P)}};u.append(w,p)},d.onBufferFlushing=function(e,a){var t=this,o=this.operationQueue,u=function(n){return{execute:t.removeExecutor.bind(t,n,a.startOffset,a.endOffset),onStart:function(){},onComplete:function(){t.hls.trigger(E.Events.BUFFER_FLUSHED,{type:n})},onError:function(l){A.logger.warn("[buffer-controller]: Failed to remove from "+n+" SourceBuffer",l)}}};a.type?o.append(u(a.type),a.type):this.getSourceBufferTypes().forEach(function(n){o.append(u(n),n)})},d.onFragParsed=function(e,a){var t=this,o=a.frag,u=a.part,n=[],l=u?u.elementaryStreams:o.elementaryStreams;l[s.ElementaryStreamTypes.AUDIOVIDEO]?n.push("audiovideo"):(l[s.ElementaryStreamTypes.AUDIO]&&n.push("audio"),l[s.ElementaryStreamTypes.VIDEO]&&n.push("video")),n.length===0&&A.logger.warn("Fragments must have at least one ElementaryStreamType set. type: "+o.type+" level: "+o.level+" sn: "+o.sn),this.blockBuffers(function(){var p=self.performance.now();o.stats.buffering.end=p,u&&(u.stats.buffering.end=p);var f=u?u.stats:o.stats;t.hls.trigger(E.Events.FRAG_BUFFERED,{frag:o,part:u,stats:f,id:o.type})},n)},d.onFragChanged=function(e,a){this.flushBackBuffer()},d.onBufferEos=function(e,a){var t=this;this.getSourceBufferTypes().reduce(function(o,u){var n=t.sourceBuffer[u];return a.type&&a.type!==u||n&&!n.ended&&(n.ended=!0,A.logger.log("[buffer-controller]: "+u+" sourceBuffer now EOS")),o&&!(n&&!n.ended)},!0)&&this.blockBuffers(function(){var o=t.mediaSource;o&&o.readyState==="open"&&o.endOfStream()})},d.onLevelUpdated=function(e,a){var t=a.details;t.fragments.length&&(this.details=t,this.getSourceBufferTypes().length?this.blockBuffers(this.updateMediaElementDuration.bind(this)):this.updateMediaElementDuration())},d.flushBackBuffer=function(){var e=this.hls,a=this.details,t=this.media,o=this.sourceBuffer;if(t&&a!==null){var u=this.getSourceBufferTypes();if(u.length){var n=a.live&&e.config.liveBackBufferLength!==null?e.config.liveBackBufferLength:e.config.backBufferLength;if(Object(R.isFiniteNumber)(n)&&!(n<0)){var l=t.currentTime,p=a.levelTargetDuration,f=Math.max(n,p),v=Math.floor(l/p)*p-f;u.forEach(function(r){var i=o[r];if(i){var c=C.BufferHelper.getBuffered(i);c.length>0&&v>c.start(0)&&(e.trigger(E.Events.BACK_BUFFER_REACHED,{bufferEnd:v}),a.live&&e.trigger(E.Events.LIVE_BACK_BUFFER_REACHED,{bufferEnd:v}),e.trigger(E.Events.BUFFER_FLUSHING,{startOffset:0,endOffset:v,type:r}))}})}}}},d.updateMediaElementDuration=function(){if(this.details&&this.media&&this.mediaSource&&this.mediaSource.readyState==="open"){var e=this.details,a=this.hls,t=this.media,o=this.mediaSource,u=e.fragments[0].start+e.totalduration,n=t.duration,l=Object(R.isFiniteNumber)(o.duration)?o.duration:0;e.live&&a.config.liveDurationInfinity?(A.logger.log("[buffer-controller]: Media Source duration is set to Infinity"),o.duration=1/0,this.updateSeekableRange(e)):(u>l&&u>n||!Object(R.isFiniteNumber)(n))&&(A.logger.log("[buffer-controller]: Updating Media Source duration to "+u.toFixed(3)),o.duration=u)}},d.updateSeekableRange=function(e){var a=this.mediaSource,t=e.fragments;if(t.length&&e.live&&a!=null&&a.setLiveSeekableRange){var o=Math.max(0,t[0].start),u=Math.max(o,o+e.totalduration);a.setLiveSeekableRange(o,u)}},d.checkPendingTracks=function(){var e=this.bufferCodecEventsExpected,a=this.operationQueue,t=this.pendingTracks,o=Object.keys(t).length;if(o&&!e||o===2){this.createSourceBuffers(t),this.pendingTracks={};var u=this.getSourceBufferTypes();if(u.length===0)return void this.hls.trigger(E.Events.ERROR,{type:k.ErrorTypes.MEDIA_ERROR,details:k.ErrorDetails.BUFFER_INCOMPATIBLE_CODECS_ERROR,fatal:!0,reason:"could not create source buffer for media codec(s)"});u.forEach(function(n){a.executeNext(n)})}},d.createSourceBuffers=function(e){var a=this.sourceBuffer,t=this.mediaSource;if(!t)throw Error("createSourceBuffers called when mediaSource was null");var o=0;for(var u in e)if(!a[u]){var n=e[u];if(!n)throw Error("source buffer exists for track "+u+", however track does not");var l=n.levelCodec||n.codec,p=n.container+";codecs="+l;A.logger.log("[buffer-controller]: creating sourceBuffer("+p+")");try{var f=a[u]=t.addSourceBuffer(p),v=u;this.addBufferListener(v,"updatestart",this._onSBUpdateStart),this.addBufferListener(v,"updateend",this._onSBUpdateEnd),this.addBufferListener(v,"error",this._onSBUpdateError),this.tracks[u]={buffer:f,codec:l,container:n.container,levelCodec:n.levelCodec,id:n.id},o++}catch(r){A.logger.error("[buffer-controller]: error while trying to add sourceBuffer: "+r.message),this.hls.trigger(E.Events.ERROR,{type:k.ErrorTypes.MEDIA_ERROR,details:k.ErrorDetails.BUFFER_ADD_CODEC_ERROR,fatal:!1,error:r,mimeType:p})}}o&&this.hls.trigger(E.Events.BUFFER_CREATED,{tracks:this.tracks})},d._onSBUpdateStart=function(e){this.operationQueue.current(e).onStart()},d._onSBUpdateEnd=function(e){var a=this.operationQueue;a.current(e).onComplete(),a.shiftAndExecuteNext(e)},d._onSBUpdateError=function(e,a){A.logger.error("[buffer-controller]: "+e+" SourceBuffer error",a),this.hls.trigger(E.Events.ERROR,{type:k.ErrorTypes.MEDIA_ERROR,details:k.ErrorDetails.BUFFER_APPENDING_ERROR,fatal:!1});var t=this.operationQueue.current(e);t&&t.onError(a)},d.removeExecutor=function(e,a,t){var o=this.media,u=this.mediaSource,n=this.operationQueue,l=this.sourceBuffer[e];if(!o||!u||!l)return A.logger.warn("[buffer-controller]: Attempting to remove from the "+e+" SourceBuffer, but it does not exist"),void n.shiftAndExecuteNext(e);var p=Object(R.isFiniteNumber)(o.duration)?o.duration:1/0,f=Object(R.isFiniteNumber)(u.duration)?u.duration:1/0,v=Math.max(0,a),r=Math.min(t,p,f);r>v?(A.logger.log("[buffer-controller]: Removing ["+v+","+r+"] from the "+e+" SourceBuffer"),console.assert(!l.updating,e+" sourceBuffer must not be updating"),l.remove(v,r)):n.shiftAndExecuteNext(e)},d.appendExecutor=function(e,a){var t=this.operationQueue,o=this.sourceBuffer[a];if(!o)return A.logger.warn("[buffer-controller]: Attempting to append to the "+a+" SourceBuffer, but it does not exist"),void t.shiftAndExecuteNext(a);o.ended=!1,console.assert(!o.updating,a+" sourceBuffer must not be updating"),o.appendBuffer(e)},d.blockBuffers=function(e,a){var t=this;if(a===void 0&&(a=this.getSourceBufferTypes()),!a.length)return A.logger.log("[buffer-controller]: Blocking operation requested, but no SourceBuffers exist"),void Promise.resolve(e);var o=this.operationQueue,u=a.map(function(n){return o.appendBlocker(n)});Promise.all(u).then(function(){e(),a.forEach(function(n){var l=t.sourceBuffer[n];l&&l.updating||o.shiftAndExecuteNext(n)})})},d.getSourceBufferTypes=function(){return Object.keys(this.sourceBuffer)},d.addBufferListener=function(e,a,t){var o=this.sourceBuffer[e];if(o){var u=t.bind(this,e);this.listeners[e].push({event:a,listener:u}),o.addEventListener(a,u)}},d.removeBufferListeners=function(e){var a=this.sourceBuffer[e];a&&this.listeners[e].forEach(function(t){a.removeEventListener(t.event,t.listener)})},y}()},"./src/controller/buffer-operation-queue.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return E});var R=g("./src/utils/logger.ts"),E=function(){function A(C){this.buffers=void 0,this.queues={video:[],audio:[],audiovideo:[]},this.buffers=C}var k=A.prototype;return k.append=function(C,D){var s=this.queues[D];s.push(C),s.length===1&&this.buffers[D]&&this.executeNext(D)},k.insertAbort=function(C,D){this.queues[D].unshift(C),this.executeNext(D)},k.appendBlocker=function(C){var D,s=new Promise(function(m){D=m}),b={execute:D,onStart:function(){},onComplete:function(){},onError:function(){}};return this.append(b,C),s},k.executeNext=function(C){var D=this.buffers,s=this.queues,b=D[C],m=s[C];if(m.length){var h=m[0];try{h.execute()}catch(T){R.logger.warn("[buffer-operation-queue]: Unhandled exception executing the current operation"),h.onError(T),b&&b.updating||(m.shift(),this.executeNext(C))}}},k.shiftAndExecuteNext=function(C){this.queues[C].shift(),this.executeNext(C)},k.current=function(C){return this.queues[C][0]},A}()},"./src/controller/cap-level-controller.ts":function(M,I,g){g.r(I);var R=g("./src/events.ts");function E(k,C){for(var D=0;D<C.length;D++){var s=C[D];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(k,s.key,s)}}var A=function(){function k(m){this.autoLevelCapping=void 0,this.firstLevel=void 0,this.media=void 0,this.restrictedLevels=void 0,this.timer=void 0,this.hls=void 0,this.streamController=void 0,this.clientRect=void 0,this.hls=m,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.firstLevel=-1,this.media=null,this.restrictedLevels=[],this.timer=void 0,this.clientRect=null,this.registerListeners()}var C,D,s,b=k.prototype;return b.setStreamController=function(m){this.streamController=m},b.destroy=function(){this.unregisterListener(),this.hls.config.capLevelToPlayerSize&&this.stopCapping(),this.media=null,this.clientRect=null,this.hls=this.streamController=null},b.registerListeners=function(){var m=this.hls;m.on(R.Events.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),m.on(R.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),m.on(R.Events.MANIFEST_PARSED,this.onManifestParsed,this),m.on(R.Events.BUFFER_CODECS,this.onBufferCodecs,this),m.on(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this)},b.unregisterListener=function(){var m=this.hls;m.off(R.Events.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),m.off(R.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),m.off(R.Events.MANIFEST_PARSED,this.onManifestParsed,this),m.off(R.Events.BUFFER_CODECS,this.onBufferCodecs,this),m.off(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this)},b.onFpsDropLevelCapping=function(m,h){k.isLevelAllowed(h.droppedLevel,this.restrictedLevels)&&this.restrictedLevels.push(h.droppedLevel)},b.onMediaAttaching=function(m,h){this.media=h.media instanceof HTMLVideoElement?h.media:null},b.onManifestParsed=function(m,h){var T=this.hls;this.restrictedLevels=[],this.firstLevel=h.firstLevel,T.config.capLevelToPlayerSize&&h.video&&this.startCapping()},b.onBufferCodecs=function(m,h){this.hls.config.capLevelToPlayerSize&&h.video&&this.startCapping()},b.onMediaDetaching=function(){this.stopCapping()},b.detectPlayerSize=function(){if(this.media&&this.mediaHeight>0&&this.mediaWidth>0){var m=this.hls.levels;if(m.length){var h=this.hls;h.autoLevelCapping=this.getMaxLevel(m.length-1),h.autoLevelCapping>this.autoLevelCapping&&this.streamController&&this.streamController.nextLevelSwitch(),this.autoLevelCapping=h.autoLevelCapping}}},b.getMaxLevel=function(m){var h=this,T=this.hls.levels;if(!T.length)return-1;var y=T.filter(function(d,e){return k.isLevelAllowed(e,h.restrictedLevels)&&e<=m});return this.clientRect=null,k.getMaxLevelByMediaSize(y,this.mediaWidth,this.mediaHeight)},b.startCapping=function(){this.timer||(this.autoLevelCapping=Number.POSITIVE_INFINITY,this.hls.firstLevel=this.getMaxLevel(this.firstLevel),self.clearInterval(this.timer),this.timer=self.setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())},b.stopCapping=function(){this.restrictedLevels=[],this.firstLevel=-1,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(self.clearInterval(this.timer),this.timer=void 0)},b.getDimensions=function(){if(this.clientRect)return this.clientRect;var m=this.media,h={width:0,height:0};if(m){var T=m.getBoundingClientRect();h.width=T.width,h.height=T.height,h.width||h.height||(h.width=T.right-T.left||m.width||0,h.height=T.bottom-T.top||m.height||0)}return this.clientRect=h,h},k.isLevelAllowed=function(m,h){return h===void 0&&(h=[]),h.indexOf(m)===-1},k.getMaxLevelByMediaSize=function(m,h,T){if(!m||!m.length)return-1;for(var y,d,e=m.length-1,a=0;a<m.length;a+=1){var t=m[a];if((t.width>=h||t.height>=T)&&(y=t,!(d=m[a+1])||y.width!==d.width||y.height!==d.height)){e=a;break}}return e},C=k,s=[{key:"contentScaleFactor",get:function(){var m=1;try{m=self.devicePixelRatio}catch{}return m}}],(D=[{key:"mediaWidth",get:function(){return this.getDimensions().width*k.contentScaleFactor}},{key:"mediaHeight",get:function(){return this.getDimensions().height*k.contentScaleFactor}}])&&E(C.prototype,D),s&&E(C,s),k}();I.default=A},"./src/controller/eme-controller.ts":function(M,I,g){g.r(I);var R=g("./src/events.ts"),E=g("./src/errors.ts"),A=g("./src/utils/logger.ts"),k=g("./src/utils/mediakeys-helper.ts");function C(s,b){for(var m=0;m<b.length;m++){var h=b[m];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(s,h.key,h)}}var D=function(){function s(T){this.hls=void 0,this._widevineLicenseUrl=void 0,this._licenseXhrSetup=void 0,this._licenseResponseCallback=void 0,this._emeEnabled=void 0,this._requestMediaKeySystemAccess=void 0,this._drmSystemOptions=void 0,this._config=void 0,this._mediaKeysList=[],this._media=null,this._hasSetMediaKeys=!1,this._requestLicenseFailureCount=0,this.mediaKeysPromise=null,this._onMediaEncrypted=this.onMediaEncrypted.bind(this),this.hls=T,this._config=T.config,this._widevineLicenseUrl=this._config.widevineLicenseUrl,this._licenseXhrSetup=this._config.licenseXhrSetup,this._licenseResponseCallback=this._config.licenseResponseCallback,this._emeEnabled=this._config.emeEnabled,this._requestMediaKeySystemAccess=this._config.requestMediaKeySystemAccessFunc,this._drmSystemOptions=this._config.drmSystemOptions,this._registerListeners()}var b,m,h=s.prototype;return h.destroy=function(){this._unregisterListeners(),this.hls=this._onMediaEncrypted=null,this._requestMediaKeySystemAccess=null},h._registerListeners=function(){this.hls.on(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(R.Events.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.on(R.Events.MANIFEST_PARSED,this.onManifestParsed,this)},h._unregisterListeners=function(){this.hls.off(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(R.Events.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.off(R.Events.MANIFEST_PARSED,this.onManifestParsed,this)},h.getLicenseServerUrl=function(T){switch(T){case k.KeySystems.WIDEVINE:if(!this._widevineLicenseUrl)break;return this._widevineLicenseUrl}throw new Error('no license server URL configured for key-system "'+T+'"')},h._attemptKeySystemAccess=function(T,y,d){var e=this,a=function(o,u,n,l){switch(o){case k.KeySystems.WIDEVINE:return function(p,f,v){var r={audioCapabilities:[],videoCapabilities:[]};return p.forEach(function(i){r.audioCapabilities.push({contentType:'audio/mp4; codecs="'+i+'"',robustness:v.audioRobustness||""})}),f.forEach(function(i){r.videoCapabilities.push({contentType:'video/mp4; codecs="'+i+'"',robustness:v.videoRobustness||""})}),[r]}(u,n,l);default:throw new Error("Unknown key-system: "+o)}}(T,y,d,this._drmSystemOptions);A.logger.log("Requesting encrypted media key-system access");var t=this.requestMediaKeySystemAccess(T,a);this.mediaKeysPromise=t.then(function(o){return e._onMediaKeySystemAccessObtained(T,o)}),t.catch(function(o){A.logger.error('Failed to obtain key-system "'+T+'" access:',o)})},h._onMediaKeySystemAccessObtained=function(T,y){var d=this;A.logger.log('Access for key-system "'+T+'" obtained');var e={mediaKeysSessionInitialized:!1,mediaKeySystemAccess:y,mediaKeySystemDomain:T};this._mediaKeysList.push(e);var a=Promise.resolve().then(function(){return y.createMediaKeys()}).then(function(t){return e.mediaKeys=t,A.logger.log('Media-keys created for key-system "'+T+'"'),d._onMediaKeysCreated(),t});return a.catch(function(t){A.logger.error("Failed to create media-keys:",t)}),a},h._onMediaKeysCreated=function(){var T=this;this._mediaKeysList.forEach(function(y){y.mediaKeysSession||(y.mediaKeysSession=y.mediaKeys.createSession(),T._onNewMediaKeySession(y.mediaKeysSession))})},h._onNewMediaKeySession=function(T){var y=this;A.logger.log("New key-system session "+T.sessionId),T.addEventListener("message",function(d){y._onKeySessionMessage(T,d.message)},!1)},h._onKeySessionMessage=function(T,y){A.logger.log("Got EME message event, creating license request"),this._requestLicense(y,function(d){A.logger.log("Received license data (length: "+(d&&d.byteLength)+"), updating key-session"),T.update(d)})},h.onMediaEncrypted=function(T){var y=this;if(A.logger.log('Media is encrypted using "'+T.initDataType+'" init data type'),!this.mediaKeysPromise)return A.logger.error("Fatal: Media is encrypted but no CDM access or no keys have been requested"),void this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.KEY_SYSTEM_ERROR,details:E.ErrorDetails.KEY_SYSTEM_NO_KEYS,fatal:!0});var d=function(e){y._media&&(y._attemptSetMediaKeys(e),y._generateRequestWithPreferredKeySession(T.initDataType,T.initData))};this.mediaKeysPromise.then(d).catch(d)},h._attemptSetMediaKeys=function(T){if(!this._media)throw new Error("Attempted to set mediaKeys without first attaching a media element");if(!this._hasSetMediaKeys){var y=this._mediaKeysList[0];if(!y||!y.mediaKeys)return A.logger.error("Fatal: Media is encrypted but no CDM access or no keys have been obtained yet"),void this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.KEY_SYSTEM_ERROR,details:E.ErrorDetails.KEY_SYSTEM_NO_KEYS,fatal:!0});A.logger.log("Setting keys for encrypted media"),this._media.setMediaKeys(y.mediaKeys),this._hasSetMediaKeys=!0}},h._generateRequestWithPreferredKeySession=function(T,y){var d=this,e=this._mediaKeysList[0];if(!e)return A.logger.error("Fatal: Media is encrypted but not any key-system access has been obtained yet"),void this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.KEY_SYSTEM_ERROR,details:E.ErrorDetails.KEY_SYSTEM_NO_ACCESS,fatal:!0});if(e.mediaKeysSessionInitialized)A.logger.warn("Key-Session already initialized but requested again");else{var a=e.mediaKeysSession;if(!a)return A.logger.error("Fatal: Media is encrypted but no key-session existing"),void this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.KEY_SYSTEM_ERROR,details:E.ErrorDetails.KEY_SYSTEM_NO_SESSION,fatal:!0});if(!y)return A.logger.warn("Fatal: initData required for generating a key session is null"),void this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.KEY_SYSTEM_ERROR,details:E.ErrorDetails.KEY_SYSTEM_NO_INIT_DATA,fatal:!0});A.logger.log('Generating key-session request for "'+T+'" init data type'),e.mediaKeysSessionInitialized=!0,a.generateRequest(T,y).then(function(){A.logger.debug("Key-session generation succeeded")}).catch(function(t){A.logger.error("Error generating key-session request:",t),d.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.KEY_SYSTEM_ERROR,details:E.ErrorDetails.KEY_SYSTEM_NO_SESSION,fatal:!1})})}},h._createLicenseXhr=function(T,y,d){var e=new XMLHttpRequest;e.responseType="arraybuffer",e.onreadystatechange=this._onLicenseRequestReadyStageChange.bind(this,e,T,y,d);var a=this._licenseXhrSetup;if(a)try{a.call(this.hls,e,T),a=void 0}catch(t){A.logger.error(t)}try{e.readyState||e.open("POST",T,!0),a&&a.call(this.hls,e,T)}catch(t){throw new Error("issue setting up KeySystem license XHR "+t)}return e},h._onLicenseRequestReadyStageChange=function(T,y,d,e){switch(T.readyState){case 4:if(T.status===200){this._requestLicenseFailureCount=0,A.logger.log("License request succeeded");var a=T.response,t=this._licenseResponseCallback;if(t)try{a=t.call(this.hls,T,y)}catch(u){A.logger.error(u)}e(a)}else{if(A.logger.error("License Request XHR failed ("+y+"). Status: "+T.status+" ("+T.statusText+")"),this._requestLicenseFailureCount++,this._requestLicenseFailureCount>3)return void this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.KEY_SYSTEM_ERROR,details:E.ErrorDetails.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0});var o=3-this._requestLicenseFailureCount+1;A.logger.warn("Retrying license request, "+o+" attempts left"),this._requestLicense(d,e)}}},h._generateLicenseRequestChallenge=function(T,y){switch(T.mediaKeySystemDomain){case k.KeySystems.WIDEVINE:return y}throw new Error("unsupported key-system: "+T.mediaKeySystemDomain)},h._requestLicense=function(T,y){A.logger.log("Requesting content license for key-system");var d=this._mediaKeysList[0];if(!d)return A.logger.error("Fatal error: Media is encrypted but no key-system access has been obtained yet"),void this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.KEY_SYSTEM_ERROR,details:E.ErrorDetails.KEY_SYSTEM_NO_ACCESS,fatal:!0});try{var e=this.getLicenseServerUrl(d.mediaKeySystemDomain),a=this._createLicenseXhr(e,T,y);A.logger.log("Sending license request to URL: "+e);var t=this._generateLicenseRequestChallenge(d,T);a.send(t)}catch(o){A.logger.error("Failure requesting DRM license: "+o),this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.KEY_SYSTEM_ERROR,details:E.ErrorDetails.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0})}},h.onMediaAttached=function(T,y){if(this._emeEnabled){var d=y.media;this._media=d,d.addEventListener("encrypted",this._onMediaEncrypted)}},h.onMediaDetached=function(){var T=this._media,y=this._mediaKeysList;T&&(T.removeEventListener("encrypted",this._onMediaEncrypted),this._media=null,this._mediaKeysList=[],Promise.all(y.map(function(d){if(d.mediaKeysSession)return d.mediaKeysSession.close().catch(function(){})})).then(function(){return T.setMediaKeys(null)}).catch(function(){}))},h.onManifestParsed=function(T,y){if(this._emeEnabled){var d=y.levels.map(function(a){return a.audioCodec}).filter(function(a){return!!a}),e=y.levels.map(function(a){return a.videoCodec}).filter(function(a){return!!a});this._attemptKeySystemAccess(k.KeySystems.WIDEVINE,d,e)}},b=s,(m=[{key:"requestMediaKeySystemAccess",get:function(){if(!this._requestMediaKeySystemAccess)throw new Error("No requestMediaKeySystemAccess function configured");return this._requestMediaKeySystemAccess}}])&&C(b.prototype,m),s}();I.default=D},"./src/controller/fps-controller.ts":function(M,I,g){g.r(I);var R=g("./src/events.ts"),E=g("./src/utils/logger.ts"),A=function(){function k(D){this.hls=void 0,this.isVideoPlaybackQualityAvailable=!1,this.timer=void 0,this.media=null,this.lastTime=void 0,this.lastDroppedFrames=0,this.lastDecodedFrames=0,this.streamController=void 0,this.hls=D,this.registerListeners()}var C=k.prototype;return C.setStreamController=function(D){this.streamController=D},C.registerListeners=function(){this.hls.on(R.Events.MEDIA_ATTACHING,this.onMediaAttaching,this)},C.unregisterListeners=function(){this.hls.off(R.Events.MEDIA_ATTACHING,this.onMediaAttaching)},C.destroy=function(){this.timer&&clearInterval(this.timer),this.unregisterListeners(),this.isVideoPlaybackQualityAvailable=!1,this.media=null},C.onMediaAttaching=function(D,s){var b=this.hls.config;if(b.capLevelOnFPSDrop){var m=s.media instanceof self.HTMLVideoElement?s.media:null;this.media=m,m&&typeof m.getVideoPlaybackQuality=="function"&&(this.isVideoPlaybackQualityAvailable=!0),self.clearInterval(this.timer),this.timer=self.setInterval(this.checkFPSInterval.bind(this),b.fpsDroppedMonitoringPeriod)}},C.checkFPS=function(D,s,b){var m=performance.now();if(s){if(this.lastTime){var h=m-this.lastTime,T=b-this.lastDroppedFrames,y=s-this.lastDecodedFrames,d=1e3*T/h,e=this.hls;if(e.trigger(R.Events.FPS_DROP,{currentDropped:T,currentDecoded:y,totalDroppedFrames:b}),d>0&&T>e.config.fpsDroppedMonitoringThreshold*y){var a=e.currentLevel;E.logger.warn("drop FPS ratio greater than max allowed value for currentLevel: "+a),a>0&&(e.autoLevelCapping===-1||e.autoLevelCapping>=a)&&(a-=1,e.trigger(R.Events.FPS_DROP_LEVEL_CAPPING,{level:a,droppedLevel:e.currentLevel}),e.autoLevelCapping=a,this.streamController.nextLevelSwitch())}}this.lastTime=m,this.lastDroppedFrames=b,this.lastDecodedFrames=s}},C.checkFPSInterval=function(){var D=this.media;if(D)if(this.isVideoPlaybackQualityAvailable){var s=D.getVideoPlaybackQuality();this.checkFPS(D,s.totalVideoFrames,s.droppedVideoFrames)}else this.checkFPS(D,D.webkitDecodedFrameCount,D.webkitDroppedFrameCount)},k}();I.default=A},"./src/controller/fragment-finders.ts":function(M,I,g){g.r(I),g.d(I,"findFragmentByPDT",function(){return A}),g.d(I,"findFragmentByPTS",function(){return k}),g.d(I,"fragmentWithinToleranceTest",function(){return C}),g.d(I,"pdtWithinToleranceTest",function(){return D}),g.d(I,"findFragWithCC",function(){return s});var R=g("./src/polyfills/number.ts"),E=g("./src/utils/binary-search.ts");function A(b,m,h){if(m===null||!Array.isArray(b)||!b.length||!Object(R.isFiniteNumber)(m)||m<(b[0].programDateTime||0)||m>=(b[b.length-1].endProgramDateTime||0))return null;h=h||0;for(var T=0;T<b.length;++T){var y=b[T];if(D(m,h,y))return y}return null}function k(b,m,h,T){h===void 0&&(h=0),T===void 0&&(T=0);var y=null;if(b?y=m[b.sn-m[0].sn+1]||null:h===0&&m[0].start===0&&(y=m[0]),y&&C(h,T,y)===0)return y;var d=E.default.search(m,C.bind(null,h,T));return d||y}function C(b,m,h){b===void 0&&(b=0),m===void 0&&(m=0);var T=Math.min(m,h.duration+(h.deltaPTS?h.deltaPTS:0));return h.start+h.duration-T<=b?1:h.start-T>b&&h.start?-1:0}function D(b,m,h){var T=1e3*Math.min(m,h.duration+(h.deltaPTS?h.deltaPTS:0));return(h.endProgramDateTime||0)-T>b}function s(b,m){return E.default.search(b,function(h){return h.cc<m?1:h.cc>m?-1:0})}},"./src/controller/fragment-tracker.ts":function(M,I,g){g.r(I),g.d(I,"FragmentState",function(){return R}),g.d(I,"FragmentTracker",function(){return C});var R,E,A=g("./src/events.ts"),k=g("./src/types/loader.ts");(E=R||(R={})).NOT_LOADED="NOT_LOADED",E.BACKTRACKED="BACKTRACKED",E.APPENDING="APPENDING",E.PARTIAL="PARTIAL",E.OK="OK";var C=function(){function b(h){this.activeFragment=null,this.activeParts=null,this.fragments=Object.create(null),this.timeRanges=Object.create(null),this.bufferPadding=.2,this.hls=void 0,this.hls=h,this._registerListeners()}var m=b.prototype;return m._registerListeners=function(){var h=this.hls;h.on(A.Events.BUFFER_APPENDED,this.onBufferAppended,this),h.on(A.Events.FRAG_BUFFERED,this.onFragBuffered,this),h.on(A.Events.FRAG_LOADED,this.onFragLoaded,this)},m._unregisterListeners=function(){var h=this.hls;h.off(A.Events.BUFFER_APPENDED,this.onBufferAppended,this),h.off(A.Events.FRAG_BUFFERED,this.onFragBuffered,this),h.off(A.Events.FRAG_LOADED,this.onFragLoaded,this)},m.destroy=function(){this._unregisterListeners(),this.fragments=this.timeRanges=null},m.getAppendedFrag=function(h,T){if(T===k.PlaylistLevelType.MAIN){var y=this.activeFragment,d=this.activeParts;if(!y)return null;if(d)for(var e=d.length;e--;){var a=d[e],t=a?a.end:y.appendedPTS;if(a.start<=h&&t!==void 0&&h<=t)return e>9&&(this.activeParts=d.slice(e-9)),a}else if(y.start<=h&&y.appendedPTS!==void 0&&h<=y.appendedPTS)return y}return this.getBufferedFrag(h,T)},m.getBufferedFrag=function(h,T){for(var y=this.fragments,d=Object.keys(y),e=d.length;e--;){var a=y[d[e]];if((a==null?void 0:a.body.type)===T&&a.buffered){var t=a.body;if(t.start<=h&&h<=t.end)return t}}return null},m.detectEvictedFragments=function(h,T,y){var d=this;Object.keys(this.fragments).forEach(function(e){var a=d.fragments[e];if(a)if(a.buffered){var t=a.range[h];t&&t.time.some(function(o){var u=!d.isTimeBuffered(o.startPTS,o.endPTS,T);return u&&d.removeFragment(a.body),u})}else a.body.type===y&&d.removeFragment(a.body)})},m.detectPartialFragments=function(h){var T=this,y=this.timeRanges,d=h.frag,e=h.part;if(y&&d.sn!=="initSegment"){var a=s(d),t=this.fragments[a];t&&(Object.keys(y).forEach(function(o){var u=d.elementaryStreams[o];if(u){var n=y[o],l=e!==null||u.partial===!0;t.range[o]=T.getBufferedTimes(d,e,l,n)}}),t.backtrack=t.loaded=null,Object.keys(t.range).length?t.buffered=!0:this.removeFragment(t.body))}},m.fragBuffered=function(h){var T=s(h),y=this.fragments[T];y&&(y.backtrack=y.loaded=null,y.buffered=!0)},m.getBufferedTimes=function(h,T,y,d){for(var e={time:[],partial:y},a=T?T.start:h.start,t=T?T.end:h.end,o=h.minEndPTS||t,u=h.maxStartPTS||a,n=0;n<d.length;n++){var l=d.start(n)-this.bufferPadding,p=d.end(n)+this.bufferPadding;if(u>=l&&o<=p){e.time.push({startPTS:Math.max(a,d.start(n)),endPTS:Math.min(t,d.end(n))});break}if(a<p&&t>l)e.partial=!0,e.time.push({startPTS:Math.max(a,d.start(n)),endPTS:Math.min(t,d.end(n))});else if(t<=l)break}return e},m.getPartialFragment=function(h){var T,y,d,e=null,a=0,t=this.bufferPadding,o=this.fragments;return Object.keys(o).forEach(function(u){var n=o[u];n&&D(n)&&(y=n.body.start-t,d=n.body.end+t,h>=y&&h<=d&&(T=Math.min(h-y,d-h),a<=T&&(e=n.body,a=T)))}),e},m.getState=function(h){var T=s(h),y=this.fragments[T];return y?y.buffered?D(y)?R.PARTIAL:R.OK:y.backtrack?R.BACKTRACKED:R.APPENDING:R.NOT_LOADED},m.backtrack=function(h,T){var y=s(h),d=this.fragments[y];if(!d||d.backtrack)return null;var e=d.backtrack=T||d.loaded;return d.loaded=null,e},m.getBacktrackData=function(h){var T=s(h),y=this.fragments[T];if(y){var d,e=y.backtrack;if(e!=null&&(d=e.payload)!==null&&d!==void 0&&d.byteLength)return e;this.removeFragment(h)}return null},m.isTimeBuffered=function(h,T,y){for(var d,e,a=0;a<y.length;a++){if(d=y.start(a)-this.bufferPadding,e=y.end(a)+this.bufferPadding,h>=d&&T<=e)return!0;if(T<=d)return!1}return!1},m.onFragLoaded=function(h,T){var y=T.frag,d=T.part;if(y.sn!=="initSegment"&&!y.bitrateTest&&!d){var e=s(y);this.fragments[e]={body:y,loaded:T,backtrack:null,buffered:!1,range:Object.create(null)}}},m.onBufferAppended=function(h,T){var y=this,d=T.frag,e=T.part,a=T.timeRanges;if(d.type===k.PlaylistLevelType.MAIN)if(this.activeFragment=d,e){var t=this.activeParts;t||(this.activeParts=t=[]),t.push(e)}else this.activeParts=null;this.timeRanges=a,Object.keys(a).forEach(function(o){var u=a[o];if(y.detectEvictedFragments(o,u),!e)for(var n=0;n<u.length;n++)d.appendedPTS=Math.max(u.end(n),d.appendedPTS||0)})},m.onFragBuffered=function(h,T){this.detectPartialFragments(T)},m.hasFragment=function(h){var T=s(h);return!!this.fragments[T]},m.removeFragmentsInRange=function(h,T,y){var d=this;Object.keys(this.fragments).forEach(function(e){var a=d.fragments[e];if(a&&a.buffered){var t=a.body;t.type===y&&t.start<T&&t.end>h&&d.removeFragment(t)}})},m.removeFragment=function(h){var T=s(h);h.stats.loaded=0,h.clearElementaryStreamInfo(),delete this.fragments[T]},m.removeAllFragments=function(){this.fragments=Object.create(null),this.activeFragment=null,this.activeParts=null},b}();function D(b){var m,h;return b.buffered&&(((m=b.range.video)===null||m===void 0?void 0:m.partial)||((h=b.range.audio)===null||h===void 0?void 0:h.partial))}function s(b){return b.type+"_"+b.level+"_"+b.urlId+"_"+b.sn}},"./src/controller/gap-controller.ts":function(M,I,g){g.r(I),g.d(I,"STALL_MINIMUM_DURATION_MS",function(){return C}),g.d(I,"MAX_START_GAP_JUMP",function(){return D}),g.d(I,"SKIP_BUFFER_HOLE_STEP_SECONDS",function(){return s}),g.d(I,"SKIP_BUFFER_RANGE_START",function(){return b}),g.d(I,"default",function(){return m});var R=g("./src/utils/buffer-helper.ts"),E=g("./src/errors.ts"),A=g("./src/events.ts"),k=g("./src/utils/logger.ts"),C=250,D=2,s=.1,b=.05,m=function(){function h(y,d,e,a){this.config=void 0,this.media=void 0,this.fragmentTracker=void 0,this.hls=void 0,this.nudgeRetry=0,this.stallReported=!1,this.stalled=null,this.moved=!1,this.seeking=!1,this.config=y,this.media=d,this.fragmentTracker=e,this.hls=a}var T=h.prototype;return T.destroy=function(){this.hls=this.fragmentTracker=this.media=null},T.poll=function(y){var d=this.config,e=this.media,a=this.stalled,t=e.currentTime,o=e.seeking,u=this.seeking&&!o,n=!this.seeking&&o;if(this.seeking=o,t===y){if((n||u)&&(this.stalled=null),!e.paused&&!e.ended&&e.playbackRate!==0&&R.BufferHelper.getBuffered(e).length){var l=R.BufferHelper.bufferInfo(e,t,0),p=l.len>0,f=l.nextStart||0;if(p||f){if(o){var v=l.len>D,r=!f||f-t>D&&!this.fragmentTracker.getPartialFragment(t);if(v||r)return;this.moved=!1}if(!this.moved&&this.stalled!==null){var i,c=Math.max(f,l.start||0)-t,S=this.hls.levels?this.hls.levels[this.hls.currentLevel]:null,L=!(S==null||(i=S.details)===null||i===void 0)&&i.live?2*S.details.targetduration:D;if(c>0&&c<=L)return void this._trySkipBufferHole(null)}var _=self.performance.now();if(a!==null){var x=_-a;!o&&x>=C&&this._reportStall(l.len);var w=R.BufferHelper.bufferInfo(e,t,d.maxBufferHole);this._tryFixBufferStall(w,x)}else this.stalled=_}}}else if(this.moved=!0,a!==null){if(this.stallReported){var O=self.performance.now()-a;k.logger.warn("playback not stuck anymore @"+t+", after "+Math.round(O)+"ms"),this.stallReported=!1}this.stalled=null,this.nudgeRetry=0}},T._tryFixBufferStall=function(y,d){var e=this.config,a=this.fragmentTracker,t=this.media.currentTime,o=a.getPartialFragment(t);o&&this._trySkipBufferHole(o)||y.len>e.maxBufferHole&&d>1e3*e.highBufferWatchdogPeriod&&(k.logger.warn("Trying to nudge playhead over buffer-hole"),this.stalled=null,this._tryNudgeBuffer())},T._reportStall=function(y){var d=this.hls,e=this.media;this.stallReported||(this.stallReported=!0,k.logger.warn("Playback stalling at @"+e.currentTime+" due to low buffer (buffer="+y+")"),d.trigger(A.Events.ERROR,{type:E.ErrorTypes.MEDIA_ERROR,details:E.ErrorDetails.BUFFER_STALLED_ERROR,fatal:!1,buffer:y}))},T._trySkipBufferHole=function(y){for(var d=this.config,e=this.hls,a=this.media,t=a.currentTime,o=0,u=R.BufferHelper.getBuffered(a),n=0;n<u.length;n++){var l=u.start(n);if(t+d.maxBufferHole>=o&&t<l){var p=Math.max(l+b,a.currentTime+s);return k.logger.warn("skipping hole, adjusting currentTime from "+t+" to "+p),this.moved=!0,this.stalled=null,a.currentTime=p,y&&e.trigger(A.Events.ERROR,{type:E.ErrorTypes.MEDIA_ERROR,details:E.ErrorDetails.BUFFER_SEEK_OVER_HOLE,fatal:!1,reason:"fragment loaded with buffer holes, seeking from "+t+" to "+p,frag:y}),p}o=u.end(n)}return 0},T._tryNudgeBuffer=function(){var y=this.config,d=this.hls,e=this.media,a=e.currentTime,t=(this.nudgeRetry||0)+1;if(this.nudgeRetry=t,t<y.nudgeMaxRetry){var o=a+t*y.nudgeOffset;k.logger.warn("Nudging 'currentTime' from "+a+" to "+o),e.currentTime=o,d.trigger(A.Events.ERROR,{type:E.ErrorTypes.MEDIA_ERROR,details:E.ErrorDetails.BUFFER_NUDGE_ON_STALL,fatal:!1})}else k.logger.error("Playhead still not moving while enough data buffered @"+a+" after "+y.nudgeMaxRetry+" nudges"),d.trigger(A.Events.ERROR,{type:E.ErrorTypes.MEDIA_ERROR,details:E.ErrorDetails.BUFFER_STALLED_ERROR,fatal:!0})},h}()},"./src/controller/id3-track-controller.ts":function(M,I,g){g.r(I);var R=g("./src/events.ts"),E=g("./src/utils/texttrack-utils.ts"),A=g("./src/demux/id3.ts"),k=function(){function C(s){this.hls=void 0,this.id3Track=null,this.media=null,this.hls=s,this._registerListeners()}var D=C.prototype;return D.destroy=function(){this._unregisterListeners()},D._registerListeners=function(){var s=this.hls;s.on(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),s.on(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this),s.on(R.Events.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),s.on(R.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)},D._unregisterListeners=function(){var s=this.hls;s.off(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),s.off(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this),s.off(R.Events.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),s.off(R.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)},D.onMediaAttached=function(s,b){this.media=b.media},D.onMediaDetaching=function(){this.id3Track&&(Object(E.clearCurrentCues)(this.id3Track),this.id3Track=null,this.media=null)},D.getID3Track=function(s){if(this.media){for(var b=0;b<s.length;b++){var m=s[b];if(m.kind==="metadata"&&m.label==="id3")return Object(E.sendAddTrackEvent)(m,this.media),m}return this.media.addTextTrack("metadata","id3")}},D.onFragParsingMetadata=function(s,b){if(this.media){var m=b.frag,h=b.samples;this.id3Track||(this.id3Track=this.getID3Track(this.media.textTracks),this.id3Track.mode="hidden");for(var T=self.WebKitDataCue||self.VTTCue||self.TextTrackCue,y=0;y<h.length;y++){var d=A.getID3Frames(h[y].data);if(d){var e=h[y].pts,a=y<h.length-1?h[y+1].pts:m.end;a-e<=0&&(a=e+.25);for(var t=0;t<d.length;t++){var o=d[t];if(!A.isTimeStampFrame(o)){var u=new T(e,a,"");u.value=o,this.id3Track.addCue(u)}}}}}},D.onBufferFlushing=function(s,b){var m=b.startOffset,h=b.endOffset,T=b.type;if(!T||T==="audio"){var y=this.id3Track;y&&Object(E.removeCuesInRange)(y,m,h)}},C}();I.default=k},"./src/controller/latency-controller.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return C});var R=g("./src/errors.ts"),E=g("./src/events.ts"),A=g("./src/utils/logger.ts");function k(D,s){for(var b=0;b<s.length;b++){var m=s[b];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(D,m.key,m)}}var C=function(){function D(h){var T=this;this.hls=void 0,this.config=void 0,this.media=null,this.levelDetails=null,this.currentTime=0,this.stallCount=0,this._latency=null,this.timeupdateHandler=function(){return T.timeupdate()},this.hls=h,this.config=h.config,this.registerListeners()}var s,b,m=D.prototype;return m.destroy=function(){this.unregisterListeners(),this.onMediaDetaching(),this.levelDetails=null,this.hls=this.timeupdateHandler=null},m.registerListeners=function(){this.hls.on(E.Events.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(E.Events.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.on(E.Events.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(E.Events.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.on(E.Events.ERROR,this.onError,this)},m.unregisterListeners=function(){this.hls.off(E.Events.MEDIA_ATTACHED,this.onMediaAttached),this.hls.off(E.Events.MEDIA_DETACHING,this.onMediaDetaching),this.hls.off(E.Events.MANIFEST_LOADING,this.onManifestLoading),this.hls.off(E.Events.LEVEL_UPDATED,this.onLevelUpdated),this.hls.off(E.Events.ERROR,this.onError)},m.onMediaAttached=function(h,T){this.media=T.media,this.media.addEventListener("timeupdate",this.timeupdateHandler)},m.onMediaDetaching=function(){this.media&&(this.media.removeEventListener("timeupdate",this.timeupdateHandler),this.media=null)},m.onManifestLoading=function(){this.levelDetails=null,this._latency=null,this.stallCount=0},m.onLevelUpdated=function(h,T){var y=T.details;this.levelDetails=y,y.advanced&&this.timeupdate(),!y.live&&this.media&&this.media.removeEventListener("timeupdate",this.timeupdateHandler)},m.onError=function(h,T){T.details===R.ErrorDetails.BUFFER_STALLED_ERROR&&(this.stallCount++,A.logger.warn("[playback-rate-controller]: Stall detected, adjusting target latency"))},m.timeupdate=function(){var h=this.media,T=this.levelDetails;if(h&&T){this.currentTime=h.currentTime;var y=this.computeLatency();if(y!==null){this._latency=y;var d=this.config,e=d.lowLatencyMode,a=d.maxLiveSyncPlaybackRate;if(e&&a!==1){var t=this.targetLatency;if(t!==null){var o=y-t,u=o<Math.min(this.maxLatency,t+T.targetduration);if(T.live&&u&&o>.05&&this.forwardBufferLength>1){var n=Math.min(2,Math.max(1,a)),l=Math.round(2/(1+Math.exp(-.75*o-this.edgeStalled))*20)/20;h.playbackRate=Math.min(n,Math.max(1,l))}else h.playbackRate!==1&&h.playbackRate!==0&&(h.playbackRate=1)}}}}},m.estimateLiveEdge=function(){var h=this.levelDetails;return h===null?null:h.edge+h.age},m.computeLatency=function(){var h=this.estimateLiveEdge();return h===null?null:h-this.currentTime},s=D,(b=[{key:"latency",get:function(){return this._latency||0}},{key:"maxLatency",get:function(){var h=this.config,T=this.levelDetails;return h.liveMaxLatencyDuration!==void 0?h.liveMaxLatencyDuration:T?h.liveMaxLatencyDurationCount*T.targetduration:0}},{key:"targetLatency",get:function(){var h=this.levelDetails;if(h===null)return null;var T=h.holdBack,y=h.partHoldBack,d=h.targetduration,e=this.config,a=e.liveSyncDuration,t=e.liveSyncDurationCount,o=e.lowLatencyMode,u=this.hls.userConfig,n=o&&y||T;(u.liveSyncDuration||u.liveSyncDurationCount||n===0)&&(n=a!==void 0?a:t*d);var l=d;return n+Math.min(1*this.stallCount,l)}},{key:"liveSyncPosition",get:function(){var h=this.estimateLiveEdge(),T=this.targetLatency,y=this.levelDetails;if(h===null||T===null||y===null)return null;var d=y.edge,e=h-T-this.edgeStalled,a=d-y.totalduration,t=d-(this.config.lowLatencyMode&&y.partTarget||y.targetduration);return Math.min(Math.max(a,e),t)}},{key:"drift",get:function(){var h=this.levelDetails;return h===null?1:h.drift}},{key:"edgeStalled",get:function(){var h=this.levelDetails;if(h===null)return 0;var T=3*(this.config.lowLatencyMode&&h.partTarget||h.targetduration);return Math.max(h.age-T,0)}},{key:"forwardBufferLength",get:function(){var h=this.media,T=this.levelDetails;if(!h||!T)return 0;var y=h.buffered.length;return y?h.buffered.end(y-1):T.edge-this.currentTime}}])&&k(s.prototype,b),D}()},"./src/controller/level-controller.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return y});var R=g("./src/types/level.ts"),E=g("./src/events.ts"),A=g("./src/errors.ts"),k=g("./src/utils/codecs.ts"),C=g("./src/controller/level-helper.ts"),D=g("./src/controller/base-playlist-controller.ts"),s=g("./src/types/loader.ts");function b(){return(b=Object.assign||function(d){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(d[t]=a[t])}return d}).apply(this,arguments)}function m(d,e){for(var a=0;a<e.length;a++){var t=e[a];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(d,t.key,t)}}function h(d,e){return(h=Object.setPrototypeOf||function(a,t){return a.__proto__=t,a})(d,e)}var T=/chrome|firefox/.test(navigator.userAgent.toLowerCase()),y=function(d){var e,a;function t(l){var p;return(p=d.call(this,l,"[level-controller]")||this)._levels=[],p._firstLevel=-1,p._startLevel=void 0,p.currentLevelIndex=-1,p.manualLevelIndex=-1,p.onParsedComplete=void 0,p._registerListeners(),p}a=d,(e=t).prototype=Object.create(a.prototype),e.prototype.constructor=e,h(e,a);var o,u,n=t.prototype;return n._registerListeners=function(){var l=this.hls;l.on(E.Events.MANIFEST_LOADED,this.onManifestLoaded,this),l.on(E.Events.LEVEL_LOADED,this.onLevelLoaded,this),l.on(E.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),l.on(E.Events.FRAG_LOADED,this.onFragLoaded,this),l.on(E.Events.ERROR,this.onError,this)},n._unregisterListeners=function(){var l=this.hls;l.off(E.Events.MANIFEST_LOADED,this.onManifestLoaded,this),l.off(E.Events.LEVEL_LOADED,this.onLevelLoaded,this),l.off(E.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),l.off(E.Events.FRAG_LOADED,this.onFragLoaded,this),l.off(E.Events.ERROR,this.onError,this)},n.destroy=function(){this._unregisterListeners(),this.manualLevelIndex=-1,this._levels.length=0,d.prototype.destroy.call(this)},n.startLoad=function(){this._levels.forEach(function(l){l.loadError=0}),d.prototype.startLoad.call(this)},n.onManifestLoaded=function(l,p){var f,v,r=[],i=[],c=[],S={},L=!1,_=!1,x=!1;if(p.levels.forEach(function(F){var N=F.attrs;L=L||!(!F.width||!F.height),_=_||!!F.videoCodec,x=x||!!F.audioCodec,T&&F.audioCodec&&F.audioCodec.indexOf("mp4a.40.34")!==-1&&(F.audioCodec=void 0);var U=F.bitrate+"-"+F.attrs.RESOLUTION+"-"+F.attrs.CODECS;(v=S[U])?v.url.push(F.url):(v=new R.Level(F),S[U]=v,r.push(v)),N&&(N.AUDIO&&Object(C.addGroupId)(v,"audio",N.AUDIO),N.SUBTITLES&&Object(C.addGroupId)(v,"text",N.SUBTITLES))}),(L||_)&&x&&(r=r.filter(function(F){var N=F.videoCodec,U=F.width,G=F.height;return!!N||!(!U||!G)})),r=r.filter(function(F){var N=F.audioCodec,U=F.videoCodec;return(!N||Object(k.isCodecSupportedInMp4)(N,"audio"))&&(!U||Object(k.isCodecSupportedInMp4)(U,"video"))}),p.audioTracks&&(i=p.audioTracks.filter(function(F){return!F.audioCodec||Object(k.isCodecSupportedInMp4)(F.audioCodec,"audio")}),Object(C.assignTrackIdsByGroup)(i)),p.subtitles&&(c=p.subtitles,Object(C.assignTrackIdsByGroup)(c)),r.length>0){f=r[0].bitrate,r.sort(function(F,N){return F.bitrate-N.bitrate}),this._levels=r;for(var w=0;w<r.length;w++)if(r[w].bitrate===f){this._firstLevel=w,this.log("manifest loaded, "+r.length+" level(s) found, first bitrate: "+f);break}var O=x&&!_,P={levels:r,audioTracks:i,subtitleTracks:c,firstLevel:this._firstLevel,stats:p.stats,audio:x,video:_,altAudio:!O&&i.some(function(F){return!!F.url})};this.hls.trigger(E.Events.MANIFEST_PARSED,P),(this.hls.config.autoStartLoad||this.hls.forceStartLoad)&&this.hls.startLoad(this.hls.config.startPosition)}else this.hls.trigger(E.Events.ERROR,{type:A.ErrorTypes.MEDIA_ERROR,details:A.ErrorDetails.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:p.url,reason:"no level with compatible codecs found in manifest"})},n.onError=function(l,p){if(d.prototype.onError.call(this,l,p),!p.fatal){var f=p.context,v=this._levels[this.currentLevelIndex];if(f&&(f.type===s.PlaylistContextType.AUDIO_TRACK&&v.audioGroupIds&&f.groupId===v.audioGroupIds[v.urlId]||f.type===s.PlaylistContextType.SUBTITLE_TRACK&&v.textGroupIds&&f.groupId===v.textGroupIds[v.urlId]))this.redundantFailover(this.currentLevelIndex);else{var r,i=!1,c=!0;switch(p.details){case A.ErrorDetails.FRAG_LOAD_ERROR:case A.ErrorDetails.FRAG_LOAD_TIMEOUT:case A.ErrorDetails.KEY_LOAD_ERROR:case A.ErrorDetails.KEY_LOAD_TIMEOUT:if(p.frag){var S=this._levels[p.frag.level];S?(S.fragmentError++,S.fragmentError>this.hls.config.fragLoadingMaxRetry&&(r=p.frag.level)):r=p.frag.level}break;case A.ErrorDetails.LEVEL_LOAD_ERROR:case A.ErrorDetails.LEVEL_LOAD_TIMEOUT:f&&(f.deliveryDirectives&&(c=!1),r=f.level),i=!0;break;case A.ErrorDetails.REMUX_ALLOC_ERROR:r=p.level,i=!0}r!==void 0&&this.recoverLevel(p,r,i,c)}}},n.recoverLevel=function(l,p,f,v){var r=l.details,i=this._levels[p];if(i.loadError++,f){if(!this.retryLoadingOrFail(l))return void(this.currentLevelIndex=-1);l.levelRetry=!0}if(v){var c=i.url.length;if(c>1&&i.loadError<c)l.levelRetry=!0,this.redundantFailover(p);else if(this.manualLevelIndex===-1){var S=p===0?this._levels.length-1:p-1;this.currentLevelIndex!==S&&this._levels[S].loadError===0&&(this.warn(r+": switch to "+S),l.levelRetry=!0,this.hls.nextAutoLevel=S)}}},n.redundantFailover=function(l){var p=this._levels[l],f=p.url.length;if(f>1){var v=(p.urlId+1)%f;this.warn("Switching to redundant URL-id "+v),this._levels.forEach(function(r){r.urlId=v}),this.level=l}},n.onFragLoaded=function(l,p){var f=p.frag;if(f!==void 0&&f.type===s.PlaylistLevelType.MAIN){var v=this._levels[f.level];v!==void 0&&(v.fragmentError=0,v.loadError=0)}},n.onLevelLoaded=function(l,p){var f,v,r=p.level,i=p.details,c=this._levels[r];if(!c)return this.warn("Invalid level index "+r),void((v=p.deliveryDirectives)!==null&&v!==void 0&&v.skip&&(i.deltaUpdateFailed=!0));r===this.currentLevelIndex?(c.fragmentError===0&&(c.loadError=0,this.retryCount=0),this.playlistLoaded(r,p,c.details)):(f=p.deliveryDirectives)!==null&&f!==void 0&&f.skip&&(i.deltaUpdateFailed=!0)},n.onAudioTrackSwitched=function(l,p){var f=this.hls.levels[this.currentLevelIndex];if(f&&f.audioGroupIds){for(var v=-1,r=this.hls.audioTracks[p.id].groupId,i=0;i<f.audioGroupIds.length;i++)if(f.audioGroupIds[i]===r){v=i;break}v!==f.urlId&&(f.urlId=v,this.startLoad())}},n.loadPlaylist=function(l){var p=this.currentLevelIndex,f=this._levels[p];if(this.canLoad&&f&&f.url.length>0){var v=f.urlId,r=f.url[v];if(l)try{r=l.addDirectives(r)}catch(i){this.warn("Could not construct new URL with HLS Delivery Directives: "+i)}this.log("Attempt loading level index "+p+(l?" at sn "+l.msn+" part "+l.part:"")+" with URL-id "+v+" "+r),this.clearTimer(),this.hls.trigger(E.Events.LEVEL_LOADING,{url:r,level:p,id:v,deliveryDirectives:l||null})}},n.removeLevel=function(l,p){var f=function(r,i){return i!==p},v=this._levels.filter(function(r,i){return i!==l||r.url.length>1&&p!==void 0&&(r.url=r.url.filter(f),r.audioGroupIds&&(r.audioGroupIds=r.audioGroupIds.filter(f)),r.textGroupIds&&(r.textGroupIds=r.textGroupIds.filter(f)),r.urlId=0,!0)}).map(function(r,i){var c=r.details;return c!=null&&c.fragments&&c.fragments.forEach(function(S){S.level=i}),r});this._levels=v,this.hls.trigger(E.Events.LEVELS_UPDATED,{levels:v})},o=t,(u=[{key:"levels",get:function(){return this._levels.length===0?null:this._levels}},{key:"level",get:function(){return this.currentLevelIndex},set:function(l){var p,f=this._levels;if(f.length!==0&&(this.currentLevelIndex!==l||(p=f[l])===null||p===void 0||!p.details)){if(l<0||l>=f.length){var v=l<0;if(this.hls.trigger(E.Events.ERROR,{type:A.ErrorTypes.OTHER_ERROR,details:A.ErrorDetails.LEVEL_SWITCH_ERROR,level:l,fatal:v,reason:"invalid level idx"}),v)return;l=Math.min(l,f.length-1)}this.clearTimer();var r=this.currentLevelIndex,i=f[r],c=f[l];this.log("switching to level "+l+" from "+r),this.currentLevelIndex=l;var S=b({},c,{level:l,maxBitrate:c.maxBitrate,uri:c.uri,urlId:c.urlId});delete S._urlId,this.hls.trigger(E.Events.LEVEL_SWITCHING,S);var L=c.details;if(!L||L.live){var _=this.switchParams(c.uri,i==null?void 0:i.details);this.loadPlaylist(_)}}}},{key:"manualLevel",get:function(){return this.manualLevelIndex},set:function(l){this.manualLevelIndex=l,this._startLevel===void 0&&(this._startLevel=l),l!==-1&&(this.level=l)}},{key:"firstLevel",get:function(){return this._firstLevel},set:function(l){this._firstLevel=l}},{key:"startLevel",get:function(){if(this._startLevel===void 0){var l=this.hls.config.startLevel;return l!==void 0?l:this._firstLevel}return this._startLevel},set:function(l){this._startLevel=l}},{key:"nextLoadLevel",get:function(){return this.manualLevelIndex!==-1?this.manualLevelIndex:this.hls.nextAutoLevel},set:function(l){this.level=l,this.manualLevelIndex===-1&&(this.hls.nextAutoLevel=l)}}])&&m(o.prototype,u),t}(D.default)},"./src/controller/level-helper.ts":function(M,I,g){g.r(I),g.d(I,"addGroupId",function(){return A}),g.d(I,"assignTrackIdsByGroup",function(){return k}),g.d(I,"updatePTS",function(){return C}),g.d(I,"updateFragPTSDTS",function(){return s}),g.d(I,"mergeDetails",function(){return b}),g.d(I,"mapPartIntersection",function(){return m}),g.d(I,"mapFragmentIntersection",function(){return h}),g.d(I,"adjustSliding",function(){return T}),g.d(I,"addSliding",function(){return y}),g.d(I,"computeReloadInterval",function(){return d}),g.d(I,"getFragmentWithSN",function(){return e}),g.d(I,"getPartWith",function(){return a});var R=g("./src/polyfills/number.ts"),E=g("./src/utils/logger.ts");function A(t,o,u){switch(o){case"audio":t.audioGroupIds||(t.audioGroupIds=[]),t.audioGroupIds.push(u);break;case"text":t.textGroupIds||(t.textGroupIds=[]),t.textGroupIds.push(u)}}function k(t){var o={};t.forEach(function(u){var n=u.groupId||"";u.id=o[n]=o[n]||0,o[n]++})}function C(t,o,u){D(t[o],t[u])}function D(t,o){var u=o.startPTS;if(Object(R.isFiniteNumber)(u)){var n,l=0;o.sn>t.sn?(l=u-t.start,n=t):(l=t.start-u,n=o),n.duration!==l&&(n.duration=l)}else o.sn>t.sn?t.cc===o.cc&&t.minEndPTS?o.start=t.start+(t.minEndPTS-t.start):o.start=t.start+t.duration:o.start=Math.max(t.start-o.duration,0)}function s(t,o,u,n,l,p){n-u<=0&&(E.logger.warn("Fragment should have a positive duration",o),n=u+o.duration,p=l+o.duration);var f=u,v=n,r=o.startPTS,i=o.endPTS;if(Object(R.isFiniteNumber)(r)){var c=Math.abs(r-u);Object(R.isFiniteNumber)(o.deltaPTS)?o.deltaPTS=Math.max(c,o.deltaPTS):o.deltaPTS=c,f=Math.max(u,r),u=Math.min(u,r),l=Math.min(l,o.startDTS),v=Math.min(n,i),n=Math.max(n,i),p=Math.max(p,o.endDTS)}o.duration=n-u;var S=u-o.start;o.appendedPTS=n,o.start=o.startPTS=u,o.maxStartPTS=f,o.startDTS=l,o.endPTS=n,o.minEndPTS=v,o.endDTS=p;var L,_=o.sn;if(!t||_<t.startSN||_>t.endSN)return 0;var x=_-t.startSN,w=t.fragments;for(w[x]=o,L=x;L>0;L--)D(w[L],w[L-1]);for(L=x;L<w.length-1;L++)D(w[L],w[L+1]);return t.fragmentHint&&D(w[w.length-1],t.fragmentHint),t.PTSKnown=t.alignedSliding=!0,S}function b(t,o){for(var u=null,n=t.fragments,l=n.length-1;l>=0;l--){var p=n[l].initSegment;if(p){u=p;break}}t.fragmentHint&&delete t.fragmentHint.endPTS;var f,v=0;if(h(t,o,function(_,x){var w;_.relurl&&(v=_.cc-x.cc),Object(R.isFiniteNumber)(_.startPTS)&&Object(R.isFiniteNumber)(_.endPTS)&&(x.start=x.startPTS=_.startPTS,x.startDTS=_.startDTS,x.appendedPTS=_.appendedPTS,x.maxStartPTS=_.maxStartPTS,x.endPTS=_.endPTS,x.endDTS=_.endDTS,x.minEndPTS=_.minEndPTS,x.duration=_.endPTS-_.startPTS,x.duration&&(f=x),o.PTSKnown=o.alignedSliding=!0),x.elementaryStreams=_.elementaryStreams,x.loader=_.loader,x.stats=_.stats,x.urlId=_.urlId,_.initSegment?(x.initSegment=_.initSegment,u=_.initSegment):x.initSegment&&x.initSegment.relurl!=((w=u)===null||w===void 0?void 0:w.relurl)||(x.initSegment=u)}),o.skippedSegments&&(o.deltaUpdateFailed=o.fragments.some(function(_){return!_}),o.deltaUpdateFailed)){E.logger.warn("[level-helper] Previous playlist missing segments skipped in delta playlist");for(var r=o.skippedSegments;r--;)o.fragments.shift();o.startSN=o.fragments[0].sn,o.startCC=o.fragments[0].cc}var i=o.fragments;if(v){E.logger.warn("discontinuity sliding from playlist, take drift into account");for(var c=0;c<i.length;c++)i[c].cc+=v}o.skippedSegments&&(o.startCC=o.fragments[0].cc),m(t.partList,o.partList,function(_,x){x.elementaryStreams=_.elementaryStreams,x.stats=_.stats}),f?s(o,f,f.startPTS,f.endPTS,f.startDTS,f.endDTS):T(t,o),i.length&&(o.totalduration=o.edge-i[0].start),o.driftStartTime=t.driftStartTime,o.driftStart=t.driftStart;var S=o.advancedDateTime;if(o.advanced&&S){var L=o.edge;o.driftStart||(o.driftStartTime=S,o.driftStart=L),o.driftEndTime=S,o.driftEnd=L}else o.driftEndTime=t.driftEndTime,o.driftEnd=t.driftEnd,o.advancedDateTime=t.advancedDateTime}function m(t,o,u){if(t&&o)for(var n=0,l=0,p=t.length;l<=p;l++){var f=t[l],v=o[l+n];f&&v&&f.index===v.index&&f.fragment.sn===v.fragment.sn?u(f,v):n--}}function h(t,o,u){for(var n=o.skippedSegments,l=Math.max(t.startSN,o.startSN)-o.startSN,p=(t.fragmentHint?1:0)+(n?o.endSN:Math.min(t.endSN,o.endSN))-o.startSN,f=o.startSN-t.startSN,v=o.fragmentHint?o.fragments.concat(o.fragmentHint):o.fragments,r=t.fragmentHint?t.fragments.concat(t.fragmentHint):t.fragments,i=l;i<=p;i++){var c=r[f+i],S=v[i];n&&!S&&i<n&&(S=o.fragments[i]=c),c&&S&&u(c,S)}}function T(t,o){var u=o.startSN+o.skippedSegments-t.startSN,n=t.fragments;u<0||u>=n.length||y(o,n[u].start)}function y(t,o){if(o){for(var u=t.fragments,n=t.skippedSegments;n<u.length;n++)u[n].start+=o;t.fragmentHint&&(t.fragmentHint.start+=o)}}function d(t,o){var u,n=1e3*t.levelTargetDuration,l=n/2,p=t.age,f=p>0&&p<3*n,v=o.loading.end-o.loading.start,r=t.availabilityDelay;if(t.updated===!1)if(f){var i=333*t.misses;u=Math.max(Math.min(l,2*v),i),t.availabilityDelay=(t.availabilityDelay||0)+u}else u=l;else f?(r=Math.min(r||n/2,p),t.availabilityDelay=r,u=r+n-p):u=n-v;return Math.round(u)}function e(t,o,u){if(!t||!t.details)return null;var n=t.details,l=n.fragments[o-n.startSN];return l||((l=n.fragmentHint)&&l.sn===o?l:o<n.startSN&&u&&u.sn===o?u:null)}function a(t,o,u){if(!t||!t.details)return null;var n=t.details.partList;if(n)for(var l=n.length;l--;){var p=n[l];if(p.index===u&&p.fragment.sn===o)return p}return null}},"./src/controller/stream-controller.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return t});var R=g("./src/polyfills/number.ts"),E=g("./src/controller/base-stream-controller.ts"),A=g("./src/is-supported.ts"),k=g("./src/events.ts"),C=g("./src/utils/buffer-helper.ts"),D=g("./src/controller/fragment-tracker.ts"),s=g("./src/types/loader.ts"),b=g("./src/loader/fragment.ts"),m=g("./src/demux/transmuxer-interface.ts"),h=g("./src/types/transmuxer.ts"),T=g("./src/controller/gap-controller.ts"),y=g("./src/errors.ts"),d=g("./src/utils/logger.ts");function e(o,u){for(var n=0;n<u.length;n++){var l=u[n];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(o,l.key,l)}}function a(o,u){return(a=Object.setPrototypeOf||function(n,l){return n.__proto__=l,n})(o,u)}var t=function(o){var u,n;function l(r,i){var c;return(c=o.call(this,r,i,"[stream-controller]")||this).audioCodecSwap=!1,c.gapController=null,c.level=-1,c._forceStartLoad=!1,c.altAudio=!1,c.audioOnly=!1,c.fragPlaying=null,c.onvplaying=null,c.onvseeked=null,c.fragLastKbps=0,c.stalled=!1,c.couldBacktrack=!1,c.audioCodecSwitch=!1,c.videoBuffer=null,c._registerListeners(),c}n=o,(u=l).prototype=Object.create(n.prototype),u.prototype.constructor=u,a(u,n);var p,f,v=l.prototype;return v._registerListeners=function(){var r=this.hls;r.on(k.Events.MEDIA_ATTACHED,this.onMediaAttached,this),r.on(k.Events.MEDIA_DETACHING,this.onMediaDetaching,this),r.on(k.Events.MANIFEST_LOADING,this.onManifestLoading,this),r.on(k.Events.MANIFEST_PARSED,this.onManifestParsed,this),r.on(k.Events.LEVEL_LOADING,this.onLevelLoading,this),r.on(k.Events.LEVEL_LOADED,this.onLevelLoaded,this),r.on(k.Events.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),r.on(k.Events.ERROR,this.onError,this),r.on(k.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),r.on(k.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),r.on(k.Events.BUFFER_CREATED,this.onBufferCreated,this),r.on(k.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),r.on(k.Events.LEVELS_UPDATED,this.onLevelsUpdated,this),r.on(k.Events.FRAG_BUFFERED,this.onFragBuffered,this)},v._unregisterListeners=function(){var r=this.hls;r.off(k.Events.MEDIA_ATTACHED,this.onMediaAttached,this),r.off(k.Events.MEDIA_DETACHING,this.onMediaDetaching,this),r.off(k.Events.MANIFEST_LOADING,this.onManifestLoading,this),r.off(k.Events.MANIFEST_PARSED,this.onManifestParsed,this),r.off(k.Events.LEVEL_LOADED,this.onLevelLoaded,this),r.off(k.Events.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),r.off(k.Events.ERROR,this.onError,this),r.off(k.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),r.off(k.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),r.off(k.Events.BUFFER_CREATED,this.onBufferCreated,this),r.off(k.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),r.off(k.Events.LEVELS_UPDATED,this.onLevelsUpdated,this),r.off(k.Events.FRAG_BUFFERED,this.onFragBuffered,this)},v.onHandlerDestroying=function(){this._unregisterListeners(),this.onMediaDetaching()},v.startLoad=function(r){if(this.levels){var i=this.lastCurrentTime,c=this.hls;if(this.stopLoad(),this.setInterval(100),this.level=-1,this.fragLoadError=0,!this.startFragRequested){var S=c.startLevel;S===-1&&(c.config.testBandwidth?(S=0,this.bitrateTest=!0):S=c.nextAutoLevel),this.level=c.nextLoadLevel=S,this.loadedmetadata=!1}i>0&&r===-1&&(this.log("Override startPosition with lastCurrentTime @"+i.toFixed(3)),r=i),this.state=E.State.IDLE,this.nextLoadPosition=this.startPosition=this.lastCurrentTime=r,this.tick()}else this._forceStartLoad=!0,this.state=E.State.STOPPED},v.stopLoad=function(){this._forceStartLoad=!1,o.prototype.stopLoad.call(this)},v.doTick=function(){switch(this.state){case E.State.IDLE:this.doTickIdle();break;case E.State.WAITING_LEVEL:var r,i=this.levels,c=this.level,S=i==null||(r=i[c])===null||r===void 0?void 0:r.details;if(S&&(!S.live||this.levelLastLoaded===this.level)){if(this.waitForCdnTuneIn(S))break;this.state=E.State.IDLE;break}break;case E.State.FRAG_LOADING_WAITING_RETRY:var L,_=self.performance.now(),x=this.retryDate;(!x||_>=x||(L=this.media)!==null&&L!==void 0&&L.seeking)&&(this.log("retryDate reached, switch back to IDLE state"),this.state=E.State.IDLE)}this.onTickEnd()},v.onTickEnd=function(){o.prototype.onTickEnd.call(this),this.checkBuffer(),this.checkFragmentChanged()},v.doTickIdle=function(){var r,i,c=this.hls,S=this.levelLastLoaded,L=this.levels,_=this.media,x=c.config,w=c.nextLoadLevel;if(S!==null&&(_||!this.startFragRequested&&x.startFragPrefetch)&&(!this.altAudio||!this.audioOnly)&&L&&L[w]){var O=L[w];this.level=c.nextLoadLevel=w;var P=O.details;if(!P||this.state===E.State.WAITING_LEVEL||P.live&&this.levelLastLoaded!==w)this.state=E.State.WAITING_LEVEL;else{var F=this.getFwdBufferInfo(this.mediaBuffer?this.mediaBuffer:_,s.PlaylistLevelType.MAIN);if(F!==null&&!(F.len>=this.getMaxBufferLength(O.maxBitrate))){if(this._streamEnded(F,P)){var N={};return this.altAudio&&(N.type="video"),this.hls.trigger(k.Events.BUFFER_EOS,N),void(this.state=E.State.ENDED)}var U=F.end,G=this.getNextFragment(U,P);if(this.couldBacktrack&&!this.fragPrevious&&G&&G.sn!=="initSegment"){var K=G.sn-P.startSN;K>1&&(G=P.fragments[K-1],this.fragmentTracker.removeFragment(G))}if(G&&this.fragmentTracker.getState(G)===D.FragmentState.OK&&this.nextLoadPosition>U){var q=this.audioOnly&&!this.altAudio?b.ElementaryStreamTypes.AUDIO:b.ElementaryStreamTypes.VIDEO;this.afterBufferFlushed(_,q,s.PlaylistLevelType.MAIN),G=this.getNextFragment(this.nextLoadPosition,P)}G&&(!G.initSegment||G.initSegment.data||this.bitrateTest||(G=G.initSegment),((r=G.decryptdata)===null||r===void 0?void 0:r.keyFormat)!=="identity"||(i=G.decryptdata)!==null&&i!==void 0&&i.key?this.loadFragment(G,P,U):this.loadKey(G,P))}}}},v.loadFragment=function(r,i,c){var S,L=this.fragmentTracker.getState(r);if(this.fragCurrent=r,L===D.FragmentState.BACKTRACKED){var _=this.fragmentTracker.getBacktrackData(r);if(_)return this._handleFragmentLoadProgress(_),void this._handleFragmentLoadComplete(_);L=D.FragmentState.NOT_LOADED}L===D.FragmentState.NOT_LOADED||L===D.FragmentState.PARTIAL?r.sn==="initSegment"?this._loadInitSegment(r):this.bitrateTest?(r.bitrateTest=!0,this.log("Fragment "+r.sn+" of level "+r.level+" is being downloaded to test bitrate and will not be buffered"),this._loadBitrateTestFrag(r)):(this.startFragRequested=!0,o.prototype.loadFragment.call(this,r,i,c)):L===D.FragmentState.APPENDING?this.reduceMaxBufferLength(r.duration)&&this.fragmentTracker.removeFragment(r):((S=this.media)===null||S===void 0?void 0:S.buffered.length)===0&&this.fragmentTracker.removeAllFragments()},v.getAppendedFrag=function(r){var i=this.fragmentTracker.getAppendedFrag(r,s.PlaylistLevelType.MAIN);return i&&"fragment"in i?i.fragment:i},v.getBufferedFrag=function(r){return this.fragmentTracker.getBufferedFrag(r,s.PlaylistLevelType.MAIN)},v.followingBufferedFrag=function(r){return r?this.getBufferedFrag(r.end+.5):null},v.immediateLevelSwitch=function(){this.abortCurrentFrag(),this.flushMainBuffer(0,Number.POSITIVE_INFINITY)},v.nextLevelSwitch=function(){var r=this.levels,i=this.media;if(i!=null&&i.readyState){var c,S=this.getAppendedFrag(i.currentTime);if(S&&S.start>1&&this.flushMainBuffer(0,S.start-1),!i.paused&&r){var L=r[this.hls.nextLoadLevel],_=this.fragLastKbps;c=_&&this.fragCurrent?this.fragCurrent.duration*L.maxBitrate/(1e3*_)+1:0}else c=0;var x=this.getBufferedFrag(i.currentTime+c);if(x){var w=this.followingBufferedFrag(x);if(w){this.abortCurrentFrag();var O=w.maxStartPTS?w.maxStartPTS:w.start,P=w.duration,F=Math.max(x.end,O+Math.min(Math.max(P-this.config.maxFragLookUpTolerance,.5*P),.75*P));this.flushMainBuffer(F,Number.POSITIVE_INFINITY)}}}},v.abortCurrentFrag=function(){var r=this.fragCurrent;this.fragCurrent=null,r!=null&&r.loader&&r.loader.abort(),this.state===E.State.KEY_LOADING&&(this.state=E.State.IDLE),this.nextLoadPosition=this.getLoadPosition()},v.flushMainBuffer=function(r,i){o.prototype.flushMainBuffer.call(this,r,i,this.altAudio?"video":null)},v.onMediaAttached=function(r,i){o.prototype.onMediaAttached.call(this,r,i);var c=i.media;this.onvplaying=this.onMediaPlaying.bind(this),this.onvseeked=this.onMediaSeeked.bind(this),c.addEventListener("playing",this.onvplaying),c.addEventListener("seeked",this.onvseeked),this.gapController=new T.default(this.config,c,this.fragmentTracker,this.hls)},v.onMediaDetaching=function(){var r=this.media;r&&(r.removeEventListener("playing",this.onvplaying),r.removeEventListener("seeked",this.onvseeked),this.onvplaying=this.onvseeked=null,this.videoBuffer=null),this.fragPlaying=null,this.gapController&&(this.gapController.destroy(),this.gapController=null),o.prototype.onMediaDetaching.call(this)},v.onMediaPlaying=function(){this.tick()},v.onMediaSeeked=function(){var r=this.media,i=r?r.currentTime:null;Object(R.isFiniteNumber)(i)&&this.log("Media seeked to "+i.toFixed(3)),this.tick()},v.onManifestLoading=function(){this.log("Trigger BUFFER_RESET"),this.hls.trigger(k.Events.BUFFER_RESET,void 0),this.fragmentTracker.removeAllFragments(),this.couldBacktrack=this.stalled=!1,this.startPosition=this.lastCurrentTime=0,this.fragPlaying=null},v.onManifestParsed=function(r,i){var c,S=!1,L=!1;i.levels.forEach(function(_){(c=_.audioCodec)&&(c.indexOf("mp4a.40.2")!==-1&&(S=!0),c.indexOf("mp4a.40.5")!==-1&&(L=!0))}),this.audioCodecSwitch=S&&L&&!Object(A.changeTypeSupported)(),this.audioCodecSwitch&&this.log("Both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.levels=i.levels,this.startFragRequested=!1},v.onLevelLoading=function(r,i){var c=this.levels;if(c&&this.state===E.State.IDLE){var S=c[i.level];(!S.details||S.details.live&&this.levelLastLoaded!==i.level||this.waitForCdnTuneIn(S.details))&&(this.state=E.State.WAITING_LEVEL)}},v.onLevelLoaded=function(r,i){var c,S=this.levels,L=i.level,_=i.details,x=_.totalduration;if(S){this.log("Level "+L+" loaded ["+_.startSN+","+_.endSN+"], cc ["+_.startCC+", "+_.endCC+"] duration:"+x);var w=this.fragCurrent;!w||this.state!==E.State.FRAG_LOADING&&this.state!==E.State.FRAG_LOADING_WAITING_RETRY||w.level!==i.level&&w.loader&&(this.state=E.State.IDLE,w.loader.abort());var O=S[L],P=0;if(_.live||(c=O.details)!==null&&c!==void 0&&c.live){if(_.fragments[0]||(_.deltaUpdateFailed=!0),_.deltaUpdateFailed)return;P=this.alignPlaylists(_,O.details)}if(O.details=_,this.levelLastLoaded=L,this.hls.trigger(k.Events.LEVEL_UPDATED,{details:_,level:L}),this.state===E.State.WAITING_LEVEL){if(this.waitForCdnTuneIn(_))return;this.state=E.State.IDLE}this.startFragRequested?_.live&&this.synchronizeToLiveEdge(_):this.setStartPosition(_,P),this.tick()}else this.warn("Levels were reset while loading level "+L)},v._handleFragmentLoadProgress=function(r){var i,c=r.frag,S=r.part,L=r.payload,_=this.levels;if(_){var x=_[c.level],w=x.details;if(w){var O=x.videoCodec,P=w.PTSKnown||!w.live,F=(i=c.initSegment)===null||i===void 0?void 0:i.data,N=this._getAudioCodec(x),U=this.transmuxer=this.transmuxer||new m.default(this.hls,s.PlaylistLevelType.MAIN,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)),G=S?S.index:-1,K=G!==-1,q=new h.ChunkMetadata(c.level,c.sn,c.stats.chunkCount,L.byteLength,G,K),Y=this.initPTS[c.cc];U.push(L,F,N,O,c,S,w.totalduration,P,q,Y)}else this.warn("Dropping fragment "+c.sn+" of level "+c.level+" after level details were reset")}else this.warn("Levels were reset while fragment load was in progress. Fragment "+c.sn+" of level "+c.level+" will not be buffered")},v.onAudioTrackSwitching=function(r,i){var c=this.altAudio,S=!!i.url,L=i.id;if(!S){if(this.mediaBuffer!==this.media){this.log("Switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media;var _=this.fragCurrent;_!=null&&_.loader&&(this.log("Switching to main audio track, cancel main fragment load"),_.loader.abort()),this.resetTransmuxer(),this.resetLoadingState()}else this.audioOnly&&this.resetTransmuxer();var x=this.hls;c&&x.trigger(k.Events.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:"audio"}),x.trigger(k.Events.AUDIO_TRACK_SWITCHED,{id:L})}},v.onAudioTrackSwitched=function(r,i){var c=i.id,S=!!this.hls.audioTracks[c].url;if(S){var L=this.videoBuffer;L&&this.mediaBuffer!==L&&(this.log("Switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=L)}this.altAudio=S,this.tick()},v.onBufferCreated=function(r,i){var c,S,L=i.tracks,_=!1;for(var x in L){var w=L[x];if(w.id==="main"){if(S=x,c=w,x==="video"){var O=L[x];O&&(this.videoBuffer=O.buffer)}}else _=!0}_&&c?(this.log("Alternate track found, use "+S+".buffered to schedule main fragment loading"),this.mediaBuffer=c.buffer):this.mediaBuffer=this.media},v.onFragBuffered=function(r,i){var c=i.frag,S=i.part;if(!c||c.type===s.PlaylistLevelType.MAIN){if(this.fragContextChanged(c))return this.warn("Fragment "+c.sn+(S?" p: "+S.index:"")+" of level "+c.level+" finished buffering, but was aborted. state: "+this.state),void(this.state===E.State.PARSED&&(this.state=E.State.IDLE));var L=S?S.stats:c.stats;this.fragLastKbps=Math.round(8*L.total/(L.buffering.end-L.loading.first)),c.sn!=="initSegment"&&(this.fragPrevious=c),this.fragBufferedComplete(c,S)}},v.onError=function(r,i){switch(i.details){case y.ErrorDetails.FRAG_LOAD_ERROR:case y.ErrorDetails.FRAG_LOAD_TIMEOUT:case y.ErrorDetails.KEY_LOAD_ERROR:case y.ErrorDetails.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(s.PlaylistLevelType.MAIN,i);break;case y.ErrorDetails.LEVEL_LOAD_ERROR:case y.ErrorDetails.LEVEL_LOAD_TIMEOUT:this.state!==E.State.ERROR&&(i.fatal?(this.warn(""+i.details),this.state=E.State.ERROR):i.levelRetry||this.state!==E.State.WAITING_LEVEL||(this.state=E.State.IDLE));break;case y.ErrorDetails.BUFFER_FULL_ERROR:if(i.parent==="main"&&(this.state===E.State.PARSING||this.state===E.State.PARSED)){var c=!0,S=this.getFwdBufferInfo(this.media,s.PlaylistLevelType.MAIN);S&&S.len>.5&&(c=!this.reduceMaxBufferLength(S.len)),c&&(this.warn("buffer full error also media.currentTime is not buffered, flush main"),this.immediateLevelSwitch()),this.resetLoadingState()}}},v.checkBuffer=function(){var r=this.media,i=this.gapController;if(r&&i&&r.readyState){var c=C.BufferHelper.getBuffered(r);!this.loadedmetadata&&c.length?(this.loadedmetadata=!0,this.seekToStartPos()):i.poll(this.lastCurrentTime),this.lastCurrentTime=r.currentTime}},v.onFragLoadEmergencyAborted=function(){this.state=E.State.IDLE,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.tickImmediate()},v.onBufferFlushed=function(r,i){var c=i.type;if(c!==b.ElementaryStreamTypes.AUDIO||this.audioOnly&&!this.altAudio){var S=(c===b.ElementaryStreamTypes.VIDEO?this.videoBuffer:this.mediaBuffer)||this.media;this.afterBufferFlushed(S,c,s.PlaylistLevelType.MAIN)}},v.onLevelsUpdated=function(r,i){this.levels=i.levels},v.swapAudioCodec=function(){this.audioCodecSwap=!this.audioCodecSwap},v.seekToStartPos=function(){var r=this.media,i=r.currentTime,c=this.startPosition;if(c>=0&&i<c){if(r.seeking)return void d.logger.log("could not seek to "+c+", already seeking at "+i);var S=C.BufferHelper.getBuffered(r),L=(S.length?S.start(0):0)-c;L>0&&L<this.config.maxBufferHole&&(d.logger.log("adjusting start position by "+L+" to match buffer start"),c+=L,this.startPosition=c),this.log("seek to target start position "+c+" from current time "+i),r.currentTime=c}},v._getAudioCodec=function(r){var i=this.config.defaultAudioCodec||r.audioCodec;return this.audioCodecSwap&&i&&(this.log("Swapping audio codec"),i=i.indexOf("mp4a.40.5")!==-1?"mp4a.40.2":"mp4a.40.5"),i},v._loadBitrateTestFrag=function(r){var i=this;this._doFragLoad(r).then(function(c){var S=i.hls;if(c&&!S.nextLoadLevel&&!i.fragContextChanged(r)){i.fragLoadError=0,i.state=E.State.IDLE,i.startFragRequested=!1,i.bitrateTest=!1;var L=r.stats;L.parsing.start=L.parsing.end=L.buffering.start=L.buffering.end=self.performance.now(),S.trigger(k.Events.FRAG_LOADED,c)}})},v._handleTransmuxComplete=function(r){var i,c="main",S=this.hls,L=r.remuxResult,_=r.chunkMeta,x=this.getCurrentContext(_);if(!x)return this.warn("The loading context changed while buffering fragment "+_.sn+" of level "+_.level+". This chunk will not be buffered."),void this.resetLiveStartWhenNotLoaded(_.level);var w=x.frag,O=x.part,P=x.level,F=L.video,N=L.text,U=L.id3,G=L.initSegment,K=this.altAudio?void 0:L.audio;if(!this.fragContextChanged(w)){if(this.state=E.State.PARSING,G){G.tracks&&(this._bufferInitSegment(P,G.tracks,w,_),S.trigger(k.Events.FRAG_PARSING_INIT_SEGMENT,{frag:w,id:c,tracks:G.tracks}));var q=G.initPTS,Y=G.timescale;Object(R.isFiniteNumber)(q)&&(this.initPTS[w.cc]=q,S.trigger(k.Events.INIT_PTS_FOUND,{frag:w,id:c,initPTS:q,timescale:Y}))}if(F&&L.independent!==!1){if(P.details){var j=F.startPTS,X=F.endPTS,Q=F.startDTS,te=F.endDTS;if(O)O.elementaryStreams[F.type]={startPTS:j,endPTS:X,startDTS:Q,endDTS:te};else if(F.firstKeyFrame&&F.independent&&(this.couldBacktrack=!0),F.dropped&&F.independent){if(this.getLoadPosition()+this.config.maxBufferHole<j)return void this.backtrack(w);w.setElementaryStreamInfo(F.type,w.start,X,w.start,te,!0)}w.setElementaryStreamInfo(F.type,j,X,Q,te),this.bufferFragmentData(F,w,O,_)}}else if(L.independent===!1)return void this.backtrack(w);if(K){var ae=K.startPTS,oe=K.endPTS,ie=K.startDTS,ue=K.endDTS;O&&(O.elementaryStreams[b.ElementaryStreamTypes.AUDIO]={startPTS:ae,endPTS:oe,startDTS:ie,endDTS:ue}),w.setElementaryStreamInfo(b.ElementaryStreamTypes.AUDIO,ae,oe,ie,ue),this.bufferFragmentData(K,w,O,_)}if(U!=null&&(i=U.samples)!==null&&i!==void 0&&i.length){var J={frag:w,id:c,samples:U.samples};S.trigger(k.Events.FRAG_PARSING_METADATA,J)}if(N){var ne={frag:w,id:c,samples:N.samples};S.trigger(k.Events.FRAG_PARSING_USERDATA,ne)}}},v._bufferInitSegment=function(r,i,c,S){var L=this;if(this.state===E.State.PARSING){this.audioOnly=!!i.audio&&!i.video,this.altAudio&&!this.audioOnly&&delete i.audio;var _=i.audio,x=i.video,w=i.audiovideo;if(_){var O=r.audioCodec,P=navigator.userAgent.toLowerCase();this.audioCodecSwitch&&(O&&(O=O.indexOf("mp4a.40.5")!==-1?"mp4a.40.2":"mp4a.40.5"),_.metadata.channelCount!==1&&P.indexOf("firefox")===-1&&(O="mp4a.40.5")),P.indexOf("android")!==-1&&_.container!=="audio/mpeg"&&(O="mp4a.40.2",this.log("Android: force audio codec to "+O)),r.audioCodec&&r.audioCodec!==O&&this.log('Swapping manifest audio codec "'+r.audioCodec+'" for "'+O+'"'),_.levelCodec=O,_.id="main",this.log("Init audio buffer, container:"+_.container+", codecs[selected/level/parsed]=["+(O||"")+"/"+(r.audioCodec||"")+"/"+_.codec+"]")}x&&(x.levelCodec=r.videoCodec,x.id="main",this.log("Init video buffer, container:"+x.container+", codecs[level/parsed]=["+(r.videoCodec||"")+"/"+x.codec+"]")),w&&this.log("Init audiovideo buffer, container:"+w.container+", codecs[level/parsed]=["+(r.attrs.CODECS||"")+"/"+w.codec+"]"),this.hls.trigger(k.Events.BUFFER_CODECS,i),Object.keys(i).forEach(function(F){var N=i[F].initSegment;N!=null&&N.byteLength&&L.hls.trigger(k.Events.BUFFER_APPENDING,{type:F,data:N,frag:c,part:null,chunkMeta:S,parent:c.type})}),this.tick()}},v.backtrack=function(r){this.couldBacktrack=!0,this.resetTransmuxer(),this.flushBufferGap(r);var i=this.fragmentTracker.backtrack(r);this.fragPrevious=null,this.nextLoadPosition=r.start,i?this.resetFragmentLoading(r):this.state=E.State.BACKTRACKING},v.checkFragmentChanged=function(){var r=this.media,i=null;if(r&&r.readyState>1&&r.seeking===!1){var c=r.currentTime;if(C.BufferHelper.isBuffered(r,c)?i=this.getAppendedFrag(c):C.BufferHelper.isBuffered(r,c+.1)&&(i=this.getAppendedFrag(c+.1)),i){var S=this.fragPlaying,L=i.level;S&&i.sn===S.sn&&S.level===L&&i.urlId===S.urlId||(this.hls.trigger(k.Events.FRAG_CHANGED,{frag:i}),S&&S.level===L||this.hls.trigger(k.Events.LEVEL_SWITCHED,{level:L}),this.fragPlaying=i)}}},p=l,(f=[{key:"nextLevel",get:function(){var r=this.nextBufferedFrag;return r?r.level:-1}},{key:"currentLevel",get:function(){var r=this.media;if(r){var i=this.getAppendedFrag(r.currentTime);if(i)return i.level}return-1}},{key:"nextBufferedFrag",get:function(){var r=this.media;if(r){var i=this.getAppendedFrag(r.currentTime);return this.followingBufferedFrag(i)}return null}},{key:"forceStartLoad",get:function(){return this._forceStartLoad}}])&&e(p.prototype,f),l}(E.default)},"./src/controller/subtitle-stream-controller.ts":function(M,I,g){g.r(I),g.d(I,"SubtitleStreamController",function(){return d});var R=g("./src/events.ts"),E=g("./src/utils/logger.ts"),A=g("./src/utils/buffer-helper.ts"),k=g("./src/controller/fragment-finders.ts"),C=g("./src/utils/discontinuities.ts"),D=g("./src/controller/level-helper.ts"),s=g("./src/controller/fragment-tracker.ts"),b=g("./src/controller/base-stream-controller.ts"),m=g("./src/types/loader.ts"),h=g("./src/types/level.ts");function T(e,a){for(var t=0;t<a.length;t++){var o=a[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function y(e,a){return(y=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t})(e,a)}var d=function(e){var a,t;function o(p,f){var v;return(v=e.call(this,p,f,"[subtitle-stream-controller]")||this).levels=[],v.currentTrackId=-1,v.tracksBuffered=[],v.mainDetails=null,v._registerListeners(),v}t=e,(a=o).prototype=Object.create(t.prototype),a.prototype.constructor=a,y(a,t);var u,n,l=o.prototype;return l.onHandlerDestroying=function(){this._unregisterListeners(),this.mainDetails=null},l._registerListeners=function(){var p=this.hls;p.on(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),p.on(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this),p.on(R.Events.MANIFEST_LOADING,this.onManifestLoading,this),p.on(R.Events.LEVEL_LOADED,this.onLevelLoaded,this),p.on(R.Events.ERROR,this.onError,this),p.on(R.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),p.on(R.Events.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),p.on(R.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),p.on(R.Events.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),p.on(R.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)},l._unregisterListeners=function(){var p=this.hls;p.off(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),p.off(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this),p.off(R.Events.MANIFEST_LOADING,this.onManifestLoading,this),p.off(R.Events.LEVEL_LOADED,this.onLevelLoaded,this),p.off(R.Events.ERROR,this.onError,this),p.off(R.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),p.off(R.Events.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),p.off(R.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),p.off(R.Events.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),p.off(R.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)},l.startLoad=function(){this.stopLoad(),this.state=b.State.IDLE,this.setInterval(500),this.tick()},l.onManifestLoading=function(){this.mainDetails=null,this.fragmentTracker.removeAllFragments()},l.onLevelLoaded=function(p,f){this.mainDetails=f.details},l.onSubtitleFragProcessed=function(p,f){var v=f.frag,r=f.success;if(this.fragPrevious=v,this.state=b.State.IDLE,r){var i=this.tracksBuffered[this.currentTrackId];if(i){for(var c,S=v.start,L=0;L<i.length;L++)if(S>=i[L].start&&S<=i[L].end){c=i[L];break}var _=v.start+v.duration;c?c.end=_:(c={start:S,end:_},i.push(c)),this.fragmentTracker.fragBuffered(v)}}},l.onBufferFlushing=function(p,f){var v=f.startOffset,r=f.endOffset;if(v===0&&r!==Number.POSITIVE_INFINITY){var i=this.currentTrackId,c=this.levels;if(!c.length||!c[i]||!c[i].details)return;var S=r-c[i].details.targetduration;if(S<=0)return;f.endOffsetSubtitles=Math.max(0,S),this.tracksBuffered.forEach(function(L){for(var _=0;_<L.length;)if(L[_].end<=S)L.shift();else{if(!(L[_].start<S))break;L[_].start=S,_++}}),this.fragmentTracker.removeFragmentsInRange(v,S,m.PlaylistLevelType.SUBTITLE)}},l.onError=function(p,f){var v,r=f.frag;r&&r.type===m.PlaylistLevelType.SUBTITLE&&((v=this.fragCurrent)!==null&&v!==void 0&&v.loader&&this.fragCurrent.loader.abort(),this.state=b.State.IDLE)},l.onSubtitleTracksUpdated=function(p,f){var v=this,r=f.subtitleTracks;this.tracksBuffered=[],this.levels=r.map(function(i){return new h.Level(i)}),this.fragmentTracker.removeAllFragments(),this.fragPrevious=null,this.levels.forEach(function(i){v.tracksBuffered[i.id]=[]}),this.mediaBuffer=null},l.onSubtitleTrackSwitch=function(p,f){if(this.currentTrackId=f.id,this.levels.length&&this.currentTrackId!==-1){var v=this.levels[this.currentTrackId];v!=null&&v.details?(this.mediaBuffer=this.mediaBufferTimeRanges,this.setInterval(500)):this.mediaBuffer=null}else this.clearInterval()},l.onSubtitleTrackLoaded=function(p,f){var v,r=f.details,i=f.id,c=this.currentTrackId,S=this.levels;if(S.length){var L=S[c];if(!(i>=S.length||i!==c)&&L){if(this.mediaBuffer=this.mediaBufferTimeRanges,r.live||(v=L.details)!==null&&v!==void 0&&v.live){var _=this.mainDetails;if(r.deltaUpdateFailed||!_)return;var x=_.fragments[0];L.details?this.alignPlaylists(r,L.details)===0&&x&&Object(D.addSliding)(r,x.start):r.hasProgramDateTime&&_.hasProgramDateTime?Object(C.alignPDT)(r,_):x&&Object(D.addSliding)(r,x.start)}L.details=r,this.levelLastLoaded=i,this.tick(),r.live&&!this.fragCurrent&&this.media&&this.state===b.State.IDLE&&(Object(k.findFragmentByPTS)(null,r.fragments,this.media.currentTime,0)||(this.warn("Subtitle playlist not aligned with playback"),L.details=void 0))}}},l._handleFragmentLoadComplete=function(p){var f=p.frag,v=p.payload,r=f.decryptdata,i=this.hls;if(!this.fragContextChanged(f)&&v&&v.byteLength>0&&r&&r.key&&r.iv&&r.method==="AES-128"){var c=performance.now();this.decrypter.webCryptoDecrypt(new Uint8Array(v),r.key.buffer,r.iv.buffer).then(function(S){var L=performance.now();i.trigger(R.Events.FRAG_DECRYPTED,{frag:f,payload:S,stats:{tstart:c,tdecrypt:L}})})}},l.doTick=function(){if(this.media){if(this.state===b.State.IDLE){var p,f=this.currentTrackId,v=this.levels;if(!v.length||!v[f]||!v[f].details)return;var r=v[f].details,i=r.targetduration,c=this.config,S=this.media,L=A.BufferHelper.bufferedInfo(this.mediaBufferTimeRanges,S.currentTime-i,c.maxBufferHole),_=L.end;if(L.len>this.getMaxBufferLength()+i)return;console.assert(r,"Subtitle track details are defined on idle subtitle stream controller tick");var x,w=r.fragments,O=w.length,P=r.edge,F=this.fragPrevious;if(_<P){var N=c.maxFragLookUpTolerance;F&&r.hasProgramDateTime&&(x=Object(k.findFragmentByPDT)(w,F.endProgramDateTime,N)),x||!(x=Object(k.findFragmentByPTS)(F,w,_,N))&&F&&F.start<w[0].start&&(x=w[0])}else x=w[O-1];(p=x)!==null&&p!==void 0&&p.encrypted?(E.logger.log("Loading key for "+x.sn),this.state=b.State.KEY_LOADING,this.hls.trigger(R.Events.KEY_LOADING,{frag:x})):x&&this.fragmentTracker.getState(x)===s.FragmentState.NOT_LOADED&&this.loadFragment(x,r,_)}}else this.state=b.State.IDLE},l.loadFragment=function(p,f,v){this.fragCurrent=p,e.prototype.loadFragment.call(this,p,f,v)},u=o,(n=[{key:"mediaBufferTimeRanges",get:function(){return this.tracksBuffered[this.currentTrackId]||[]}}])&&T(u.prototype,n),o}(b.default)},"./src/controller/subtitle-track-controller.ts":function(M,I,g){g.r(I);var R=g("./src/events.ts"),E=g("./src/utils/texttrack-utils.ts"),A=g("./src/controller/base-playlist-controller.ts"),k=g("./src/types/loader.ts");function C(m,h){for(var T=0;T<h.length;T++){var y=h[T];y.enumerable=y.enumerable||!1,y.configurable=!0,"value"in y&&(y.writable=!0),Object.defineProperty(m,y.key,y)}}function D(m,h){return(D=Object.setPrototypeOf||function(T,y){return T.__proto__=y,T})(m,h)}var s=function(m){var h,T;function y(t){var o;return(o=m.call(this,t,"[subtitle-track-controller]")||this).media=null,o.tracks=[],o.groupId=null,o.tracksInGroup=[],o.trackId=-1,o.selectDefaultTrack=!0,o.queuedDefaultTrack=-1,o.trackChangeListener=function(){return o.onTextTracksChanged()},o.asyncPollTrackChange=function(){return o.pollTrackChange(0)},o.useTextTrackPolling=!1,o.subtitlePollingInterval=-1,o.subtitleDisplay=!0,o.registerListeners(),o}T=m,(h=y).prototype=Object.create(T.prototype),h.prototype.constructor=h,D(h,T);var d,e,a=y.prototype;return a.destroy=function(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.trackChangeListener=this.asyncPollTrackChange=null,m.prototype.destroy.call(this)},a.registerListeners=function(){var t=this.hls;t.on(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(R.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(R.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.on(R.Events.LEVEL_LOADING,this.onLevelLoading,this),t.on(R.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),t.on(R.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.on(R.Events.ERROR,this.onError,this)},a.unregisterListeners=function(){var t=this.hls;t.off(R.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(R.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(R.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(R.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.off(R.Events.LEVEL_LOADING,this.onLevelLoading,this),t.off(R.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),t.off(R.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.off(R.Events.ERROR,this.onError,this)},a.onMediaAttached=function(t,o){this.media=o.media,this.media&&(this.queuedDefaultTrack>-1&&(this.subtitleTrack=this.queuedDefaultTrack,this.queuedDefaultTrack=-1),this.useTextTrackPolling=!(this.media.textTracks&&"onchange"in this.media.textTracks),this.useTextTrackPolling?this.pollTrackChange(500):this.media.textTracks.addEventListener("change",this.asyncPollTrackChange))},a.pollTrackChange=function(t){self.clearInterval(this.subtitlePollingInterval),this.subtitlePollingInterval=self.setInterval(this.trackChangeListener,t)},a.onMediaDetaching=function(){this.media&&(self.clearInterval(this.subtitlePollingInterval),this.useTextTrackPolling||this.media.textTracks.removeEventListener("change",this.asyncPollTrackChange),this.trackId>-1&&(this.queuedDefaultTrack=this.trackId),b(this.media.textTracks).forEach(function(t){Object(E.clearCurrentCues)(t)}),this.subtitleTrack=-1,this.media=null)},a.onManifestLoading=function(){this.tracks=[],this.groupId=null,this.tracksInGroup=[],this.trackId=-1,this.selectDefaultTrack=!0},a.onManifestParsed=function(t,o){this.tracks=o.subtitleTracks},a.onSubtitleTrackLoaded=function(t,o){var u=o.id,n=o.details,l=this.trackId,p=this.tracksInGroup[l];if(p){var f=p.details;p.details=o.details,this.log("subtitle track "+u+" loaded ["+n.startSN+"-"+n.endSN+"]"),u===this.trackId&&(this.retryCount=0,this.playlistLoaded(u,o,f))}else this.warn("Invalid subtitle track id "+u)},a.onLevelLoading=function(t,o){this.switchLevel(o.level)},a.onLevelSwitching=function(t,o){this.switchLevel(o.level)},a.switchLevel=function(t){var o=this.hls.levels[t];if(o!=null&&o.textGroupIds){var u=o.textGroupIds[o.urlId];if(this.groupId!==u){var n=this.tracksInGroup?this.tracksInGroup[this.trackId]:void 0,l=this.tracks.filter(function(v){return!u||v.groupId===u});this.tracksInGroup=l;var p=this.findTrackId(n==null?void 0:n.name)||this.findTrackId();this.groupId=u;var f={subtitleTracks:l};this.log("Updating subtitle tracks, "+l.length+' track(s) found in "'+u+'" group-id'),this.hls.trigger(R.Events.SUBTITLE_TRACKS_UPDATED,f),p!==-1&&this.setSubtitleTrack(p,n)}}},a.findTrackId=function(t){for(var o=this.tracksInGroup,u=0;u<o.length;u++){var n=o[u];if((!this.selectDefaultTrack||n.default)&&(!t||t===n.name))return n.id}return-1},a.onError=function(t,o){m.prototype.onError.call(this,t,o),!o.fatal&&o.context&&o.context.type===k.PlaylistContextType.SUBTITLE_TRACK&&o.context.id===this.trackId&&o.context.groupId===this.groupId&&this.retryLoadingOrFail(o)},a.loadPlaylist=function(t){var o=this.tracksInGroup[this.trackId];if(this.shouldLoadTrack(o)){var u=o.id,n=o.groupId,l=o.url;if(t)try{l=t.addDirectives(l)}catch(p){this.warn("Could not construct new URL with HLS Delivery Directives: "+p)}this.log("Loading subtitle playlist for id "+u),this.hls.trigger(R.Events.SUBTITLE_TRACK_LOADING,{url:l,id:u,groupId:n,deliveryDirectives:t||null})}},a.toggleTrackModes=function(t){var o=this,u=this.media,n=this.subtitleDisplay,l=this.trackId;if(u){var p=b(u.textTracks),f=p.filter(function(i){return i.groupId===o.groupId});if(t===-1)[].slice.call(p).forEach(function(i){i.mode="disabled"});else{var v=f[l];v&&(v.mode="disabled")}var r=f[t];r&&(r.mode=n?"showing":"hidden")}},a.setSubtitleTrack=function(t,o){var u,n=this.tracksInGroup;if(this.media){if(this.trackId!==t&&this.toggleTrackModes(t),!(this.trackId===t&&(t===-1||(u=n[t])!==null&&u!==void 0&&u.details)||t<-1||t>=n.length)){this.clearTimer();var l=n[t];if(this.log("Switching to subtitle track "+t),this.trackId=t,l){var p=l.id,f=l.groupId,v=f===void 0?"":f,r=l.name,i=l.type,c=l.url;this.hls.trigger(R.Events.SUBTITLE_TRACK_SWITCH,{id:p,groupId:v,name:r,type:i,url:c});var S=this.switchParams(l.url,o==null?void 0:o.details);this.loadPlaylist(S)}else this.hls.trigger(R.Events.SUBTITLE_TRACK_SWITCH,{id:t})}}else this.queuedDefaultTrack=t},a.onTextTracksChanged=function(){if(this.useTextTrackPolling||self.clearInterval(this.subtitlePollingInterval),this.media&&this.hls.config.renderTextTracksNatively){for(var t=-1,o=b(this.media.textTracks),u=0;u<o.length;u++)if(o[u].mode==="hidden")t=u;else if(o[u].mode==="showing"){t=u;break}this.subtitleTrack!==t&&(this.subtitleTrack=t)}},d=y,(e=[{key:"subtitleTracks",get:function(){return this.tracksInGroup}},{key:"subtitleTrack",get:function(){return this.trackId},set:function(t){this.selectDefaultTrack=!1;var o=this.tracksInGroup?this.tracksInGroup[this.trackId]:void 0;this.setSubtitleTrack(t,o)}}])&&C(d.prototype,e),y}(A.default);function b(m){for(var h=[],T=0;T<m.length;T++){var y=m[T];y.kind==="subtitles"&&y.label&&h.push(m[T])}return h}I.default=s},"./src/controller/timeline-controller.ts":function(M,I,g){g.r(I),g.d(I,"TimelineController",function(){return h});var R=g("./src/polyfills/number.ts"),E=g("./src/events.ts"),A=g("./src/utils/cea-608-parser.ts"),k=g("./src/utils/output-filter.ts"),C=g("./src/utils/webvtt-parser.ts"),D=g("./src/utils/texttrack-utils.ts"),s=g("./src/utils/imsc1-ttml-parser.ts"),b=g("./src/types/loader.ts"),m=g("./src/utils/logger.ts"),h=function(){function y(e){if(this.hls=void 0,this.media=null,this.config=void 0,this.enabled=!0,this.Cues=void 0,this.textTracks=[],this.tracks=[],this.initPTS=[],this.timescale=[],this.unparsedVttFrags=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.cea608Parser1=void 0,this.cea608Parser2=void 0,this.lastSn=-1,this.prevCC=-1,this.vttCCs={ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!1}},this.captionsProperties=void 0,this.hls=e,this.config=e.config,this.Cues=e.config.cueHandler,this.captionsProperties={textTrack1:{label:this.config.captionsTextTrack1Label,languageCode:this.config.captionsTextTrack1LanguageCode},textTrack2:{label:this.config.captionsTextTrack2Label,languageCode:this.config.captionsTextTrack2LanguageCode},textTrack3:{label:this.config.captionsTextTrack3Label,languageCode:this.config.captionsTextTrack3LanguageCode},textTrack4:{label:this.config.captionsTextTrack4Label,languageCode:this.config.captionsTextTrack4LanguageCode}},this.config.enableCEA708Captions){var a=new k.default(this,"textTrack1"),t=new k.default(this,"textTrack2"),o=new k.default(this,"textTrack3"),u=new k.default(this,"textTrack4");this.cea608Parser1=new A.default(1,a,t),this.cea608Parser2=new A.default(3,o,u)}e.on(E.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(E.Events.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(E.Events.MANIFEST_LOADING,this.onManifestLoading,this),e.on(E.Events.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(E.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.on(E.Events.FRAG_LOADING,this.onFragLoading,this),e.on(E.Events.FRAG_LOADED,this.onFragLoaded,this),e.on(E.Events.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),e.on(E.Events.FRAG_DECRYPTED,this.onFragDecrypted,this),e.on(E.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),e.on(E.Events.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),e.on(E.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)}var d=y.prototype;return d.destroy=function(){var e=this.hls;e.off(E.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(E.Events.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(E.Events.MANIFEST_LOADING,this.onManifestLoading,this),e.off(E.Events.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(E.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.off(E.Events.FRAG_LOADING,this.onFragLoading,this),e.off(E.Events.FRAG_LOADED,this.onFragLoaded,this),e.off(E.Events.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),e.off(E.Events.FRAG_DECRYPTED,this.onFragDecrypted,this),e.off(E.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),e.off(E.Events.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),e.off(E.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),this.hls=this.config=this.cea608Parser1=this.cea608Parser2=null},d.addCues=function(e,a,t,o,u){for(var n,l,p,f,v=!1,r=u.length;r--;){var i=u[r],c=(n=i[0],l=i[1],p=a,f=t,Math.min(l,f)-Math.max(n,p));if(c>=0&&(i[0]=Math.min(i[0],a),i[1]=Math.max(i[1],t),v=!0,c/(t-a)>.5))return}if(v||u.push([a,t]),this.config.renderTextTracksNatively){var S=this.captionsTracks[e];this.Cues.newCue(S,a,t,o)}else{var L=this.Cues.newCue(null,a,t,o);this.hls.trigger(E.Events.CUES_PARSED,{type:"captions",cues:L,track:e})}},d.onInitPtsFound=function(e,a){var t=this,o=a.frag,u=a.id,n=a.initPTS,l=a.timescale,p=this.unparsedVttFrags;u==="main"&&(this.initPTS[o.cc]=n,this.timescale[o.cc]=l),p.length&&(this.unparsedVttFrags=[],p.forEach(function(f){t.onFragLoaded(E.Events.FRAG_LOADED,f)}))},d.getExistingTrack=function(e){var a=this.media;if(a)for(var t=0;t<a.textTracks.length;t++){var o=a.textTracks[t];if(o[e])return o}return null},d.createCaptionsTrack=function(e){this.config.renderTextTracksNatively?this.createNativeTrack(e):this.createNonNativeTrack(e)},d.createNativeTrack=function(e){if(!this.captionsTracks[e]){var a=this.captionsProperties,t=this.captionsTracks,o=this.media,u=a[e],n=u.label,l=u.languageCode,p=this.getExistingTrack(e);if(p)t[e]=p,Object(D.clearCurrentCues)(t[e]),Object(D.sendAddTrackEvent)(t[e],o);else{var f=this.createTextTrack("captions",n,l);f&&(f[e]=!0,t[e]=f)}}},d.createNonNativeTrack=function(e){if(!this.nonNativeCaptionsTracks[e]){var a=this.captionsProperties[e];if(a){var t={_id:e,label:a.label,kind:"captions",default:!!a.media&&!!a.media.default,closedCaptions:a.media};this.nonNativeCaptionsTracks[e]=t,this.hls.trigger(E.Events.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:[t]})}}},d.createTextTrack=function(e,a,t){var o=this.media;if(o)return o.addTextTrack(e,a,t)},d.onMediaAttaching=function(e,a){this.media=a.media,this._cleanTracks()},d.onMediaDetaching=function(){var e=this.captionsTracks;Object.keys(e).forEach(function(a){Object(D.clearCurrentCues)(e[a]),delete e[a]}),this.nonNativeCaptionsTracks={}},d.onManifestLoading=function(){this.lastSn=-1,this.prevCC=-1,this.vttCCs={ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!1}},this._cleanTracks(),this.tracks=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.textTracks=[],this.unparsedVttFrags=this.unparsedVttFrags||[],this.initPTS=[],this.timescale=[],this.cea608Parser1&&this.cea608Parser2&&(this.cea608Parser1.reset(),this.cea608Parser2.reset())},d._cleanTracks=function(){var e=this.media;if(e){var a=e.textTracks;if(a)for(var t=0;t<a.length;t++)Object(D.clearCurrentCues)(a[t])}},d.onSubtitleTracksUpdated=function(e,a){var t=this;this.textTracks=[];var o=a.subtitleTracks||[],u=o.some(function(f){return f.textCodec===s.IMSC1_CODEC});if(this.config.enableWebVTT||u&&this.config.enableIMSC1){var n=this.tracks&&o&&this.tracks.length===o.length;if(this.tracks=o||[],this.config.renderTextTracksNatively){var l=this.media?this.media.textTracks:[];this.tracks.forEach(function(f,v){var r;if(v<l.length){for(var i=null,c=0;c<l.length;c++)if(T(l[c],f)){i=l[c];break}i&&(r=i)}r?Object(D.clearCurrentCues)(r):(r=t.createTextTrack("subtitles",f.name,f.lang))&&(r.mode="disabled"),r&&(r.groupId=f.groupId,t.textTracks.push(r))})}else if(!n&&this.tracks&&this.tracks.length){var p=this.tracks.map(function(f){return{label:f.name,kind:f.type.toLowerCase(),default:f.default,subtitleTrack:f}});this.hls.trigger(E.Events.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:p})}}},d.onManifestLoaded=function(e,a){var t=this;this.config.enableCEA708Captions&&a.captions&&a.captions.forEach(function(o){var u=/(?:CC|SERVICE)([1-4])/.exec(o.instreamId);if(u){var n="textTrack"+u[1],l=t.captionsProperties[n];l&&(l.label=o.name,o.lang&&(l.languageCode=o.lang),l.media=o)}})},d.onFragLoading=function(e,a){var t=this.cea608Parser1,o=this.cea608Parser2,u=this.lastSn;if(this.enabled&&t&&o&&a.frag.type===b.PlaylistLevelType.MAIN){var n=a.frag.sn;n!==u+1&&(t.reset(),o.reset()),this.lastSn=n}},d.onFragLoaded=function(e,a){var t=a.frag,o=a.payload,u=this.initPTS,n=this.unparsedVttFrags;if(t.type===b.PlaylistLevelType.SUBTITLE)if(o.byteLength){if(!Object(R.isFiniteNumber)(u[t.cc]))return n.push(a),void(u.length&&this.hls.trigger(E.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t,error:new Error("Missing initial subtitle PTS")}));var l=t.decryptdata;if(l==null||l.key==null||l.method!=="AES-128"){var p=this.tracks[t.level],f=this.vttCCs;f[t.cc]||(f[t.cc]={start:t.start,prevCC:this.prevCC,new:!0},this.prevCC=t.cc),p&&p.textCodec===s.IMSC1_CODEC?this._parseIMSC1(t,o):this._parseVTTs(t,o,f)}}else this.hls.trigger(E.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t,error:new Error("Empty subtitle payload")})},d._parseIMSC1=function(e,a){var t=this,o=this.hls;Object(s.parseIMSC1)(a,this.initPTS[e.cc],this.timescale[e.cc],function(u){t._appendCues(u,e.level),o.trigger(E.Events.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:e})},function(u){m.logger.log("Failed to parse IMSC1: "+u),o.trigger(E.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:e,error:u})})},d._parseVTTs=function(e,a,t){var o=this,u=this.hls;Object(C.parseWebVTT)(a,this.initPTS[e.cc],this.timescale[e.cc],t,e.cc,e.start,function(n){o._appendCues(n,e.level),u.trigger(E.Events.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:e})},function(n){o._fallbackToIMSC1(e,a),m.logger.log("Failed to parse VTT cue: "+n),u.trigger(E.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:e,error:n})})},d._fallbackToIMSC1=function(e,a){var t=this,o=this.tracks[e.level];o.textCodec||Object(s.parseIMSC1)(a,this.initPTS[e.cc],this.timescale[e.cc],function(){o.textCodec=s.IMSC1_CODEC,t._parseIMSC1(e,a)},function(){o.textCodec="wvtt"})},d._appendCues=function(e,a){var t=this.hls;if(this.config.renderTextTracksNatively){var o=this.textTracks[a];if(o.mode==="disabled")return;e.forEach(function(n){return Object(D.addCueToTrack)(o,n)})}else{var u=this.tracks[a].default?"default":"subtitles"+a;t.trigger(E.Events.CUES_PARSED,{type:"subtitles",cues:e,track:u})}},d.onFragDecrypted=function(e,a){var t=a.frag;if(t.type===b.PlaylistLevelType.SUBTITLE){if(!Object(R.isFiniteNumber)(this.initPTS[t.cc]))return void this.unparsedVttFrags.push(a);this.onFragLoaded(E.Events.FRAG_LOADED,a)}},d.onSubtitleTracksCleared=function(){this.tracks=[],this.captionsTracks={}},d.onFragParsingUserdata=function(e,a){var t=this.cea608Parser1,o=this.cea608Parser2;if(this.enabled&&t&&o)for(var u=0;u<a.samples.length;u++){var n=a.samples[u].bytes;if(n){var l=this.extractCea608Data(n);t.addData(a.samples[u].pts,l[0]),o.addData(a.samples[u].pts,l[1])}}},d.onBufferFlushing=function(e,a){var t=a.startOffset,o=a.endOffset,u=a.endOffsetSubtitles,n=a.type,l=this.media;if(l&&!(l.currentTime<o)){if(!n||n==="video"){var p=this.captionsTracks;Object.keys(p).forEach(function(v){return Object(D.removeCuesInRange)(p[v],t,o)})}if(this.config.renderTextTracksNatively&&t===0&&u!==void 0){var f=this.textTracks;Object.keys(f).forEach(function(v){return Object(D.removeCuesInRange)(f[v],t,u)})}}},d.extractCea608Data=function(e){for(var a=31&e[0],t=2,o=[[],[]],u=0;u<a;u++){var n=e[t++],l=127&e[t++],p=127&e[t++],f=3&n;l===0&&p===0||4&n&&(f!==0&&f!==1||(o[f].push(l),o[f].push(p)))}return o},y}();function T(y,d){return y&&y.label===d.name&&!(y.textTrack1||y.textTrack2)}},"./src/crypt/aes-crypto.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return R});var R=function(){function E(A,k){this.subtle=void 0,this.aesIV=void 0,this.subtle=A,this.aesIV=k}return E.prototype.decrypt=function(A,k){return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},k,A)},E}()},"./src/crypt/aes-decryptor.ts":function(M,I,g){g.r(I),g.d(I,"removePadding",function(){return E}),g.d(I,"default",function(){return A});var R=g("./src/utils/typed-array.ts");function E(k){var C=k.byteLength,D=C&&new DataView(k.buffer).getUint8(C-1);return D?Object(R.sliceUint8)(k,0,C-D):k}var A=function(){function k(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.ksRows=0,this.keySize=0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.initTable()}var C=k.prototype;return C.uint8ArrayToUint32Array_=function(D){for(var s=new DataView(D),b=new Uint32Array(4),m=0;m<4;m++)b[m]=s.getUint32(4*m);return b},C.initTable=function(){var D=this.sBox,s=this.invSBox,b=this.subMix,m=b[0],h=b[1],T=b[2],y=b[3],d=this.invSubMix,e=d[0],a=d[1],t=d[2],o=d[3],u=new Uint32Array(256),n=0,l=0,p=0;for(p=0;p<256;p++)u[p]=p<128?p<<1:p<<1^283;for(p=0;p<256;p++){var f=l^l<<1^l<<2^l<<3^l<<4;f=f>>>8^255&f^99,D[n]=f,s[f]=n;var v=u[n],r=u[v],i=u[r],c=257*u[f]^16843008*f;m[n]=c<<24|c>>>8,h[n]=c<<16|c>>>16,T[n]=c<<8|c>>>24,y[n]=c,c=16843009*i^65537*r^257*v^16843008*n,e[f]=c<<24|c>>>8,a[f]=c<<16|c>>>16,t[f]=c<<8|c>>>24,o[f]=c,n?(n=v^u[u[u[i^v]]],l^=u[u[l]]):n=l=1}},C.expandKey=function(D){for(var s=this.uint8ArrayToUint32Array_(D),b=!0,m=0;m<s.length&&b;)b=s[m]===this.key[m],m++;if(!b){this.key=s;var h=this.keySize=s.length;if(h!==4&&h!==6&&h!==8)throw new Error("Invalid aes key size="+h);var T,y,d,e,a=this.ksRows=4*(h+6+1),t=this.keySchedule=new Uint32Array(a),o=this.invKeySchedule=new Uint32Array(a),u=this.sBox,n=this.rcon,l=this.invSubMix,p=l[0],f=l[1],v=l[2],r=l[3];for(T=0;T<a;T++)T<h?d=t[T]=s[T]:(e=d,T%h==0?(e=u[(e=e<<8|e>>>24)>>>24]<<24|u[e>>>16&255]<<16|u[e>>>8&255]<<8|u[255&e],e^=n[T/h|0]<<24):h>6&&T%h==4&&(e=u[e>>>24]<<24|u[e>>>16&255]<<16|u[e>>>8&255]<<8|u[255&e]),t[T]=d=(t[T-h]^e)>>>0);for(y=0;y<a;y++)T=a-y,e=3&y?t[T]:t[T-4],o[y]=y<4||T<=4?e:p[u[e>>>24]]^f[u[e>>>16&255]]^v[u[e>>>8&255]]^r[u[255&e]],o[y]=o[y]>>>0}},C.networkToHostOrderSwap=function(D){return D<<24|(65280&D)<<8|(16711680&D)>>8|D>>>24},C.decrypt=function(D,s,b){for(var m,h,T,y,d,e,a,t,o,u,n,l,p,f,v=this.keySize+6,r=this.invKeySchedule,i=this.invSBox,c=this.invSubMix,S=c[0],L=c[1],_=c[2],x=c[3],w=this.uint8ArrayToUint32Array_(b),O=w[0],P=w[1],F=w[2],N=w[3],U=new Int32Array(D),G=new Int32Array(U.length),K=this.networkToHostOrderSwap;s<U.length;){for(o=K(U[s]),u=K(U[s+1]),n=K(U[s+2]),l=K(U[s+3]),d=o^r[0],e=l^r[1],a=n^r[2],t=u^r[3],p=4,f=1;f<v;f++)m=S[d>>>24]^L[e>>16&255]^_[a>>8&255]^x[255&t]^r[p],h=S[e>>>24]^L[a>>16&255]^_[t>>8&255]^x[255&d]^r[p+1],T=S[a>>>24]^L[t>>16&255]^_[d>>8&255]^x[255&e]^r[p+2],y=S[t>>>24]^L[d>>16&255]^_[e>>8&255]^x[255&a]^r[p+3],d=m,e=h,a=T,t=y,p+=4;m=i[d>>>24]<<24^i[e>>16&255]<<16^i[a>>8&255]<<8^i[255&t]^r[p],h=i[e>>>24]<<24^i[a>>16&255]<<16^i[t>>8&255]<<8^i[255&d]^r[p+1],T=i[a>>>24]<<24^i[t>>16&255]<<16^i[d>>8&255]<<8^i[255&e]^r[p+2],y=i[t>>>24]<<24^i[d>>16&255]<<16^i[e>>8&255]<<8^i[255&a]^r[p+3],G[s]=K(m^O),G[s+1]=K(y^P),G[s+2]=K(T^F),G[s+3]=K(h^N),O=o,P=u,F=n,N=l,s+=4}return G.buffer},k}()},"./src/crypt/decrypter.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return s});var R=g("./src/crypt/aes-crypto.ts"),E=g("./src/crypt/fast-aes-key.ts"),A=g("./src/crypt/aes-decryptor.ts"),k=g("./src/utils/logger.ts"),C=g("./src/utils/mp4-tools.ts"),D=g("./src/utils/typed-array.ts"),s=function(){function b(h,T,y){var d=(y===void 0?{}:y).removePKCS7Padding,e=d===void 0||d;if(this.logEnabled=!0,this.observer=void 0,this.config=void 0,this.removePKCS7Padding=void 0,this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null,this.observer=h,this.config=T,this.removePKCS7Padding=e,e)try{var a=self.crypto;a&&(this.subtle=a.subtle||a.webkitSubtle)}catch{}this.subtle===null&&(this.config.enableSoftwareAES=!0)}var m=b.prototype;return m.destroy=function(){this.observer=null},m.isSync=function(){return this.config.enableSoftwareAES},m.flush=function(){var h=this.currentResult;if(h){var T=new Uint8Array(h);return this.reset(),this.removePKCS7Padding?Object(A.removePadding)(T):T}this.reset()},m.reset=function(){this.currentResult=null,this.currentIV=null,this.remainderData=null,this.softwareDecrypter&&(this.softwareDecrypter=null)},m.decrypt=function(h,T,y,d){if(this.config.enableSoftwareAES){this.softwareDecrypt(new Uint8Array(h),T,y);var e=this.flush();e&&d(e.buffer)}else this.webCryptoDecrypt(new Uint8Array(h),T,y).then(d)},m.softwareDecrypt=function(h,T,y){var d=this.currentIV,e=this.currentResult,a=this.remainderData;this.logOnce("JS AES decrypt"),a&&(h=Object(C.appendUint8Array)(a,h),this.remainderData=null);var t=this.getValidChunk(h);if(!t.length)return null;d&&(y=d);var o=this.softwareDecrypter;o||(o=this.softwareDecrypter=new A.default),o.expandKey(T);var u=e;return this.currentResult=o.decrypt(t.buffer,0,y),this.currentIV=Object(D.sliceUint8)(t,-16).buffer,u||null},m.webCryptoDecrypt=function(h,T,y){var d=this,e=this.subtle;return this.key===T&&this.fastAesKey||(this.key=T,this.fastAesKey=new E.default(e,T)),this.fastAesKey.expandKey().then(function(a){return e?new R.default(e,y).decrypt(h.buffer,a):Promise.reject(new Error("web crypto not initialized"))}).catch(function(a){return d.onWebCryptoError(a,h,T,y)})},m.onWebCryptoError=function(h,T,y,d){return k.logger.warn("[decrypter.ts]: WebCrypto Error, disable WebCrypto API:",h),this.config.enableSoftwareAES=!0,this.logEnabled=!0,this.softwareDecrypt(T,y,d)},m.getValidChunk=function(h){var T=h,y=h.length-h.length%16;return y!==h.length&&(T=Object(D.sliceUint8)(h,0,y),this.remainderData=Object(D.sliceUint8)(h,y)),T},m.logOnce=function(h){this.logEnabled&&(k.logger.log("[decrypter.ts]: "+h),this.logEnabled=!1)},b}()},"./src/crypt/fast-aes-key.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return R});var R=function(){function E(A,k){this.subtle=void 0,this.key=void 0,this.subtle=A,this.key=k}return E.prototype.expandKey=function(){return this.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])},E}()},"./src/demux/aacdemuxer.ts":function(M,I,g){g.r(I);var R=g("./src/demux/base-audio-demuxer.ts"),E=g("./src/demux/adts.ts"),A=g("./src/utils/logger.ts"),k=g("./src/demux/id3.ts");function C(s,b){return(C=Object.setPrototypeOf||function(m,h){return m.__proto__=h,m})(s,b)}var D=function(s){var b,m;function h(y,d){var e;return(e=s.call(this)||this).observer=void 0,e.config=void 0,e.observer=y,e.config=d,e}m=s,(b=h).prototype=Object.create(m.prototype),b.prototype.constructor=b,C(b,m);var T=h.prototype;return T.resetInitSegment=function(y,d,e){s.prototype.resetInitSegment.call(this,y,d,e),this._audioTrack={container:"audio/adts",type:"audio",id:0,pid:-1,sequenceNumber:0,isAAC:!0,samples:[],manifestCodec:y,duration:e,inputTimeScale:9e4,dropped:0}},h.probe=function(y){if(!y)return!1;for(var d=(k.getID3Data(y,0)||[]).length,e=y.length;d<e;d++)if(E.probe(y,d))return A.logger.log("ADTS sync word found !"),!0;return!1},T.canParse=function(y,d){return E.canParse(y,d)},T.appendFrame=function(y,d,e){E.initTrackConfig(y,this.observer,d,e,y.manifestCodec);var a=E.appendFrame(y,d,e,this.initPTS,this.frameIndex);if(a&&a.missing===0)return a},h}(R.default);D.minProbeByteLength=9,I.default=D},"./src/demux/adts.ts":function(M,I,g){g.r(I),g.d(I,"getAudioConfig",function(){return k}),g.d(I,"isHeaderPattern",function(){return C}),g.d(I,"getHeaderLength",function(){return D}),g.d(I,"getFullFrameLength",function(){return s}),g.d(I,"canGetFrameLength",function(){return b}),g.d(I,"isHeader",function(){return m}),g.d(I,"canParse",function(){return h}),g.d(I,"probe",function(){return T}),g.d(I,"initTrackConfig",function(){return y}),g.d(I,"getFrameDuration",function(){return d}),g.d(I,"parseFrameHeader",function(){return e}),g.d(I,"appendFrame",function(){return a});var R=g("./src/utils/logger.ts"),E=g("./src/errors.ts"),A=g("./src/events.ts");function k(t,o,u,n){var l,p,f,v,r=navigator.userAgent.toLowerCase(),i=n,c=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];l=1+((192&o[u+2])>>>6);var S=(60&o[u+2])>>>2;if(!(S>c.length-1))return f=(1&o[u+2])<<2,f|=(192&o[u+3])>>>6,R.logger.log("manifest codec:"+n+", ADTS type:"+l+", samplingIndex:"+S),/firefox/i.test(r)?S>=6?(l=5,v=new Array(4),p=S-3):(l=2,v=new Array(2),p=S):r.indexOf("android")!==-1?(l=2,v=new Array(2),p=S):(l=5,v=new Array(4),n&&(n.indexOf("mp4a.40.29")!==-1||n.indexOf("mp4a.40.5")!==-1)||!n&&S>=6?p=S-3:((n&&n.indexOf("mp4a.40.2")!==-1&&(S>=6&&f===1||/vivaldi/i.test(r))||!n&&f===1)&&(l=2,v=new Array(2)),p=S)),v[0]=l<<3,v[0]|=(14&S)>>1,v[1]|=(1&S)<<7,v[1]|=f<<3,l===5&&(v[1]|=(14&p)>>1,v[2]=(1&p)<<7,v[2]|=8,v[3]=0),{config:v,samplerate:c[S],channelCount:f,codec:"mp4a.40."+l,manifestCodec:i};t.trigger(A.Events.ERROR,{type:E.ErrorTypes.MEDIA_ERROR,details:E.ErrorDetails.FRAG_PARSING_ERROR,fatal:!0,reason:"invalid ADTS sampling index:"+S})}function C(t,o){return t[o]===255&&(246&t[o+1])==240}function D(t,o){return 1&t[o+1]?7:9}function s(t,o){return(3&t[o+3])<<11|t[o+4]<<3|(224&t[o+5])>>>5}function b(t,o){return o+5<t.length}function m(t,o){return o+1<t.length&&C(t,o)}function h(t,o){return b(t,o)&&C(t,o)&&s(t,o)<=t.length-o}function T(t,o){if(m(t,o)){var u=D(t,o);if(o+u>=t.length)return!1;var n=s(t,o);if(n<=u)return!1;var l=o+n;return l===t.length||m(t,l)}return!1}function y(t,o,u,n,l){if(!t.samplerate){var p=k(o,u,n,l);if(!p)return;t.config=p.config,t.samplerate=p.samplerate,t.channelCount=p.channelCount,t.codec=p.codec,t.manifestCodec=p.manifestCodec,R.logger.log("parsed codec:"+t.codec+", rate:"+p.samplerate+", channels:"+p.channelCount)}}function d(t){return 9216e4/t}function e(t,o,u,n,l){var p=D(t,o),f=s(t,o);if((f-=p)>0)return{headerLength:p,frameLength:f,stamp:u+n*l}}function a(t,o,u,n,l){var p=e(o,u,n,l,d(t.samplerate));if(p){var f,v=p.frameLength,r=p.headerLength,i=p.stamp,c=r+v,S=Math.max(0,u+c-o.length);S?(f=new Uint8Array(c-r)).set(o.subarray(u+r,o.length),0):f=o.subarray(u+r,u+c);var L={unit:f,pts:i};return S||t.samples.push(L),{sample:L,length:c,missing:S}}}},"./src/demux/base-audio-demuxer.ts":function(M,I,g){g.r(I),g.d(I,"initPTSFn",function(){return s});var R=g("./src/polyfills/number.ts"),E=g("./src/demux/id3.ts"),A=g("./src/demux/dummy-demuxed-track.ts"),k=g("./src/utils/mp4-tools.ts"),C=g("./src/utils/typed-array.ts"),D=function(){function b(){this._audioTrack=void 0,this._id3Track=void 0,this.frameIndex=0,this.cachedData=null,this.initPTS=null}var m=b.prototype;return m.resetInitSegment=function(h,T,y){this._id3Track={type:"id3",id:0,pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0}},m.resetTimeStamp=function(){},m.resetContiguity=function(){},m.canParse=function(h,T){return!1},m.appendFrame=function(h,T,y){},m.demux=function(h,T){this.cachedData&&(h=Object(k.appendUint8Array)(this.cachedData,h),this.cachedData=null);var y,d,e=E.getID3Data(h,0),a=e?e.length:0,t=this._audioTrack,o=this._id3Track,u=e?E.getTimeStamp(e):void 0,n=h.length;for(this.frameIndex!==0&&this.initPTS!==null||(this.initPTS=s(u,T)),e&&e.length>0&&o.samples.push({pts:this.initPTS,dts:this.initPTS,data:e}),d=this.initPTS;a<n;){if(this.canParse(h,a)){var l=this.appendFrame(t,h,a);l?(this.frameIndex++,d=l.sample.pts,y=a+=l.length):a=n}else E.canParse(h,a)?(e=E.getID3Data(h,a),o.samples.push({pts:d,dts:d,data:e}),y=a+=e.length):a++;if(a===n&&y!==n){var p=Object(C.sliceUint8)(h,y);this.cachedData?this.cachedData=Object(k.appendUint8Array)(this.cachedData,p):this.cachedData=p}}return{audioTrack:t,avcTrack:Object(A.dummyTrack)(),id3Track:o,textTrack:Object(A.dummyTrack)()}},m.demuxSampleAes=function(h,T,y){return Promise.reject(new Error("["+this+"] This demuxer does not support Sample-AES decryption"))},m.flush=function(h){var T=this.cachedData;return T&&(this.cachedData=null,this.demux(T,0)),this.frameIndex=0,{audioTrack:this._audioTrack,avcTrack:Object(A.dummyTrack)(),id3Track:this._id3Track,textTrack:Object(A.dummyTrack)()}},m.destroy=function(){},b}(),s=function(b,m){return Object(R.isFiniteNumber)(b)?90*b:9e4*m};I.default=D},"./src/demux/chunk-cache.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return R});var R=function(){function E(){this.chunks=[],this.dataLength=0}var A=E.prototype;return A.push=function(k){this.chunks.push(k),this.dataLength+=k.length},A.flush=function(){var k,C=this.chunks,D=this.dataLength;return C.length?(k=C.length===1?C[0]:function(s,b){for(var m=new Uint8Array(b),h=0,T=0;T<s.length;T++){var y=s[T];m.set(y,h),h+=y.length}return m}(C,D),this.reset(),k):new Uint8Array(0)},A.reset=function(){this.chunks.length=0,this.dataLength=0},E}()},"./src/demux/dummy-demuxed-track.ts":function(M,I,g){function R(){return{type:"",id:-1,pid:-1,inputTimeScale:9e4,sequenceNumber:-1,samples:[],dropped:0}}g.r(I),g.d(I,"dummyTrack",function(){return R})},"./src/demux/exp-golomb.ts":function(M,I,g){g.r(I);var R=g("./src/utils/logger.ts"),E=function(){function A(C){this.data=void 0,this.bytesAvailable=void 0,this.word=void 0,this.bitsAvailable=void 0,this.data=C,this.bytesAvailable=C.byteLength,this.word=0,this.bitsAvailable=0}var k=A.prototype;return k.loadWord=function(){var C=this.data,D=this.bytesAvailable,s=C.byteLength-D,b=new Uint8Array(4),m=Math.min(4,D);if(m===0)throw new Error("no bytes available");b.set(C.subarray(s,s+m)),this.word=new DataView(b.buffer).getUint32(0),this.bitsAvailable=8*m,this.bytesAvailable-=m},k.skipBits=function(C){var D;this.bitsAvailable>C?(this.word<<=C,this.bitsAvailable-=C):(C-=this.bitsAvailable,C-=(D=C>>3)>>3,this.bytesAvailable-=D,this.loadWord(),this.word<<=C,this.bitsAvailable-=C)},k.readBits=function(C){var D=Math.min(this.bitsAvailable,C),s=this.word>>>32-D;return C>32&&R.logger.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=D,this.bitsAvailable>0?this.word<<=D:this.bytesAvailable>0&&this.loadWord(),(D=C-D)>0&&this.bitsAvailable?s<<D|this.readBits(D):s},k.skipLZ=function(){var C;for(C=0;C<this.bitsAvailable;++C)if(this.word&2147483648>>>C)return this.word<<=C,this.bitsAvailable-=C,C;return this.loadWord(),C+this.skipLZ()},k.skipUEG=function(){this.skipBits(1+this.skipLZ())},k.skipEG=function(){this.skipBits(1+this.skipLZ())},k.readUEG=function(){var C=this.skipLZ();return this.readBits(C+1)-1},k.readEG=function(){var C=this.readUEG();return 1&C?1+C>>>1:-1*(C>>>1)},k.readBoolean=function(){return this.readBits(1)===1},k.readUByte=function(){return this.readBits(8)},k.readUShort=function(){return this.readBits(16)},k.readUInt=function(){return this.readBits(32)},k.skipScalingList=function(C){for(var D=8,s=8,b=0;b<C;b++)s!==0&&(s=(D+this.readEG()+256)%256),D=s===0?D:s},k.readSPS=function(){var C,D,s,b=0,m=0,h=0,T=0,y=this.readUByte.bind(this),d=this.readBits.bind(this),e=this.readUEG.bind(this),a=this.readBoolean.bind(this),t=this.skipBits.bind(this),o=this.skipEG.bind(this),u=this.skipUEG.bind(this),n=this.skipScalingList.bind(this);y();var l=y();if(d(5),t(3),y(),u(),l===100||l===110||l===122||l===244||l===44||l===83||l===86||l===118||l===128){var p=e();if(p===3&&t(1),u(),u(),t(1),a())for(D=p!==3?8:12,s=0;s<D;s++)a()&&n(s<6?16:64)}u();var f=e();if(f===0)e();else if(f===1)for(t(1),o(),o(),C=e(),s=0;s<C;s++)o();u(),t(1);var v=e(),r=e(),i=d(1);i===0&&t(1),t(1),a()&&(b=e(),m=e(),h=e(),T=e());var c=[1,1];if(a()&&a())switch(y()){case 1:c=[1,1];break;case 2:c=[12,11];break;case 3:c=[10,11];break;case 4:c=[16,11];break;case 5:c=[40,33];break;case 6:c=[24,11];break;case 7:c=[20,11];break;case 8:c=[32,11];break;case 9:c=[80,33];break;case 10:c=[18,11];break;case 11:c=[15,11];break;case 12:c=[64,33];break;case 13:c=[160,99];break;case 14:c=[4,3];break;case 15:c=[3,2];break;case 16:c=[2,1];break;case 255:c=[y()<<8|y(),y()<<8|y()]}return{width:Math.ceil(16*(v+1)-2*b-2*m),height:(2-i)*(r+1)*16-(i?2:4)*(h+T),pixelRatio:c}},k.readSliceType=function(){return this.readUByte(),this.readUEG(),this.readUEG()},A}();I.default=E},"./src/demux/id3.ts":function(M,I,g){g.r(I),g.d(I,"isHeader",function(){return E}),g.d(I,"isFooter",function(){return A}),g.d(I,"getID3Data",function(){return k}),g.d(I,"canParse",function(){return D}),g.d(I,"getTimeStamp",function(){return s}),g.d(I,"isTimeStampFrame",function(){return b}),g.d(I,"getID3Frames",function(){return h}),g.d(I,"decodeFrame",function(){return T}),g.d(I,"utf8ArrayToStr",function(){return t}),g.d(I,"testables",function(){return o});var R,E=function(u,n){return n+10<=u.length&&u[n]===73&&u[n+1]===68&&u[n+2]===51&&u[n+3]<255&&u[n+4]<255&&u[n+6]<128&&u[n+7]<128&&u[n+8]<128&&u[n+9]<128},A=function(u,n){return n+10<=u.length&&u[n]===51&&u[n+1]===68&&u[n+2]===73&&u[n+3]<255&&u[n+4]<255&&u[n+6]<128&&u[n+7]<128&&u[n+8]<128&&u[n+9]<128},k=function(u,n){for(var l=n,p=0;E(u,n);)p+=10,p+=C(u,n+6),A(u,n+10)&&(p+=10),n+=p;if(p>0)return u.subarray(l,l+p)},C=function(u,n){var l=0;return l=(127&u[n])<<21,l|=(127&u[n+1])<<14,l|=(127&u[n+2])<<7,l|=127&u[n+3]},D=function(u,n){return E(u,n)&&C(u,n+6)+10<=u.length-n},s=function(u){for(var n=h(u),l=0;l<n.length;l++){var p=n[l];if(b(p))return a(p)}},b=function(u){return u&&u.key==="PRIV"&&u.info==="com.apple.streaming.transportStreamTimestamp"},m=function(u){var n=String.fromCharCode(u[0],u[1],u[2],u[3]),l=C(u,4);return{type:n,size:l,data:u.subarray(10,10+l)}},h=function(u){for(var n=0,l=[];E(u,n);){for(var p=C(u,n+6),f=(n+=10)+p;n+8<f;){var v=m(u.subarray(n)),r=T(v);r&&l.push(r),n+=v.size+10}A(u,n)&&(n+=10)}return l},T=function(u){return u.type==="PRIV"?y(u):u.type[0]==="W"?e(u):d(u)},y=function(u){if(!(u.size<2)){var n=t(u.data,!0),l=new Uint8Array(u.data.subarray(n.length+1));return{key:u.type,info:n,data:l.buffer}}},d=function(u){if(!(u.size<2)){if(u.type==="TXXX"){var n=1,l=t(u.data.subarray(n),!0);n+=l.length+1;var p=t(u.data.subarray(n));return{key:u.type,info:l,data:p}}var f=t(u.data.subarray(1));return{key:u.type,data:f}}},e=function(u){if(u.type==="WXXX"){if(u.size<2)return;var n=1,l=t(u.data.subarray(n),!0);n+=l.length+1;var p=t(u.data.subarray(n));return{key:u.type,info:l,data:p}}var f=t(u.data);return{key:u.type,data:f}},a=function(u){if(u.data.byteLength===8){var n=new Uint8Array(u.data),l=1&n[3],p=(n[4]<<23)+(n[5]<<15)+(n[6]<<7)+n[7];return p/=45,l&&(p+=4772185884e-2),Math.round(p)}},t=function(u,n){n===void 0&&(n=!1);var l=(R||self.TextDecoder===void 0||(R=new self.TextDecoder("utf-8")),R);if(l){var p=l.decode(u);if(n){var f=p.indexOf("\0");return f!==-1?p.substring(0,f):p}return p.replace(/\0/g,"")}for(var v,r,i,c=u.length,S="",L=0;L<c;){if((v=u[L++])===0&&n)return S;if(v!==0&&v!==3)switch(v>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:S+=String.fromCharCode(v);break;case 12:case 13:r=u[L++],S+=String.fromCharCode((31&v)<<6|63&r);break;case 14:r=u[L++],i=u[L++],S+=String.fromCharCode((15&v)<<12|(63&r)<<6|(63&i)<<0)}}return S},o={decodeTextFrame:d}},"./src/demux/mp3demuxer.ts":function(M,I,g){g.r(I);var R=g("./src/demux/base-audio-demuxer.ts"),E=g("./src/demux/id3.ts"),A=g("./src/utils/logger.ts"),k=g("./src/demux/mpegaudio.ts");function C(s,b){return(C=Object.setPrototypeOf||function(m,h){return m.__proto__=h,m})(s,b)}var D=function(s){var b,m;function h(){return s.apply(this,arguments)||this}m=s,(b=h).prototype=Object.create(m.prototype),b.prototype.constructor=b,C(b,m);var T=h.prototype;return T.resetInitSegment=function(y,d,e){s.prototype.resetInitSegment.call(this,y,d,e),this._audioTrack={container:"audio/mpeg",type:"audio",id:0,pid:-1,sequenceNumber:0,isAAC:!1,samples:[],manifestCodec:y,duration:e,inputTimeScale:9e4,dropped:0}},h.probe=function(y){if(!y)return!1;for(var d=(E.getID3Data(y,0)||[]).length,e=y.length;d<e;d++)if(k.probe(y,d))return A.logger.log("MPEG Audio sync word found !"),!0;return!1},T.canParse=function(y,d){return k.canParse(y,d)},T.appendFrame=function(y,d,e){if(this.initPTS!==null)return k.appendFrame(y,d,e,this.initPTS,this.frameIndex)},h}(R.default);D.minProbeByteLength=4,I.default=D},"./src/demux/mp4demuxer.ts":function(M,I,g){g.r(I);var R=g("./src/utils/mp4-tools.ts"),E=g("./src/demux/dummy-demuxed-track.ts"),A=function(){function k(D,s){this.remainderData=null,this.config=void 0,this.config=s}var C=k.prototype;return C.resetTimeStamp=function(){},C.resetInitSegment=function(){},C.resetContiguity=function(){},k.probe=function(D){return Object(R.findBox)({data:D,start:0,end:Math.min(D.length,16384)},["moof"]).length>0},C.demux=function(D){var s=D,b=Object(E.dummyTrack)();if(this.config.progressive){this.remainderData&&(s=Object(R.appendUint8Array)(this.remainderData,D));var m=Object(R.segmentValidRange)(s);this.remainderData=m.remainder,b.samples=m.valid||new Uint8Array}else b.samples=s;return{audioTrack:Object(E.dummyTrack)(),avcTrack:b,id3Track:Object(E.dummyTrack)(),textTrack:Object(E.dummyTrack)()}},C.flush=function(){var D=Object(E.dummyTrack)();return D.samples=this.remainderData||new Uint8Array,this.remainderData=null,{audioTrack:Object(E.dummyTrack)(),avcTrack:D,id3Track:Object(E.dummyTrack)(),textTrack:Object(E.dummyTrack)()}},C.demuxSampleAes=function(D,s,b){return Promise.reject(new Error("The MP4 demuxer does not support SAMPLE-AES decryption"))},C.destroy=function(){},k}();A.minProbeByteLength=1024,I.default=A},"./src/demux/mpegaudio.ts":function(M,I,g){g.r(I),g.d(I,"appendFrame",function(){return D}),g.d(I,"parseHeader",function(){return s}),g.d(I,"isHeaderPattern",function(){return b}),g.d(I,"isHeader",function(){return m}),g.d(I,"canParse",function(){return h}),g.d(I,"probe",function(){return T});var R=null,E=[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],A=[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],k=[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],C=[0,1,1,4];function D(y,d,e,a,t){if(!(e+24>d.length)){var o=s(d,e);if(o&&e+o.frameLength<=d.length){var u=a+t*(9e4*o.samplesPerFrame/o.sampleRate),n={unit:d.subarray(e,e+o.frameLength),pts:u,dts:u};return y.config=[],y.channelCount=o.channelCount,y.samplerate=o.sampleRate,y.samples.push(n),{sample:n,length:o.frameLength,missing:0}}}}function s(y,d){var e=y[d+1]>>3&3,a=y[d+1]>>1&3,t=y[d+2]>>4&15,o=y[d+2]>>2&3;if(e!==1&&t!==0&&t!==15&&o!==3){var u=y[d+2]>>1&1,n=y[d+3]>>6,l=1e3*E[14*(e===3?3-a:a===3?3:4)+t-1],p=A[3*(e===3?0:e===2?1:2)+o],f=n===3?1:2,v=k[e][a],r=C[a],i=8*v*r,c=Math.floor(v*l/p+u)*r;if(R===null){var S=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);R=S?parseInt(S[1]):0}return R&&R<=87&&a===2&&l>=224e3&&n===0&&(y[d+3]=128|y[d+3]),{sampleRate:p,channelCount:f,frameLength:c,samplesPerFrame:i}}}function b(y,d){return y[d]===255&&(224&y[d+1])==224&&(6&y[d+1])!=0}function m(y,d){return d+1<y.length&&b(y,d)}function h(y,d){return b(y,d)&&4<=y.length-d}function T(y,d){if(d+1<y.length&&b(y,d)){var e=s(y,d),a=4;e!=null&&e.frameLength&&(a=e.frameLength);var t=d+a;return t===y.length||m(y,t)}return!1}},"./src/demux/sample-aes.ts":function(M,I,g){g.r(I);var R=g("./src/crypt/decrypter.ts"),E=g("./src/demux/tsdemuxer.ts"),A=function(){function k(D,s,b){this.keyData=void 0,this.decrypter=void 0,this.keyData=b,this.decrypter=new R.default(D,s,{removePKCS7Padding:!1})}var C=k.prototype;return C.decryptBuffer=function(D,s){this.decrypter.decrypt(D,this.keyData.key.buffer,this.keyData.iv.buffer,s)},C.decryptAacSample=function(D,s,b,m){var h=D[s].unit,T=h.subarray(16,h.length-h.length%16),y=T.buffer.slice(T.byteOffset,T.byteOffset+T.length),d=this;this.decryptBuffer(y,function(e){var a=new Uint8Array(e);h.set(a,16),m||d.decryptAacSamples(D,s+1,b)})},C.decryptAacSamples=function(D,s,b){for(;;s++){if(s>=D.length)return void b();if(!(D[s].unit.length<32)){var m=this.decrypter.isSync();if(this.decryptAacSample(D,s,b,m),!m)return}}},C.getAvcEncryptedData=function(D){for(var s=16*Math.floor((D.length-48)/160)+16,b=new Int8Array(s),m=0,h=32;h<=D.length-16;h+=160,m+=16)b.set(D.subarray(h,h+16),m);return b},C.getAvcDecryptedUnit=function(D,s){for(var b=new Uint8Array(s),m=0,h=32;h<=D.length-16;h+=160,m+=16)D.set(b.subarray(m,m+16),h);return D},C.decryptAvcSample=function(D,s,b,m,h,T){var y=Object(E.discardEPB)(h.data),d=this.getAvcEncryptedData(y),e=this;this.decryptBuffer(d.buffer,function(a){h.data=e.getAvcDecryptedUnit(y,a),T||e.decryptAvcSamples(D,s,b+1,m)})},C.decryptAvcSamples=function(D,s,b,m){if(D instanceof Uint8Array)throw new Error("Cannot decrypt samples of type Uint8Array");for(;;s++,b=0){if(s>=D.length)return void m();for(var h=D[s].units;!(b>=h.length);b++){var T=h[b];if(!(T.data.length<=48||T.type!==1&&T.type!==5)){var y=this.decrypter.isSync();if(this.decryptAvcSample(D,s,b,m,T,y),!y)return}}}},k}();I.default=A},"./src/demux/transmuxer-interface.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return m});var R=g("./node_modules/webworkify-webpack/index.js"),E=g("./src/events.ts"),A=g("./src/demux/transmuxer.ts"),k=g("./src/utils/logger.ts"),C=g("./src/errors.ts"),D=g("./src/utils/mediasource-helper.ts"),s=g("./node_modules/eventemitter3/index.js"),b=Object(D.getMediaSource)()||{isTypeSupported:function(){return!1}},m=function(){function h(y,d,e,a){var t=this;this.hls=void 0,this.id=void 0,this.observer=void 0,this.frag=null,this.part=null,this.worker=void 0,this.onwmsg=void 0,this.transmuxer=null,this.onTransmuxComplete=void 0,this.onFlush=void 0,this.hls=y,this.id=d,this.onTransmuxComplete=e,this.onFlush=a;var o=y.config,u=function(f,v){(v=v||{}).frag=t.frag,v.id=t.id,y.trigger(f,v)};this.observer=new s.EventEmitter,this.observer.on(E.Events.FRAG_DECRYPTED,u),this.observer.on(E.Events.ERROR,u);var n={mp4:b.isTypeSupported("video/mp4"),mpeg:b.isTypeSupported("audio/mpeg"),mp3:b.isTypeSupported('audio/mp4; codecs="mp3"')},l=navigator.vendor;if(o.enableWorker&&typeof Worker<"u"){var p;k.logger.log("demuxing in webworker");try{p=this.worker=R("./src/demux/transmuxer-worker.ts"),this.onwmsg=this.onWorkerMessage.bind(this),p.addEventListener("message",this.onwmsg),p.onerror=function(f){y.trigger(E.Events.ERROR,{type:C.ErrorTypes.OTHER_ERROR,details:C.ErrorDetails.INTERNAL_EXCEPTION,fatal:!0,event:"demuxerWorker",error:new Error(f.message+"  ("+f.filename+":"+f.lineno+")")})},p.postMessage({cmd:"init",typeSupported:n,vendor:l,id:d,config:JSON.stringify(o)})}catch(f){k.logger.warn("Error in worker:",f),k.logger.error("Error while initializing DemuxerWorker, fallback to inline"),p&&self.URL.revokeObjectURL(p.objectURL),this.transmuxer=new A.default(this.observer,n,o,l,d),this.worker=null}}else this.transmuxer=new A.default(this.observer,n,o,l,d)}var T=h.prototype;return T.destroy=function(){var y=this.worker;if(y)y.removeEventListener("message",this.onwmsg),y.terminate(),this.worker=null;else{var d=this.transmuxer;d&&(d.destroy(),this.transmuxer=null)}var e=this.observer;e&&e.removeAllListeners(),this.observer=null},T.push=function(y,d,e,a,t,o,u,n,l,p){var f=this;l.transmuxing.start=self.performance.now();var v=this.transmuxer,r=this.worker,i=o?o.start:t.start,c=t.decryptdata,S=this.frag,L=!(S&&t.cc===S.cc),_=!(S&&l.level===S.level),x=S?l.sn-S.sn:-1,w=this.part?l.part-this.part.index:1,O=!_&&(x===1||x===0&&w===1),P=self.performance.now();(_||x||t.stats.parsing.start===0)&&(t.stats.parsing.start=P),!o||!w&&O||(o.stats.parsing.start=P);var F=new A.TransmuxState(L,O,n,_,i);if(!O||L){k.logger.log("[transmuxer-interface, "+t.type+"]: Starting new transmux session for sn: "+l.sn+" p: "+l.part+" level: "+l.level+" id: "+l.id+`
        discontinuity: `+L+`
        trackSwitch: `+_+`
        contiguous: `+O+`
        accurateTimeOffset: `+n+`
        timeOffset: `+i);var N=new A.TransmuxConfig(e,a,d,u,p);this.configureTransmuxer(N)}if(this.frag=t,this.part=o,r)r.postMessage({cmd:"demux",data:y,decryptdata:c,chunkMeta:l,state:F},y instanceof ArrayBuffer?[y]:[]);else if(v){var U=v.push(y,c,l,F);Object(A.isPromise)(U)?U.then(function(G){f.handleTransmuxComplete(G)}):this.handleTransmuxComplete(U)}},T.flush=function(y){var d=this;y.transmuxing.start=self.performance.now();var e=this.transmuxer,a=this.worker;if(a)a.postMessage({cmd:"flush",chunkMeta:y});else if(e){var t=e.flush(y);Object(A.isPromise)(t)?t.then(function(o){d.handleFlushResult(o,y)}):this.handleFlushResult(t,y)}},T.handleFlushResult=function(y,d){var e=this;y.forEach(function(a){e.handleTransmuxComplete(a)}),this.onFlush(d)},T.onWorkerMessage=function(y){var d=y.data,e=this.hls;switch(d.event){case"init":self.URL.revokeObjectURL(this.worker.objectURL);break;case"transmuxComplete":this.handleTransmuxComplete(d.data);break;case"flush":this.onFlush(d.data);break;default:d.data=d.data||{},d.data.frag=this.frag,d.data.id=this.id,e.trigger(d.event,d.data)}},T.configureTransmuxer=function(y){var d=this.worker,e=this.transmuxer;d?d.postMessage({cmd:"configure",config:y}):e&&e.configure(y)},T.handleTransmuxComplete=function(y){y.chunkMeta.transmuxing.end=self.performance.now(),this.onTransmuxComplete(y)},h}()},"./src/demux/transmuxer-worker.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return C});var R=g("./src/demux/transmuxer.ts"),E=g("./src/events.ts"),A=g("./src/utils/logger.ts"),k=g("./node_modules/eventemitter3/index.js");function C(m){var h=new k.EventEmitter,T=function(y,d){m.postMessage({event:y,data:d})};h.on(E.Events.FRAG_DECRYPTED,T),h.on(E.Events.ERROR,T),m.addEventListener("message",function(y){var d=y.data;switch(d.cmd){case"init":var e=JSON.parse(d.config);m.transmuxer=new R.default(h,d.typeSupported,e,d.vendor,d.id),Object(A.enableLogs)(e.debug),T("init",null);break;case"configure":m.transmuxer.configure(d.config);break;case"demux":var a=m.transmuxer.push(d.data,d.decryptdata,d.chunkMeta,d.state);Object(R.isPromise)(a)?a.then(function(u){D(m,u)}):D(m,a);break;case"flush":var t=d.chunkMeta,o=m.transmuxer.flush(t);Object(R.isPromise)(o)?o.then(function(u){b(m,u,t)}):b(m,o,t)}})}function D(m,h){if((T=h.remuxResult).audio||T.video||T.text||T.id3||T.initSegment){var T,y=[],d=h.remuxResult,e=d.audio,a=d.video;e&&s(y,e),a&&s(y,a),m.postMessage({event:"transmuxComplete",data:h},y)}}function s(m,h){h.data1&&m.push(h.data1.buffer),h.data2&&m.push(h.data2.buffer)}function b(m,h,T){h.forEach(function(y){D(m,y)}),m.postMessage({event:"flush",data:T})}},"./src/demux/transmuxer.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return t}),g.d(I,"isPromise",function(){return u}),g.d(I,"TransmuxConfig",function(){return n}),g.d(I,"TransmuxState",function(){return l});var R,E=g("./src/events.ts"),A=g("./src/errors.ts"),k=g("./src/crypt/decrypter.ts"),C=g("./src/demux/aacdemuxer.ts"),D=g("./src/demux/mp4demuxer.ts"),s=g("./src/demux/tsdemuxer.ts"),b=g("./src/demux/mp3demuxer.ts"),m=g("./src/remux/mp4-remuxer.ts"),h=g("./src/remux/passthrough-remuxer.ts"),T=g("./src/demux/chunk-cache.ts"),y=g("./src/utils/mp4-tools.ts"),d=g("./src/utils/logger.ts");try{R=self.performance.now.bind(self.performance)}catch{d.logger.debug("Unable to use Performance API on this environment"),R=self.Date.now}var e=[{demux:s.default,remux:m.default},{demux:D.default,remux:h.default},{demux:C.default,remux:m.default},{demux:b.default,remux:m.default}],a=1024;e.forEach(function(p){var f=p.demux;a=Math.max(a,f.minProbeByteLength)});var t=function(){function p(v,r,i,c,S){this.observer=void 0,this.typeSupported=void 0,this.config=void 0,this.vendor=void 0,this.id=void 0,this.demuxer=void 0,this.remuxer=void 0,this.decrypter=void 0,this.probe=void 0,this.decryptionPromise=null,this.transmuxConfig=void 0,this.currentTransmuxState=void 0,this.cache=new T.default,this.observer=v,this.typeSupported=r,this.config=i,this.vendor=c,this.id=S}var f=p.prototype;return f.configure=function(v){this.transmuxConfig=v,this.decrypter&&this.decrypter.reset()},f.push=function(v,r,i,c){var S=this,L=i.transmuxing;L.executeStart=R();var _=new Uint8Array(v),x=this.cache,w=this.config,O=this.currentTransmuxState,P=this.transmuxConfig;c&&(this.currentTransmuxState=c);var F=function(le,se){var pe=null;return le.byteLength>0&&se!=null&&se.key!=null&&se.iv!==null&&se.method!=null&&(pe=se),pe}(_,r);if(F&&F.method==="AES-128"){var N=this.getDecrypter();if(!w.enableSoftwareAES)return this.decryptionPromise=N.webCryptoDecrypt(_,F.key.buffer,F.iv.buffer).then(function(le){var se=S.push(le,null,i);return S.decryptionPromise=null,se}),this.decryptionPromise;var U=N.softwareDecrypt(_,F.key.buffer,F.iv.buffer);if(!U)return L.executeEnd=R(),o(i);_=new Uint8Array(U)}var G=c||O,K=G.contiguous,q=G.discontinuity,Y=G.trackSwitch,j=G.accurateTimeOffset,X=G.timeOffset,Q=P.audioCodec,te=P.videoCodec,ae=P.defaultInitPts,oe=P.duration,ie=P.initSegmentData;if((q||Y)&&this.resetInitSegment(ie,Q,te,oe),q&&this.resetInitialTimestamp(ae),K||this.resetContiguity(),this.needsProbing(_,q,Y)){if(x.dataLength){var ue=x.flush();_=Object(y.appendUint8Array)(ue,_)}this.configureTransmuxer(_,P)}var J=this.transmux(_,F,X,j,i),ne=this.currentTransmuxState;return ne.contiguous=!0,ne.discontinuity=!1,ne.trackSwitch=!1,L.executeEnd=R(),J},f.flush=function(v){var r=this,i=v.transmuxing;i.executeStart=R();var c=this.decrypter,S=this.cache,L=this.currentTransmuxState,_=this.decryptionPromise;if(_)return _.then(function(){return r.flush(v)});var x=[],w=L.timeOffset;if(c){var O=c.flush();O&&x.push(this.push(O,null,v))}var P=S.dataLength;S.reset();var F=this.demuxer,N=this.remuxer;if(!F||!N)return P>=a&&this.observer.emit(E.Events.ERROR,E.Events.ERROR,{type:A.ErrorTypes.MEDIA_ERROR,details:A.ErrorDetails.FRAG_PARSING_ERROR,fatal:!0,reason:"no demux matching with content found"}),i.executeEnd=R(),[o(v)];var U=F.flush(w);return u(U)?U.then(function(G){return r.flushRemux(x,G,v),x}):(this.flushRemux(x,U,v),x)},f.flushRemux=function(v,r,i){var c=r.audioTrack,S=r.avcTrack,L=r.id3Track,_=r.textTrack,x=this.currentTransmuxState,w=x.accurateTimeOffset,O=x.timeOffset;d.logger.log("[transmuxer.ts]: Flushed fragment "+i.sn+(i.part>-1?" p: "+i.part:"")+" of level "+i.level);var P=this.remuxer.remux(c,S,L,_,O,w,!0,this.id);v.push({remuxResult:P,chunkMeta:i}),i.transmuxing.executeEnd=R()},f.resetInitialTimestamp=function(v){var r=this.demuxer,i=this.remuxer;r&&i&&(r.resetTimeStamp(v),i.resetTimeStamp(v))},f.resetContiguity=function(){var v=this.demuxer,r=this.remuxer;v&&r&&(v.resetContiguity(),r.resetNextTimestamp())},f.resetInitSegment=function(v,r,i,c){var S=this.demuxer,L=this.remuxer;S&&L&&(S.resetInitSegment(r,i,c),L.resetInitSegment(v,r,i))},f.destroy=function(){this.demuxer&&(this.demuxer.destroy(),this.demuxer=void 0),this.remuxer&&(this.remuxer.destroy(),this.remuxer=void 0)},f.transmux=function(v,r,i,c,S){return r&&r.method==="SAMPLE-AES"?this.transmuxSampleAes(v,r,i,c,S):this.transmuxUnencrypted(v,i,c,S)},f.transmuxUnencrypted=function(v,r,i,c){var S=this.demuxer.demux(v,r,!1,!this.config.progressive),L=S.audioTrack,_=S.avcTrack,x=S.id3Track,w=S.textTrack;return{remuxResult:this.remuxer.remux(L,_,x,w,r,i,!1,this.id),chunkMeta:c}},f.transmuxSampleAes=function(v,r,i,c,S){var L=this;return this.demuxer.demuxSampleAes(v,r,i).then(function(_){return{remuxResult:L.remuxer.remux(_.audioTrack,_.avcTrack,_.id3Track,_.textTrack,i,c,!1,L.id),chunkMeta:S}})},f.configureTransmuxer=function(v,r){for(var i,c=this.config,S=this.observer,L=this.typeSupported,_=this.vendor,x=r.audioCodec,w=r.defaultInitPts,O=r.duration,P=r.initSegmentData,F=r.videoCodec,N=0,U=e.length;N<U;N++)if(e[N].demux.probe(v)){i=e[N];break}i||(d.logger.warn("Failed to find demuxer by probing frag, treating as mp4 passthrough"),i={demux:D.default,remux:h.default});var G=this.demuxer,K=this.remuxer,q=i.remux,Y=i.demux;K&&K instanceof q||(this.remuxer=new q(S,c,L,_)),G&&G instanceof Y||(this.demuxer=new Y(S,c,L),this.probe=Y.probe),this.resetInitSegment(P,x,F,O),this.resetInitialTimestamp(w)},f.needsProbing=function(v,r,i){return!this.demuxer||!this.remuxer||r||i},f.getDecrypter=function(){var v=this.decrypter;return v||(v=this.decrypter=new k.default(this.observer,this.config)),v},p}(),o=function(p){return{remuxResult:{},chunkMeta:p}};function u(p){return"then"in p&&p.then instanceof Function}var n=function(p,f,v,r,i){this.audioCodec=void 0,this.videoCodec=void 0,this.initSegmentData=void 0,this.duration=void 0,this.defaultInitPts=void 0,this.audioCodec=p,this.videoCodec=f,this.initSegmentData=v,this.duration=r,this.defaultInitPts=i},l=function(p,f,v,r,i){this.discontinuity=void 0,this.contiguous=void 0,this.accurateTimeOffset=void 0,this.trackSwitch=void 0,this.timeOffset=void 0,this.discontinuity=p,this.contiguous=f,this.accurateTimeOffset=v,this.trackSwitch=r,this.timeOffset=i}},"./src/demux/tsdemuxer.ts":function(M,I,g){g.r(I),g.d(I,"discardEPB",function(){return u});var R=g("./src/demux/adts.ts"),E=g("./src/demux/mpegaudio.ts"),A=g("./src/demux/exp-golomb.ts"),k=g("./src/demux/id3.ts"),C=g("./src/demux/sample-aes.ts"),D=g("./src/events.ts"),s=g("./src/utils/mp4-tools.ts"),b=g("./src/utils/logger.ts"),m=g("./src/errors.ts"),h={video:1,audio:2,id3:3,text:4},T=function(){function n(p,f,v){this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.sampleAes=null,this.pmtParsed=!1,this.audioCodec=void 0,this.videoCodec=void 0,this._duration=0,this.aacLastPTS=null,this._initPTS=null,this._initDTS=null,this._pmtId=-1,this._avcTrack=void 0,this._audioTrack=void 0,this._id3Track=void 0,this._txtTrack=void 0,this.aacOverFlow=null,this.avcSample=null,this.remainderData=null,this.observer=p,this.config=f,this.typeSupported=v}n.probe=function(p){var f=n.syncOffset(p);return!(f<0||(f&&b.logger.warn("MPEG2-TS detected but first sync word found @ offset "+f+", junk ahead ?"),0))},n.syncOffset=function(p){for(var f=Math.min(1e3,p.length-564),v=0;v<f;){if(p[v]===71&&p[v+188]===71&&p[v+376]===71)return v;v++}return-1},n.createTrack=function(p,f){return{container:p==="video"||p==="audio"?"video/mp2t":void 0,type:p,id:h[p],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0,duration:p==="audio"?f:void 0}};var l=n.prototype;return l.resetInitSegment=function(p,f,v){this.pmtParsed=!1,this._pmtId=-1,this._avcTrack=n.createTrack("video",v),this._audioTrack=n.createTrack("audio",v),this._id3Track=n.createTrack("id3",v),this._txtTrack=n.createTrack("text",v),this._audioTrack.isAAC=!0,this.aacOverFlow=null,this.aacLastPTS=null,this.avcSample=null,this.audioCodec=p,this.videoCodec=f,this._duration=v},l.resetTimeStamp=function(){},l.resetContiguity=function(){var p=this._audioTrack,f=this._avcTrack,v=this._id3Track;p&&(p.pesData=null),f&&(f.pesData=null),v&&(v.pesData=null),this.aacOverFlow=null,this.aacLastPTS=null},l.demux=function(p,f,v,r){var i;v===void 0&&(v=!1),r===void 0&&(r=!1),v||(this.sampleAes=null);var c=this._avcTrack,S=this._audioTrack,L=this._id3Track,_=c.pid,x=c.pesData,w=S.pid,O=L.pid,P=S.pesData,F=L.pesData,N=!1,U=this.pmtParsed,G=this._pmtId,K=p.length;if(this.remainderData&&(K=(p=Object(s.appendUint8Array)(this.remainderData,p)).length,this.remainderData=null),K<188&&!r)return this.remainderData=p,{audioTrack:S,avcTrack:c,id3Track:L,textTrack:this._txtTrack};var q=Math.max(0,n.syncOffset(p));(K-=(K+q)%188)<p.byteLength&&!r&&(this.remainderData=new Uint8Array(p.buffer,K,p.buffer.byteLength-K));for(var Y=q;Y<K;Y+=188)if(p[Y]===71){var j=!!(64&p[Y+1]),X=((31&p[Y+1])<<8)+p[Y+2],Q=void 0;if((48&p[Y+3])>>4>1){if((Q=Y+5+p[Y+4])===Y+188)continue}else Q=Y+4;switch(X){case _:j&&(x&&(i=a(x))&&this.parseAVCPES(i,!1),x={data:[],size:0}),x&&(x.data.push(p.subarray(Q,Y+188)),x.size+=Y+188-Q);break;case w:j&&(P&&(i=a(P))&&(S.isAAC?this.parseAACPES(i):this.parseMPEGPES(i)),P={data:[],size:0}),P&&(P.data.push(p.subarray(Q,Y+188)),P.size+=Y+188-Q);break;case O:j&&(F&&(i=a(F))&&this.parseID3PES(i),F={data:[],size:0}),F&&(F.data.push(p.subarray(Q,Y+188)),F.size+=Y+188-Q);break;case 0:j&&(Q+=p[Q]+1),G=this._pmtId=d(p,Q);break;case G:j&&(Q+=p[Q]+1);var te=e(p,Q,this.typeSupported.mpeg===!0||this.typeSupported.mp3===!0,v);(_=te.avc)>0&&(c.pid=_),(w=te.audio)>0&&(S.pid=w,S.isAAC=te.isAAC),(O=te.id3)>0&&(L.pid=O),N&&!U&&(b.logger.log("reparse from beginning"),N=!1,Y=q-188),U=this.pmtParsed=!0;break;case 17:case 8191:break;default:N=!0}}else this.observer.emit(D.Events.ERROR,D.Events.ERROR,{type:m.ErrorTypes.MEDIA_ERROR,details:m.ErrorDetails.FRAG_PARSING_ERROR,fatal:!1,reason:"TS packet did not start with 0x47"});c.pesData=x,S.pesData=P,L.pesData=F;var ae={audioTrack:S,avcTrack:c,id3Track:L,textTrack:this._txtTrack};return r&&this.extractRemainingSamples(ae),ae},l.flush=function(){var p,f=this.remainderData;return this.remainderData=null,p=f?this.demux(f,-1,!1,!0):{audioTrack:this._audioTrack,avcTrack:this._avcTrack,textTrack:this._txtTrack,id3Track:this._id3Track},this.extractRemainingSamples(p),this.sampleAes?this.decrypt(p,this.sampleAes):p},l.extractRemainingSamples=function(p){var f,v=p.audioTrack,r=p.avcTrack,i=p.id3Track,c=r.pesData,S=v.pesData,L=i.pesData;c&&(f=a(c))?(this.parseAVCPES(f,!0),r.pesData=null):r.pesData=c,S&&(f=a(S))?(v.isAAC?this.parseAACPES(f):this.parseMPEGPES(f),v.pesData=null):(S!=null&&S.size&&b.logger.log("last AAC PES packet truncated,might overlap between fragments"),v.pesData=S),L&&(f=a(L))?(this.parseID3PES(f),i.pesData=null):i.pesData=L},l.demuxSampleAes=function(p,f,v){var r=this.demux(p,v,!0,!this.config.progressive),i=this.sampleAes=new C.default(this.observer,this.config,f);return this.decrypt(r,i)},l.decrypt=function(p,f){return new Promise(function(v){var r=p.audioTrack,i=p.avcTrack;r.samples&&r.isAAC?f.decryptAacSamples(r.samples,0,function(){i.samples?f.decryptAvcSamples(i.samples,0,0,function(){v(p)}):v(p)}):i.samples&&f.decryptAvcSamples(i.samples,0,0,function(){v(p)})})},l.destroy=function(){this._initPTS=this._initDTS=null,this._duration=0},l.parseAVCPES=function(p,f){var v,r=this,i=this._avcTrack,c=this.parseAVCNALu(p.data),S=this.avcSample,L=!1;p.data=null,S&&c.length&&!i.audFound&&(t(S,i),S=this.avcSample=y(!1,p.pts,p.dts,"")),c.forEach(function(_){switch(_.type){case 1:v=!0,S||(S=r.avcSample=y(!0,p.pts,p.dts,"")),S.frame=!0;var x=_.data;if(L&&x.length>4){var w=new A.default(x).readSliceType();w!==2&&w!==4&&w!==7&&w!==9||(S.key=!0)}break;case 5:v=!0,S||(S=r.avcSample=y(!0,p.pts,p.dts,"")),S.key=!0,S.frame=!0;break;case 6:v=!0;var O=new A.default(u(_.data));O.readUByte();for(var P=0,F=0,N=!1,U=0;!N&&O.bytesAvailable>1;){P=0;do P+=U=O.readUByte();while(U===255);F=0;do F+=U=O.readUByte();while(U===255);if(P===4&&O.bytesAvailable!==0){if(N=!0,O.readUByte()===181&&O.readUShort()===49&&O.readUInt()===1195456820&&O.readUByte()===3){for(var G=O.readUByte(),K=31&G,q=[G,O.readUByte()],Y=0;Y<K;Y++)q.push(O.readUByte()),q.push(O.readUByte()),q.push(O.readUByte());o(r._txtTrack.samples,{type:3,pts:p.pts,bytes:q})}}else if(P===5&&O.bytesAvailable!==0){if(N=!0,F>16){for(var j=[],X=0;X<16;X++)j.push(O.readUByte().toString(16)),X!==3&&X!==5&&X!==7&&X!==9||j.push("-");for(var Q=F-16,te=new Uint8Array(Q),ae=0;ae<Q;ae++)te[ae]=O.readUByte();o(r._txtTrack.samples,{pts:p.pts,payloadType:P,uuid:j.join(""),userData:Object(k.utf8ArrayToStr)(te),userDataBytes:te})}}else if(F<O.bytesAvailable)for(var oe=0;oe<F;oe++)O.readUByte()}break;case 7:if(v=!0,L=!0,!i.sps){var ie=new A.default(_.data).readSPS();i.width=ie.width,i.height=ie.height,i.pixelRatio=ie.pixelRatio,i.sps=[_.data],i.duration=r._duration;for(var ue=_.data.subarray(1,4),J="avc1.",ne=0;ne<3;ne++){var le=ue[ne].toString(16);le.length<2&&(le="0"+le),J+=le}i.codec=J}break;case 8:v=!0,i.pps||(i.pps=[_.data]);break;case 9:v=!1,i.audFound=!0,S&&t(S,i),S=r.avcSample=y(!1,p.pts,p.dts,"");break;case 12:v=!1;break;default:v=!1,S&&(S.debug+="unknown NAL "+_.type+" ")}S&&v&&S.units.push(_)}),f&&S&&(t(S,i),this.avcSample=null)},l.getLastNalUnit=function(){var p,f,v=this.avcSample;if(!v||v.units.length===0){var r=this._avcTrack.samples;v=r[r.length-1]}if((p=v)!==null&&p!==void 0&&p.units){var i=v.units;f=i[i.length-1]}return f},l.parseAVCNALu=function(p){var f,v,r=p.byteLength,i=this._avcTrack,c=i.naluState||0,S=c,L=[],_=0,x=-1,w=0;for(c===-1&&(x=0,w=31&p[0],c=0,_=1);_<r;)if(f=p[_++],c)if(c!==1)if(f)if(f===1){if(x>=0){var O={data:p.subarray(x,_-c-1),type:w};L.push(O)}else{var P=this.getLastNalUnit();if(P&&(S&&_<=4-S&&P.state&&(P.data=P.data.subarray(0,P.data.byteLength-S)),(v=_-c-1)>0)){var F=new Uint8Array(P.data.byteLength+v);F.set(P.data,0),F.set(p.subarray(0,v),P.data.byteLength),P.data=F}}_<r?(x=_,w=31&p[_],c=0):c=-1}else c=0;else c=3;else c=f?0:2;else c=f?0:1;if(x>=0&&c>=0){var N={data:p.subarray(x,r),type:w,state:c};L.push(N)}if(L.length===0){var U=this.getLastNalUnit();if(U){var G=new Uint8Array(U.data.byteLength+p.byteLength);G.set(U.data,0),G.set(p,U.data.byteLength),U.data=G}}return i.naluState=c,L},l.parseAACPES=function(p){var f,v,r,i,c,S=0,L=this._audioTrack,_=this.aacOverFlow,x=p.data;if(_){this.aacOverFlow=null;var w=_.sample.unit.byteLength,O=Math.min(_.missing,w),P=w-O;_.sample.unit.set(x.subarray(0,O),P),L.samples.push(_.sample),S=_.missing}for(f=S,v=x.length;f<v-1&&!R.isHeader(x,f);f++);if(f===S||(f<v-1?(r="AAC PES did not start with ADTS header,offset:"+f,i=!1):(r="no ADTS header found in AAC PES",i=!0),b.logger.warn("parsing error:"+r),this.observer.emit(D.Events.ERROR,D.Events.ERROR,{type:m.ErrorTypes.MEDIA_ERROR,details:m.ErrorDetails.FRAG_PARSING_ERROR,fatal:i,reason:r}),!i)){if(R.initTrackConfig(L,this.observer,x,f,this.audioCodec),p.pts!==void 0)c=p.pts;else{if(!_)return void b.logger.warn("[tsdemuxer]: AAC PES unknown PTS");var F=R.getFrameDuration(L.samplerate);c=_.sample.pts+F}for(var N=0;f<v;){if(R.isHeader(x,f)){if(f+5<v){var U=R.appendFrame(L,x,f,c,N);if(U){if(!U.missing){f+=U.length,N++;continue}this.aacOverFlow=U}}break}f++}}},l.parseMPEGPES=function(p){var f=p.data,v=f.length,r=0,i=0,c=p.pts;if(c!==void 0)for(;i<v;)if(E.isHeader(f,i)){var S=E.appendFrame(this._audioTrack,f,i,c,r);if(!S)break;i+=S.length,r++}else i++;else b.logger.warn("[tsdemuxer]: MPEG PES unknown PTS")},l.parseID3PES=function(p){p.pts!==void 0?this._id3Track.samples.push(p):b.logger.warn("[tsdemuxer]: ID3 PES unknown PTS")},n}();function y(n,l,p,f){return{key:n,frame:!1,pts:l,dts:p,units:[],debug:f,length:0}}function d(n,l){return(31&n[l+10])<<8|n[l+11]}function e(n,l,p,f){var v={audio:-1,avc:-1,id3:-1,isAAC:!0},r=l+3+((15&n[l+1])<<8|n[l+2])-4;for(l+=12+((15&n[l+10])<<8|n[l+11]);l<r;){var i=(31&n[l+1])<<8|n[l+2];switch(n[l]){case 207:if(!f){b.logger.log("ADTS AAC with AES-128-CBC frame encryption found in unencrypted stream");break}case 15:v.audio===-1&&(v.audio=i);break;case 21:v.id3===-1&&(v.id3=i);break;case 219:if(!f){b.logger.log("H.264 with AES-128-CBC slice encryption found in unencrypted stream");break}case 27:v.avc===-1&&(v.avc=i);break;case 3:case 4:p?v.audio===-1&&(v.audio=i,v.isAAC=!1):b.logger.log("MPEG audio found, not supported in this browser");break;case 36:b.logger.warn("Unsupported HEVC stream type found")}l+=5+((15&n[l+3])<<8|n[l+4])}return v}function a(n){var l,p,f,v,r,i=0,c=n.data;if(!n||n.size===0)return null;for(;c[0].length<19&&c.length>1;){var S=new Uint8Array(c[0].length+c[1].length);S.set(c[0]),S.set(c[1],c[0].length),c[0]=S,c.splice(1,1)}if(((l=c[0])[0]<<16)+(l[1]<<8)+l[2]===1){if((p=(l[4]<<8)+l[5])&&p>n.size-6)return null;var L=l[7];192&L&&(v=536870912*(14&l[9])+4194304*(255&l[10])+16384*(254&l[11])+128*(255&l[12])+(254&l[13])/2,64&L?v-(r=536870912*(14&l[14])+4194304*(255&l[15])+16384*(254&l[16])+128*(255&l[17])+(254&l[18])/2)>54e5&&(b.logger.warn(Math.round((v-r)/9e4)+"s delta between PTS and DTS, align them"),v=r):r=v);var _=(f=l[8])+9;if(n.size<=_)return null;n.size-=_;for(var x=new Uint8Array(n.size),w=0,O=c.length;w<O;w++){var P=(l=c[w]).byteLength;if(_){if(_>P){_-=P;continue}l=l.subarray(_),P-=_,_=0}x.set(l,i),i+=P}return p&&(p-=f+3),{data:x,pts:v,dts:r,len:p}}return null}function t(n,l){if(n.units.length&&n.frame){if(n.pts===void 0){var p=l.samples,f=p.length;if(!f)return void l.dropped++;var v=p[f-1];n.pts=v.pts,n.dts=v.dts}l.samples.push(n)}n.debug.length&&b.logger.log(n.pts+"/"+n.dts+":"+n.debug)}function o(n,l){var p=n.length;if(p>0){if(l.pts>=n[p-1].pts)n.push(l);else for(var f=p-1;f>=0;f--)if(l.pts<n[f].pts){n.splice(f,0,l);break}}else n.push(l)}function u(n){for(var l=n.byteLength,p=[],f=1;f<l-2;)n[f]===0&&n[f+1]===0&&n[f+2]===3?(p.push(f+2),f+=2):f++;if(p.length===0)return n;var v=l-p.length,r=new Uint8Array(v),i=0;for(f=0;f<v;i++,f++)i===p[0]&&(i++,p.shift()),r[f]=n[i];return r}T.minProbeByteLength=188,I.default=T},"./src/errors.ts":function(M,I,g){var R,E,A,k;g.r(I),g.d(I,"ErrorTypes",function(){return R}),g.d(I,"ErrorDetails",function(){return A}),(E=R||(R={})).NETWORK_ERROR="networkError",E.MEDIA_ERROR="mediaError",E.KEY_SYSTEM_ERROR="keySystemError",E.MUX_ERROR="muxError",E.OTHER_ERROR="otherError",(k=A||(A={})).KEY_SYSTEM_NO_KEYS="keySystemNoKeys",k.KEY_SYSTEM_NO_ACCESS="keySystemNoAccess",k.KEY_SYSTEM_NO_SESSION="keySystemNoSession",k.KEY_SYSTEM_LICENSE_REQUEST_FAILED="keySystemLicenseRequestFailed",k.KEY_SYSTEM_NO_INIT_DATA="keySystemNoInitData",k.MANIFEST_LOAD_ERROR="manifestLoadError",k.MANIFEST_LOAD_TIMEOUT="manifestLoadTimeOut",k.MANIFEST_PARSING_ERROR="manifestParsingError",k.MANIFEST_INCOMPATIBLE_CODECS_ERROR="manifestIncompatibleCodecsError",k.LEVEL_EMPTY_ERROR="levelEmptyError",k.LEVEL_LOAD_ERROR="levelLoadError",k.LEVEL_LOAD_TIMEOUT="levelLoadTimeOut",k.LEVEL_SWITCH_ERROR="levelSwitchError",k.AUDIO_TRACK_LOAD_ERROR="audioTrackLoadError",k.AUDIO_TRACK_LOAD_TIMEOUT="audioTrackLoadTimeOut",k.SUBTITLE_LOAD_ERROR="subtitleTrackLoadError",k.SUBTITLE_TRACK_LOAD_TIMEOUT="subtitleTrackLoadTimeOut",k.FRAG_LOAD_ERROR="fragLoadError",k.FRAG_LOAD_TIMEOUT="fragLoadTimeOut",k.FRAG_DECRYPT_ERROR="fragDecryptError",k.FRAG_PARSING_ERROR="fragParsingError",k.REMUX_ALLOC_ERROR="remuxAllocError",k.KEY_LOAD_ERROR="keyLoadError",k.KEY_LOAD_TIMEOUT="keyLoadTimeOut",k.BUFFER_ADD_CODEC_ERROR="bufferAddCodecError",k.BUFFER_INCOMPATIBLE_CODECS_ERROR="bufferIncompatibleCodecsError",k.BUFFER_APPEND_ERROR="bufferAppendError",k.BUFFER_APPENDING_ERROR="bufferAppendingError",k.BUFFER_STALLED_ERROR="bufferStalledError",k.BUFFER_FULL_ERROR="bufferFullError",k.BUFFER_SEEK_OVER_HOLE="bufferSeekOverHole",k.BUFFER_NUDGE_ON_STALL="bufferNudgeOnStall",k.INTERNAL_EXCEPTION="internalException",k.INTERNAL_ABORTED="aborted",k.UNKNOWN="unknown"},"./src/events.ts":function(M,I,g){var R,E;g.r(I),g.d(I,"Events",function(){return R}),(E=R||(R={})).MEDIA_ATTACHING="hlsMediaAttaching",E.MEDIA_ATTACHED="hlsMediaAttached",E.MEDIA_DETACHING="hlsMediaDetaching",E.MEDIA_DETACHED="hlsMediaDetached",E.BUFFER_RESET="hlsBufferReset",E.BUFFER_CODECS="hlsBufferCodecs",E.BUFFER_CREATED="hlsBufferCreated",E.BUFFER_APPENDING="hlsBufferAppending",E.BUFFER_APPENDED="hlsBufferAppended",E.BUFFER_EOS="hlsBufferEos",E.BUFFER_FLUSHING="hlsBufferFlushing",E.BUFFER_FLUSHED="hlsBufferFlushed",E.MANIFEST_LOADING="hlsManifestLoading",E.MANIFEST_LOADED="hlsManifestLoaded",E.MANIFEST_PARSED="hlsManifestParsed",E.LEVEL_SWITCHING="hlsLevelSwitching",E.LEVEL_SWITCHED="hlsLevelSwitched",E.LEVEL_LOADING="hlsLevelLoading",E.LEVEL_LOADED="hlsLevelLoaded",E.LEVEL_UPDATED="hlsLevelUpdated",E.LEVEL_PTS_UPDATED="hlsLevelPtsUpdated",E.LEVELS_UPDATED="hlsLevelsUpdated",E.AUDIO_TRACKS_UPDATED="hlsAudioTracksUpdated",E.AUDIO_TRACK_SWITCHING="hlsAudioTrackSwitching",E.AUDIO_TRACK_SWITCHED="hlsAudioTrackSwitched",E.AUDIO_TRACK_LOADING="hlsAudioTrackLoading",E.AUDIO_TRACK_LOADED="hlsAudioTrackLoaded",E.SUBTITLE_TRACKS_UPDATED="hlsSubtitleTracksUpdated",E.SUBTITLE_TRACKS_CLEARED="hlsSubtitleTracksCleared",E.SUBTITLE_TRACK_SWITCH="hlsSubtitleTrackSwitch",E.SUBTITLE_TRACK_LOADING="hlsSubtitleTrackLoading",E.SUBTITLE_TRACK_LOADED="hlsSubtitleTrackLoaded",E.SUBTITLE_FRAG_PROCESSED="hlsSubtitleFragProcessed",E.CUES_PARSED="hlsCuesParsed",E.NON_NATIVE_TEXT_TRACKS_FOUND="hlsNonNativeTextTracksFound",E.INIT_PTS_FOUND="hlsInitPtsFound",E.FRAG_LOADING="hlsFragLoading",E.FRAG_LOAD_EMERGENCY_ABORTED="hlsFragLoadEmergencyAborted",E.FRAG_LOADED="hlsFragLoaded",E.FRAG_DECRYPTED="hlsFragDecrypted",E.FRAG_PARSING_INIT_SEGMENT="hlsFragParsingInitSegment",E.FRAG_PARSING_USERDATA="hlsFragParsingUserdata",E.FRAG_PARSING_METADATA="hlsFragParsingMetadata",E.FRAG_PARSED="hlsFragParsed",E.FRAG_BUFFERED="hlsFragBuffered",E.FRAG_CHANGED="hlsFragChanged",E.FPS_DROP="hlsFpsDrop",E.FPS_DROP_LEVEL_CAPPING="hlsFpsDropLevelCapping",E.ERROR="hlsError",E.DESTROYING="hlsDestroying",E.KEY_LOADING="hlsKeyLoading",E.KEY_LOADED="hlsKeyLoaded",E.LIVE_BACK_BUFFER_REACHED="hlsLiveBackBufferReached",E.BACK_BUFFER_REACHED="hlsBackBufferReached"},"./src/hls.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return t});var R=g("./node_modules/url-toolkit/src/url-toolkit.js"),E=g("./src/loader/playlist-loader.ts"),A=g("./src/loader/key-loader.ts"),k=g("./src/controller/id3-track-controller.ts"),C=g("./src/controller/latency-controller.ts"),D=g("./src/controller/level-controller.ts"),s=g("./src/controller/fragment-tracker.ts"),b=g("./src/controller/stream-controller.ts"),m=g("./src/is-supported.ts"),h=g("./src/utils/logger.ts"),T=g("./src/config.ts"),y=g("./node_modules/eventemitter3/index.js"),d=g("./src/events.ts"),e=g("./src/errors.ts");function a(o,u){for(var n=0;n<u.length;n++){var l=u[n];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(o,l.key,l)}}var t=function(){function o(f){f===void 0&&(f={}),this.config=void 0,this.userConfig=void 0,this.coreComponents=void 0,this.networkControllers=void 0,this._emitter=new y.EventEmitter,this._autoLevelCapping=void 0,this.abrController=void 0,this.bufferController=void 0,this.capLevelController=void 0,this.latencyController=void 0,this.levelController=void 0,this.streamController=void 0,this.audioTrackController=void 0,this.subtitleTrackController=void 0,this.emeController=void 0,this._media=null,this.url=null;var v=this.config=Object(T.mergeConfig)(o.DefaultConfig,f);this.userConfig=f,Object(h.enableLogs)(v.debug),this._autoLevelCapping=-1,v.progressive&&Object(T.enableStreamingMode)(v);var r=v.abrController,i=v.bufferController,c=v.capLevelController,S=v.fpsController,L=this.abrController=new r(this),_=this.bufferController=new i(this),x=this.capLevelController=new c(this),w=new S(this),O=new E.default(this),P=new A.default(this),F=new k.default(this),N=this.levelController=new D.default(this),U=new s.FragmentTracker(this),G=this.streamController=new b.default(this,U);x.setStreamController(G),w.setStreamController(G);var K=[N,G];this.networkControllers=K;var q=[O,P,L,_,x,w,F,U];this.audioTrackController=this.createController(v.audioTrackController,null,K),this.createController(v.audioStreamController,U,K),this.subtitleTrackController=this.createController(v.subtitleTrackController,null,K),this.createController(v.subtitleStreamController,U,K),this.createController(v.timelineController,null,q),this.emeController=this.createController(v.emeController,null,q),this.latencyController=this.createController(C.default,null,q),this.coreComponents=q}o.isSupported=function(){return Object(m.isSupported)()};var u,n,l,p=o.prototype;return p.createController=function(f,v,r){if(f){var i=v?new f(this,v):new f(this);return r&&r.push(i),i}return null},p.on=function(f,v,r){r===void 0&&(r=this),this._emitter.on(f,v,r)},p.once=function(f,v,r){r===void 0&&(r=this),this._emitter.once(f,v,r)},p.removeAllListeners=function(f){this._emitter.removeAllListeners(f)},p.off=function(f,v,r,i){r===void 0&&(r=this),this._emitter.off(f,v,r,i)},p.listeners=function(f){return this._emitter.listeners(f)},p.emit=function(f,v,r){return this._emitter.emit(f,v,r)},p.trigger=function(f,v){if(this.config.debug)return this.emit(f,f,v);try{return this.emit(f,f,v)}catch(r){h.logger.error("An internal error happened while handling event "+f+'. Error message: "'+r.message+'". Here is a stacktrace:',r),this.trigger(d.Events.ERROR,{type:e.ErrorTypes.OTHER_ERROR,details:e.ErrorDetails.INTERNAL_EXCEPTION,fatal:!1,event:f,error:r})}return!1},p.listenerCount=function(f){return this._emitter.listenerCount(f)},p.destroy=function(){h.logger.log("destroy"),this.trigger(d.Events.DESTROYING,void 0),this.detachMedia(),this.removeAllListeners(),this._autoLevelCapping=-1,this.url=null,this.networkControllers.forEach(function(f){return f.destroy()}),this.networkControllers.length=0,this.coreComponents.forEach(function(f){return f.destroy()}),this.coreComponents.length=0},p.attachMedia=function(f){h.logger.log("attachMedia"),this._media=f,this.trigger(d.Events.MEDIA_ATTACHING,{media:f})},p.detachMedia=function(){h.logger.log("detachMedia"),this.trigger(d.Events.MEDIA_DETACHING,void 0),this._media=null},p.loadSource=function(f){this.stopLoad();var v=this.media,r=this.url,i=this.url=R.buildAbsoluteURL(self.location.href,f,{alwaysNormalize:!0});h.logger.log("loadSource:"+i),v&&r&&r!==i&&this.bufferController.hasSourceTypes()&&(this.detachMedia(),this.attachMedia(v)),this.trigger(d.Events.MANIFEST_LOADING,{url:f})},p.startLoad=function(f){f===void 0&&(f=-1),h.logger.log("startLoad("+f+")"),this.networkControllers.forEach(function(v){v.startLoad(f)})},p.stopLoad=function(){h.logger.log("stopLoad"),this.networkControllers.forEach(function(f){f.stopLoad()})},p.swapAudioCodec=function(){h.logger.log("swapAudioCodec"),this.streamController.swapAudioCodec()},p.recoverMediaError=function(){h.logger.log("recoverMediaError");var f=this._media;this.detachMedia(),f&&this.attachMedia(f)},p.removeLevel=function(f,v){v===void 0&&(v=0),this.levelController.removeLevel(f,v)},u=o,l=[{key:"version",get:function(){return"1.0.10"}},{key:"Events",get:function(){return d.Events}},{key:"ErrorTypes",get:function(){return e.ErrorTypes}},{key:"ErrorDetails",get:function(){return e.ErrorDetails}},{key:"DefaultConfig",get:function(){return o.defaultConfig?o.defaultConfig:T.hlsDefaultConfig},set:function(f){o.defaultConfig=f}}],(n=[{key:"levels",get:function(){var f=this.levelController.levels;return f||[]}},{key:"currentLevel",get:function(){return this.streamController.currentLevel},set:function(f){h.logger.log("set currentLevel:"+f),this.loadLevel=f,this.abrController.clearTimer(),this.streamController.immediateLevelSwitch()}},{key:"nextLevel",get:function(){return this.streamController.nextLevel},set:function(f){h.logger.log("set nextLevel:"+f),this.levelController.manualLevel=f,this.streamController.nextLevelSwitch()}},{key:"loadLevel",get:function(){return this.levelController.level},set:function(f){h.logger.log("set loadLevel:"+f),this.levelController.manualLevel=f}},{key:"nextLoadLevel",get:function(){return this.levelController.nextLoadLevel},set:function(f){this.levelController.nextLoadLevel=f}},{key:"firstLevel",get:function(){return Math.max(this.levelController.firstLevel,this.minAutoLevel)},set:function(f){h.logger.log("set firstLevel:"+f),this.levelController.firstLevel=f}},{key:"startLevel",get:function(){return this.levelController.startLevel},set:function(f){h.logger.log("set startLevel:"+f),f!==-1&&(f=Math.max(f,this.minAutoLevel)),this.levelController.startLevel=f}},{key:"capLevelToPlayerSize",get:function(){return this.config.capLevelToPlayerSize},set:function(f){var v=!!f;v!==this.config.capLevelToPlayerSize&&(v?this.capLevelController.startCapping():(this.capLevelController.stopCapping(),this.autoLevelCapping=-1,this.streamController.nextLevelSwitch()),this.config.capLevelToPlayerSize=v)}},{key:"autoLevelCapping",get:function(){return this._autoLevelCapping},set:function(f){this._autoLevelCapping!==f&&(h.logger.log("set autoLevelCapping:"+f),this._autoLevelCapping=f)}},{key:"bandwidthEstimate",get:function(){var f=this.abrController.bwEstimator;return f?f.getEstimate():NaN}},{key:"autoLevelEnabled",get:function(){return this.levelController.manualLevel===-1}},{key:"manualLevel",get:function(){return this.levelController.manualLevel}},{key:"minAutoLevel",get:function(){var f=this.levels,v=this.config.minAutoBitrate;if(!f)return 0;for(var r=f.length,i=0;i<r;i++)if(f[i].maxBitrate>v)return i;return 0}},{key:"maxAutoLevel",get:function(){var f=this.levels,v=this.autoLevelCapping;return v===-1&&f&&f.length?f.length-1:v}},{key:"nextAutoLevel",get:function(){return Math.min(Math.max(this.abrController.nextAutoLevel,this.minAutoLevel),this.maxAutoLevel)},set:function(f){this.abrController.nextAutoLevel=Math.max(this.minAutoLevel,f)}},{key:"audioTracks",get:function(){var f=this.audioTrackController;return f?f.audioTracks:[]}},{key:"audioTrack",get:function(){var f=this.audioTrackController;return f?f.audioTrack:-1},set:function(f){var v=this.audioTrackController;v&&(v.audioTrack=f)}},{key:"subtitleTracks",get:function(){var f=this.subtitleTrackController;return f?f.subtitleTracks:[]}},{key:"subtitleTrack",get:function(){var f=this.subtitleTrackController;return f?f.subtitleTrack:-1},set:function(f){var v=this.subtitleTrackController;v&&(v.subtitleTrack=f)}},{key:"media",get:function(){return this._media}},{key:"subtitleDisplay",get:function(){var f=this.subtitleTrackController;return!!f&&f.subtitleDisplay},set:function(f){var v=this.subtitleTrackController;v&&(v.subtitleDisplay=f)}},{key:"lowLatencyMode",get:function(){return this.config.lowLatencyMode},set:function(f){this.config.lowLatencyMode=f}},{key:"liveSyncPosition",get:function(){return this.latencyController.liveSyncPosition}},{key:"latency",get:function(){return this.latencyController.latency}},{key:"maxLatency",get:function(){return this.latencyController.maxLatency}},{key:"targetLatency",get:function(){return this.latencyController.targetLatency}},{key:"drift",get:function(){return this.latencyController.drift}},{key:"forceStartLoad",get:function(){return this.streamController.forceStartLoad}}])&&a(u.prototype,n),l&&a(u,l),o}();t.defaultConfig=void 0},"./src/is-supported.ts":function(M,I,g){g.r(I),g.d(I,"isSupported",function(){return A}),g.d(I,"changeTypeSupported",function(){return k});var R=g("./src/utils/mediasource-helper.ts");function E(){return self.SourceBuffer||self.WebKitSourceBuffer}function A(){var C=Object(R.getMediaSource)();if(!C)return!1;var D=E(),s=C&&typeof C.isTypeSupported=="function"&&C.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'),b=!D||D.prototype&&typeof D.prototype.appendBuffer=="function"&&typeof D.prototype.remove=="function";return!!s&&!!b}function k(){var C,D=E();return typeof(D==null||(C=D.prototype)===null||C===void 0?void 0:C.changeType)=="function"}},"./src/loader/fragment-loader.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return m}),g.d(I,"LoadError",function(){return T});var R=g("./src/polyfills/number.ts"),E=g("./src/errors.ts");function A(y){var d=typeof Map=="function"?new Map:void 0;return(A=function(e){if(e===null||(a=e,Function.toString.call(a).indexOf("[native code]")===-1))return e;var a;if(typeof e!="function")throw new TypeError("Super expression must either be null or a function");if(d!==void 0){if(d.has(e))return d.get(e);d.set(e,t)}function t(){return k(e,arguments,s(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),D(t,e)})(y)}function k(y,d,e){return(k=C()?Reflect.construct:function(a,t,o){var u=[null];u.push.apply(u,t);var n=new(Function.bind.apply(a,u));return o&&D(n,o.prototype),n}).apply(null,arguments)}function C(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function D(y,d){return(D=Object.setPrototypeOf||function(e,a){return e.__proto__=a,e})(y,d)}function s(y){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(d){return d.__proto__||Object.getPrototypeOf(d)})(y)}var b=Math.pow(2,17),m=function(){function y(e){this.config=void 0,this.loader=null,this.partLoadTimeout=-1,this.config=e}var d=y.prototype;return d.destroy=function(){this.loader&&(this.loader.destroy(),this.loader=null)},d.abort=function(){this.loader&&this.loader.abort()},d.load=function(e,a){var t=this,o=e.url;if(!o)return Promise.reject(new T({type:E.ErrorTypes.NETWORK_ERROR,details:E.ErrorDetails.FRAG_LOAD_ERROR,fatal:!1,frag:e,networkDetails:null},"Fragment does not have a "+(o?"part list":"url")));this.abort();var u=this.config,n=u.fLoader,l=u.loader;return new Promise(function(p,f){t.loader&&t.loader.destroy();var v=t.loader=e.loader=n?new n(u):new l(u),r=h(e),i={timeout:u.fragLoadingTimeOut,maxRetry:0,retryDelay:0,maxRetryDelay:u.fragLoadingMaxRetryTimeout,highWaterMark:b};e.stats=v.stats,v.load(r,i,{onSuccess:function(c,S,L,_){t.resetLoader(e,v),p({frag:e,part:null,payload:c.data,networkDetails:_})},onError:function(c,S,L){t.resetLoader(e,v),f(new T({type:E.ErrorTypes.NETWORK_ERROR,details:E.ErrorDetails.FRAG_LOAD_ERROR,fatal:!1,frag:e,response:c,networkDetails:L}))},onAbort:function(c,S,L){t.resetLoader(e,v),f(new T({type:E.ErrorTypes.NETWORK_ERROR,details:E.ErrorDetails.INTERNAL_ABORTED,fatal:!1,frag:e,networkDetails:L}))},onTimeout:function(c,S,L){t.resetLoader(e,v),f(new T({type:E.ErrorTypes.NETWORK_ERROR,details:E.ErrorDetails.FRAG_LOAD_TIMEOUT,fatal:!1,frag:e,networkDetails:L}))},onProgress:function(c,S,L,_){a&&a({frag:e,part:null,payload:L,networkDetails:_})}})})},d.loadPart=function(e,a,t){var o=this;this.abort();var u=this.config,n=u.fLoader,l=u.loader;return new Promise(function(p,f){o.loader&&o.loader.destroy();var v=o.loader=e.loader=n?new n(u):new l(u),r=h(e,a),i={timeout:u.fragLoadingTimeOut,maxRetry:0,retryDelay:0,maxRetryDelay:u.fragLoadingMaxRetryTimeout,highWaterMark:b};a.stats=v.stats,v.load(r,i,{onSuccess:function(c,S,L,_){o.resetLoader(e,v),o.updateStatsFromPart(e,a);var x={frag:e,part:a,payload:c.data,networkDetails:_};t(x),p(x)},onError:function(c,S,L){o.resetLoader(e,v),f(new T({type:E.ErrorTypes.NETWORK_ERROR,details:E.ErrorDetails.FRAG_LOAD_ERROR,fatal:!1,frag:e,part:a,response:c,networkDetails:L}))},onAbort:function(c,S,L){e.stats.aborted=a.stats.aborted,o.resetLoader(e,v),f(new T({type:E.ErrorTypes.NETWORK_ERROR,details:E.ErrorDetails.INTERNAL_ABORTED,fatal:!1,frag:e,part:a,networkDetails:L}))},onTimeout:function(c,S,L){o.resetLoader(e,v),f(new T({type:E.ErrorTypes.NETWORK_ERROR,details:E.ErrorDetails.FRAG_LOAD_TIMEOUT,fatal:!1,frag:e,part:a,networkDetails:L}))}})})},d.updateStatsFromPart=function(e,a){var t=e.stats,o=a.stats,u=o.total;if(t.loaded+=o.loaded,u){var n=Math.round(e.duration/a.duration),l=Math.min(Math.round(t.loaded/u),n),p=(n-l)*Math.round(t.loaded/l);t.total=t.loaded+p}else t.total=Math.max(t.loaded,t.total);var f=t.loading,v=o.loading;f.start?f.first+=v.first-v.start:(f.start=v.start,f.first=v.first),f.end=v.end},d.resetLoader=function(e,a){e.loader=null,this.loader===a&&(self.clearTimeout(this.partLoadTimeout),this.loader=null),a.destroy()},y}();function h(y,d){d===void 0&&(d=null);var e=d||y,a={frag:y,part:d,responseType:"arraybuffer",url:e.url,rangeStart:0,rangeEnd:0},t=e.byteRangeStartOffset,o=e.byteRangeEndOffset;return Object(R.isFiniteNumber)(t)&&Object(R.isFiniteNumber)(o)&&(a.rangeStart=t,a.rangeEnd=o),a}var T=function(y){var d,e;function a(t){for(var o,u=arguments.length,n=new Array(u>1?u-1:0),l=1;l<u;l++)n[l-1]=arguments[l];return(o=y.call.apply(y,[this].concat(n))||this).data=void 0,o.data=t,o}return e=y,(d=a).prototype=Object.create(e.prototype),d.prototype.constructor=d,D(d,e),a}(A(Error))},"./src/loader/fragment.ts":function(M,I,g){g.r(I),g.d(I,"ElementaryStreamTypes",function(){return R}),g.d(I,"BaseSegment",function(){return y}),g.d(I,"Fragment",function(){return d}),g.d(I,"Part",function(){return e});var R,E,A=g("./src/polyfills/number.ts"),k=g("./node_modules/url-toolkit/src/url-toolkit.js"),C=g("./src/utils/logger.ts"),D=g("./src/loader/level-key.ts"),s=g("./src/loader/load-stats.ts");function b(a,t){a.prototype=Object.create(t.prototype),a.prototype.constructor=a,m(a,t)}function m(a,t){return(m=Object.setPrototypeOf||function(o,u){return o.__proto__=u,o})(a,t)}function h(a,t){for(var o=0;o<t.length;o++){var u=t[o];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(a,u.key,u)}}function T(a,t,o){return t&&h(a.prototype,t),a}(E=R||(R={})).AUDIO="audio",E.VIDEO="video",E.AUDIOVIDEO="audiovideo";var y=function(){function a(t){var o;this._byteRange=null,this._url=null,this.baseurl=void 0,this.relurl=void 0,this.elementaryStreams=((o={})[R.AUDIO]=null,o[R.VIDEO]=null,o[R.AUDIOVIDEO]=null,o),this.baseurl=t}return a.prototype.setByteRange=function(t,o){var u=t.split("@",2),n=[];u.length===1?n[0]=o?o.byteRangeEndOffset:0:n[0]=parseInt(u[1]),n[1]=parseInt(u[0])+n[0],this._byteRange=n},T(a,[{key:"byteRange",get:function(){return this._byteRange?this._byteRange:[]}},{key:"byteRangeStartOffset",get:function(){return this.byteRange[0]}},{key:"byteRangeEndOffset",get:function(){return this.byteRange[1]}},{key:"url",get:function(){return!this._url&&this.baseurl&&this.relurl&&(this._url=Object(k.buildAbsoluteURL)(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url||""},set:function(t){this._url=t}}]),a}(),d=function(a){function t(u,n){var l;return(l=a.call(this,n)||this)._decryptdata=null,l.rawProgramDateTime=null,l.programDateTime=null,l.tagList=[],l.duration=0,l.sn=0,l.levelkey=void 0,l.type=void 0,l.loader=null,l.level=-1,l.cc=0,l.startPTS=void 0,l.endPTS=void 0,l.appendedPTS=void 0,l.startDTS=void 0,l.endDTS=void 0,l.start=0,l.deltaPTS=void 0,l.maxStartPTS=void 0,l.minEndPTS=void 0,l.stats=new s.LoadStats,l.urlId=0,l.data=void 0,l.bitrateTest=!1,l.title=null,l.initSegment=null,l.type=u,l}b(t,a);var o=t.prototype;return o.createInitializationVector=function(u){for(var n=new Uint8Array(16),l=12;l<16;l++)n[l]=u>>8*(15-l)&255;return n},o.setDecryptDataFromLevelKey=function(u,n){var l=u;return(u==null?void 0:u.method)==="AES-128"&&u.uri&&!u.iv&&((l=D.LevelKey.fromURI(u.uri)).method=u.method,l.iv=this.createInitializationVector(n),l.keyFormat="identity"),l},o.setElementaryStreamInfo=function(u,n,l,p,f,v){v===void 0&&(v=!1);var r=this.elementaryStreams,i=r[u];i?(i.startPTS=Math.min(i.startPTS,n),i.endPTS=Math.max(i.endPTS,l),i.startDTS=Math.min(i.startDTS,p),i.endDTS=Math.max(i.endDTS,f)):r[u]={startPTS:n,endPTS:l,startDTS:p,endDTS:f,partial:v}},o.clearElementaryStreamInfo=function(){var u=this.elementaryStreams;u[R.AUDIO]=null,u[R.VIDEO]=null,u[R.AUDIOVIDEO]=null},T(t,[{key:"decryptdata",get:function(){if(!this.levelkey&&!this._decryptdata)return null;if(!this._decryptdata&&this.levelkey){var u=this.sn;typeof u!="number"&&(this.levelkey&&this.levelkey.method==="AES-128"&&!this.levelkey.iv&&C.logger.warn('missing IV for initialization segment with method="'+this.levelkey.method+'" - compliance issue'),u=0),this._decryptdata=this.setDecryptDataFromLevelKey(this.levelkey,u)}return this._decryptdata}},{key:"end",get:function(){return this.start+this.duration}},{key:"endProgramDateTime",get:function(){if(this.programDateTime===null||!Object(A.isFiniteNumber)(this.programDateTime))return null;var u=Object(A.isFiniteNumber)(this.duration)?this.duration:0;return this.programDateTime+1e3*u}},{key:"encrypted",get:function(){var u;return!((u=this.decryptdata)===null||u===void 0||!u.keyFormat||!this.decryptdata.uri)}}]),t}(y),e=function(a){function t(o,u,n,l,p){var f;(f=a.call(this,n)||this).fragOffset=0,f.duration=0,f.gap=!1,f.independent=!1,f.relurl=void 0,f.fragment=void 0,f.index=void 0,f.stats=new s.LoadStats,f.duration=o.decimalFloatingPoint("DURATION"),f.gap=o.bool("GAP"),f.independent=o.bool("INDEPENDENT"),f.relurl=o.enumeratedString("URI"),f.fragment=u,f.index=l;var v=o.enumeratedString("BYTERANGE");return v&&f.setByteRange(v,p),p&&(f.fragOffset=p.fragOffset+p.duration),f}return b(t,a),T(t,[{key:"start",get:function(){return this.fragment.start+this.fragOffset}},{key:"end",get:function(){return this.start+this.duration}},{key:"loaded",get:function(){var o=this.elementaryStreams;return!!(o.audio||o.video||o.audiovideo)}}]),t}(y)},"./src/loader/key-loader.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return k});var R=g("./src/events.ts"),E=g("./src/errors.ts"),A=g("./src/utils/logger.ts"),k=function(){function C(s){this.hls=void 0,this.loaders={},this.decryptkey=null,this.decrypturl=null,this.hls=s,this._registerListeners()}var D=C.prototype;return D._registerListeners=function(){this.hls.on(R.Events.KEY_LOADING,this.onKeyLoading,this)},D._unregisterListeners=function(){this.hls.off(R.Events.KEY_LOADING,this.onKeyLoading)},D.destroy=function(){for(var s in this._unregisterListeners(),this.loaders){var b=this.loaders[s];b&&b.destroy()}this.loaders={}},D.onKeyLoading=function(s,b){var m=b.frag,h=m.type,T=this.loaders[h];if(m.decryptdata){var y=m.decryptdata.uri;if(y!==this.decrypturl||this.decryptkey===null){var d=this.hls.config;if(T&&(A.logger.warn("abort previous key loader for type:"+h),T.abort()),!y)return void A.logger.warn("key uri is falsy");var e=d.loader,a=m.loader=this.loaders[h]=new e(d);this.decrypturl=y,this.decryptkey=null;var t={url:y,frag:m,responseType:"arraybuffer"},o={timeout:d.fragLoadingTimeOut,maxRetry:0,retryDelay:d.fragLoadingRetryDelay,maxRetryDelay:d.fragLoadingMaxRetryTimeout,highWaterMark:0},u={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)};a.load(t,o,u)}else this.decryptkey&&(m.decryptdata.key=this.decryptkey,this.hls.trigger(R.Events.KEY_LOADED,{frag:m}))}else A.logger.warn("Missing decryption data on fragment in onKeyLoading")},D.loadsuccess=function(s,b,m){var h=m.frag;h.decryptdata?(this.decryptkey=h.decryptdata.key=new Uint8Array(s.data),h.loader=null,delete this.loaders[h.type],this.hls.trigger(R.Events.KEY_LOADED,{frag:h})):A.logger.error("after key load, decryptdata unset")},D.loaderror=function(s,b){var m=b.frag,h=m.loader;h&&h.abort(),delete this.loaders[m.type],this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.NETWORK_ERROR,details:E.ErrorDetails.KEY_LOAD_ERROR,fatal:!1,frag:m,response:s})},D.loadtimeout=function(s,b){var m=b.frag,h=m.loader;h&&h.abort(),delete this.loaders[m.type],this.hls.trigger(R.Events.ERROR,{type:E.ErrorTypes.NETWORK_ERROR,details:E.ErrorDetails.KEY_LOAD_TIMEOUT,fatal:!1,frag:m})},C}()},"./src/loader/level-details.ts":function(M,I,g){g.r(I),g.d(I,"LevelDetails",function(){return A});var R=g("./src/polyfills/number.ts");function E(k,C){for(var D=0;D<C.length;D++){var s=C[D];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(k,s.key,s)}}var A=function(){function k(s){this.PTSKnown=!1,this.alignedSliding=!1,this.averagetargetduration=void 0,this.endCC=0,this.endSN=0,this.fragments=void 0,this.fragmentHint=void 0,this.partList=null,this.live=!0,this.ageHeader=0,this.advancedDateTime=void 0,this.updated=!0,this.advanced=!0,this.availabilityDelay=void 0,this.misses=0,this.needSidxRanges=!1,this.startCC=0,this.startSN=0,this.startTimeOffset=null,this.targetduration=0,this.totalduration=0,this.type=null,this.url=void 0,this.m3u8="",this.version=null,this.canBlockReload=!1,this.canSkipUntil=0,this.canSkipDateRanges=!1,this.skippedSegments=0,this.recentlyRemovedDateranges=void 0,this.partHoldBack=0,this.holdBack=0,this.partTarget=0,this.preloadHint=void 0,this.renditionReports=void 0,this.tuneInGoal=0,this.deltaUpdateFailed=void 0,this.driftStartTime=0,this.driftEndTime=0,this.driftStart=0,this.driftEnd=0,this.fragments=[],this.url=s}var C,D;return k.prototype.reloaded=function(s){if(!s)return this.advanced=!0,void(this.updated=!0);var b=this.lastPartSn-s.lastPartSn,m=this.lastPartIndex-s.lastPartIndex;this.updated=this.endSN!==s.endSN||!!m||!!b,this.advanced=this.endSN>s.endSN||b>0||b===0&&m>0,this.updated||this.advanced?this.misses=Math.floor(.6*s.misses):this.misses=s.misses+1,this.availabilityDelay=s.availabilityDelay},C=k,(D=[{key:"hasProgramDateTime",get:function(){return!!this.fragments.length&&Object(R.isFiniteNumber)(this.fragments[this.fragments.length-1].programDateTime)}},{key:"levelTargetDuration",get:function(){return this.averagetargetduration||this.targetduration||10}},{key:"drift",get:function(){var s=this.driftEndTime-this.driftStartTime;return s>0?1e3*(this.driftEnd-this.driftStart)/s:1}},{key:"edge",get:function(){return this.partEnd||this.fragmentEnd}},{key:"partEnd",get:function(){var s;return(s=this.partList)!==null&&s!==void 0&&s.length?this.partList[this.partList.length-1].end:this.fragmentEnd}},{key:"fragmentEnd",get:function(){var s;return(s=this.fragments)!==null&&s!==void 0&&s.length?this.fragments[this.fragments.length-1].end:0}},{key:"age",get:function(){return this.advancedDateTime?Math.max(Date.now()-this.advancedDateTime,0)/1e3:0}},{key:"lastPartIndex",get:function(){var s;return(s=this.partList)!==null&&s!==void 0&&s.length?this.partList[this.partList.length-1].index:-1}},{key:"lastPartSn",get:function(){var s;return(s=this.partList)!==null&&s!==void 0&&s.length?this.partList[this.partList.length-1].fragment.sn:this.endSN}}])&&E(C.prototype,D),k}()},"./src/loader/level-key.ts":function(M,I,g){g.r(I),g.d(I,"LevelKey",function(){return A});var R=g("./node_modules/url-toolkit/src/url-toolkit.js");function E(k,C){for(var D=0;D<C.length;D++){var s=C[D];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(k,s.key,s)}}var A=function(){function k(s,b){this._uri=null,this.method=null,this.keyFormat=null,this.keyFormatVersions=null,this.keyID=null,this.key=null,this.iv=null,this._uri=b?Object(R.buildAbsoluteURL)(s,b,{alwaysNormalize:!0}):s}var C,D;return k.fromURL=function(s,b){return new k(s,b)},k.fromURI=function(s){return new k(s)},C=k,(D=[{key:"uri",get:function(){return this._uri}}])&&E(C.prototype,D),k}()},"./src/loader/load-stats.ts":function(M,I,g){g.r(I),g.d(I,"LoadStats",function(){return R});var R=function(){this.aborted=!1,this.loaded=0,this.retry=0,this.total=0,this.chunkCount=0,this.bwEstimate=0,this.loading={start:0,first:0,end:0},this.parsing={start:0,end:0},this.buffering={start:0,first:0,end:0}}},"./src/loader/m3u8-parser.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return e});var R=g("./src/polyfills/number.ts"),E=g("./node_modules/url-toolkit/src/url-toolkit.js"),A=g("./src/loader/fragment.ts"),k=g("./src/loader/level-details.ts"),C=g("./src/loader/level-key.ts"),D=g("./src/utils/attr-list.ts"),s=g("./src/utils/logger.ts"),b=g("./src/utils/codecs.ts"),m=/#EXT-X-STREAM-INF:([^\r\n]*)(?:[\r\n](?:#[^\r\n]*)?)*([^\r\n]+)|#EXT-X-SESSION-DATA:([^\r\n]*)[\r\n]+/g,h=/#EXT-X-MEDIA:(.*)/g,T=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/(?!#) *(\S[\S ]*)/.source,/#EXT-X-BYTERANGE:*(.+)/.source,/#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/#.*/.source].join("|"),"g"),y=new RegExp([/#(EXTM3U)/.source,/#EXT-X-(PLAYLIST-TYPE):(.+)/.source,/#EXT-X-(MEDIA-SEQUENCE): *(\d+)/.source,/#EXT-X-(SKIP):(.+)/.source,/#EXT-X-(TARGETDURATION): *(\d+)/.source,/#EXT-X-(KEY):(.+)/.source,/#EXT-X-(START):(.+)/.source,/#EXT-X-(ENDLIST)/.source,/#EXT-X-(DISCONTINUITY-SEQ)UENCE: *(\d+)/.source,/#EXT-X-(DIS)CONTINUITY/.source,/#EXT-X-(VERSION):(\d+)/.source,/#EXT-X-(MAP):(.+)/.source,/#EXT-X-(SERVER-CONTROL):(.+)/.source,/#EXT-X-(PART-INF):(.+)/.source,/#EXT-X-(GAP)/.source,/#EXT-X-(BITRATE):\s*(\d+)/.source,/#EXT-X-(PART):(.+)/.source,/#EXT-X-(PRELOAD-HINT):(.+)/.source,/#EXT-X-(RENDITION-REPORT):(.+)/.source,/(#)([^:]*):(.*)/.source,/(#)(.*)(?:.*)\r?\n?/.source].join("|")),d=/\.(mp4|m4s|m4v|m4a)$/i,e=function(){function u(){}return u.findGroup=function(n,l){for(var p=0;p<n.length;p++){var f=n[p];if(f.id===l)return f}},u.convertAVC1ToAVCOTI=function(n){var l=n.split(".");if(l.length>2){var p=l.shift()+".";return p+=parseInt(l.shift()).toString(16),p+=("000"+parseInt(l.shift()).toString(16)).substr(-4)}return n},u.resolve=function(n,l){return E.buildAbsoluteURL(l,n,{alwaysNormalize:!0})},u.parseMasterPlaylist=function(n,l){var p,f=[],v={},r=!1;for(m.lastIndex=0;(p=m.exec(n))!=null;)if(p[1]){var i=new D.AttrList(p[1]),c={attrs:i,bitrate:i.decimalInteger("AVERAGE-BANDWIDTH")||i.decimalInteger("BANDWIDTH"),name:i.NAME,url:u.resolve(p[2],l)},S=i.decimalResolution("RESOLUTION");S&&(c.width=S.width,c.height=S.height),a((i.CODECS||"").split(/[ ,]+/).filter(function(_){return _}),c),c.videoCodec&&c.videoCodec.indexOf("avc1")!==-1&&(c.videoCodec=u.convertAVC1ToAVCOTI(c.videoCodec)),f.push(c)}else if(p[3]){var L=new D.AttrList(p[3]);L["DATA-ID"]&&(r=!0,v[L["DATA-ID"]]=L)}return{levels:f,sessionData:r?v:null}},u.parseMasterPlaylistMedia=function(n,l,p,f){var v;f===void 0&&(f=[]);var r=[],i=0;for(h.lastIndex=0;(v=h.exec(n))!==null;){var c=new D.AttrList(v[1]);if(c.TYPE===p){var S={attrs:c,bitrate:0,id:i++,groupId:c["GROUP-ID"],instreamId:c["INSTREAM-ID"],name:c.NAME||c.LANGUAGE||"",type:p,default:c.bool("DEFAULT"),autoselect:c.bool("AUTOSELECT"),forced:c.bool("FORCED"),lang:c.LANGUAGE,url:c.URI?u.resolve(c.URI,l):""};if(f.length){var L=u.findGroup(f,S.groupId)||f[0];t(S,L,"audioCodec"),t(S,L,"textCodec")}r.push(S)}}return r},u.parseLevelPlaylist=function(n,l,p,f,v){var r,i,c,S=new k.LevelDetails(l),L=S.fragments,_=null,x=0,w=0,O=0,P=0,F=null,N=new A.Fragment(f,l),U=-1,G=!1;for(T.lastIndex=0,S.m3u8=n;(r=T.exec(n))!==null;){G&&(G=!1,(N=new A.Fragment(f,l)).start=O,N.sn=x,N.cc=P,N.level=p,_&&(N.initSegment=_,N.rawProgramDateTime=_.rawProgramDateTime));var K=r[1];if(K){N.duration=parseFloat(K);var q=(" "+r[2]).slice(1);N.title=q||null,N.tagList.push(q?["INF",K,q]:["INF",K])}else if(r[3])Object(R.isFiniteNumber)(N.duration)&&(N.start=O,c&&(N.levelkey=c),N.sn=x,N.level=p,N.cc=P,N.urlId=v,L.push(N),N.relurl=(" "+r[3]).slice(1),o(N,F),F=N,O+=N.duration,x++,w=0,G=!0);else if(r[4]){var Y=(" "+r[4]).slice(1);F?N.setByteRange(Y,F):N.setByteRange(Y)}else if(r[5])N.rawProgramDateTime=(" "+r[5]).slice(1),N.tagList.push(["PROGRAM-DATE-TIME",N.rawProgramDateTime]),U===-1&&(U=L.length);else{if(!(r=r[0].match(y))){s.logger.warn("No matches on slow regex match for level playlist!");continue}for(i=1;i<r.length&&r[i]===void 0;i++);var j=(" "+r[i]).slice(1),X=(" "+r[i+1]).slice(1),Q=r[i+2]?(" "+r[i+2]).slice(1):"";switch(j){case"PLAYLIST-TYPE":S.type=X.toUpperCase();break;case"MEDIA-SEQUENCE":x=S.startSN=parseInt(X);break;case"SKIP":var te=new D.AttrList(X),ae=te.decimalInteger("SKIPPED-SEGMENTS");if(Object(R.isFiniteNumber)(ae)){S.skippedSegments=ae;for(var oe=ae;oe--;)L.unshift(null);x+=ae}var ie=te.enumeratedString("RECENTLY-REMOVED-DATERANGES");ie&&(S.recentlyRemovedDateranges=ie.split("	"));break;case"TARGETDURATION":S.targetduration=parseFloat(X);break;case"VERSION":S.version=parseInt(X);break;case"EXTM3U":break;case"ENDLIST":S.live=!1;break;case"#":(X||Q)&&N.tagList.push(Q?[X,Q]:[X]);break;case"DIS":P++;case"GAP":N.tagList.push([j]);break;case"BITRATE":N.tagList.push([j,X]);break;case"DISCONTINUITY-SEQ":P=parseInt(X);break;case"KEY":var ue,J=new D.AttrList(X),ne=J.enumeratedString("METHOD"),le=J.URI,se=J.hexadecimalInteger("IV"),pe=J.enumeratedString("KEYFORMATVERSIONS"),me=J.enumeratedString("KEYID"),Se=(ue=J.enumeratedString("KEYFORMAT"))!=null?ue:"identity";if(["com.apple.streamingkeydelivery","com.microsoft.playready","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed","com.widevine"].indexOf(Se)>-1){s.logger.warn("Keyformat "+Se+" is not supported from the manifest");continue}if(Se!=="identity")continue;ne&&(c=C.LevelKey.fromURL(l,le),le&&["AES-128","SAMPLE-AES","SAMPLE-AES-CENC"].indexOf(ne)>=0&&(c.method=ne,c.keyFormat=Se,me&&(c.keyID=me),pe&&(c.keyFormatVersions=pe),c.iv=se));break;case"START":var be=new D.AttrList(X).decimalFloatingPoint("TIME-OFFSET");Object(R.isFiniteNumber)(be)&&(S.startTimeOffset=be);break;case"MAP":var fe=new D.AttrList(X);N.relurl=fe.URI,fe.BYTERANGE&&N.setByteRange(fe.BYTERANGE),N.level=p,N.sn="initSegment",c&&(N.levelkey=c),N.initSegment=null,_=N,G=!0;break;case"SERVER-CONTROL":var ge=new D.AttrList(X);S.canBlockReload=ge.bool("CAN-BLOCK-RELOAD"),S.canSkipUntil=ge.optionalFloat("CAN-SKIP-UNTIL",0),S.canSkipDateRanges=S.canSkipUntil>0&&ge.bool("CAN-SKIP-DATERANGES"),S.partHoldBack=ge.optionalFloat("PART-HOLD-BACK",0),S.holdBack=ge.optionalFloat("HOLD-BACK",0);break;case"PART-INF":var we=new D.AttrList(X);S.partTarget=we.decimalFloatingPoint("PART-TARGET");break;case"PART":var Ee=S.partList;Ee||(Ee=S.partList=[]);var Re=w>0?Ee[Ee.length-1]:void 0,Ne=w++,xe=new A.Part(new D.AttrList(X),N,l,Ne,Re);Ee.push(xe),N.duration+=xe.duration;break;case"PRELOAD-HINT":var H=new D.AttrList(X);S.preloadHint=H;break;case"RENDITION-REPORT":var de=new D.AttrList(X);S.renditionReports=S.renditionReports||[],S.renditionReports.push(de);break;default:s.logger.warn("line parsed but not handled: "+r)}}}F&&!F.relurl?(L.pop(),O-=F.duration,S.partList&&(S.fragmentHint=F)):S.partList&&(o(N,F),N.cc=P,S.fragmentHint=N);var Oe=L.length,Pe=L[0],Ze=L[Oe-1];if((O+=S.skippedSegments*S.targetduration)>0&&Oe&&Ze){S.averagetargetduration=O/Oe;var qe=Ze.sn;S.endSN=qe!=="initSegment"?qe:0,Pe&&(S.startCC=Pe.cc,Pe.initSegment||S.fragments.every(function(Ge){return Ge.relurl&&(Be=Ge.relurl,d.test((je=(Ce=E.parseURL(Be))===null||Ce===void 0?void 0:Ce.path)!=null?je:""));var Be,je,Ce})&&(s.logger.warn("MP4 fragments found but no init segment (probably no MAP, incomplete M3U8), trying to fetch SIDX"),(N=new A.Fragment(f,l)).relurl=Ze.relurl,N.level=p,N.sn="initSegment",Pe.initSegment=N,S.needSidxRanges=!0))}else S.endSN=0,S.startCC=0;return S.fragmentHint&&(O+=S.fragmentHint.duration),S.totalduration=O,S.endCC=P,U>0&&function(Ge,Be){for(var je=Ge[Be],Ce=Be;Ce--;){var Ve=Ge[Ce];if(!Ve)return;Ve.programDateTime=je.programDateTime-1e3*Ve.duration,je=Ve}}(L,U),S},u}();function a(u,n){["video","audio","text"].forEach(function(l){var p=u.filter(function(v){return Object(b.isCodecType)(v,l)});if(p.length){var f=p.filter(function(v){return v.lastIndexOf("avc1",0)===0||v.lastIndexOf("mp4a",0)===0});n[l+"Codec"]=f.length>0?f[0]:p[0],u=u.filter(function(v){return p.indexOf(v)===-1})}}),n.unknownCodecs=u}function t(u,n,l){var p=n[l];p&&(u[l]=p)}function o(u,n){u.rawProgramDateTime?u.programDateTime=Date.parse(u.rawProgramDateTime):n!=null&&n.programDateTime&&(u.programDateTime=n.endProgramDateTime),Object(R.isFiniteNumber)(u.programDateTime)||(u.programDateTime=null,u.rawProgramDateTime=null)}},"./src/loader/playlist-loader.ts":function(M,I,g){g.r(I);var R=g("./src/polyfills/number.ts"),E=g("./src/events.ts"),A=g("./src/errors.ts"),k=g("./src/utils/logger.ts"),C=g("./src/utils/mp4-tools.ts"),D=g("./src/loader/m3u8-parser.ts"),s=g("./src/types/loader.ts"),b=g("./src/utils/attr-list.ts");function m(T,y){var d=T.url;return d!==void 0&&d.indexOf("data:")!==0||(d=y.url),d}var h=function(){function T(d){this.hls=void 0,this.loaders=Object.create(null),this.hls=d,this.registerListeners()}var y=T.prototype;return y.registerListeners=function(){var d=this.hls;d.on(E.Events.MANIFEST_LOADING,this.onManifestLoading,this),d.on(E.Events.LEVEL_LOADING,this.onLevelLoading,this),d.on(E.Events.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),d.on(E.Events.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)},y.unregisterListeners=function(){var d=this.hls;d.off(E.Events.MANIFEST_LOADING,this.onManifestLoading,this),d.off(E.Events.LEVEL_LOADING,this.onLevelLoading,this),d.off(E.Events.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),d.off(E.Events.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)},y.createInternalLoader=function(d){var e=this.hls.config,a=e.pLoader,t=e.loader,o=new(a||t)(e);return d.loader=o,this.loaders[d.type]=o,o},y.getInternalLoader=function(d){return this.loaders[d.type]},y.resetInternalLoader=function(d){this.loaders[d]&&delete this.loaders[d]},y.destroyInternalLoaders=function(){for(var d in this.loaders){var e=this.loaders[d];e&&e.destroy(),this.resetInternalLoader(d)}},y.destroy=function(){this.unregisterListeners(),this.destroyInternalLoaders()},y.onManifestLoading=function(d,e){var a=e.url;this.load({id:null,groupId:null,level:0,responseType:"text",type:s.PlaylistContextType.MANIFEST,url:a,deliveryDirectives:null})},y.onLevelLoading=function(d,e){var a=e.id,t=e.level,o=e.url,u=e.deliveryDirectives;this.load({id:a,groupId:null,level:t,responseType:"text",type:s.PlaylistContextType.LEVEL,url:o,deliveryDirectives:u})},y.onAudioTrackLoading=function(d,e){var a=e.id,t=e.groupId,o=e.url,u=e.deliveryDirectives;this.load({id:a,groupId:t,level:null,responseType:"text",type:s.PlaylistContextType.AUDIO_TRACK,url:o,deliveryDirectives:u})},y.onSubtitleTrackLoading=function(d,e){var a=e.id,t=e.groupId,o=e.url,u=e.deliveryDirectives;this.load({id:a,groupId:t,level:null,responseType:"text",type:s.PlaylistContextType.SUBTITLE_TRACK,url:o,deliveryDirectives:u})},y.load=function(d){var e,a,t,o,u,n,l=this.hls.config,p=this.getInternalLoader(d);if(p){var f=p.context;if(f&&f.url===d.url)return void k.logger.trace("[playlist-loader]: playlist request ongoing");k.logger.log("[playlist-loader]: aborting previous loader for type: "+d.type),p.abort()}switch(d.type){case s.PlaylistContextType.MANIFEST:a=l.manifestLoadingMaxRetry,t=l.manifestLoadingTimeOut,o=l.manifestLoadingRetryDelay,u=l.manifestLoadingMaxRetryTimeout;break;case s.PlaylistContextType.LEVEL:case s.PlaylistContextType.AUDIO_TRACK:case s.PlaylistContextType.SUBTITLE_TRACK:a=0,t=l.levelLoadingTimeOut;break;default:a=l.levelLoadingMaxRetry,t=l.levelLoadingTimeOut,o=l.levelLoadingRetryDelay,u=l.levelLoadingMaxRetryTimeout}if(p=this.createInternalLoader(d),(e=d.deliveryDirectives)!==null&&e!==void 0&&e.part&&(d.type===s.PlaylistContextType.LEVEL&&d.level!==null?n=this.hls.levels[d.level].details:d.type===s.PlaylistContextType.AUDIO_TRACK&&d.id!==null?n=this.hls.audioTracks[d.id].details:d.type===s.PlaylistContextType.SUBTITLE_TRACK&&d.id!==null&&(n=this.hls.subtitleTracks[d.id].details),n)){var v=n.partTarget,r=n.targetduration;v&&r&&(t=Math.min(1e3*Math.max(3*v,.8*r),t))}var i={timeout:t,maxRetry:a,retryDelay:o,maxRetryDelay:u,highWaterMark:0},c={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)};p.load(d,i,c)},y.loadsuccess=function(d,e,a,t){if(t===void 0&&(t=null),a.isSidxRequest)return this.handleSidxRequest(d,a),void this.handlePlaylistLoaded(d,e,a,t);this.resetInternalLoader(a.type);var o=d.data;o.indexOf("#EXTM3U")===0?(e.parsing.start=performance.now(),o.indexOf("#EXTINF:")>0||o.indexOf("#EXT-X-TARGETDURATION:")>0?this.handleTrackOrLevelPlaylist(d,e,a,t):this.handleMasterPlaylist(d,e,a,t)):this.handleManifestParsingError(d,a,"no EXTM3U delimiter",t)},y.loaderror=function(d,e,a){a===void 0&&(a=null),this.handleNetworkError(e,a,!1,d)},y.loadtimeout=function(d,e,a){a===void 0&&(a=null),this.handleNetworkError(e,a,!0)},y.handleMasterPlaylist=function(d,e,a,t){var o=this.hls,u=d.data,n=m(d,a),l=D.default.parseMasterPlaylist(u,n),p=l.levels,f=l.sessionData;if(p.length){var v=p.map(function(L){return{id:L.attrs.AUDIO,audioCodec:L.audioCodec}}),r=p.map(function(L){return{id:L.attrs.SUBTITLES,textCodec:L.textCodec}}),i=D.default.parseMasterPlaylistMedia(u,n,"AUDIO",v),c=D.default.parseMasterPlaylistMedia(u,n,"SUBTITLES",r),S=D.default.parseMasterPlaylistMedia(u,n,"CLOSED-CAPTIONS");i.length&&(i.some(function(L){return!L.url})||!p[0].audioCodec||p[0].attrs.AUDIO||(k.logger.log("[playlist-loader]: audio codec signaled in quality level, but no embedded audio track signaled, create one"),i.unshift({type:"main",name:"main",default:!1,autoselect:!1,forced:!1,id:-1,attrs:new b.AttrList({}),bitrate:0,url:""}))),o.trigger(E.Events.MANIFEST_LOADED,{levels:p,audioTracks:i,subtitles:c,captions:S,url:n,stats:e,networkDetails:t,sessionData:f})}else this.handleManifestParsingError(d,a,"no level found in manifest",t)},y.handleTrackOrLevelPlaylist=function(d,e,a,t){var o=this.hls,u=a.id,n=a.level,l=a.type,p=m(d,a),f=Object(R.isFiniteNumber)(u)?u:0,v=Object(R.isFiniteNumber)(n)?n:f,r=function(_){switch(_.type){case s.PlaylistContextType.AUDIO_TRACK:return s.PlaylistLevelType.AUDIO;case s.PlaylistContextType.SUBTITLE_TRACK:return s.PlaylistLevelType.SUBTITLE;default:return s.PlaylistLevelType.MAIN}}(a),i=D.default.parseLevelPlaylist(d.data,p,v,r,f);if(i.fragments.length){if(l===s.PlaylistContextType.MANIFEST){var c={attrs:new b.AttrList({}),bitrate:0,details:i,name:"",url:p};o.trigger(E.Events.MANIFEST_LOADED,{levels:[c],audioTracks:[],url:p,stats:e,networkDetails:t,sessionData:null})}if(e.parsing.end=performance.now(),i.needSidxRanges){var S,L=(S=i.fragments[0].initSegment)===null||S===void 0?void 0:S.url;this.load({url:L,isSidxRequest:!0,type:l,level:n,levelDetails:i,id:u,groupId:null,rangeStart:0,rangeEnd:2048,responseType:"arraybuffer",deliveryDirectives:null})}else a.levelDetails=i,this.handlePlaylistLoaded(d,e,a,t)}else o.trigger(E.Events.ERROR,{type:A.ErrorTypes.NETWORK_ERROR,details:A.ErrorDetails.LEVEL_EMPTY_ERROR,fatal:!1,url:p,reason:"no fragments found in level",level:typeof a.level=="number"?a.level:void 0})},y.handleSidxRequest=function(d,e){var a=Object(C.parseSegmentIndex)(new Uint8Array(d.data));if(a){var t=a.references,o=e.levelDetails;t.forEach(function(u,n){var l=u.info,p=o.fragments[n];p.byteRange.length===0&&p.setByteRange(String(1+l.end-l.start)+"@"+String(l.start)),p.initSegment&&p.initSegment.setByteRange(String(a.moovEndOffset)+"@0")})}},y.handleManifestParsingError=function(d,e,a,t){this.hls.trigger(E.Events.ERROR,{type:A.ErrorTypes.NETWORK_ERROR,details:A.ErrorDetails.MANIFEST_PARSING_ERROR,fatal:e.type===s.PlaylistContextType.MANIFEST,url:d.url,reason:a,response:d,context:e,networkDetails:t})},y.handleNetworkError=function(d,e,a,t){a===void 0&&(a=!1),k.logger.warn("[playlist-loader]: A network "+(a?"timeout":"error")+" occurred while loading "+d.type+" level: "+d.level+" id: "+d.id+' group-id: "'+d.groupId+'"');var o=A.ErrorDetails.UNKNOWN,u=!1,n=this.getInternalLoader(d);switch(d.type){case s.PlaylistContextType.MANIFEST:o=a?A.ErrorDetails.MANIFEST_LOAD_TIMEOUT:A.ErrorDetails.MANIFEST_LOAD_ERROR,u=!0;break;case s.PlaylistContextType.LEVEL:o=a?A.ErrorDetails.LEVEL_LOAD_TIMEOUT:A.ErrorDetails.LEVEL_LOAD_ERROR,u=!1;break;case s.PlaylistContextType.AUDIO_TRACK:o=a?A.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT:A.ErrorDetails.AUDIO_TRACK_LOAD_ERROR,u=!1;break;case s.PlaylistContextType.SUBTITLE_TRACK:o=a?A.ErrorDetails.SUBTITLE_TRACK_LOAD_TIMEOUT:A.ErrorDetails.SUBTITLE_LOAD_ERROR,u=!1}n&&this.resetInternalLoader(d.type);var l={type:A.ErrorTypes.NETWORK_ERROR,details:o,fatal:u,url:d.url,loader:n,context:d,networkDetails:e};t&&(l.response=t),this.hls.trigger(E.Events.ERROR,l)},y.handlePlaylistLoaded=function(d,e,a,t){var o=a.type,u=a.level,n=a.id,l=a.groupId,p=a.loader,f=a.levelDetails,v=a.deliveryDirectives;if(f!=null&&f.targetduration){if(p)switch(f.live&&(p.getCacheAge&&(f.ageHeader=p.getCacheAge()||0),p.getCacheAge&&!isNaN(f.ageHeader)||(f.ageHeader=0)),o){case s.PlaylistContextType.MANIFEST:case s.PlaylistContextType.LEVEL:this.hls.trigger(E.Events.LEVEL_LOADED,{details:f,level:u||0,id:n||0,stats:e,networkDetails:t,deliveryDirectives:v});break;case s.PlaylistContextType.AUDIO_TRACK:this.hls.trigger(E.Events.AUDIO_TRACK_LOADED,{details:f,id:n||0,groupId:l||"",stats:e,networkDetails:t,deliveryDirectives:v});break;case s.PlaylistContextType.SUBTITLE_TRACK:this.hls.trigger(E.Events.SUBTITLE_TRACK_LOADED,{details:f,id:n||0,groupId:l||"",stats:e,networkDetails:t,deliveryDirectives:v})}}else this.handleManifestParsingError(d,a,"invalid target duration",t)},T}();I.default=h},"./src/polyfills/number.ts":function(M,I,g){g.r(I),g.d(I,"isFiniteNumber",function(){return R}),g.d(I,"MAX_SAFE_INTEGER",function(){return E});var R=Number.isFinite||function(A){return typeof A=="number"&&isFinite(A)},E=Number.MAX_SAFE_INTEGER||9007199254740991},"./src/remux/aac-helper.ts":function(M,I,g){g.r(I);var R=function(){function E(){}return E.getSilentFrame=function(A,k){switch(A){case"mp4a.40.2":if(k===1)return new Uint8Array([0,200,0,128,35,128]);if(k===2)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(k===3)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(k===4)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(k===5)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(k===6)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224]);break;default:if(k===1)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(k===2)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(k===3)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}},E}();I.default=R},"./src/remux/mp4-generator.ts":function(M,I,g){g.r(I);var R=Math.pow(2,32)-1,E=function(){function A(){}return A.init=function(){var k;for(k in A.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]},A.types)A.types.hasOwnProperty(k)&&(A.types[k]=[k.charCodeAt(0),k.charCodeAt(1),k.charCodeAt(2),k.charCodeAt(3)]);var C=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),D=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);A.HDLR_TYPES={video:C,audio:D};var s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),b=new Uint8Array([0,0,0,0,0,0,0,0]);A.STTS=A.STSC=A.STCO=b,A.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),A.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),A.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),A.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);var m=new Uint8Array([105,115,111,109]),h=new Uint8Array([97,118,99,49]),T=new Uint8Array([0,0,0,1]);A.FTYP=A.box(A.types.ftyp,m,T,m,h),A.DINF=A.box(A.types.dinf,A.box(A.types.dref,s))},A.box=function(k){for(var C=8,D=arguments.length,s=new Array(D>1?D-1:0),b=1;b<D;b++)s[b-1]=arguments[b];for(var m=s.length,h=m;m--;)C+=s[m].byteLength;var T=new Uint8Array(C);for(T[0]=C>>24&255,T[1]=C>>16&255,T[2]=C>>8&255,T[3]=255&C,T.set(k,4),m=0,C=8;m<h;m++)T.set(s[m],C),C+=s[m].byteLength;return T},A.hdlr=function(k){return A.box(A.types.hdlr,A.HDLR_TYPES[k])},A.mdat=function(k){return A.box(A.types.mdat,k)},A.mdhd=function(k,C){C*=k;var D=Math.floor(C/(R+1)),s=Math.floor(C%(R+1));return A.box(A.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,k>>24&255,k>>16&255,k>>8&255,255&k,D>>24,D>>16&255,D>>8&255,255&D,s>>24,s>>16&255,s>>8&255,255&s,85,196,0,0]))},A.mdia=function(k){return A.box(A.types.mdia,A.mdhd(k.timescale,k.duration),A.hdlr(k.type),A.minf(k))},A.mfhd=function(k){return A.box(A.types.mfhd,new Uint8Array([0,0,0,0,k>>24,k>>16&255,k>>8&255,255&k]))},A.minf=function(k){return k.type==="audio"?A.box(A.types.minf,A.box(A.types.smhd,A.SMHD),A.DINF,A.stbl(k)):A.box(A.types.minf,A.box(A.types.vmhd,A.VMHD),A.DINF,A.stbl(k))},A.moof=function(k,C,D){return A.box(A.types.moof,A.mfhd(k),A.traf(D,C))},A.moov=function(k){for(var C=k.length,D=[];C--;)D[C]=A.trak(k[C]);return A.box.apply(null,[A.types.moov,A.mvhd(k[0].timescale,k[0].duration)].concat(D).concat(A.mvex(k)))},A.mvex=function(k){for(var C=k.length,D=[];C--;)D[C]=A.trex(k[C]);return A.box.apply(null,[A.types.mvex].concat(D))},A.mvhd=function(k,C){C*=k;var D=Math.floor(C/(R+1)),s=Math.floor(C%(R+1)),b=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,k>>24&255,k>>16&255,k>>8&255,255&k,D>>24,D>>16&255,D>>8&255,255&D,s>>24,s>>16&255,s>>8&255,255&s,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return A.box(A.types.mvhd,b)},A.sdtp=function(k){var C,D,s=k.samples||[],b=new Uint8Array(4+s.length);for(C=0;C<s.length;C++)D=s[C].flags,b[C+4]=D.dependsOn<<4|D.isDependedOn<<2|D.hasRedundancy;return A.box(A.types.sdtp,b)},A.stbl=function(k){return A.box(A.types.stbl,A.stsd(k),A.box(A.types.stts,A.STTS),A.box(A.types.stsc,A.STSC),A.box(A.types.stsz,A.STSZ),A.box(A.types.stco,A.STCO))},A.avc1=function(k){var C,D,s,b=[],m=[];for(C=0;C<k.sps.length;C++)s=(D=k.sps[C]).byteLength,b.push(s>>>8&255),b.push(255&s),b=b.concat(Array.prototype.slice.call(D));for(C=0;C<k.pps.length;C++)s=(D=k.pps[C]).byteLength,m.push(s>>>8&255),m.push(255&s),m=m.concat(Array.prototype.slice.call(D));var h=A.box(A.types.avcC,new Uint8Array([1,b[3],b[4],b[5],255,224|k.sps.length].concat(b).concat([k.pps.length]).concat(m))),T=k.width,y=k.height,d=k.pixelRatio[0],e=k.pixelRatio[1];return A.box(A.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,T>>8&255,255&T,y>>8&255,255&y,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),h,A.box(A.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),A.box(A.types.pasp,new Uint8Array([d>>24,d>>16&255,d>>8&255,255&d,e>>24,e>>16&255,e>>8&255,255&e])))},A.esds=function(k){var C=k.config.length;return new Uint8Array([0,0,0,0,3,23+C,0,1,0,4,15+C,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([C]).concat(k.config).concat([6,1,2]))},A.mp4a=function(k){var C=k.samplerate;return A.box(A.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,k.channelCount,0,16,0,0,0,0,C>>8&255,255&C,0,0]),A.box(A.types.esds,A.esds(k)))},A.mp3=function(k){var C=k.samplerate;return A.box(A.types[".mp3"],new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,k.channelCount,0,16,0,0,0,0,C>>8&255,255&C,0,0]))},A.stsd=function(k){return k.type==="audio"?k.isAAC||k.codec!=="mp3"?A.box(A.types.stsd,A.STSD,A.mp4a(k)):A.box(A.types.stsd,A.STSD,A.mp3(k)):A.box(A.types.stsd,A.STSD,A.avc1(k))},A.tkhd=function(k){var C=k.id,D=k.duration*k.timescale,s=k.width,b=k.height,m=Math.floor(D/(R+1)),h=Math.floor(D%(R+1));return A.box(A.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,C>>24&255,C>>16&255,C>>8&255,255&C,0,0,0,0,m>>24,m>>16&255,m>>8&255,255&m,h>>24,h>>16&255,h>>8&255,255&h,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,s>>8&255,255&s,0,0,b>>8&255,255&b,0,0]))},A.traf=function(k,C){var D=A.sdtp(k),s=k.id,b=Math.floor(C/(R+1)),m=Math.floor(C%(R+1));return A.box(A.types.traf,A.box(A.types.tfhd,new Uint8Array([0,0,0,0,s>>24,s>>16&255,s>>8&255,255&s])),A.box(A.types.tfdt,new Uint8Array([1,0,0,0,b>>24,b>>16&255,b>>8&255,255&b,m>>24,m>>16&255,m>>8&255,255&m])),A.trun(k,D.length+16+20+8+16+8+8),D)},A.trak=function(k){return k.duration=k.duration||4294967295,A.box(A.types.trak,A.tkhd(k),A.mdia(k))},A.trex=function(k){var C=k.id;return A.box(A.types.trex,new Uint8Array([0,0,0,0,C>>24,C>>16&255,C>>8&255,255&C,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))},A.trun=function(k,C){var D,s,b,m,h,T,y=k.samples||[],d=y.length,e=12+16*d,a=new Uint8Array(e);for(C+=8+e,a.set([0,0,15,1,d>>>24&255,d>>>16&255,d>>>8&255,255&d,C>>>24&255,C>>>16&255,C>>>8&255,255&C],0),D=0;D<d;D++)b=(s=y[D]).duration,m=s.size,h=s.flags,T=s.cts,a.set([b>>>24&255,b>>>16&255,b>>>8&255,255&b,m>>>24&255,m>>>16&255,m>>>8&255,255&m,h.isLeading<<2|h.dependsOn,h.isDependedOn<<6|h.hasRedundancy<<4|h.paddingValue<<1|h.isNonSync,61440&h.degradPrio,15&h.degradPrio,T>>>24&255,T>>>16&255,T>>>8&255,255&T],12+16*D);return A.box(A.types.trun,a)},A.initSegment=function(k){A.types||A.init();var C=A.moov(k),D=new Uint8Array(A.FTYP.byteLength+C.byteLength);return D.set(A.FTYP),D.set(C,A.FTYP.byteLength),D},A}();E.types=void 0,E.HDLR_TYPES=void 0,E.STTS=void 0,E.STSC=void 0,E.STCO=void 0,E.STSZ=void 0,E.VMHD=void 0,E.SMHD=void 0,E.STSD=void 0,E.FTYP=void 0,E.DINF=void 0,I.default=E},"./src/remux/mp4-remuxer.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return d}),g.d(I,"normalizePts",function(){return e});var R=g("./src/polyfills/number.ts"),E=g("./src/remux/aac-helper.ts"),A=g("./src/remux/mp4-generator.ts"),k=g("./src/events.ts"),C=g("./src/errors.ts"),D=g("./src/utils/logger.ts"),s=g("./src/types/loader.ts"),b=g("./src/utils/timescale-conversion.ts");function m(){return(m=Object.assign||function(o){for(var u=1;u<arguments.length;u++){var n=arguments[u];for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(o[l]=n[l])}return o}).apply(this,arguments)}var h=null,T=null,y=!1,d=function(){function o(n,l,p,f){if(this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.ISGenerated=!1,this._initPTS=void 0,this._initDTS=void 0,this.nextAvcDts=null,this.nextAudioPts=null,this.isAudioContiguous=!1,this.isVideoContiguous=!1,this.observer=n,this.config=l,this.typeSupported=p,this.ISGenerated=!1,h===null){var v=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);h=v?parseInt(v[1]):0}if(T===null){var r=navigator.userAgent.match(/Safari\/(\d+)/i);T=r?parseInt(r[1]):0}y=!!h&&h<75||!!T&&T<600}var u=o.prototype;return u.destroy=function(){},u.resetTimeStamp=function(n){D.logger.log("[mp4-remuxer]: initPTS & initDTS reset"),this._initPTS=this._initDTS=n},u.resetNextTimestamp=function(){D.logger.log("[mp4-remuxer]: reset next timestamp"),this.isVideoContiguous=!1,this.isAudioContiguous=!1},u.resetInitSegment=function(){D.logger.log("[mp4-remuxer]: ISGenerated flag reset"),this.ISGenerated=!1},u.getVideoStartPts=function(n){var l=!1,p=n.reduce(function(f,v){var r=v.pts-f;return r<-4294967296?(l=!0,e(f,v.pts)):r>0?f:v.pts},n[0].pts);return l&&D.logger.debug("PTS rollover detected"),p},u.remux=function(n,l,p,f,v,r,i,c){var S,L,_,x,w,O,P=v,F=v,N=n.pid>-1,U=l.pid>-1,G=l.samples.length,K=n.samples.length>0,q=G>1;if((!N||K)&&(!U||q)||this.ISGenerated||i){this.ISGenerated||(_=this.generateIS(n,l,v));var Y=this.isVideoContiguous,j=-1;if(q&&(j=function(oe){for(var ie=0;ie<oe.length;ie++)if(oe[ie].key)return ie;return-1}(l.samples),!Y&&this.config.forceKeyFrameOnDiscontinuity))if(O=!0,j>0){D.logger.warn("[mp4-remuxer]: Dropped "+j+" out of "+G+" video samples due to a missing keyframe");var X=this.getVideoStartPts(l.samples);l.samples=l.samples.slice(j),l.dropped+=j,F+=(l.samples[0].pts-X)/(l.timescale||9e4)}else j===-1&&(D.logger.warn("[mp4-remuxer]: No keyframe found out of "+G+" video samples"),O=!1);if(this.ISGenerated){if(K&&q){var Q=this.getVideoStartPts(l.samples),te=(e(n.samples[0].pts,Q)-Q)/l.inputTimeScale;P+=Math.max(0,te),F+=Math.max(0,-te)}if(K){if(n.samplerate||(D.logger.warn("[mp4-remuxer]: regenerate InitSegment as audio detected"),_=this.generateIS(n,l,v)),L=this.remuxAudio(n,P,this.isAudioContiguous,r,U||q||c===s.PlaylistLevelType.AUDIO?F:void 0),q){var ae=L?L.endPTS-L.startPTS:0;l.inputTimeScale||(D.logger.warn("[mp4-remuxer]: regenerate InitSegment as video detected"),_=this.generateIS(n,l,v)),S=this.remuxVideo(l,F,Y,ae)}}else q&&(S=this.remuxVideo(l,F,Y,0));S&&(S.firstKeyFrame=j,S.independent=j!==-1)}}return this.ISGenerated&&(p.samples.length&&(w=this.remuxID3(p,v)),f.samples.length&&(x=this.remuxText(f,v))),{audio:L,video:S,initSegment:_,independent:O,text:x,id3:w}},u.generateIS=function(n,l,p){var f,v,r,i=n.samples,c=l.samples,S=this.typeSupported,L={},_=!Object(R.isFiniteNumber)(this._initPTS),x="audio/mp4";if(_&&(f=v=1/0),n.config&&i.length&&(n.timescale=n.samplerate,n.isAAC||(S.mpeg?(x="audio/mpeg",n.codec=""):S.mp3&&(n.codec="mp3")),L.audio={id:"audio",container:x,codec:n.codec,initSegment:!n.isAAC&&S.mpeg?new Uint8Array(0):A.default.initSegment([n]),metadata:{channelCount:n.channelCount}},_&&(r=n.inputTimeScale,f=v=i[0].pts-Math.round(r*p))),l.sps&&l.pps&&c.length&&(l.timescale=l.inputTimeScale,L.video={id:"main",container:"video/mp4",codec:l.codec,initSegment:A.default.initSegment([l]),metadata:{width:l.width,height:l.height}},_)){r=l.inputTimeScale;var w=this.getVideoStartPts(c),O=Math.round(r*p);v=Math.min(v,e(c[0].dts,w)-O),f=Math.min(f,w-O)}if(Object.keys(L).length)return this.ISGenerated=!0,_&&(this._initPTS=f,this._initDTS=v),{tracks:L,initPTS:f,timescale:r}},u.remuxVideo=function(n,l,p,f){var v,r,i,c=n.inputTimeScale,S=n.samples,L=[],_=S.length,x=this._initPTS,w=this.nextAvcDts,O=8,P=Number.POSITIVE_INFINITY,F=Number.NEGATIVE_INFINITY,N=0,U=!1;p&&w!==null||(w=l*c-(S[0].pts-e(S[0].dts,S[0].pts)));for(var G=0;G<_;G++){var K=S[G];K.pts=e(K.pts-x,w),K.dts=e(K.dts-x,w),K.dts>K.pts&&(N=Math.max(Math.min(N,K.pts-K.dts),-18e3)),K.dts<S[G>0?G-1:G].dts&&(U=!0)}U&&S.sort(function(Ce,Ve){var Ct=Ce.dts-Ve.dts,It=Ce.pts-Ve.pts;return Ct||It}),r=S[0].dts,i=S[S.length-1].dts;var q=Math.round((i-r)/(_-1));if(N<0){if(N<-2*q){D.logger.warn("PTS < DTS detected in video samples, offsetting DTS from PTS by "+Object(b.toMsFromMpegTsClock)(-q,!0)+" ms");for(var Y=N,j=0;j<_;j++)S[j].dts=Y=Math.max(Y,S[j].pts-q),S[j].pts=Math.max(Y,S[j].pts)}else{D.logger.warn("PTS < DTS detected in video samples, shifting DTS by "+Object(b.toMsFromMpegTsClock)(N,!0)+" ms to overcome this issue");for(var X=0;X<_;X++)S[X].dts=S[X].dts+N}r=S[0].dts}if(p){var Q=r-w,te=Q>q;if(te||Q<-1){te?D.logger.warn("AVC: "+Object(b.toMsFromMpegTsClock)(Q,!0)+" ms ("+Q+"dts) hole between fragments detected, filling it"):D.logger.warn("AVC: "+Object(b.toMsFromMpegTsClock)(-Q,!0)+" ms ("+Q+"dts) overlapping between fragments detected"),r=w;var ae=S[0].pts-Q;S[0].dts=r,S[0].pts=ae,D.logger.log("Video: First PTS/DTS adjusted: "+Object(b.toMsFromMpegTsClock)(ae,!0)+"/"+Object(b.toMsFromMpegTsClock)(r,!0)+", delta: "+Object(b.toMsFromMpegTsClock)(Q,!0)+" ms")}}y&&(r=Math.max(0,r));for(var oe=0,ie=0,ue=0;ue<_;ue++){for(var J=S[ue],ne=J.units,le=ne.length,se=0,pe=0;pe<le;pe++)se+=ne[pe].data.length;ie+=se,oe+=le,J.length=se,J.dts=Math.max(J.dts,r),J.pts=Math.max(J.pts,J.dts,0),P=Math.min(J.pts,P),F=Math.max(J.pts,F)}i=S[_-1].dts;var me,Se=ie+4*oe+8;try{me=new Uint8Array(Se)}catch{return void this.observer.emit(k.Events.ERROR,k.Events.ERROR,{type:C.ErrorTypes.MUX_ERROR,details:C.ErrorDetails.REMUX_ALLOC_ERROR,fatal:!1,bytes:Se,reason:"fail allocating video mdat "+Se})}var be=new DataView(me.buffer);be.setUint32(0,Se),me.set(A.default.types.mdat,4);for(var fe=0;fe<_;fe++){for(var ge=S[fe],we=ge.units,Ee=0,Re=0,Ne=we.length;Re<Ne;Re++){var xe=we[Re],H=xe.data,de=xe.data.byteLength;be.setUint32(O,de),O+=4,me.set(H,O),O+=de,Ee+=4+de}if(fe<_-1)v=S[fe+1].dts-ge.dts;else{var Oe=this.config,Pe=ge.dts-S[fe>0?fe-1:fe].dts;if(Oe.stretchShortVideoTrack&&this.nextAudioPts!==null){var Ze=Math.floor(Oe.maxBufferHole*c),qe=(f?P+f*c:this.nextAudioPts)-ge.pts;qe>Ze?((v=qe-Pe)<0&&(v=Pe),D.logger.log("[mp4-remuxer]: It is approximately "+qe/90+" ms to the next segment; using duration "+v/90+" ms for the last video frame.")):v=Pe}else v=Pe}var Ge=Math.round(ge.pts-ge.dts);L.push(new a(ge.key,v,Ee,Ge))}if(L.length&&h&&h<70){var Be=L[0].flags;Be.dependsOn=2,Be.isNonSync=0}console.assert(v!==void 0,"mp4SampleDuration must be computed"),this.nextAvcDts=w=i+v,this.isVideoContiguous=!0;var je={data1:A.default.moof(n.sequenceNumber++,r,m({},n,{samples:L})),data2:me,startPTS:P/c,endPTS:(F+v)/c,startDTS:r/c,endDTS:w/c,type:"video",hasAudio:!1,hasVideo:!0,nb:L.length,dropped:n.dropped};return n.samples=[],n.dropped=0,console.assert(me.length,"MDAT length must not be zero"),je},u.remuxAudio=function(n,l,p,f,v){var r=n.inputTimeScale,i=r/(n.samplerate?n.samplerate:r),c=n.isAAC?1024:1152,S=c*i,L=this._initPTS,_=!n.isAAC&&this.typeSupported.mpeg,x=[],w=n.samples,O=_?0:8,P=this.nextAudioPts||-1,F=l*r;if(this.isAudioContiguous=p=p||w.length&&P>0&&(f&&Math.abs(F-P)<9e3||Math.abs(e(w[0].pts-L,F)-P)<20*S),w.forEach(function(H){H.pts=e(H.pts-L,F)}),!p||P<0){if(!(w=w.filter(function(H){return H.pts>=0})).length)return;P=v===0?0:f?Math.max(0,F):w[0].pts}if(n.isAAC)for(var N=v!==void 0,U=this.config.maxAudioFramesDrift,G=0,K=P;G<w.length;G++){var q=w[G],Y=q.pts,j=Y-K,X=Math.abs(1e3*j/r);if(j<=-U*S&&N)G===0&&(D.logger.warn("Audio frame @ "+(Y/r).toFixed(3)+"s overlaps nextAudioPts by "+Math.round(1e3*j/r)+" ms."),this.nextAudioPts=P=K=Y);else if(j>=U*S&&X<1e4&&N){var Q=Math.round(j/S);(K=Y-Q*S)<0&&(Q--,K+=S),G===0&&(this.nextAudioPts=P=K),D.logger.warn("[mp4-remuxer]: Injecting "+Q+" audio frame @ "+(K/r).toFixed(3)+"s due to "+Math.round(1e3*j/r)+" ms gap.");for(var te=0;te<Q;te++){var ae=Math.max(K,0),oe=E.default.getSilentFrame(n.manifestCodec||n.codec,n.channelCount);oe||(D.logger.log("[mp4-remuxer]: Unable to get silent frame for given audio codec; duplicating last frame instead."),oe=q.unit.subarray()),w.splice(G,0,{unit:oe,pts:ae}),K+=S,G++}}q.pts=K,K+=S}for(var ie,ue=null,J=null,ne=0,le=w.length;le--;)ne+=w[le].unit.byteLength;for(var se=0,pe=w.length;se<pe;se++){var me=w[se],Se=me.unit,be=me.pts;if(J!==null)x[se-1].duration=Math.round((be-J)/i);else{if(p&&n.isAAC&&(be=P),ue=be,!(ne>0))return;ne+=O;try{ie=new Uint8Array(ne)}catch{return void this.observer.emit(k.Events.ERROR,k.Events.ERROR,{type:C.ErrorTypes.MUX_ERROR,details:C.ErrorDetails.REMUX_ALLOC_ERROR,fatal:!1,bytes:ne,reason:"fail allocating audio mdat "+ne})}_||(new DataView(ie.buffer).setUint32(0,ne),ie.set(A.default.types.mdat,4))}ie.set(Se,O);var fe=Se.byteLength;O+=fe,x.push(new a(!0,c,fe,0)),J=be}var ge=x.length;if(ge){var we=x[x.length-1];this.nextAudioPts=P=J+i*we.duration;var Ee=_?new Uint8Array(0):A.default.moof(n.sequenceNumber++,ue/i,m({},n,{samples:x}));n.samples=[];var Re=ue/r,Ne=P/r,xe={data1:Ee,data2:ie,startPTS:Re,endPTS:Ne,startDTS:Re,endDTS:Ne,type:"audio",hasAudio:!0,hasVideo:!1,nb:ge};return this.isAudioContiguous=!0,console.assert(ie.length,"MDAT length must not be zero"),xe}},u.remuxEmptyAudio=function(n,l,p,f){var v=n.inputTimeScale,r=v/(n.samplerate?n.samplerate:v),i=this.nextAudioPts,c=(i!==null?i:f.startDTS*v)+this._initDTS,S=f.endDTS*v+this._initDTS,L=1024*r,_=Math.ceil((S-c)/L),x=E.default.getSilentFrame(n.manifestCodec||n.codec,n.channelCount);if(D.logger.warn("[mp4-remuxer]: remux empty Audio"),x){for(var w=[],O=0;O<_;O++){var P=c+O*L;w.push({unit:x,pts:P,dts:P})}return n.samples=w,this.remuxAudio(n,l,p,!1)}D.logger.trace("[mp4-remuxer]: Unable to remuxEmptyAudio since we were unable to get a silent frame for given audio codec")},u.remuxID3=function(n,l){var p=n.samples.length;if(p){for(var f=n.inputTimeScale,v=this._initPTS,r=this._initDTS,i=0;i<p;i++){var c=n.samples[i];c.pts=e(c.pts-v,l*f)/f,c.dts=e(c.dts-r,l*f)/f}var S=n.samples;return n.samples=[],{samples:S}}},u.remuxText=function(n,l){var p=n.samples.length;if(p){for(var f=n.inputTimeScale,v=this._initPTS,r=0;r<p;r++){var i=n.samples[r];i.pts=e(i.pts-v,l*f)/f}n.samples.sort(function(S,L){return S.pts-L.pts});var c=n.samples;return n.samples=[],{samples:c}}},o}();function e(o,u){var n;if(u===null)return o;for(n=u<o?-8589934592:8589934592;Math.abs(o-u)>4294967296;)o+=n;return o}var a=function(o,u,n,l){this.size=void 0,this.duration=void 0,this.cts=void 0,this.flags=void 0,this.duration=u,this.size=n,this.cts=l,this.flags=new t(o)},t=function(o){this.isLeading=0,this.isDependedOn=0,this.hasRedundancy=0,this.degradPrio=0,this.dependsOn=1,this.isNonSync=1,this.dependsOn=o?2:1,this.isNonSync=o?0:1}},"./src/remux/passthrough-remuxer.ts":function(M,I,g){g.r(I);var R=g("./src/polyfills/number.ts"),E=g("./src/utils/mp4-tools.ts"),A=g("./src/loader/fragment.ts"),k=g("./src/utils/logger.ts"),C=function(){function b(){this.emitInitSegment=!1,this.audioCodec=void 0,this.videoCodec=void 0,this.initData=void 0,this.initPTS=void 0,this.initTracks=void 0,this.lastEndDTS=null}var m=b.prototype;return m.destroy=function(){},m.resetTimeStamp=function(h){this.initPTS=h,this.lastEndDTS=null},m.resetNextTimestamp=function(){this.lastEndDTS=null},m.resetInitSegment=function(h,T,y){this.audioCodec=T,this.videoCodec=y,this.generateInitSegment(h),this.emitInitSegment=!0},m.generateInitSegment=function(h){var T=this.audioCodec,y=this.videoCodec;if(!h||!h.byteLength)return this.initTracks=void 0,void(this.initData=void 0);var d=this.initData=Object(E.parseInitSegment)(h);T||(T=s(d.audio,A.ElementaryStreamTypes.AUDIO)),y||(y=s(d.video,A.ElementaryStreamTypes.VIDEO));var e={};d.audio&&d.video?e.audiovideo={container:"video/mp4",codec:T+","+y,initSegment:h,id:"main"}:d.audio?e.audio={container:"audio/mp4",codec:T,initSegment:h,id:"audio"}:d.video?e.video={container:"video/mp4",codec:y,initSegment:h,id:"main"}:k.logger.warn("[passthrough-remuxer.ts]: initSegment does not contain moov or trak boxes."),this.initTracks=e},m.remux=function(h,T,y,d,e){var a=this.initPTS,t=this.lastEndDTS,o={audio:void 0,video:void 0,text:d,id3:y,initSegment:void 0};Object(R.isFiniteNumber)(t)||(t=this.lastEndDTS=e||0);var u=T.samples;if(!u||!u.length)return o;var n={initPTS:void 0,timescale:1},l=this.initData;if(l&&l.length||(this.generateInitSegment(u),l=this.initData),!l||!l.length)return k.logger.warn("[passthrough-remuxer.ts]: Failed to generate initSegment."),o;this.emitInitSegment&&(n.tracks=this.initTracks,this.emitInitSegment=!1),Object(R.isFiniteNumber)(a)||(this.initPTS=n.initPTS=a=D(l,u,t));var p=Object(E.getDuration)(u,l),f=t,v=p+f;Object(E.offsetStartDTS)(l,u,a),p>0?this.lastEndDTS=v:(k.logger.warn("Duration parsed from mp4 should be greater than zero"),this.resetNextTimestamp());var r=!!l.audio,i=!!l.video,c="";r&&(c+="audio"),i&&(c+="video");var S={data1:u,startPTS:f,startDTS:f,endPTS:v,endDTS:v,type:c,hasAudio:r,hasVideo:i,nb:1,dropped:0};return o.audio=S.type==="audio"?S:void 0,o.video=S.type!=="audio"?S:void 0,o.text=d,o.id3=y,o.initSegment=n,o},b}(),D=function(b,m,h){return Object(E.getStartDTS)(b,m)-h};function s(b,m){var h=b==null?void 0:b.codec;return h&&h.length>4?h:h==="hvc1"?"hvc1.1.c.L120.90":h==="av01"?"av01.0.04M.08":h==="avc1"||m===A.ElementaryStreamTypes.VIDEO?"avc1.42e01e":"mp4a.40.5"}I.default=C},"./src/task-loop.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return R});var R=function(){function E(){this._boundTick=void 0,this._tickTimer=null,this._tickInterval=null,this._tickCallCount=0,this._boundTick=this.tick.bind(this)}var A=E.prototype;return A.destroy=function(){this.onHandlerDestroying(),this.onHandlerDestroyed()},A.onHandlerDestroying=function(){this.clearNextTick(),this.clearInterval()},A.onHandlerDestroyed=function(){},A.hasInterval=function(){return!!this._tickInterval},A.hasNextTick=function(){return!!this._tickTimer},A.setInterval=function(k){return!this._tickInterval&&(this._tickInterval=self.setInterval(this._boundTick,k),!0)},A.clearInterval=function(){return!!this._tickInterval&&(self.clearInterval(this._tickInterval),this._tickInterval=null,!0)},A.clearNextTick=function(){return!!this._tickTimer&&(self.clearTimeout(this._tickTimer),this._tickTimer=null,!0)},A.tick=function(){this._tickCallCount++,this._tickCallCount===1&&(this.doTick(),this._tickCallCount>1&&this.tickImmediate(),this._tickCallCount=0)},A.tickImmediate=function(){this.clearNextTick(),this._tickTimer=self.setTimeout(this._boundTick,0)},A.doTick=function(){},E}()},"./src/types/level.ts":function(M,I,g){function R(s,b){for(var m=0;m<b.length;m++){var h=b[m];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(s,h.key,h)}}var E,A;function k(s,b){var m=s.canSkipUntil,h=s.canSkipDateRanges,T=s.endSN;return m&&(b!==void 0?b-T:0)<m?h?E.v2:E.Yes:E.No}g.r(I),g.d(I,"HlsSkip",function(){return E}),g.d(I,"getSkipValue",function(){return k}),g.d(I,"HlsUrlParameters",function(){return C}),g.d(I,"Level",function(){return D}),(A=E||(E={})).No="",A.Yes="YES",A.v2="v2";var C=function(){function s(b,m,h){this.msn=void 0,this.part=void 0,this.skip=void 0,this.msn=b,this.part=m,this.skip=h}return s.prototype.addDirectives=function(b){var m=new self.URL(b);return this.msn!==void 0&&m.searchParams.set("_HLS_msn",this.msn.toString()),this.part!==void 0&&m.searchParams.set("_HLS_part",this.part.toString()),this.skip&&m.searchParams.set("_HLS_skip",this.skip),m.toString()},s}(),D=function(){function s(h){this.attrs=void 0,this.audioCodec=void 0,this.bitrate=void 0,this.codecSet=void 0,this.height=void 0,this.id=void 0,this.name=void 0,this.videoCodec=void 0,this.width=void 0,this.unknownCodecs=void 0,this.audioGroupIds=void 0,this.details=void 0,this.fragmentError=0,this.loadError=0,this.loaded=void 0,this.realBitrate=0,this.textGroupIds=void 0,this.url=void 0,this._urlId=0,this.url=[h.url],this.attrs=h.attrs,this.bitrate=h.bitrate,h.details&&(this.details=h.details),this.id=h.id||0,this.name=h.name,this.width=h.width||0,this.height=h.height||0,this.audioCodec=h.audioCodec,this.videoCodec=h.videoCodec,this.unknownCodecs=h.unknownCodecs,this.codecSet=[h.videoCodec,h.audioCodec].filter(function(T){return T}).join(",").replace(/\.[^.,]+/g,"")}var b,m;return b=s,(m=[{key:"maxBitrate",get:function(){return Math.max(this.realBitrate,this.bitrate)}},{key:"uri",get:function(){return this.url[this._urlId]||""}},{key:"urlId",get:function(){return this._urlId},set:function(h){var T=h%this.url.length;this._urlId!==T&&(this.details=void 0,this._urlId=T)}}])&&R(b.prototype,m),s}()},"./src/types/loader.ts":function(M,I,g){var R,E,A,k;g.r(I),g.d(I,"PlaylistContextType",function(){return R}),g.d(I,"PlaylistLevelType",function(){return A}),(E=R||(R={})).MANIFEST="manifest",E.LEVEL="level",E.AUDIO_TRACK="audioTrack",E.SUBTITLE_TRACK="subtitleTrack",(k=A||(A={})).MAIN="main",k.AUDIO="audio",k.SUBTITLE="subtitle"},"./src/types/transmuxer.ts":function(M,I,g){g.r(I),g.d(I,"ChunkMetadata",function(){return R});var R=function(E,A,k,C,D,s){C===void 0&&(C=0),D===void 0&&(D=-1),s===void 0&&(s=!1),this.level=void 0,this.sn=void 0,this.part=void 0,this.id=void 0,this.size=void 0,this.partial=void 0,this.transmuxing={start:0,executeStart:0,executeEnd:0,end:0},this.buffering={audio:{start:0,executeStart:0,executeEnd:0,end:0},video:{start:0,executeStart:0,executeEnd:0,end:0},audiovideo:{start:0,executeStart:0,executeEnd:0,end:0}},this.level=E,this.sn=A,this.id=k,this.size=C,this.part=D,this.partial=s}},"./src/utils/attr-list.ts":function(M,I,g){g.r(I),g.d(I,"AttrList",function(){return A});var R=/^(\d+)x(\d+)$/,E=/\s*(.+?)\s*=((?:\".*?\")|.*?)(?:,|$)/g,A=function(){function k(D){for(var s in typeof D=="string"&&(D=k.parseAttrList(D)),D)D.hasOwnProperty(s)&&(this[s]=D[s])}var C=k.prototype;return C.decimalInteger=function(D){var s=parseInt(this[D],10);return s>Number.MAX_SAFE_INTEGER?1/0:s},C.hexadecimalInteger=function(D){if(this[D]){var s=(this[D]||"0x").slice(2);s=(1&s.length?"0":"")+s;for(var b=new Uint8Array(s.length/2),m=0;m<s.length/2;m++)b[m]=parseInt(s.slice(2*m,2*m+2),16);return b}return null},C.hexadecimalIntegerAsNumber=function(D){var s=parseInt(this[D],16);return s>Number.MAX_SAFE_INTEGER?1/0:s},C.decimalFloatingPoint=function(D){return parseFloat(this[D])},C.optionalFloat=function(D,s){var b=this[D];return b?parseFloat(b):s},C.enumeratedString=function(D){return this[D]},C.bool=function(D){return this[D]==="YES"},C.decimalResolution=function(D){var s=R.exec(this[D]);if(s!==null)return{width:parseInt(s[1],10),height:parseInt(s[2],10)}},k.parseAttrList=function(D){var s,b={};for(E.lastIndex=0;(s=E.exec(D))!==null;){var m=s[2];m.indexOf('"')===0&&m.lastIndexOf('"')===m.length-1&&(m=m.slice(1,-1)),b[s[1]]=m}return b},k}()},"./src/utils/binary-search.ts":function(M,I,g){g.r(I),I.default={search:function(R,E){for(var A=0,k=R.length-1,C=null,D=null;A<=k;){var s=E(D=R[C=(A+k)/2|0]);if(s>0)A=C+1;else{if(!(s<0))return D;k=C-1}}return null}}},"./src/utils/buffer-helper.ts":function(M,I,g){g.r(I),g.d(I,"BufferHelper",function(){return A});var R=g("./src/utils/logger.ts"),E={length:0,start:function(){return 0},end:function(){return 0}},A=function(){function k(){}return k.isBuffered=function(C,D){try{if(C){for(var s=k.getBuffered(C),b=0;b<s.length;b++)if(D>=s.start(b)&&D<=s.end(b))return!0}}catch{}return!1},k.bufferInfo=function(C,D,s){try{if(C){var b,m=k.getBuffered(C),h=[];for(b=0;b<m.length;b++)h.push({start:m.start(b),end:m.end(b)});return this.bufferedInfo(h,D,s)}}catch{}return{len:0,start:D,end:D,nextStart:void 0}},k.bufferedInfo=function(C,D,s){D=Math.max(0,D),C.sort(function(n,l){var p=n.start-l.start;return p||l.end-n.end});var b=[];if(s)for(var m=0;m<C.length;m++){var h=b.length;if(h){var T=b[h-1].end;C[m].start-T<s?C[m].end>T&&(b[h-1].end=C[m].end):b.push(C[m])}else b.push(C[m])}else b=C;for(var y,d=0,e=D,a=D,t=0;t<b.length;t++){var o=b[t].start,u=b[t].end;if(D+s>=o&&D<u)e=o,d=(a=u)-D;else if(D+s<o){y=o;break}}return{len:d,start:e||0,end:a||0,nextStart:y}},k.getBuffered=function(C){try{return C.buffered}catch(D){return R.logger.log("failed to get media.buffered",D),E}},k}()},"./src/utils/cea-608-parser.ts":function(M,I,g){g.r(I),g.d(I,"Row",function(){return o}),g.d(I,"CaptionScreen",function(){return u});var R,E,A=g("./src/utils/logger.ts"),k={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,128:174,129:176,130:189,131:191,132:8482,133:162,134:163,135:9834,136:224,137:32,138:232,139:226,140:234,141:238,142:244,143:251,144:193,145:201,146:211,147:218,148:220,149:252,150:8216,151:161,152:42,153:8217,154:9473,155:169,156:8480,157:8226,158:8220,159:8221,160:192,161:194,162:199,163:200,164:202,165:203,166:235,167:206,168:207,169:239,170:212,171:217,172:249,173:219,174:171,175:187,176:195,177:227,178:205,179:204,180:236,181:210,182:242,183:213,184:245,185:123,186:125,187:92,188:94,189:95,190:124,191:8764,192:196,193:228,194:214,195:246,196:223,197:165,198:164,199:9475,200:197,201:229,202:216,203:248,204:9487,205:9491,206:9495,207:9499},C=function(v){var r=v;return k.hasOwnProperty(v)&&(r=k[v]),String.fromCharCode(r)},D=15,s=100,b={17:1,18:3,21:5,22:7,23:9,16:11,19:12,20:14},m={17:2,18:4,21:6,22:8,23:10,19:13,20:15},h={25:1,26:3,29:5,30:7,31:9,24:11,27:12,28:14},T={25:2,26:4,29:6,30:8,31:10,27:13,28:15},y=["white","green","blue","cyan","red","yellow","magenta","black","transparent"];(E=R||(R={}))[E.ERROR=0]="ERROR",E[E.TEXT=1]="TEXT",E[E.WARNING=2]="WARNING",E[E.INFO=2]="INFO",E[E.DEBUG=3]="DEBUG",E[E.DATA=3]="DATA";var d=function(){function v(){this.time=null,this.verboseLevel=R.ERROR}return v.prototype.log=function(r,i){this.verboseLevel>=r&&A.logger.log(this.time+" ["+r+"] "+i)},v}(),e=function(v){for(var r=[],i=0;i<v.length;i++)r.push(v[i].toString(16));return r},a=function(){function v(i,c,S,L,_){this.foreground=void 0,this.underline=void 0,this.italics=void 0,this.background=void 0,this.flash=void 0,this.foreground=i||"white",this.underline=c||!1,this.italics=S||!1,this.background=L||"black",this.flash=_||!1}var r=v.prototype;return r.reset=function(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1},r.setStyles=function(i){for(var c=["foreground","underline","italics","background","flash"],S=0;S<c.length;S++){var L=c[S];i.hasOwnProperty(L)&&(this[L]=i[L])}},r.isDefault=function(){return this.foreground==="white"&&!this.underline&&!this.italics&&this.background==="black"&&!this.flash},r.equals=function(i){return this.foreground===i.foreground&&this.underline===i.underline&&this.italics===i.italics&&this.background===i.background&&this.flash===i.flash},r.copy=function(i){this.foreground=i.foreground,this.underline=i.underline,this.italics=i.italics,this.background=i.background,this.flash=i.flash},r.toString=function(){return"color="+this.foreground+", underline="+this.underline+", italics="+this.italics+", background="+this.background+", flash="+this.flash},v}(),t=function(){function v(i,c,S,L,_,x){this.uchar=void 0,this.penState=void 0,this.uchar=i||" ",this.penState=new a(c,S,L,_,x)}var r=v.prototype;return r.reset=function(){this.uchar=" ",this.penState.reset()},r.setChar=function(i,c){this.uchar=i,this.penState.copy(c)},r.setPenState=function(i){this.penState.copy(i)},r.equals=function(i){return this.uchar===i.uchar&&this.penState.equals(i.penState)},r.copy=function(i){this.uchar=i.uchar,this.penState.copy(i.penState)},r.isEmpty=function(){return this.uchar===" "&&this.penState.isDefault()},v}(),o=function(){function v(i){this.chars=void 0,this.pos=void 0,this.currPenState=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chars=[];for(var c=0;c<s;c++)this.chars.push(new t);this.logger=i,this.pos=0,this.currPenState=new a}var r=v.prototype;return r.equals=function(i){for(var c=!0,S=0;S<s;S++)if(!this.chars[S].equals(i.chars[S])){c=!1;break}return c},r.copy=function(i){for(var c=0;c<s;c++)this.chars[c].copy(i.chars[c])},r.isEmpty=function(){for(var i=!0,c=0;c<s;c++)if(!this.chars[c].isEmpty()){i=!1;break}return i},r.setCursor=function(i){this.pos!==i&&(this.pos=i),this.pos<0?(this.logger.log(R.DEBUG,"Negative cursor position "+this.pos),this.pos=0):this.pos>s&&(this.logger.log(R.DEBUG,"Too large cursor position "+this.pos),this.pos=s)},r.moveCursor=function(i){var c=this.pos+i;if(i>1)for(var S=this.pos+1;S<c+1;S++)this.chars[S].setPenState(this.currPenState);this.setCursor(c)},r.backSpace=function(){this.moveCursor(-1),this.chars[this.pos].setChar(" ",this.currPenState)},r.insertChar=function(i){i>=144&&this.backSpace();var c=C(i);this.pos>=s?this.logger.log(R.ERROR,"Cannot insert "+i.toString(16)+" ("+c+") at position "+this.pos+". Skipping it!"):(this.chars[this.pos].setChar(c,this.currPenState),this.moveCursor(1))},r.clearFromPos=function(i){var c;for(c=i;c<s;c++)this.chars[c].reset()},r.clear=function(){this.clearFromPos(0),this.pos=0,this.currPenState.reset()},r.clearToEndOfRow=function(){this.clearFromPos(this.pos)},r.getTextString=function(){for(var i=[],c=!0,S=0;S<s;S++){var L=this.chars[S].uchar;L!==" "&&(c=!1),i.push(L)}return c?"":i.join("")},r.setPenStyles=function(i){this.currPenState.setStyles(i),this.chars[this.pos].setPenState(this.currPenState)},v}(),u=function(){function v(i){this.rows=void 0,this.currRow=void 0,this.nrRollUpRows=void 0,this.lastOutputScreen=void 0,this.logger=void 0,this.rows=[];for(var c=0;c<D;c++)this.rows.push(new o(i));this.logger=i,this.currRow=14,this.nrRollUpRows=null,this.lastOutputScreen=null,this.reset()}var r=v.prototype;return r.reset=function(){for(var i=0;i<D;i++)this.rows[i].clear();this.currRow=14},r.equals=function(i){for(var c=!0,S=0;S<D;S++)if(!this.rows[S].equals(i.rows[S])){c=!1;break}return c},r.copy=function(i){for(var c=0;c<D;c++)this.rows[c].copy(i.rows[c])},r.isEmpty=function(){for(var i=!0,c=0;c<D;c++)if(!this.rows[c].isEmpty()){i=!1;break}return i},r.backSpace=function(){this.rows[this.currRow].backSpace()},r.clearToEndOfRow=function(){this.rows[this.currRow].clearToEndOfRow()},r.insertChar=function(i){this.rows[this.currRow].insertChar(i)},r.setPen=function(i){this.rows[this.currRow].setPenStyles(i)},r.moveCursor=function(i){this.rows[this.currRow].moveCursor(i)},r.setCursor=function(i){this.logger.log(R.INFO,"setCursor: "+i),this.rows[this.currRow].setCursor(i)},r.setPAC=function(i){this.logger.log(R.INFO,"pacData = "+JSON.stringify(i));var c=i.row-1;if(this.nrRollUpRows&&c<this.nrRollUpRows-1&&(c=this.nrRollUpRows-1),this.nrRollUpRows&&this.currRow!==c){for(var S=0;S<D;S++)this.rows[S].clear();var L=this.currRow+1-this.nrRollUpRows,_=this.lastOutputScreen;if(_){var x=_.rows[L].cueStartTime,w=this.logger.time;if(x&&w!==null&&x<w)for(var O=0;O<this.nrRollUpRows;O++)this.rows[c-this.nrRollUpRows+O+1].copy(_.rows[L+O])}}this.currRow=c;var P=this.rows[this.currRow];if(i.indent!==null){var F=i.indent,N=Math.max(F-1,0);P.setCursor(i.indent),i.color=P.chars[N].penState.foreground}var U={foreground:i.color,underline:i.underline,italics:i.italics,background:"black",flash:!1};this.setPen(U)},r.setBkgData=function(i){this.logger.log(R.INFO,"bkgData = "+JSON.stringify(i)),this.backSpace(),this.setPen(i),this.insertChar(32)},r.setRollUpRows=function(i){this.nrRollUpRows=i},r.rollUp=function(){if(this.nrRollUpRows!==null){this.logger.log(R.TEXT,this.getDisplayText());var i=this.currRow+1-this.nrRollUpRows,c=this.rows.splice(i,1)[0];c.clear(),this.rows.splice(this.currRow,0,c),this.logger.log(R.INFO,"Rolling up")}else this.logger.log(R.DEBUG,"roll_up but nrRollUpRows not set yet")},r.getDisplayText=function(i){i=i||!1;for(var c=[],S="",L=-1,_=0;_<D;_++){var x=this.rows[_].getTextString();x&&(L=_+1,i?c.push("Row "+L+": '"+x+"'"):c.push(x.trim()))}return c.length>0&&(S=i?"["+c.join(" | ")+"]":c.join(`
`)),S},r.getTextAndFormat=function(){return this.rows},v}(),n=function(){function v(i,c,S){this.chNr=void 0,this.outputFilter=void 0,this.mode=void 0,this.verbose=void 0,this.displayedMemory=void 0,this.nonDisplayedMemory=void 0,this.lastOutputScreen=void 0,this.currRollUpRow=void 0,this.writeScreen=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chNr=i,this.outputFilter=c,this.mode=null,this.verbose=0,this.displayedMemory=new u(S),this.nonDisplayedMemory=new u(S),this.lastOutputScreen=new u(S),this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null,this.logger=S}var r=v.prototype;return r.reset=function(){this.mode=null,this.displayedMemory.reset(),this.nonDisplayedMemory.reset(),this.lastOutputScreen.reset(),this.outputFilter.reset(),this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null},r.getHandler=function(){return this.outputFilter},r.setHandler=function(i){this.outputFilter=i},r.setPAC=function(i){this.writeScreen.setPAC(i)},r.setBkgData=function(i){this.writeScreen.setBkgData(i)},r.setMode=function(i){i!==this.mode&&(this.mode=i,this.logger.log(R.INFO,"MODE="+i),this.mode==="MODE_POP-ON"?this.writeScreen=this.nonDisplayedMemory:(this.writeScreen=this.displayedMemory,this.writeScreen.reset()),this.mode!=="MODE_ROLL-UP"&&(this.displayedMemory.nrRollUpRows=null,this.nonDisplayedMemory.nrRollUpRows=null),this.mode=i)},r.insertChars=function(i){for(var c=0;c<i.length;c++)this.writeScreen.insertChar(i[c]);var S=this.writeScreen===this.displayedMemory?"DISP":"NON_DISP";this.logger.log(R.INFO,S+": "+this.writeScreen.getDisplayText(!0)),this.mode!=="MODE_PAINT-ON"&&this.mode!=="MODE_ROLL-UP"||(this.logger.log(R.TEXT,"DISPLAYED: "+this.displayedMemory.getDisplayText(!0)),this.outputDataUpdate())},r.ccRCL=function(){this.logger.log(R.INFO,"RCL - Resume Caption Loading"),this.setMode("MODE_POP-ON")},r.ccBS=function(){this.logger.log(R.INFO,"BS - BackSpace"),this.mode!=="MODE_TEXT"&&(this.writeScreen.backSpace(),this.writeScreen===this.displayedMemory&&this.outputDataUpdate())},r.ccAOF=function(){},r.ccAON=function(){},r.ccDER=function(){this.logger.log(R.INFO,"DER- Delete to End of Row"),this.writeScreen.clearToEndOfRow(),this.outputDataUpdate()},r.ccRU=function(i){this.logger.log(R.INFO,"RU("+i+") - Roll Up"),this.writeScreen=this.displayedMemory,this.setMode("MODE_ROLL-UP"),this.writeScreen.setRollUpRows(i)},r.ccFON=function(){this.logger.log(R.INFO,"FON - Flash On"),this.writeScreen.setPen({flash:!0})},r.ccRDC=function(){this.logger.log(R.INFO,"RDC - Resume Direct Captioning"),this.setMode("MODE_PAINT-ON")},r.ccTR=function(){this.logger.log(R.INFO,"TR"),this.setMode("MODE_TEXT")},r.ccRTD=function(){this.logger.log(R.INFO,"RTD"),this.setMode("MODE_TEXT")},r.ccEDM=function(){this.logger.log(R.INFO,"EDM - Erase Displayed Memory"),this.displayedMemory.reset(),this.outputDataUpdate(!0)},r.ccCR=function(){this.logger.log(R.INFO,"CR - Carriage Return"),this.writeScreen.rollUp(),this.outputDataUpdate(!0)},r.ccENM=function(){this.logger.log(R.INFO,"ENM - Erase Non-displayed Memory"),this.nonDisplayedMemory.reset()},r.ccEOC=function(){if(this.logger.log(R.INFO,"EOC - End Of Caption"),this.mode==="MODE_POP-ON"){var i=this.displayedMemory;this.displayedMemory=this.nonDisplayedMemory,this.nonDisplayedMemory=i,this.writeScreen=this.nonDisplayedMemory,this.logger.log(R.TEXT,"DISP: "+this.displayedMemory.getDisplayText())}this.outputDataUpdate(!0)},r.ccTO=function(i){this.logger.log(R.INFO,"TO("+i+") - Tab Offset"),this.writeScreen.moveCursor(i)},r.ccMIDROW=function(i){var c={flash:!1};if(c.underline=i%2==1,c.italics=i>=46,c.italics)c.foreground="white";else{var S=Math.floor(i/2)-16;c.foreground=["white","green","blue","cyan","red","yellow","magenta"][S]}this.logger.log(R.INFO,"MIDROW: "+JSON.stringify(c)),this.writeScreen.setPen(c)},r.outputDataUpdate=function(i){i===void 0&&(i=!1);var c=this.logger.time;c!==null&&this.outputFilter&&(this.cueStartTime!==null||this.displayedMemory.isEmpty()?this.displayedMemory.equals(this.lastOutputScreen)||(this.outputFilter.newCue(this.cueStartTime,c,this.lastOutputScreen),i&&this.outputFilter.dispatchCue&&this.outputFilter.dispatchCue(),this.cueStartTime=this.displayedMemory.isEmpty()?null:c):this.cueStartTime=c,this.lastOutputScreen.copy(this.displayedMemory))},r.cueSplitAtTime=function(i){this.outputFilter&&(this.displayedMemory.isEmpty()||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,i,this.displayedMemory),this.cueStartTime=i))},v}(),l=function(){function v(i,c,S){this.channels=void 0,this.currentChannel=0,this.cmdHistory=void 0,this.logger=void 0;var L=new d;this.channels=[null,new n(i,c,L),new n(i+1,S,L)],this.cmdHistory={a:null,b:null},this.logger=L}var r=v.prototype;return r.getHandler=function(i){return this.channels[i].getHandler()},r.setHandler=function(i,c){this.channels[i].setHandler(c)},r.addData=function(i,c){var S,L,_,x=!1;this.logger.time=i;for(var w=0;w<c.length;w+=2)if(L=127&c[w],_=127&c[w+1],L!==0||_!==0){if(this.logger.log(R.DATA,"["+e([c[w],c[w+1]])+"] -> ("+e([L,_])+")"),(S=this.parseCmd(L,_))||(S=this.parseMidrow(L,_)),S||(S=this.parsePAC(L,_)),S||(S=this.parseBackgroundAttributes(L,_)),!S&&(x=this.parseChars(L,_))){var O=this.currentChannel;O&&O>0?this.channels[O].insertChars(x):this.logger.log(R.WARNING,"No channel found yet. TEXT-MODE?")}S||x||this.logger.log(R.WARNING,"Couldn't parse cleaned data "+e([L,_])+" orig: "+e([c[w],c[w+1]]))}},r.parseCmd=function(i,c){var S=this.cmdHistory;if(!((i===20||i===28||i===21||i===29)&&c>=32&&c<=47||(i===23||i===31)&&c>=33&&c<=35))return!1;if(f(i,c,S))return p(null,null,S),this.logger.log(R.DEBUG,"Repeated command ("+e([i,c])+") is dropped"),!0;var L=i===20||i===21||i===23?1:2,_=this.channels[L];return i===20||i===21||i===28||i===29?c===32?_.ccRCL():c===33?_.ccBS():c===34?_.ccAOF():c===35?_.ccAON():c===36?_.ccDER():c===37?_.ccRU(2):c===38?_.ccRU(3):c===39?_.ccRU(4):c===40?_.ccFON():c===41?_.ccRDC():c===42?_.ccTR():c===43?_.ccRTD():c===44?_.ccEDM():c===45?_.ccCR():c===46?_.ccENM():c===47&&_.ccEOC():_.ccTO(c-32),p(i,c,S),this.currentChannel=L,!0},r.parseMidrow=function(i,c){var S=0;if((i===17||i===25)&&c>=32&&c<=47){if((S=i===17?1:2)!==this.currentChannel)return this.logger.log(R.ERROR,"Mismatch channel in midrow parsing"),!1;var L=this.channels[S];return!!L&&(L.ccMIDROW(c),this.logger.log(R.DEBUG,"MIDROW ("+e([i,c])+")"),!0)}return!1},r.parsePAC=function(i,c){var S,L=this.cmdHistory;if(!((i>=17&&i<=23||i>=25&&i<=31)&&c>=64&&c<=127||(i===16||i===24)&&c>=64&&c<=95))return!1;if(f(i,c,L))return p(null,null,L),!0;var _=i<=23?1:2;S=c>=64&&c<=95?_===1?b[i]:h[i]:_===1?m[i]:T[i];var x=this.channels[_];return!!x&&(x.setPAC(this.interpretPAC(S,c)),p(i,c,L),this.currentChannel=_,!0)},r.interpretPAC=function(i,c){var S,L={color:null,italics:!1,indent:null,underline:!1,row:i};return S=c>95?c-96:c-64,L.underline=(1&S)==1,S<=13?L.color=["white","green","blue","cyan","red","yellow","magenta","white"][Math.floor(S/2)]:S<=15?(L.italics=!0,L.color="white"):L.indent=4*Math.floor((S-16)/2),L},r.parseChars=function(i,c){var S,L,_=null,x=null;if(i>=25?(S=2,x=i-8):(S=1,x=i),x>=17&&x<=19?(L=x===17?c+80:x===18?c+112:c+144,this.logger.log(R.INFO,"Special char '"+C(L)+"' in channel "+S),_=[L]):i>=32&&i<=127&&(_=c===0?[i]:[i,c]),_){var w=e(_);this.logger.log(R.DEBUG,"Char codes =  "+w.join(",")),p(i,c,this.cmdHistory)}return _},r.parseBackgroundAttributes=function(i,c){var S;if(!((i===16||i===24)&&c>=32&&c<=47||(i===23||i===31)&&c>=45&&c<=47))return!1;var L={};i===16||i===24?(S=Math.floor((c-32)/2),L.background=y[S],c%2==1&&(L.background=L.background+"_semi")):c===45?L.background="transparent":(L.foreground="black",c===47&&(L.underline=!0));var _=i<=23?1:2;return this.channels[_].setBkgData(L),p(i,c,this.cmdHistory),!0},r.reset=function(){for(var i=0;i<Object.keys(this.channels).length;i++){var c=this.channels[i];c&&c.reset()}this.cmdHistory={a:null,b:null}},r.cueSplitAtTime=function(i){for(var c=0;c<this.channels.length;c++){var S=this.channels[c];S&&S.cueSplitAtTime(i)}},v}();function p(v,r,i){i.a=v,i.b=r}function f(v,r,i){return i.a===v&&i.b===r}I.default=l},"./src/utils/codecs.ts":function(M,I,g){g.r(I),g.d(I,"isCodecType",function(){return E}),g.d(I,"isCodecSupportedInMp4",function(){return A});var R={audio:{a3ds:!0,"ac-3":!0,"ac-4":!0,alac:!0,alaw:!0,dra1:!0,"dts+":!0,"dts-":!0,dtsc:!0,dtse:!0,dtsh:!0,"ec-3":!0,enca:!0,g719:!0,g726:!0,m4ae:!0,mha1:!0,mha2:!0,mhm1:!0,mhm2:!0,mlpa:!0,mp4a:!0,"raw ":!0,Opus:!0,samr:!0,sawb:!0,sawp:!0,sevc:!0,sqcp:!0,ssmv:!0,twos:!0,ulaw:!0},video:{avc1:!0,avc2:!0,avc3:!0,avc4:!0,avcp:!0,av01:!0,drac:!0,dvav:!0,dvhe:!0,encv:!0,hev1:!0,hvc1:!0,mjp2:!0,mp4v:!0,mvc1:!0,mvc2:!0,mvc3:!0,mvc4:!0,resv:!0,rv60:!0,s263:!0,svc1:!0,svc2:!0,"vc-1":!0,vp08:!0,vp09:!0},text:{stpp:!0,wvtt:!0}};function E(k,C){var D=R[C];return!!D&&D[k.slice(0,4)]===!0}function A(k,C){return MediaSource.isTypeSupported((C||"video")+'/mp4;codecs="'+k+'"')}},"./src/utils/cues.ts":function(M,I,g){g.r(I);var R=g("./src/utils/vttparser.ts"),E=g("./src/utils/webvtt-parser.ts"),A=g("./src/utils/texttrack-utils.ts"),k=/\s/,C={newCue:function(D,s,b,m){for(var h,T,y,d,e,a=[],t=self.VTTCue||self.TextTrackCue,o=0;o<m.rows.length;o++)if(y=!0,d=0,e="",!(h=m.rows[o]).isEmpty()){for(var u=0;u<h.chars.length;u++)k.test(h.chars[u].uchar)&&y?d++:(e+=h.chars[u].uchar,y=!1);h.cueStartTime=s,s===b&&(b+=1e-4),d>=16?d--:d++;var n=Object(R.fixLineBreaks)(e.trim()),l=Object(E.generateCueId)(s,b,n);D&&D.cues&&D.cues.getCueById(l)||((T=new t(s,b,n)).id=l,T.line=o+1,T.align="left",T.position=10+Math.min(80,10*Math.floor(8*d/32)),a.push(T))}return D&&a.length&&(a.sort(function(p,f){return p.line==="auto"||f.line==="auto"?0:p.line>8&&f.line>8?f.line-p.line:p.line-f.line}),a.forEach(function(p){return Object(A.addCueToTrack)(D,p)})),a}};I.default=C},"./src/utils/discontinuities.ts":function(M,I,g){g.r(I),g.d(I,"findFirstFragWithCC",function(){return k}),g.d(I,"shouldAlignOnDiscontinuities",function(){return C}),g.d(I,"findDiscontinuousReferenceFrag",function(){return D}),g.d(I,"adjustSlidingStart",function(){return b}),g.d(I,"alignStream",function(){return m}),g.d(I,"alignPDT",function(){return h});var R=g("./src/polyfills/number.ts"),E=g("./src/utils/logger.ts"),A=g("./src/controller/level-helper.ts");function k(T,y){for(var d=null,e=0,a=T.length;e<a;e++){var t=T[e];if(t&&t.cc===y){d=t;break}}return d}function C(T,y,d){return!(!y.details||!(d.endCC>d.startCC||T&&T.cc<d.startCC))}function D(T,y){var d=T.fragments,e=y.fragments;if(e.length&&d.length){var a=k(d,e[0].cc);if(a&&(!a||a.startPTS))return a;E.logger.log("No frag in previous level to align on")}else E.logger.log("No fragments to align")}function s(T,y){if(T){var d=T.start+y;T.start=T.startPTS=d,T.endPTS=d+T.duration}}function b(T,y){for(var d=y.fragments,e=0,a=d.length;e<a;e++)s(d[e],T);y.fragmentHint&&s(y.fragmentHint,T),y.alignedSliding=!0}function m(T,y,d){y&&(function(e,a,t){if(C(e,t,a)){var o=D(t.details,a);o&&Object(R.isFiniteNumber)(o.start)&&(E.logger.log("Adjusting PTS using last level due to CC increase within current level "+a.url),b(o.start,a))}}(T,d,y),!d.alignedSliding&&y.details&&h(d,y.details),d.alignedSliding||!y.details||d.skippedSegments||Object(A.adjustSliding)(y.details,d))}function h(T,y){if(y.fragments.length&&T.hasProgramDateTime&&y.hasProgramDateTime){var d=y.fragments[0].programDateTime,e=T.fragments[0].programDateTime,a=(e-d)/1e3+y.fragments[0].start;a&&Object(R.isFiniteNumber)(a)&&(E.logger.log("Adjusting PTS using programDateTime delta "+(e-d)+"ms, sliding:"+a.toFixed(3)+" "+T.url+" "),b(a,T))}}},"./src/utils/ewma-bandwidth-estimator.ts":function(M,I,g){g.r(I);var R=g("./src/utils/ewma.ts"),E=function(){function A(C,D,s){this.defaultEstimate_=void 0,this.minWeight_=void 0,this.minDelayMs_=void 0,this.slow_=void 0,this.fast_=void 0,this.defaultEstimate_=s,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new R.default(C),this.fast_=new R.default(D)}var k=A.prototype;return k.update=function(C,D){var s=this.slow_,b=this.fast_;this.slow_.halfLife!==C&&(this.slow_=new R.default(C,s.getEstimate(),s.getTotalWeight())),this.fast_.halfLife!==D&&(this.fast_=new R.default(D,b.getEstimate(),b.getTotalWeight()))},k.sample=function(C,D){var s=(C=Math.max(C,this.minDelayMs_))/1e3,b=8*D/s;this.fast_.sample(s,b),this.slow_.sample(s,b)},k.canEstimate=function(){var C=this.fast_;return C&&C.getTotalWeight()>=this.minWeight_},k.getEstimate=function(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_},k.destroy=function(){},A}();I.default=E},"./src/utils/ewma.ts":function(M,I,g){g.r(I);var R=function(){function E(k,C,D){C===void 0&&(C=0),D===void 0&&(D=0),this.halfLife=void 0,this.alpha_=void 0,this.estimate_=void 0,this.totalWeight_=void 0,this.halfLife=k,this.alpha_=k?Math.exp(Math.log(.5)/k):0,this.estimate_=C,this.totalWeight_=D}var A=E.prototype;return A.sample=function(k,C){var D=Math.pow(this.alpha_,k);this.estimate_=C*(1-D)+D*this.estimate_,this.totalWeight_+=k},A.getTotalWeight=function(){return this.totalWeight_},A.getEstimate=function(){if(this.alpha_){var k=1-Math.pow(this.alpha_,this.totalWeight_);if(k)return this.estimate_/k}return this.estimate_},E}();I.default=R},"./src/utils/fetch-loader.ts":function(M,I,g){g.r(I),g.d(I,"fetchSupported",function(){return m});var R=g("./src/polyfills/number.ts"),E=g("./src/loader/load-stats.ts"),A=g("./src/demux/chunk-cache.ts");function k(d){var e=typeof Map=="function"?new Map:void 0;return(k=function(a){if(a===null||(t=a,Function.toString.call(t).indexOf("[native code]")===-1))return a;var t;if(typeof a!="function")throw new TypeError("Super expression must either be null or a function");if(e!==void 0){if(e.has(a))return e.get(a);e.set(a,o)}function o(){return C(a,arguments,b(this).constructor)}return o.prototype=Object.create(a.prototype,{constructor:{value:o,enumerable:!1,writable:!0,configurable:!0}}),s(o,a)})(d)}function C(d,e,a){return(C=D()?Reflect.construct:function(t,o,u){var n=[null];n.push.apply(n,o);var l=new(Function.bind.apply(t,n));return u&&s(l,u.prototype),l}).apply(null,arguments)}function D(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function s(d,e){return(s=Object.setPrototypeOf||function(a,t){return a.__proto__=t,a})(d,e)}function b(d){return(b=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(d)}function m(){if(self.fetch&&self.AbortController&&self.ReadableStream&&self.Request)try{return new self.ReadableStream({}),!0}catch{}return!1}var h=function(){function d(a){this.fetchSetup=void 0,this.requestTimeout=void 0,this.request=void 0,this.response=void 0,this.controller=void 0,this.context=void 0,this.config=null,this.callbacks=null,this.stats=void 0,this.loader=null,this.fetchSetup=a.fetchSetup||T,this.controller=new self.AbortController,this.stats=new E.LoadStats}var e=d.prototype;return e.destroy=function(){this.loader=this.callbacks=null,this.abortInternal()},e.abortInternal=function(){var a=this.response;a&&a.ok||(this.stats.aborted=!0,this.controller.abort())},e.abort=function(){var a;this.abortInternal(),(a=this.callbacks)!==null&&a!==void 0&&a.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.response)},e.load=function(a,t,o){var u=this,n=this.stats;if(n.loading.start)throw new Error("Loader can only be used once.");n.loading.start=self.performance.now();var l=function(r,i){var c={method:"GET",mode:"cors",credentials:"same-origin",signal:i};return r.rangeEnd&&(c.headers=new self.Headers({Range:"bytes="+r.rangeStart+"-"+String(r.rangeEnd-1)})),c}(a,this.controller.signal),p=o.onProgress,f=a.responseType==="arraybuffer",v=f?"byteLength":"length";this.context=a,this.config=t,this.callbacks=o,this.request=this.fetchSetup(a,l),self.clearTimeout(this.requestTimeout),this.requestTimeout=self.setTimeout(function(){u.abortInternal(),o.onTimeout(n,a,u.response)},t.timeout),self.fetch(this.request).then(function(r){if(u.response=u.loader=r,!r.ok){var i=r.status,c=r.statusText;throw new y(c||"fetch, bad network response",i,r)}return n.loading.first=Math.max(self.performance.now(),n.loading.start),n.total=parseInt(r.headers.get("Content-Length")||"0"),p&&Object(R.isFiniteNumber)(t.highWaterMark)?u.loadProgressively(r,n,a,t.highWaterMark,p):f?r.arrayBuffer():r.text()}).then(function(r){var i=u.response;self.clearTimeout(u.requestTimeout),n.loading.end=Math.max(self.performance.now(),n.loading.first),n.loaded=n.total=r[v];var c={url:i.url,data:r};p&&!Object(R.isFiniteNumber)(t.highWaterMark)&&p(n,a,r,i),o.onSuccess(c,n,a,i)}).catch(function(r){if(self.clearTimeout(u.requestTimeout),!n.aborted){var i=r.code||0;o.onError({code:i,text:r.message},a,r.details)}})},e.getCacheAge=function(){var a=null;if(this.response){var t=this.response.headers.get("age");a=t?parseFloat(t):null}return a},e.loadProgressively=function(a,t,o,u,n){u===void 0&&(u=0);var l=new A.default,p=a.body.getReader();return function f(){return p.read().then(function(v){if(v.done)return l.dataLength&&n(t,o,l.flush(),a),Promise.resolve(new ArrayBuffer(0));var r=v.value,i=r.length;return t.loaded+=i,i<u||l.dataLength?(l.push(r),l.dataLength>=u&&n(t,o,l.flush(),a)):n(t,o,r,a),f()}).catch(function(){return Promise.reject()})}()},d}();function T(d,e){return new self.Request(d.url,e)}var y=function(d){var e,a;function t(o,u,n){var l;return(l=d.call(this,o)||this).code=void 0,l.details=void 0,l.code=u,l.details=n,l}return a=d,(e=t).prototype=Object.create(a.prototype),e.prototype.constructor=e,s(e,a),t}(k(Error));I.default=h},"./src/utils/imsc1-ttml-parser.ts":function(M,I,g){g.r(I),g.d(I,"IMSC1_CODEC",function(){return b}),g.d(I,"parseIMSC1",function(){return y});var R=g("./src/utils/mp4-tools.ts"),E=g("./src/utils/vttparser.ts"),A=g("./src/utils/vttcue.ts"),k=g("./src/demux/id3.ts"),C=g("./src/utils/timescale-conversion.ts"),D=g("./src/utils/webvtt-parser.ts");function s(){return(s=Object.assign||function(n){for(var l=1;l<arguments.length;l++){var p=arguments[l];for(var f in p)Object.prototype.hasOwnProperty.call(p,f)&&(n[f]=p[f])}return n}).apply(this,arguments)}var b="stpp.ttml.im1t",m=/^(\d{2,}):(\d{2}):(\d{2}):(\d{2})\.?(\d+)?$/,h=/^(\d*(?:\.\d*)?)(h|m|s|ms|f|t)$/,T={left:"start",center:"center",right:"end",start:"start",end:"end"};function y(n,l,p,f,v){var r=Object(R.findBox)(new Uint8Array(n),["mdat"]);if(r.length!==0){var i=r[0],c=Object(k.utf8ArrayToStr)(new Uint8Array(n,i.start,i.end-i.start)),S=Object(C.toTimescaleFromScale)(l,1,p);try{f(function(L,_){var x=new DOMParser().parseFromString(L,"text/xml").getElementsByTagName("tt")[0];if(!x)throw new Error("Invalid ttml");var w={frameRate:30,subFrameRate:1,frameRateMultiplier:0,tickRate:0},O=Object.keys(w).reduce(function(G,K){return G[K]=x.getAttribute("ttp:"+K)||w[K],G},{}),P=x.getAttribute("xml:space")!=="preserve",F=e(d(x,"styling","style")),N=e(d(x,"layout","region")),U=d(x,"body","[begin]");return[].map.call(U,function(G){var K=a(G,P);if(!K||!G.hasAttribute("begin"))return null;var q=u(G.getAttribute("begin"),O),Y=u(G.getAttribute("dur"),O),j=u(G.getAttribute("end"),O);if(q===null)throw o(G);if(j===null){if(Y===null)throw o(G);j=q+Y}var X=new A.default(q-_,j-_,K);X.id=Object(D.generateCueId)(X.startTime,X.endTime,X.text);var Q=N[G.getAttribute("region")],te=F[G.getAttribute("style")];X.position=10,X.size=80;var ae=function(ue,J){var ne="http://www.w3.org/ns/ttml#styling";return["displayAlign","textAlign","color","backgroundColor","fontSize","fontFamily"].reduce(function(le,se){var pe=t(J,ne,se)||t(ue,ne,se);return pe&&(le[se]=pe),le},{})}(Q,te),oe=ae.textAlign;if(oe){var ie=T[oe];ie&&(X.lineAlign=ie),X.align=oe}return s(X,ae),X}).filter(function(G){return G!==null})}(c,S))}catch(L){v(L)}}else v(new Error("Could not parse IMSC1 mdat"))}function d(n,l,p){var f=n.getElementsByTagName(l)[0];return f?[].slice.call(f.querySelectorAll(p)):[]}function e(n){return n.reduce(function(l,p){var f=p.getAttribute("xml:id");return f&&(l[f]=p),l},{})}function a(n,l){return[].slice.call(n.childNodes).reduce(function(p,f,v){var r;return f.nodeName==="br"&&v?p+`
`:(r=f.childNodes)!==null&&r!==void 0&&r.length?a(f,l):l?p+f.textContent.trim().replace(/\s+/g," "):p+f.textContent},"")}function t(n,l,p){return n.hasAttributeNS(l,p)?n.getAttributeNS(l,p):null}function o(n){return new Error("Could not parse ttml timestamp "+n)}function u(n,l){if(!n)return null;var p=Object(E.parseTimeStamp)(n);return p===null&&(m.test(n)?p=function(f,v){var r=m.exec(f),i=(0|r[4])+(0|r[5])/v.subFrameRate;return 3600*(0|r[1])+60*(0|r[2])+(0|r[3])+i/v.frameRate}(n,l):h.test(n)&&(p=function(f,v){var r=h.exec(f),i=Number(r[1]);switch(r[2]){case"h":return 3600*i;case"m":return 60*i;case"ms":return 1e3*i;case"f":return i/v.frameRate;case"t":return i/v.tickRate}return i}(n,l))),p}},"./src/utils/logger.ts":function(M,I,g){g.r(I),g.d(I,"enableLogs",function(){return C}),g.d(I,"logger",function(){return D});var R=function(){},E={trace:R,debug:R,log:R,warn:R,info:R,error:R},A=E;function k(s){var b=self.console[s];return b?b.bind(self.console,"["+s+"] >"):R}function C(s){if(self.console&&s===!0||typeof s=="object"){(function(b){for(var m=arguments.length,h=new Array(m>1?m-1:0),T=1;T<m;T++)h[T-1]=arguments[T];h.forEach(function(y){A[y]=b[y]?b[y].bind(b):k(y)})})(s,"debug","log","info","warn","error");try{A.log()}catch{A=E}}else A=E}var D=E},"./src/utils/mediakeys-helper.ts":function(M,I,g){var R,E;g.r(I),g.d(I,"KeySystems",function(){return R}),g.d(I,"requestMediaKeySystemAccess",function(){return A}),(E=R||(R={})).WIDEVINE="com.widevine.alpha",E.PLAYREADY="com.microsoft.playready";var A=typeof self<"u"&&self.navigator&&self.navigator.requestMediaKeySystemAccess?self.navigator.requestMediaKeySystemAccess.bind(self.navigator):null},"./src/utils/mediasource-helper.ts":function(M,I,g){function R(){return self.MediaSource||self.WebKitMediaSource}g.r(I),g.d(I,"getMediaSource",function(){return R})},"./src/utils/mp4-tools.ts":function(M,I,g){g.r(I),g.d(I,"bin2str",function(){return C}),g.d(I,"readUint16",function(){return D}),g.d(I,"readUint32",function(){return s}),g.d(I,"writeUint32",function(){return b}),g.d(I,"findBox",function(){return m}),g.d(I,"parseSegmentIndex",function(){return h}),g.d(I,"parseInitSegment",function(){return T}),g.d(I,"getStartDTS",function(){return y}),g.d(I,"getDuration",function(){return d}),g.d(I,"computeRawDurationFromSamples",function(){return e}),g.d(I,"offsetStartDTS",function(){return a}),g.d(I,"segmentValidRange",function(){return t}),g.d(I,"appendUint8Array",function(){return o});var R=g("./src/utils/typed-array.ts"),E=g("./src/loader/fragment.ts"),A=Math.pow(2,32)-1,k=[].push;function C(u){return String.fromCharCode.apply(null,u)}function D(u,n){"data"in u&&(n+=u.start,u=u.data);var l=u[n]<<8|u[n+1];return l<0?65536+l:l}function s(u,n){"data"in u&&(n+=u.start,u=u.data);var l=u[n]<<24|u[n+1]<<16|u[n+2]<<8|u[n+3];return l<0?4294967296+l:l}function b(u,n,l){"data"in u&&(n+=u.start,u=u.data),u[n]=l>>24,u[n+1]=l>>16&255,u[n+2]=l>>8&255,u[n+3]=255&l}function m(u,n){var l,p,f,v=[];if(!n.length)return v;"data"in u?(l=u.data,p=u.start,f=u.end):(p=0,f=(l=u).byteLength);for(var r=p;r<f;){var i=s(l,r),c=i>1?r+i:f;if(C(l.subarray(r+4,r+8))===n[0])if(n.length===1)v.push({data:l,start:r+8,end:c});else{var S=m({data:l,start:r+8,end:c},n.slice(1));S.length&&k.apply(v,S)}r=c}return v}function h(u){var n=m(u,["moov"])[0],l=n?n.end:null,p=m(u,["sidx"]);if(!p||!p[0])return null;var f=[],v=p[0],r=v.data[0],i=r===0?8:16,c=s(v,i);i+=4,i+=r===0?8:16,i+=2;var S=v.end+0,L=D(v,i);i+=2;for(var _=0;_<L;_++){var x=i,w=s(v,x);x+=4;var O=2147483647&w;if((2147483648&w)>>>31==1)return console.warn("SIDX has hierarchical references (not supported)"),null;var P=s(v,x);x+=4,f.push({referenceSize:O,subsegmentDuration:P,info:{duration:P/c,start:S,end:S+O-1}}),S+=O,i=x+=4}return{earliestPresentationTime:0,timescale:c,version:r,referencesCount:L,references:f,moovEndOffset:l}}function T(u){for(var n=[],l=m(u,["moov","trak"]),p=0;p<l.length;p++){var f=l[p],v=m(f,["tkhd"])[0];if(v){var r=v.data[v.start],i=r===0?12:20,c=s(v,i),S=m(f,["mdia","mdhd"])[0];if(S){var L=s(S,i=(r=S.data[S.start])===0?12:20),_=m(f,["mdia","hdlr"])[0];if(_){var x=C(_.data.subarray(_.start+8,_.start+12)),w={soun:E.ElementaryStreamTypes.AUDIO,vide:E.ElementaryStreamTypes.VIDEO}[x];if(w){var O=m(f,["mdia","minf","stbl","stsd"])[0],P=void 0;O&&(P=C(O.data.subarray(O.start+12,O.start+16))),n[c]={timescale:L,type:w},n[w]={timescale:L,id:c,codec:P}}}}}}return m(u,["moov","mvex","trex"]).forEach(function(F){var N=s(F,4),U=n[N];U&&(U.default={duration:s(F,12),flags:s(F,20)})}),n}function y(u,n){return m(n,["moof","traf"]).reduce(function(l,p){var f=m(p,["tfdt"])[0],v=f.data[f.start],r=m(p,["tfhd"]).reduce(function(i,c){var S=s(c,4),L=u[S];if(L){var _=s(f,4);v===1&&(_*=Math.pow(2,32),_+=s(f,8));var x=_/(L.timescale||9e4);if(isFinite(x)&&(i===null||x<i))return x}return i},null);return r!==null&&isFinite(r)&&(l===null||r<l)?r:l},null)||0}function d(u,n){for(var l=0,p=0,f=0,v=m(u,["moof","traf"]),r=0;r<v.length;r++){var i=v[r],c=m(i,["tfhd"])[0],S=n[s(c,4)];if(S){var L=S.default,_=s(c,0)|(L==null?void 0:L.flags),x=L==null?void 0:L.duration;8&_&&(x=s(c,2&_?12:8));for(var w=S.timescale||9e4,O=m(i,["trun"]),P=0;P<O.length;P++)l=x?x*s(O[P],4):e(O[P]),S.type===E.ElementaryStreamTypes.VIDEO?p+=l/w:S.type===E.ElementaryStreamTypes.AUDIO&&(f+=l/w)}}if(p===0&&f===0){var F=h(u);if(F!=null&&F.references)return F.references.reduce(function(N,U){return N+U.info.duration||0},0)}return p||f}function e(u){var n=s(u,0),l=8;1&n&&(l+=4),4&n&&(l+=4);for(var p=0,f=s(u,4),v=0;v<f;v++)256&n&&(p+=s(u,l),l+=4),512&n&&(l+=4),1024&n&&(l+=4),2048&n&&(l+=4);return p}function a(u,n,l){m(n,["moof","traf"]).forEach(function(p){m(p,["tfhd"]).forEach(function(f){var v=s(f,4),r=u[v];if(r){var i=r.timescale||9e4;m(p,["tfdt"]).forEach(function(c){var S=c.data[c.start],L=s(c,4);if(S===0)b(c,4,L-l*i);else{L*=Math.pow(2,32),L+=s(c,8),L-=l*i,L=Math.max(L,0);var _=Math.floor(L/(A+1)),x=Math.floor(L%(A+1));b(c,4,_),b(c,8,x)}})}})})}function t(u){var n={valid:null,remainder:null},l=m(u,["moof"]);if(!l)return n;if(l.length<2)return n.remainder=u,n;var p=l[l.length-1];return n.valid=Object(R.sliceUint8)(u,0,p.start-8),n.remainder=Object(R.sliceUint8)(u,p.start-8),n}function o(u,n){var l=new Uint8Array(u.length+n.length);return l.set(u),l.set(n,u.length),l}},"./src/utils/output-filter.ts":function(M,I,g){g.r(I),g.d(I,"default",function(){return R});var R=function(){function E(k,C){this.timelineController=void 0,this.cueRanges=[],this.trackName=void 0,this.startTime=null,this.endTime=null,this.screen=null,this.timelineController=k,this.trackName=C}var A=E.prototype;return A.dispatchCue=function(){this.startTime!==null&&(this.timelineController.addCues(this.trackName,this.startTime,this.endTime,this.screen,this.cueRanges),this.startTime=null)},A.newCue=function(k,C,D){(this.startTime===null||this.startTime>k)&&(this.startTime=k),this.endTime=C,this.screen=D,this.timelineController.createCaptionsTrack(this.trackName)},A.reset=function(){this.cueRanges=[]},E}()},"./src/utils/texttrack-utils.ts":function(M,I,g){g.r(I),g.d(I,"sendAddTrackEvent",function(){return E}),g.d(I,"addCueToTrack",function(){return A}),g.d(I,"clearCurrentCues",function(){return k}),g.d(I,"removeCuesInRange",function(){return C}),g.d(I,"getCuesInRange",function(){return D});var R=g("./src/utils/logger.ts");function E(s,b){var m;try{m=new Event("addtrack")}catch{(m=document.createEvent("Event")).initEvent("addtrack",!1,!1)}m.track=s,b.dispatchEvent(m)}function A(s,b){var m=s.mode;if(m==="disabled"&&(s.mode="hidden"),s.cues&&!s.cues.getCueById(b.id))try{if(s.addCue(b),!s.cues.getCueById(b.id))throw new Error("addCue is failed for: "+b)}catch(T){R.logger.debug("[texttrack-utils]: "+T);var h=new self.TextTrackCue(b.startTime,b.endTime,b.text);h.id=b.id,s.addCue(h)}m==="disabled"&&(s.mode=m)}function k(s){var b=s.mode;if(b==="disabled"&&(s.mode="hidden"),s.cues)for(var m=s.cues.length;m--;)s.removeCue(s.cues[m]);b==="disabled"&&(s.mode=b)}function C(s,b,m){var h=s.mode;if(h==="disabled"&&(s.mode="hidden"),s.cues&&s.cues.length>0)for(var T=D(s.cues,b,m),y=0;y<T.length;y++)s.removeCue(T[y]);h==="disabled"&&(s.mode=h)}function D(s,b,m){var h=[],T=function(a,t){if(t<a[0].startTime)return 0;var o=a.length-1;if(t>a[o].endTime)return-1;for(var u=0,n=o;u<=n;){var l=Math.floor((n+u)/2);if(t<a[l].startTime)n=l-1;else{if(!(t>a[l].startTime&&u<o))return l;u=l+1}}return a[u].startTime-t<t-a[n].startTime?u:n}(s,b);if(T>-1)for(var y=T,d=s.length;y<d;y++){var e=s[y];if(e.startTime>=b&&e.endTime<=m)h.push(e);else if(e.startTime>m)return h}return h}},"./src/utils/time-ranges.ts":function(M,I,g){g.r(I),I.default={toString:function(R){for(var E="",A=R.length,k=0;k<A;k++)E+="["+R.start(k).toFixed(3)+","+R.end(k).toFixed(3)+"]";return E}}},"./src/utils/timescale-conversion.ts":function(M,I,g){function R(C,D,s,b){s===void 0&&(s=1),b===void 0&&(b=!1);var m=C*D*s;return b?Math.round(m):m}function E(C,D,s,b){return s===void 0&&(s=1),b===void 0&&(b=!1),R(C,D,1/s,b)}function A(C,D){return D===void 0&&(D=!1),R(C,1e3,1/9e4,D)}function k(C,D){return D===void 0&&(D=1),R(C,9e4,1/D)}g.r(I),g.d(I,"toTimescaleFromBase",function(){return R}),g.d(I,"toTimescaleFromScale",function(){return E}),g.d(I,"toMsFromMpegTsClock",function(){return A}),g.d(I,"toMpegTsClockFromTimescale",function(){return k})},"./src/utils/typed-array.ts":function(M,I,g){function R(E,A,k){return Uint8Array.prototype.slice?E.slice(A,k):new Uint8Array(Array.prototype.slice.call(E,A,k))}g.r(I),g.d(I,"sliceUint8",function(){return R})},"./src/utils/vttcue.ts":function(M,I,g){g.r(I),I.default=function(){if(typeof self<"u"&&self.VTTCue)return self.VTTCue;var R=["","lr","rl"],E=["start","middle","end","left","right"];function A(s,b){if(typeof b!="string"||!Array.isArray(s))return!1;var m=b.toLowerCase();return!!~s.indexOf(m)&&m}function k(s){return A(E,s)}function C(s){for(var b=arguments.length,m=new Array(b>1?b-1:0),h=1;h<b;h++)m[h-1]=arguments[h];for(var T=1;T<arguments.length;T++){var y=arguments[T];for(var d in y)s[d]=y[d]}return s}function D(s,b,m){var h=this,T={enumerable:!0};h.hasBeenReset=!1;var y="",d=!1,e=s,a=b,t=m,o=null,u="",n=!0,l="auto",p="start",f=50,v="middle",r=50,i="middle";Object.defineProperty(h,"id",C({},T,{get:function(){return y},set:function(c){y=""+c}})),Object.defineProperty(h,"pauseOnExit",C({},T,{get:function(){return d},set:function(c){d=!!c}})),Object.defineProperty(h,"startTime",C({},T,{get:function(){return e},set:function(c){if(typeof c!="number")throw new TypeError("Start time must be set to a number.");e=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"endTime",C({},T,{get:function(){return a},set:function(c){if(typeof c!="number")throw new TypeError("End time must be set to a number.");a=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"text",C({},T,{get:function(){return t},set:function(c){t=""+c,this.hasBeenReset=!0}})),Object.defineProperty(h,"region",C({},T,{get:function(){return o},set:function(c){o=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"vertical",C({},T,{get:function(){return u},set:function(c){var S=function(L){return A(R,L)}(c);if(S===!1)throw new SyntaxError("An invalid or illegal string was specified.");u=S,this.hasBeenReset=!0}})),Object.defineProperty(h,"snapToLines",C({},T,{get:function(){return n},set:function(c){n=!!c,this.hasBeenReset=!0}})),Object.defineProperty(h,"line",C({},T,{get:function(){return l},set:function(c){if(typeof c!="number"&&c!=="auto")throw new SyntaxError("An invalid number or illegal string was specified.");l=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"lineAlign",C({},T,{get:function(){return p},set:function(c){var S=k(c);if(!S)throw new SyntaxError("An invalid or illegal string was specified.");p=S,this.hasBeenReset=!0}})),Object.defineProperty(h,"position",C({},T,{get:function(){return f},set:function(c){if(c<0||c>100)throw new Error("Position must be between 0 and 100.");f=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"positionAlign",C({},T,{get:function(){return v},set:function(c){var S=k(c);if(!S)throw new SyntaxError("An invalid or illegal string was specified.");v=S,this.hasBeenReset=!0}})),Object.defineProperty(h,"size",C({},T,{get:function(){return r},set:function(c){if(c<0||c>100)throw new Error("Size must be between 0 and 100.");r=c,this.hasBeenReset=!0}})),Object.defineProperty(h,"align",C({},T,{get:function(){return i},set:function(c){var S=k(c);if(!S)throw new SyntaxError("An invalid or illegal string was specified.");i=S,this.hasBeenReset=!0}})),h.displayState=void 0}return D.prototype.getCueAsHTML=function(){return self.WebVTT.convertCueToDOMTree(self,this.text)},D}()},"./src/utils/vttparser.ts":function(M,I,g){g.r(I),g.d(I,"parseTimeStamp",function(){return A}),g.d(I,"fixLineBreaks",function(){return m}),g.d(I,"VTTParser",function(){return h});var R=g("./src/utils/vttcue.ts"),E=function(){function T(){}return T.prototype.decode=function(y,d){if(!y)return"";if(typeof y!="string")throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(y))},T}();function A(T){function y(e,a,t,o){return 3600*(0|e)+60*(0|a)+(0|t)+parseFloat(o||0)}var d=T.match(/^(?:(\d+):)?(\d{2}):(\d{2})(\.\d+)?/);return d?parseFloat(d[2])>59?y(d[2],d[3],0,d[4]):y(d[1],d[2],d[3],d[4]):null}var k=function(){function T(){this.values=Object.create(null)}var y=T.prototype;return y.set=function(d,e){this.get(d)||e===""||(this.values[d]=e)},y.get=function(d,e,a){return a?this.has(d)?this.values[d]:e[a]:this.has(d)?this.values[d]:e},y.has=function(d){return d in this.values},y.alt=function(d,e,a){for(var t=0;t<a.length;++t)if(e===a[t]){this.set(d,e);break}},y.integer=function(d,e){/^-?\d+$/.test(e)&&this.set(d,parseInt(e,10))},y.percent=function(d,e){if(/^([\d]{1,3})(\.[\d]*)?%$/.test(e)){var a=parseFloat(e);if(a>=0&&a<=100)return this.set(d,a),!0}return!1},T}();function C(T,y,d,e){var a=e?T.split(e):[T];for(var t in a)if(typeof a[t]=="string"){var o=a[t].split(d);o.length===2&&y(o[0],o[1])}}var D=new R.default(0,0,""),s=D.align==="middle"?"middle":"center";function b(T,y,d){var e=T;function a(){var o=A(T);if(o===null)throw new Error("Malformed timestamp: "+e);return T=T.replace(/^[^\sa-zA-Z-]+/,""),o}function t(){T=T.replace(/^\s+/,"")}if(t(),y.startTime=a(),t(),T.substr(0,3)!=="-->")throw new Error("Malformed time stamp (time stamps must be separated by '-->'): "+e);T=T.substr(3),t(),y.endTime=a(),t(),function(o,u){var n=new k;C(o,function(f,v){var r;switch(f){case"region":for(var i=d.length-1;i>=0;i--)if(d[i].id===v){n.set(f,d[i].region);break}break;case"vertical":n.alt(f,v,["rl","lr"]);break;case"line":r=v.split(","),n.integer(f,r[0]),n.percent(f,r[0])&&n.set("snapToLines",!1),n.alt(f,r[0],["auto"]),r.length===2&&n.alt("lineAlign",r[1],["start",s,"end"]);break;case"position":r=v.split(","),n.percent(f,r[0]),r.length===2&&n.alt("positionAlign",r[1],["start",s,"end","line-left","line-right","auto"]);break;case"size":n.percent(f,v);break;case"align":n.alt(f,v,["start",s,"end","left","right"])}},/:/,/\s/),u.region=n.get("region",null),u.vertical=n.get("vertical","");var l=n.get("line","auto");l==="auto"&&D.line===-1&&(l=-1),u.line=l,u.lineAlign=n.get("lineAlign","start"),u.snapToLines=n.get("snapToLines",!0),u.size=n.get("size",100),u.align=n.get("align",s);var p=n.get("position","auto");p==="auto"&&D.position===50&&(p=u.align==="start"||u.align==="left"?0:u.align==="end"||u.align==="right"?100:50),u.position=p}(T,y)}function m(T){return T.replace(/<br(?: \/)?>/gi,`
`)}var h=function(){function T(){this.state="INITIAL",this.buffer="",this.decoder=new E,this.regionList=[],this.cue=null,this.oncue=void 0,this.onparsingerror=void 0,this.onflush=void 0}var y=T.prototype;return y.parse=function(d){var e=this;function a(){var l=e.buffer,p=0;for(l=m(l);p<l.length&&l[p]!=="\r"&&l[p]!==`
`;)++p;var f=l.substr(0,p);return l[p]==="\r"&&++p,l[p]===`
`&&++p,e.buffer=l.substr(p),f}d&&(e.buffer+=e.decoder.decode(d,{stream:!0}));try{var t="";if(e.state==="INITIAL"){if(!/\r\n|\n/.test(e.buffer))return this;var o=(t=a()).match(/^(ï»¿)?WEBVTT([ \t].*)?$/);if(!o||!o[0])throw new Error("Malformed WebVTT signature.");e.state="HEADER"}for(var u=!1;e.buffer;){if(!/\r\n|\n/.test(e.buffer))return this;switch(u?u=!1:t=a(),e.state){case"HEADER":/:/.test(t)?C(t,function(l,p){},/:/):t||(e.state="ID");continue;case"NOTE":t||(e.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(t)){e.state="NOTE";break}if(!t)continue;if(e.cue=new R.default(0,0,""),e.state="CUE",t.indexOf("-->")===-1){e.cue.id=t;continue}case"CUE":if(!e.cue){e.state="BADCUE";continue}try{b(t,e.cue,e.regionList)}catch{e.cue=null,e.state="BADCUE";continue}e.state="CUETEXT";continue;case"CUETEXT":var n=t.indexOf("-->")!==-1;if(!t||n&&(u=!0)){e.oncue&&e.cue&&e.oncue(e.cue),e.cue=null,e.state="ID";continue}if(e.cue===null)continue;e.cue.text&&(e.cue.text+=`
`),e.cue.text+=t;continue;case"BADCUE":t||(e.state="ID")}}}catch{e.state==="CUETEXT"&&e.cue&&e.oncue&&e.oncue(e.cue),e.cue=null,e.state=e.state==="INITIAL"?"BADWEBVTT":"BADCUE"}return this},y.flush=function(){var d=this;try{if((d.cue||d.state==="HEADER")&&(d.buffer+=`

`,d.parse()),d.state==="INITIAL"||d.state==="BADWEBVTT")throw new Error("Malformed WebVTT signature.")}catch(e){d.onparsingerror&&d.onparsingerror(e)}return d.onflush&&d.onflush(),this},T}()},"./src/utils/webvtt-parser.ts":function(M,I,g){g.r(I),g.d(I,"generateCueId",function(){return m}),g.d(I,"parseWebVTT",function(){return h});var R=g("./src/polyfills/number.ts"),E=g("./src/utils/vttparser.ts"),A=g("./src/demux/id3.ts"),k=g("./src/utils/timescale-conversion.ts"),C=g("./src/remux/mp4-remuxer.ts"),D=/\r\n|\n\r|\n|\r/g,s=function(T,y,d){return d===void 0&&(d=0),T.substr(d,y.length)===y},b=function(T){for(var y=5381,d=T.length;d;)y=33*y^T.charCodeAt(--d);return(y>>>0).toString()};function m(T,y,d){return b(T.toString())+b(y.toString())+b(d)}function h(T,y,d,e,a,t,o,u){var n,l=new E.VTTParser,p=Object(A.utf8ArrayToStr)(new Uint8Array(T)).trim().replace(D,`
`).split(`
`),f=[],v=Object(k.toMpegTsClockFromTimescale)(y,d),r="00:00.000",i=0,c=0,S=!0,L=!1;l.oncue=function(_){var x=e[a],w=e.ccOffset,O=(i-v)/9e4;if(x!=null&&x.new&&(c!==void 0?w=e.ccOffset=x.start:function(U,G,K){var q=U[G],Y=U[q.prevCC];if(!Y||!Y.new&&q.new)return U.ccOffset=U.presentationOffset=q.start,void(q.new=!1);for(;(j=Y)!==null&&j!==void 0&&j.new;){var j;U.ccOffset+=q.start-Y.start,q.new=!1,Y=U[(q=Y).prevCC]}U.presentationOffset=K}(e,a,O)),O&&(w=O-e.presentationOffset),L){var P=_.endTime-_.startTime,F=Object(C.normalizePts)(9e4*(_.startTime+w-c),9e4*t)/9e4;_.startTime=F,_.endTime=F+P}var N=_.text.trim();_.text=decodeURIComponent(encodeURIComponent(N)),_.id||(_.id=m(_.startTime,_.endTime,N)),_.endTime>0&&f.push(_)},l.onparsingerror=function(_){n=_},l.onflush=function(){n?u(n):o(f)},p.forEach(function(_){if(S){if(s(_,"X-TIMESTAMP-MAP=")){S=!1,L=!0,_.substr(16).split(",").forEach(function(x){s(x,"LOCAL:")?r=x.substr(6):s(x,"MPEGTS:")&&(i=parseInt(x.substr(7)))});try{c=function(x){var w=parseInt(x.substr(-3)),O=parseInt(x.substr(-6,2)),P=parseInt(x.substr(-9,2)),F=x.length>9?parseInt(x.substr(0,x.indexOf(":"))):0;if(!(Object(R.isFiniteNumber)(w)&&Object(R.isFiniteNumber)(O)&&Object(R.isFiniteNumber)(P)&&Object(R.isFiniteNumber)(F)))throw Error("Malformed X-TIMESTAMP-MAP: Local:"+x);return w+=1e3*O,(w+=6e4*P)+36e5*F}(r)/1e3}catch(x){L=!1,n=x}return}_===""&&(S=!1)}l.parse(_+`
`)}),l.flush()}},"./src/utils/xhr-loader.ts":function(M,I,g){g.r(I);var R=g("./src/utils/logger.ts"),E=g("./src/loader/load-stats.ts"),A=/^age:\s*[\d.]+\s*$/m,k=function(){function C(s){this.xhrSetup=void 0,this.requestTimeout=void 0,this.retryTimeout=void 0,this.retryDelay=void 0,this.config=null,this.callbacks=null,this.context=void 0,this.loader=null,this.stats=void 0,this.xhrSetup=s?s.xhrSetup:null,this.stats=new E.LoadStats,this.retryDelay=0}var D=C.prototype;return D.destroy=function(){this.callbacks=null,this.abortInternal(),this.loader=null,this.config=null},D.abortInternal=function(){var s=this.loader;self.clearTimeout(this.requestTimeout),self.clearTimeout(this.retryTimeout),s&&(s.onreadystatechange=null,s.onprogress=null,s.readyState!==4&&(this.stats.aborted=!0,s.abort()))},D.abort=function(){var s;this.abortInternal(),(s=this.callbacks)!==null&&s!==void 0&&s.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.loader)},D.load=function(s,b,m){if(this.stats.loading.start)throw new Error("Loader can only be used once.");this.stats.loading.start=self.performance.now(),this.context=s,this.config=b,this.callbacks=m,this.retryDelay=b.retryDelay,this.loadInternal()},D.loadInternal=function(){var s=this.config,b=this.context;if(s){var m=this.loader=new self.XMLHttpRequest,h=this.stats;h.loading.first=0,h.loaded=0;var T=this.xhrSetup;try{if(T)try{T(m,b.url)}catch{m.open("GET",b.url,!0),T(m,b.url)}m.readyState||m.open("GET",b.url,!0)}catch(y){return void this.callbacks.onError({code:m.status,text:y.message},b,m)}b.rangeEnd&&m.setRequestHeader("Range","bytes="+b.rangeStart+"-"+(b.rangeEnd-1)),m.onreadystatechange=this.readystatechange.bind(this),m.onprogress=this.loadprogress.bind(this),m.responseType=b.responseType,self.clearTimeout(this.requestTimeout),this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),s.timeout),m.send()}},D.readystatechange=function(){var s=this.context,b=this.loader,m=this.stats;if(s&&b){var h=b.readyState,T=this.config;if(!m.aborted&&h>=2)if(self.clearTimeout(this.requestTimeout),m.loading.first===0&&(m.loading.first=Math.max(self.performance.now(),m.loading.start)),h===4){b.onreadystatechange=null,b.onprogress=null;var y=b.status;if(y>=200&&y<300){var d,e;if(m.loading.end=Math.max(self.performance.now(),m.loading.first),e=s.responseType==="arraybuffer"?(d=b.response).byteLength:(d=b.responseText).length,m.loaded=m.total=e,!this.callbacks)return;var a=this.callbacks.onProgress;if(a&&a(m,s,d,b),!this.callbacks)return;var t={url:b.responseURL,data:d};this.callbacks.onSuccess(t,m,s,b)}else m.retry>=T.maxRetry||y>=400&&y<499?(R.logger.error(y+" while loading "+s.url),this.callbacks.onError({code:y,text:b.statusText},s,b)):(R.logger.warn(y+" while loading "+s.url+", retrying in "+this.retryDelay+"..."),this.abortInternal(),this.loader=null,self.clearTimeout(this.retryTimeout),this.retryTimeout=self.setTimeout(this.loadInternal.bind(this),this.retryDelay),this.retryDelay=Math.min(2*this.retryDelay,T.maxRetryDelay),m.retry++)}else self.clearTimeout(this.requestTimeout),this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),T.timeout)}},D.loadtimeout=function(){R.logger.warn("timeout while loading "+this.context.url);var s=this.callbacks;s&&(this.abortInternal(),s.onTimeout(this.stats,this.context,this.loader))},D.loadprogress=function(s){var b=this.stats;b.loaded=s.loaded,s.lengthComputable&&(b.total=s.total)},D.getCacheAge=function(){var s=null;if(this.loader&&A.test(this.loader.getAllResponseHeaders())){var b=this.loader.getResponseHeader("age");s=b?parseFloat(b):null}return s},C}();I.default=k}}).default);var st=Br(kt.exports);const Ur={name:"d-icon"},De=Object.assign(Ur,{props:{icon:String,size:[Number,String]},setup:function(M){const I=M,g=Ie(()=>({fontSize:/^\d+$/.test(I.size)?I.size+"px":I.size}));return(R,E)=>(z(),Z("i",{class:_e(["d-icon iconfont",M.icon]),style:Me(B(g))},null,6))}});De.__scopeId="data-v-0c690e66";We("data-v-4cb76d59");const Gr={class:"d-player-top"},jr={class:"top-title"},Vr={class:"top-title"};Ye();const Kr={props:{title:{default:""}},setup(M){Date.prototype.format=function(R){let E={"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds()};for(var A in E)new RegExp("("+A+")").test(R)&&(R=R.replace(RegExp.$1,RegExp.$1.length==1?E[A]:("00"+E[A]).substr((""+E[A]).length)));return R};let I=he("00:00:00");I.value=new Date().format("hh:mm:ss");let g=null;return g=setInterval(()=>{I.value=new Date().format("hh:mm:ss")},1e3),Lt(()=>{clearInterval(g)}),(R,E)=>(z(),Z("div",Gr,[V("p",jr,ce(M.title||""),1),V("p",Vr,ce(B(I)),1)]))},__scopeId:"data-v-4cb76d59"};We("data-v-ac2469ec");const Hr={class:"d-status"},Wr={class:"d-flex-center"},Yr={class:"d-flex-center"},qr=re("5X速播放中 ");Ye();var Dt=ke({props:["state"],setup:M=>(I,g)=>ye((z(),Z("div",Hr,[ye(V("li",Wr,[W(De,{size:"18",class:"d-status-icon",icon:"icon-volume-"+(M.state.volume==0?"mute":M.state.volume>.5?"up":"down")},null,8,["icon"]),re(" "+ce(~~(100*M.state.volume))+"% ",1)],512),[[Te,M.state.handleType=="volume"]]),ye(V("li",Yr,[W(De,{size:"12",icon:"icon-play"}),W(De,{size:"12",icon:"icon-play",style:{"margin-right":"5px"}}),qr],512),[[Te,M.state.handleType=="playbackRate"||M.state.isMultiplesPlay]])],512)),[[Te,M.state.handleType||M.state.isMultiplesPlay]])});Dt.__scopeId="data-v-ac2469ec",We("data-v-385f7870");const zr=["checked","true-value","false-value"],Xr=V("span",{class:"d-switch_action"},null,-1);Ye();var rt=ke({props:{modelValue:{type:[Number,String,Boolean]},width:{type:String,default:"40px"},trueValue:{type:[Number,String,Boolean],default:!0},falseValue:{type:[Number,String,Boolean],default:!0},activeColor:{type:[String],default:"#409EFF"}},emits:["update:modelValue","change"],setup(M,{emit:I}){const g=M;gt(k=>({"014e5dc0":M.width,e4e32852:M.activeColor}));const R=he(null),E=Ie(()=>g.modelValue===g.trueValue),A=()=>{it(()=>{const k=R.value.checked;I("update:modelValue",k),I("change",k)})};return(k,C)=>(z(),Z("div",{class:_e(["d-switch",{"is-checked":B(E)}])},[V("input",{class:"d-switch__input",ref:R,type:"checkbox",checked:B(E),onChange:A,"true-value":M.trueValue,"false-value":M.falseValue},null,40,zr),Xr],2))}});rt.__scopeId="data-v-385f7870",We("data-v-b2384226");const $r={key:0},Qr=V("i",{class:"rotating iconfont icon-loading f50"},null,-1),Zr=[V("i",{class:"rotating iconfont icon-loading f50"},null,-1),V("p",null,"正在缓冲...",-1)],Jr=[V("i",{class:"iconfont icon-replay f24 mr5"},null,-1),re("重新播放 ")],ei=[V("i",{class:"iconfont icon-replay f24 mr5"},null,-1),re("请求错误 ")];Ye();var _t=ke({props:{loadType:String,text:{type:String,default:""}},setup(M){const I=M,{proxy:g}=Gt(),R=["loadstart","waiting","ended","error","stalled"],E=()=>{g.$parent.play()},A=Ie(()=>{let k="background: rgba(0, 0, 0, .1);z-index:1";return I.loadType=="loadstart"&&(k="background: rgba(0, 0, 0, 1);;z-index:3"),k});return(k,C)=>ye((z(),Z("div",{class:"d-loading",style:Me(B(A))},[V("div",null,[M.loadType=="loadstart"?(z(),Z("span",$r,[Qr,V("p",null,ce(M.text),1)])):ee("",!0),ye(V("span",null,Zr,512),[[Te,M.loadType=="waiting"]]),ye(V("span",null,[V("p",{onClick:E,class:"d-flex-x d-pointer"},Jr)],512),[[Te,M.loadType=="ended"]]),ye(V("span",null,[V("p",{onClick:E,class:"d-flex-x d-pointer"},ei)],512),[[Te,M.loadType=="error"||M.loadType=="stalled"]])])],4)),[[Te,R.includes(M.loadType)]])}});_t.__scopeId="data-v-b2384226";const He=function(M,I,g,R=!1){M&&I&&g&&M.addEventListener(I,g,R)},Le=function(M,I,g,R=!1){M&&I&&g&&M.removeEventListener(I,g,R)};We("data-v-5a794390");const ti=["onMousedown"];Ye();var $e=ke(ht(ct({},{name:"DSlider"}),{props:{modelValue:{required:!0,type:Number,default:0},disabled:{type:Boolean,default:!1},vertical:{type:Boolean,default:!1},hover:{type:Boolean,default:!0},hoverText:{type:String,default:""},preload:{type:Number,default:0},size:{type:String,default:"10px"}},emits:["update:modelValue","change","onMousemove"],setup:function(M,{emit:I}){const g=M;gt(e=>({"5242b67b":M.size}));const R=he(null),E=he(null),A=Qe({dragging:!1,hoverPosition:0,hoverTipsLeft:"50%"}),k=Ie(()=>{let e=g.modelValue<0?0:g.modelValue>1?1:g.modelValue;return g.vertical?`height:${100*e}%`:`width:${100*e}%`}),C=Ie(()=>{let e=g.preload<0?0:g.preload>1?1:g.preload;return g.vertical?`height:${100*e}%`:`width:${100*e}%`}),D=Ie(()=>{let e=A.hoverPosition<0?0:A.hoverPosition>1?1:A.hoverPosition;return g.vertical?`bottom:${100*e}%`:`left:${100*e}%`}),s=e=>{e.preventDefault()},b=e=>{g.disabled||(e.preventDefault(),A.dragging=!0,h(e),He(window,"mousemove",y),He(window,"touchmove",y),He(window,"mouseup",d),He(window,"touchend",d))},m=e=>{if(!g.hover)return;let a=T(e);if(I("onMousemove",e,a),A.hoverPosition=a,g.vertical)return;let t=R.value,o=E.value.clientWidth/2,u=e.clientX-t.getBoundingClientRect().left;u<o?A.hoverTipsLeft=o-u+"px":t.clientWidth-u<o?A.hoverTipsLeft=t.clientWidth-u-o+"px":A.hoverTipsLeft="50%"},h=e=>{let a=T(e);I("update:modelValue",a),I("change",e,a)},T=e=>{let a=R.value,t=0;if(g.vertical){let o=a.clientHeight;t=(a.getBoundingClientRect().bottom-e.clientY)/o}else t=(e.clientX-a.getBoundingClientRect().left)/a.clientWidth;return t<0?0:t>1?1:t},y=e=>{h(e)},d=e=>{A.dragging&&(Le(window,"mousemove",y),Le(window,"touchmove",y),Le(window,"mouseup",d),Le(window,"touchend",d),Le(window,"contextmenu",d),setTimeout(()=>{A.dragging=!1},0))};return(e,a)=>(z(),Z("div",{ref:R,class:_e(["d-slider",{"is-vertical":g.vertical}]),onMousedown:et(b,["stop"]),onContextmenu:s},[V("div",{class:"d-slider__runway",onMousemove:m},[ye(V("div",{class:"d-slider__cursor",style:Me(B(D))},[ye(V("div",{class:"d-slider__tips",ref:E,style:Me({left:B(A).hoverTipsLeft})},ce(g.hoverText),5),[[Te,g.hoverText]])],4),[[Te,g.hover]]),V("div",{class:"d-slider__preload",style:Me(B(C))},null,4),V("div",{class:"d-slider__bar",style:Me(B(k))},null,4)],32)],42,ti))}}));$e.__scopeId="data-v-5a794390";We("data-v-570fa0d1");const ri={key:0,class:"d-player-dialog"},ii={class:"d-player-dialog-body"},ni={class:"d-player-dialog-title"},ai={class:"d-player-hotkey-panel"},si={class:"d-player-filter-panel"},oi={class:"d-player-filter-panel-item"},li=V("span",null,"饱和度",-1),ui={class:"d-player-filter-panel-item"},di=V("span",null,"亮度",-1),ci={class:"d-player-filter-panel-item"},hi=V("span",null,"对比度",-1),fi={key:0,class:"d-player-contextmenu"},gi=["dplayerKeyCode"],vi=V("input",{class:"d-player-copyText"},null,-1);Ye();var Rt=ke({setup(M){const I=Qe({show:!1,dialogType:"",dialogTitle:"",version:"1.3.1-beta.6",mouseX:0,mouseY:0}),g=[{label:"视频色彩调整",key:"filter"},{label:"快捷键说明",key:"hotkey"},{label:"复制视频网址",key:"copy"},{label:"版本：1.3.1-beta.6",key:"version"}],R=[{key:"Space",label:"播放/暂停"},{key:"→",label:"单次快进10s，长按5倍速播放"},{key:"←",label:"快退5s"},{key:"↑",label:"音量增加10%"},{key:"↓",label:"音量增加降低10%"},{key:"Esc",label:"退出全屏/退出网页全屏"},{key:"F",label:"全屏/退出全屏"}],E=Qe({saturate:.392,brightness:.392,contrast:.392}),A=Ie(()=>({left:I.mouseX+"px",top:I.mouseY+"px"}));Xe(E,b=>{let m=document.querySelector("#dPlayerVideo"),h=(2.55*b.saturate).toFixed(2),T=(2.55*b.brightness).toFixed(2),y=(2.55*b.contrast).toFixed(2);m.style.filter=`saturate(${h}) brightness(${T}) contrast(${y})`});const k=()=>{E.saturate=.392,E.brightness=.392,E.contrast=.392},C=b=>{b.key=="Escape"&&s(0)},D=b=>{b.preventDefault(),He(window,"keydown",C),He(window,"click",s);let m=document.querySelector("#refPlayerWrap"),h=m.clientWidth;m.clientHeight,I.mouseX=b.clientX-m.getBoundingClientRect().left,h-I.mouseX<130&&(I.mouseX=I.mouseX+(h-I.mouseX-130)),I.mouseY=b.clientY-m.getBoundingClientRect().top,I.show=!0},s=b=>{let m=b.path[0].tagName=="LI",h=b.path[0].attributes.dplayerKeyCode&&b.path[0].attributes.dplayerKeyCode.value,T=g.map(y=>y.key);if(m&&T.includes(h))if(I.dialogTitle=b.path[0].innerText,I.dialogType=h,h=="copy"){let y=document.querySelector(".d-player-copyText");y.value=window.location.href,y.select(),document.execCommand("copy"),I.dialogType=""}else h=="version"&&(I.dialogType="");I.show=!1,Le(window,"keydown",C),Le(window,"click",s)};return ft(()=>{let b=document.querySelector("#refPlayerWrap");Le(window,"keydown",C),Le(window,"click",s),Le(b,"contextmenu",D),He(b,"contextmenu",D)}),Lt(()=>{let b=document.querySelector("#refPlayerWrap");Le(window,"keydown",C),Le(window,"click",s),Le(b,"contextmenu",D)}),(b,m)=>(z(),Z("div",null,[W(dt,{name:"d-fade-in"},{default:$(()=>[B(I).dialogType?(z(),Z("div",ri,[V("div",ii,[V("h5",ni,[re(ce(B(I).dialogTitle)+" ",1),V("i",{onClick:m[0]||(m[0]=h=>B(I).dialogType=!1),class:"icon icon-close"},"X")]),ye(V("ul",ai,[(z(),Z(Ae,null,Fe(R,h=>V("li",{class:"d-player-hotkey-panel-item",key:h.key},[V("span",null,ce(h.key),1),V("span",null,ce(h.label),1)])),64))],512),[[Te,B(I).dialogType=="hotkey"]]),ye(V("ul",si,[V("li",oi,[li,W($e,{class:"filter-panel-slider",size:"5px",modelValue:B(E).saturate,"onUpdate:modelValue":m[1]||(m[1]=h=>B(E).saturate=h)},null,8,["modelValue"]),V("span",null,ce(Math.round(255*B(E).saturate)),1)]),V("li",ui,[di,W($e,{class:"filter-panel-slider",size:"5px",modelValue:B(E).brightness,"onUpdate:modelValue":m[2]||(m[2]=h=>B(E).brightness=h)},null,8,["modelValue"]),V("span",null,ce(Math.round(255*B(E).brightness)),1)]),V("li",ci,[hi,W($e,{class:"filter-panel-slider",size:"5px",modelValue:B(E).contrast,"onUpdate:modelValue":m[3]||(m[3]=h=>B(E).contrast=h)},null,8,["modelValue"]),V("span",null,ce(Math.round(255*B(E).contrast)),1)]),V("span",{onClick:k,title:"重置","aria-label":"重置",class:"d-player-filter-reset"},"重置")],512),[[Te,B(I).dialogType=="filter"]])])])):ee("",!0)]),_:1}),B(I).show?(z(),Z("div",fi,[V("ul",{class:"d-player-contextmenu-body",style:Me(B(A))},[(z(),Z(Ae,null,Fe(g,h=>V("li",{dplayerKeyCode:h.key,key:h.key},ce(h.label),9,gi)),64))],4),vi])):ee("",!0)]))}});Rt.__scopeId="data-v-570fa0d1";const ot=M=>{let I=~~(M/3600),g=~~(M%3600/60),R=~~(M%60);return I=I<10?"0"+I:I,g=g<10?"0"+g:g,R=R<10?"0"+R:R,`${I}:${g}:${R}`},Je="ontouchstart"in window,Et=["loadstart","play","pause","playing","seeking","seeked","waiting","durationchange","progress","canplay","timeupdate","ended","error","stalled"],pi={width:{type:String,default:"800px"},height:{type:String,default:"450px"},color:{type:String,default:"#409eff"},src:{required:!0,type:String,default:""},title:{type:String,default:""},type:{type:String,default:"video/mp4"},poster:{type:String,default:""},webFullScreen:{type:Boolean,default:!1},speed:{type:Boolean,default:!0},currentTime:{type:Number,default:0},playsinline:{type:Boolean,default:!1},muted:{type:Boolean,default:!1},speedRate:{type:Array,default:()=>["2.0","1.5","1.25","1.0","0.75","0.5"]},autoPlay:{type:Boolean,default:!1},loop:{type:Boolean,default:!1},mirror:{type:Boolean,default:!1},ligthOff:{type:Boolean,default:!1},volume:{type:[String,Number],default:.3},control:{type:Boolean,default:!0},controlBtns:{type:Array,default:["audioTrack","quality","speedRate","volume","setting","pip","pageFullScreen","fullScreen"]},preload:{type:String,default:"auto"}};We("data-v-01791e9e");const mi={class:"d-player-video",id:"dPlayerVideo"},yi=["controls","webkit-playsinline","playsinline","volume","muted","loop","preload","src","poster"],Ei={class:"d-player-lightoff"},Ti={key:1,class:"d-player-state"},Si={class:"d-play-btn"},bi=["onKeyup","onKeydown"],Li={class:"d-control-progress"},Ai={class:"d-tool-bar"},ki={key:0,class:"d-tool-item d-tool-time audioTrack-btn"},Di=V("span",{style:{margin:"0 3px"}},"/",-1),_i={class:"total-time"},Ri={class:"d-tool-bar"},Ci={key:0,class:"d-tool-item quality-btn"},Ii={class:"d-tool-item-main"},wi={class:"speed-main",style:{"text-align":"center"}},xi=["onClick"],Oi={key:1,class:"d-tool-item speedRate-btn"},Pi={class:"d-tool-item-main"},Fi={class:"speed-main"},Mi=["onClick"],Ni={key:2,class:"d-tool-item volume-btn"},Bi={class:"d-tool-item-main volume-box",style:{width:"52px"}},Ui={class:"volume-text-size"},Gi={key:3,class:"d-tool-item setting-btn"},ji={class:"d-tool-item-main"},Vi={class:"speed-main"},Ki=re(" 镜像画面 "),Hi=re(" 循环播放 "),Wi=re(" 关灯模式 "),Yi=V("div",{class:"d-tool-item-main"},"画中画",-1),qi=V("div",{class:"d-tool-item-main"},"网页全屏",-1),zi=V("div",{class:"d-tool-item-main"},"全屏",-1);Ye();var tt=ke(ht(ct({},{name:"vue3VideoPlay",inheritAttrs:!1}),{props:pi,emits:[...Et,"mirrorChange","loopChange","lightOffChange"],setup:function(M,{expose:I,emit:g}){const R=M;gt(w=>({"51d4439c":B(T),"77e758a6":w.width,b8a1afc0:w.height}));const E=new st({fragLoadingTimeOut:2e3}),A=he(null),k=he(null),C=he(null),D=he(null),s=Qe(ht(ct({dVideo:null},R),{muted:R.muted,playBtnState:R.autoPlay?"pause":"play",loadStateType:"loadstart",fullScreen:!1,handleType:"",currentTime:"00:00:00",preloadBar:0,totalTime:"00:00:00",isVideoHovering:!0,speedActive:"1.0",playProgress:0,isMultiplesPlay:!1,longPressTimeout:null,progressCursorTime:"00:00:00",qualityLevels:[],currentLevel:0})),b=(...w)=>O=>w.reverse().reduce((P,F)=>F(P),O),m=Et.reduce((w,O)=>{var P;return w[`on${P=O,P.charAt(0).toUpperCase()+P.slice(1)}`]=F=>{s.loadStateType=O,g(O,F)},w},{});m.onCanplay=b(m.onCanplay,()=>{s.playBtnState!="play"&&s.dVideo.play(),s.autoPlay&&(s.dVideo.play(),s.playBtnState="pause")}),m.onEnded=b(m.onEnded,()=>{s.playBtnState="replay"}),m.onDurationchange=w=>{g("durationchange",w),R.currentTime!=0&&(s.dVideo.currentTime=R.currentTime),m.onTimeupdate(w)},m.onProgress=w=>{console.log("缓冲中..."),g("progress",w);let O=w.target.duration,P=w.target.buffered,F=w.target.buffered.length&&w.target.buffered.end(P-1);s.preloadBar=F/O},m.onTimeupdate=w=>{g("timeupdate",w);let O=w.duration||w.target.duration||0,P=w.currentTime||w.target.currentTime;s.playProgress=P/O,s.currentTime=ot(P),s.totalTime=ot(O)},m.onError=b(m.onError,()=>{s.playBtnState="replay"});let h=Ut();for(let w in h)m[w]=h[w];const T=(y=s.color,`${parseInt("0x"+y.slice(1,3))},${parseInt("0x"+y.slice(3,5))},${parseInt("0x"+y.slice(5,7))}`);var y;const d=yt(500,()=>{s.handleType=""}),e=w=>{w.preventDefault(),w.code=="ArrowUp"?s.volume=s.volume+.1>1?1:s.volume+.1:s.volume=s.volume-.1<0?0:s.volume-.1,s.muted=!1,s.handleType="volume",d()},a=w=>{R.speed&&(s.dVideo.currentTime=s.dVideo.currentTime<10?.1:s.dVideo.currentTime-10,m.onTimeupdate(s.dVideo),u())},t=w=>{w.preventDefault();let O=w.type;if(w.key=="ArrowRight"){if(u(),O=="keyup"){if(clearTimeout(s.longPressTimeout),!R.speed&&!s.longPressTimeout)return;s.isMultiplesPlay?(s.dVideo.playbackRate=s.speedActive,s.isMultiplesPlay=!1):(s.dVideo.currentTime=s.dVideo.currentTime+10,m.onTimeupdate(s.dVideo))}else if(O=="keydown"){if(!R.speed)return;s.isMultiplesPlay&&clearTimeout(s.longPressTimeout),s.longPressTimeout=setTimeout(()=>{s.isMultiplesPlay=!0,s.dVideo.playbackRate=5,s.handleType="playbackRate",d()},500)}}},o=()=>{Je||D.value.focus()},u=()=>{s.loadStateType="play",s.dVideo.play().catch(()=>{setTimeout(()=>{s.playBtnState="replay",s.loadStateType="error"},500)}),s.playBtnState="pause"},n=()=>{s.dVideo.pause(),s.playBtnState="play"},l=w=>{w&&w.preventDefault(),s.playBtnState=="play"||s.playBtnState=="replay"?u():s.playBtnState=="pause"&&n()},p=()=>{s.muted=!s.muted,s.volume==0&&(s.volume=.05)},f=(w,O)=>{let P=s.dVideo.duration||s.dVideo.target.duration;s.dVideo.currentTime=P*O,s.playBtnState=="play"&&(s.dVideo.play(),s.playBtnState="pause")},v=(w,O)=>{s.progressCursorTime=ot(s.dVideo.duration*O)},r=yt(2500,()=>{s.isVideoHovering=!1}),i=w=>{s.isVideoHovering=!0,r()},c=w=>{g("mirrorChange",w,s.dVideo)},S=w=>{g("loopChange",w,s.dVideo)},L=w=>{g("lightOffChange",w,s.dVideo)},_=()=>{var w;w=s.dVideo,document.pictureInPictureElement?document.exitPictureInPicture().catch(O=>{console.log(O,"Video failed to leave Picture-in-Picture mode.")}):w.requestPictureInPicture().catch(O=>{console.log(O,"Video failed to enter Picture-in-Picture mode.")})},x=()=>{s.fullScreen=(w=>{let O=document,P=O.webkitIsFullScreen||O.fullscreen;return P?(document.exitFullscreen||O.webkitExitFullScreen).call(O):(w.requestFullscreen||w.webkitRequestFullScreen).call(w),!P})(A.value)};return Xe(()=>R.src,()=>{it(()=>{s.dVideo.canPlayType(R.type)||s.dVideo.canPlayType("application/vnd.apple.mpegurl")?s.muted=R.autoPlay:st.isSupported()&&(E.detachMedia(),E.attachMedia(s.dVideo),E.on(st.Events.MEDIA_ATTACHED,()=>{E.loadSource(R.src),E.on("hlsManifestParsed",(w,O)=>{console.log(O),s.currentLevel=O.level,s.qualityLevels=O.levels||[]})}),E.on("hlsLevelSwitching",(w,O)=>{console.log(O),console.log("LEVEL_SWITCHING")}),E.on("hlsLevelSwitched",(w,O)=>{s.currentLevel=O.level,console.log("LEVEL_SWITCHED")}))})},{immediate:!0}),ft(()=>{s.dVideo=k,o()}),I({play:u,pause:n,togglePlay:l}),(w,O)=>(z(),Z("div",{ref:A,id:"refPlayerWrap",class:_e(["d-player-wrap",{"web-full-screen":B(s).webFullScreen,"is-lightoff":B(s).lightOff,"d-player-wrap-hover":B(s).playBtnState=="play"||B(s).isVideoHovering}]),onMousemove:i},[V("div",mi,[V("video",At({ref:k,class:["d-player-video-main",{"video-mirror":B(s).mirror}],id:"dPlayerVideoMain",controls:!(!B(Je)||!B(s).speed),"webkit-playsinline":R.playsinline,playsinline:R.playsinline},B(m),{volume:B(s).volume,muted:B(s).muted,loop:B(s).loop,preload:w.preload,width:"100%",height:"100%",src:R.src,poster:R.poster}),"您的浏览器不支持Video标签。",16,yi)]),W(dt,{name:"d-fade-in"},{default:$(()=>[ye(V("div",Ei,null,512),[[Te,B(s).lightOff]])]),_:1}),B(s).fullScreen?(z(),ve(Kr,{key:0,title:R.title},null,8,["title"])):ee("",!0),B(Je)?ee("",!0):(z(),Z("div",Ti,[W(dt,{name:"d-scale-out"},{default:$(()=>[ye(V("div",Si,[W(De,{icon:"icon-play",size:40})],512),[[Te,B(s).playBtnState=="play"]])]),_:1}),W(Dt,{state:B(s)},null,8,["state"])])),B(Je)?ee("",!0):(z(),Z("input",{key:2,type:"input",readonly:"readonly",ref:D,onDblclick:x,onKeyup:[ze(x,["f"]),O[0]||(O[0]=ze(P=>B(s).webFullScreen=!1,["esc"])),t],onClick:l,onKeydown:[ze(l,["space"]),ze(a,["arrow-left"]),ze(e,["arrow-up","arrow-down"]),t],class:"d-player-input",maxlength:"0"},null,40,bi)),W(_t,{loadType:B(s).loadStateType},null,8,["loadType"]),W(Rt),!B(Je)&&B(s).control?(z(),Z("div",{key:3,class:"d-player-control",ref:C},[V("div",Li,[W($e,{class:"d-progress-bar",onOnMousemove:v,onChange:f,disabled:!B(s).speed,hoverText:B(s).progressCursorTime,modelValue:B(s).playProgress,"onUpdate:modelValue":O[1]||(O[1]=P=>B(s).playProgress=P),preload:B(s).preloadBar},null,8,["disabled","hoverText","modelValue","preload"])]),V("div",{class:"d-control-tool",onClick:o},[V("div",Ai,[V("div",{class:"d-tool-item",onClick:l},[W(De,{size:"24",icon:`icon-${B(s).playBtnState}`},null,8,["icon"])]),R.controlBtns.includes("audioTrack")?(z(),Z("div",ki,[V("span",null,ce(B(s).currentTime),1),Di,V("span",_i,ce(B(s).totalTime),1)])):ee("",!0)]),V("div",Ri,[B(s).qualityLevels.length&&R.controlBtns.includes("quality")?(z(),Z("div",Ci,[re(ce(B(s).qualityLevels.length&&(B(s).qualityLevels[B(s).currentLevel]||{}).height)+"P ",1),V("div",Ii,[V("ul",wi,[(z(!0),Z(Ae,null,Fe(B(s).qualityLevels,(P,F)=>(z(),Z("li",{class:_e({"speed-active":B(s).currentLevel==F}),onClick:N=>((U,G)=>{E.currentLevel=G,s.currentLevel=G})(0,F),key:P},ce(P.height)+"P",11,xi))),128))])])])):ee("",!0),R.controlBtns.includes("speedRate")?(z(),Z("div",Oi,[re(ce(B(s).speedActive=="1.0"?"倍速":B(s).speedActive+"x")+" ",1),V("div",Pi,[V("ul",Fi,[(z(!0),Z(Ae,null,Fe(B(s).speedRate,P=>(z(),Z("li",{class:_e({"speed-active":B(s).speedActive==P}),onClick:F=>(N=>{s.speedActive=N,s.dVideo.playbackRate=N})(P),key:P},ce(P)+"x",11,Mi))),128))])])])):ee("",!0),R.controlBtns.includes("volume")?(z(),Z("div",Ni,[V("div",Bi,[V("div",{class:_e(["volume-main",{"is-muted":B(s).muted}])},[V("span",Ui,ce(B(s).muted?0:~~(100*B(s).volume))+"%",1),W($e,{onChange:O[2]||(O[2]=P=>B(s).muted=!1),hover:!1,size:"5px",vertical:!0,modelValue:B(s).volume,"onUpdate:modelValue":O[3]||(O[3]=P=>B(s).volume=P)},null,8,["modelValue"])],2)]),V("span",{onClick:p,style:{display:"flex"}},[W(De,{size:"20",icon:"icon-volume-"+(B(s).volume==0||B(s).muted?"mute":B(s).volume>.5?"up":"down")},null,8,["icon"])])])):ee("",!0),R.controlBtns.includes("setting")?(z(),Z("div",Gi,[W(De,{size:"20",class:"rotateHover",icon:"icon-settings"}),V("div",ji,[V("ul",Vi,[V("li",null,[Ki,W(rt,{onChange:c,modelValue:B(s).mirror,"onUpdate:modelValue":O[4]||(O[4]=P=>B(s).mirror=P)},null,8,["modelValue"])]),V("li",null,[Hi,W(rt,{onChange:S,modelValue:B(s).loop,"onUpdate:modelValue":O[5]||(O[5]=P=>B(s).loop=P)},null,8,["modelValue"])]),V("li",null,[Wi,W(rt,{onChange:L,modelValue:B(s).lightOff,"onUpdate:modelValue":O[6]||(O[6]=P=>B(s).lightOff=P)},null,8,["modelValue"])])])])])):ee("",!0),R.controlBtns.includes("pip")?(z(),Z("div",{key:4,class:"d-tool-item pip-btn",onClick:_},[W(De,{size:"20",icon:"icon-pip"}),Yi])):ee("",!0),R.controlBtns.includes("pageFullScreen")?(z(),Z("div",{key:5,class:"d-tool-item pip-btn",onClick:O[7]||(O[7]=P=>B(s).webFullScreen=!B(s).webFullScreen)},[W(De,{size:"20",icon:"icon-web-screen"}),qi])):ee("",!0),R.controlBtns.includes("fullScreen")?(z(),Z("div",{key:6,class:"d-tool-item fullScreen-btn",onClick:x},[zi,W(De,{size:"20",icon:"icon-screen"})])):ee("",!0)])])],512)):ee("",!0)],34))}}));function Xi(M){M.component(tt.name,tt)}tt.__scopeId="data-v-01791e9e",tt.install=Xi;const $i=ke({__name:"index",props:{src:{type:String,required:!0},width:String,height:String,poster:String},setup(M,{expose:I}){const g=M,R=nt(),E=Qe({color:"var(--el-color-primary)",muted:!1,webFullScreen:!1,speedRate:["0.75","1.0","1.25","1.5","2.0"],autoPlay:!0,loop:!1,mirror:!1,ligthOff:!1,volume:.3,control:!0,title:"",poster:"",...g}),A=()=>{R.value.play()},k=()=>{R.value.pause()},C=m=>{console.log(m,"播放")},D=m=>{console.log(m,"暂停")},s=m=>{console.log(m,"时间更新")},b=m=>{console.log(m,"可以播放")};return I({play:A,pause:k}),(m,h)=>(z(),Z("div",null,[W(B(tt),At({ref_key:"playerRef",ref:R},E,{src:M.src,onPlay:C,onPause:D,onTimeupdate:s,onCanplay:b}),null,16,["src"])]))}}),Qi={key:0},Zi={key:1},Ji=ke({__name:"preview",props:{modelValue:{type:Boolean,default:!1},url:{type:String,default:""},type:{type:String,default:"image"}},emits:["update:modelValue"],setup(M,{emit:I}){const g=M,R=I,E=nt(),A=Ie({get(){return g.modelValue},set(D){R("update:modelValue",D)}}),k=()=>{R("update:modelValue",!1)},C=he([]);return Xe(()=>g.modelValue,D=>{D?it(()=>{var s;C.value=[g.url],(s=E.value)==null||s.play()}):it(()=>{var s;C.value=[],(s=E.value)==null||s.pause()})}),(D,s)=>{const b=jt,m=$i,h=cr;return ye((z(),Z("div",null,[M.type=="image"?(z(),Z("div",Qi,[B(C).length?(z(),ve(b,{key:0,"url-list":B(C),"hide-on-click-modal":"",onClose:k},null,8,["url-list"])):ee("",!0)])):ee("",!0),M.type=="video"?(z(),Z("div",Zi,[W(h,{modelValue:B(A),"onUpdate:modelValue":s[0]||(s[0]=T=>Ke(A)?A.value=T:null),width:"740px",title:"视频预览","before-close":k},{default:$(()=>[W(m,{ref_key:"playerRef",ref:E,src:M.url,width:"100%",height:"450px"},null,8,["src"])]),_:1},8,["modelValue"])])):ee("",!0)],512)),[[Te,M.modelValue]])}}}),en={class:"material"},tn={class:"material__left"},rn={class:"flex-1 min-h-0"},nn={class:"material-left__content pt-4 p-b-4"},an={class:"flex flex-1 items-center min-w-0 pr-4"},sn={class:"flex-1 truncate mr-2"},on=["onClick"],ln={class:"flex justify-center p-2 border-t border-br"},un={class:"material__center flex flex-col"},dn={class:"operate-btn flex"},cn={class:"flex-1 flex"},hn={class:"flex items-center ml-2"},fn={key:0,class:"mt-3"},gn={class:"material-center__content flex flex-col flex-1 mb-1 min-h-0"},vn={class:"file-list flex flex-wrap mt-4"},pn={key:0,class:"item-selected"},mn={class:"operation-btns flex items-center"},yn={class:"inline-block"},En={class:"inline-block"},Tn={class:"inline-block"},Sn={key:1,class:"flex flex-1 justify-center items-center"},bn={class:"material-center__footer flex justify-between items-center mt-2"},Ln={class:"flex"},An={class:"mr-3"},kn={key:0,class:"material__right"},Dn={class:"flex justify-between p-2 flex-wrap"},_n={class:"sm flex items-center"},Rn={key:0},Cn={class:"flex-1 min-h-0"},In={class:"select-lists flex flex-col p-t-3"},wn={class:"select-item"},xn=ke({__name:"index",props:{fileSize:{type:String,default:"100px"},limit:{type:Number,default:1},type:{type:String,default:"image"},mode:{type:String,default:"picker"},pageSize:{type:Number,default:15}},emits:["change"],setup(M,{expose:I,emit:g}){const R=M,E=g,{limit:A}=Vt(R),k=Ie(()=>{switch(R.type){case"image":return 10;case"video":return 20;case"file":return 30;default:return 0}}),C=[{value:"0",label:"后台上传"},{value:"1",label:"前端上传"}],D=Kt("visible",he(!1)),s=he(""),b=he(!1),{treeRef:m,cateId:h,cateLists:T,handleAddCate:y,handleAddChildCate:d,handleEditCate:e,handleDeleteCate:a,getCateLists:t,handleCatSelect:o}=Ir(k.value),{tableRef:u,listShowType:n,moveId:l,pager:p,fileParams:f,select:v,isCheckAll:r,isIndeterminate:i,getFileList:c,refresh:S,batchFileDelete:L,batchFileMove:_,selectFile:x,isSelect:w,clearSelect:O,cancelSelete:P,selectAll:F,handleFileRename:N}=wr(h,k,A,R.pageSize),U=async()=>{var Y;await t(),(Y=m.value)==null||Y.setCurrentKey(h.value),c()},G=Y=>{s.value=Y,b.value=!0};Xe(()=>D.value,async Y=>{Y&&U()},{immediate:!0}),Xe(h,()=>{f.name="",S()}),Xe(v,Y=>{if(E("change",Y),Y.length==p.lists.length&&Y.length!==0){i.value=!1,r.value=!0;return}Y.length>0?i.value=!0:(r.value=!1,i.value=!1)},{deep:!0}),ft(()=>{R.mode=="page"&&U()}),I({clearSelect:O});const K=he(),q=Y=>{const j=Y,X=K.value;X.value=j,X.select(),document.execCommand("copy"),bt({message:"地址复制成功",type:"success"})};return(Y,j)=>{const X=Ht,Q=Wt,te=ur,ae=Yt,oe=qt,ie=lr,ue=zt,J=Xt,ne=or,le=ar,se=sr,pe=nr,me=St,Se=$t,be=Qt,fe=rr,ge=ir,we=mr,Ee=er,Re=tr,Ne=Jt,xe=Zt;return ye((z(),Z("div",en,[V("div",tn,[V("div",rn,[W(ue,null,{default:$(()=>[V("div",nn,[W(ie,{ref_key:"treeRef",ref:m,"node-key":"id",data:B(T),"empty-text":"","highlight-current":!0,"expand-on-click-node":!1,"current-node-key":B(h),onNodeClick:B(o)},{default:$(({data:H})=>[V("div",an,[j[16]||(j[16]=V("img",{class:"w-[20px] h-[16px] mr-3",src:yr},null,-1)),V("span",sn,[W(X,{content:H.name},null,8,["content"])]),H.id>0?(z(),ve(oe,{key:0,"hide-on-click":!1},{dropdown:$(()=>[W(ae,null,{default:$(()=>[W(te,{onConfirm:de=>B(e)(de,H.id),size:"default",value:H.name,width:"400px",limit:20,"show-limit":"",teleported:""},{default:$(()=>[V("div",null,[W(Q,null,{default:$(()=>j[12]||(j[12]=[re(" 命名分组 ")])),_:1})])]),_:2},1032,["onConfirm","value"]),W(te,{onConfirm:de=>B(d)(de,H.id),size:"default",width:"400px",limit:20,"show-limit":"",teleported:""},{default:$(()=>[V("div",null,[W(Q,null,{default:$(()=>j[13]||(j[13]=[re(" 添加分组 ")])),_:1})])]),_:2},1032,["onConfirm"]),V("div",{onClick:de=>{var Oe;return B(a)(H.id,(Oe=H==null?void 0:H.children)==null?void 0:Oe.length)}},[W(Q,null,{default:$(()=>j[14]||(j[14]=[re("删除分组")])),_:1})],8,on)]),_:2},1024)]),default:$(()=>[j[15]||(j[15]=V("span",{class:"muted m-r-10"},"···",-1))]),_:2},1024)):ee("",!0)])]),_:1},8,["data","current-node-key","onNodeClick"])])]),_:1})]),V("div",ln,[W(te,{onConfirm:B(y),size:"default",width:"400px",limit:20,"show-limit":"",teleported:""},{default:$(()=>[W(J,null,{default:$(()=>j[17]||(j[17]=[re(" 添加分组 ")])),_:1})]),_:1},8,["onConfirm"])])]),V("div",un,[V("div",dn,[V("div",cn,[M.type=="image"?(z(),ve(ne,{key:0,class:"mr-3",data:{cid:B(h)},type:M.type,"show-progress":!0,onChange:B(S)},{default:$(()=>[W(J,{type:"primary"},{default:$(()=>j[18]||(j[18]=[re("本地上传")])),_:1})]),_:1},8,["data","type","onChange"])):ee("",!0),M.type=="video"?(z(),ve(ne,{key:1,class:"mr-3",data:{cid:B(h)},type:M.type,"show-progress":!0,onAllSuccess:B(S)},{default:$(()=>[W(J,{type:"primary"},{default:$(()=>j[19]||(j[19]=[re("本地上传")])),_:1})]),_:1},8,["data","type","onAllSuccess"])):ee("",!0),M.type=="file"?(z(),ve(ne,{key:2,class:"mr-3",data:{cid:B(h)},type:M.type,"show-progress":!0,onAllSuccess:B(S)},{default:$(()=>[W(J,{type:"primary"},{default:$(()=>j[20]||(j[20]=[re("本地上传")])),_:1})]),_:1},8,["data","type","onAllSuccess"])):ee("",!0),M.mode=="page"?(z(),ve(J,{key:3,disabled:!B(v).length,onClick:j[0]||(j[0]=et(H=>B(L)(),["stop"]))},{default:$(()=>j[21]||(j[21]=[re(" 删除 ")])),_:1},8,["disabled"])):ee("",!0),M.mode=="page"?(z(),ve(pe,{key:4,class:"ml-3",onConfirm:B(_),disabled:!B(v).length,title:"移动文件"},{trigger:$(()=>[W(J,{disabled:!B(v).length},{default:$(()=>j[22]||(j[22]=[re("移动")])),_:1},8,["disabled"])]),default:$(()=>[V("div",null,[j[23]||(j[23]=V("span",{class:"mr-5"},"移动文件至",-1)),W(se,{modelValue:B(l),"onUpdate:modelValue":j[1]||(j[1]=H=>Ke(l)?l.value=H:null),placeholder:"请选择"},{default:$(()=>[(z(!0),Z(Ae,null,Fe(B(T),H=>(z(),Z(Ae,{key:H.id},[H.id!==""?(z(),ve(le,{key:0,label:H.name,value:H.id},null,8,["label","value"])):ee("",!0)],64))),128))]),_:1},8,["modelValue"])])]),_:1},8,["onConfirm","disabled"])):ee("",!0)]),W(se,{modelValue:B(f).source,"onUpdate:modelValue":j[2]||(j[2]=H=>B(f).source=H),placeholder:"请选择文件来源",clearable:"",style:{"margin-right":"20px"},class:"max-w-52 ml-3"},{default:$(()=>[(z(),Z(Ae,null,Fe(C,H=>W(le,{key:H.value,label:H.label,value:H.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),W(Se,{class:"w-60",placeholder:"请输入名称",modelValue:B(f).name,"onUpdate:modelValue":j[3]||(j[3]=H=>B(f).name=H),onKeyup:ze(B(S),["enter"])},{append:$(()=>[W(J,{onClick:B(S)},{icon:$(()=>[W(me,{name:"el-icon-Search"})]),_:1},8,["onClick"])]),_:1},8,["modelValue","onKeyup"]),V("div",hn,[W(be,{content:"列表视图",placement:"top"},{default:$(()=>[V("div",{class:_e(["list-icon",{select:B(n)=="table"}]),onClick:j[4]||(j[4]=H=>n.value="table")},[W(me,{name:"local-icon-list-2",size:18})],2)]),_:1}),W(be,{content:"平铺视图",placement:"top"},{default:$(()=>[V("div",{class:_e(["list-icon",{select:B(n)=="normal"}]),onClick:j[5]||(j[5]=H=>n.value="normal")},[W(me,{name:"el-icon-Menu",size:18})],2)]),_:1})])]),M.mode=="page"?(z(),Z("div",fn,[W(fe,{disabled:!B(p).lists.length,modelValue:B(r),"onUpdate:modelValue":j[6]||(j[6]=H=>Ke(r)?r.value=H:null),onChange:B(F),indeterminate:B(i)},{default:$(()=>j[24]||(j[24]=[re(" 当页全选 ")])),_:1},8,["disabled","modelValue","onChange","indeterminate"])])):ee("",!0),V("div",gn,[B(p).lists.length?ye((z(),ve(ue,{key:0},{default:$(()=>[V("ul",vn,[(z(!0),Z(Ae,null,Fe(B(p).lists,H=>(z(),Z("li",{class:"file-item-wrap",key:H.id,style:Me({width:M.fileSize})},[W(ge,{onClose:de=>B(L)([H.id])},{default:$(()=>[W(at,{uri:H.url,"file-size":M.fileSize,type:M.type,onClick:de=>B(x)(H)},{default:$(()=>[B(w)(H.id)?(z(),Z("div",pn,[W(me,{size:24,name:"el-icon-Check",color:"#fff"})])):ee("",!0)]),_:2},1032,["uri","file-size","type","onClick"])]),_:2},1032,["onClose"]),W(X,{class:"mt-1",content:H.name},null,8,["content"]),V("div",mn,[W(te,{onConfirm:de=>B(N)(de,H.id),size:"default",value:H.name,width:"400px",limit:50,"show-limit":"",teleported:""},{default:$(()=>[W(J,{type:"primary",link:""},{default:$(()=>j[25]||(j[25]=[re(" 重命名 ")])),_:1})]),_:2},1032,["onConfirm","value"]),H.type===10||H.type===20?(z(),ve(J,{key:0,type:"primary",link:"",onClick:de=>G(H.url)},{default:$(()=>j[26]||(j[26]=[re(" 查看 ")])),_:2},1032,["onClick"])):ee("",!0),H.type===10||H.type===20?(z(),ve(J,{key:1,type:"primary",link:"",onClick:de=>q(H.url),style:{"margin-left":"1px"}},{default:$(()=>j[27]||(j[27]=[re("地址")])),_:2},1032,["onClick"])):(z(),ve(we,{key:2,type:"primary",underline:!1,style:{"margin-left":"25px"},href:H.url},{default:$(()=>j[28]||(j[28]=[re("下载")])),_:2},1032,["href"]))])],4))),128))])]),_:1},512)),[[Te,B(n)=="normal"]]):ee("",!0),ye(W(Re,{ref_key:"tableRef",ref:u,class:"mt-4",data:B(p).lists,width:"100%",height:"100%",size:"large",onRowClick:B(x)},{default:$(()=>[W(Ee,{width:"55"},{default:$(({row:H})=>[W(fe,{modelValue:B(w)(H.id),onChange:de=>B(x)(H)},null,8,["modelValue","onChange"])]),_:1}),W(Ee,{label:"图片",width:"100"},{default:$(({row:H})=>[W(at,{uri:H.url,"file-size":"50px",type:M.type},null,8,["uri","type"])]),_:1}),W(Ee,{label:"名称","min-width":"100","show-overflow-tooltip":""},{default:$(({row:H})=>[W(we,{onClick:et(de=>G(H.url),["stop"]),underline:!1},{default:$(()=>[re(ce(H.name),1)]),_:2},1032,["onClick"])]),_:1}),W(Ee,{prop:"create_time",label:"上传时间","min-width":"100"}),W(Ee,{label:"操作",width:"150",fixed:"right"},{default:$(({row:H})=>[V("div",yn,[W(te,{onConfirm:de=>B(N)(de,H.id),size:"default",value:H.name,width:"400px",limit:50,"show-limit":"",teleported:""},{default:$(()=>[W(J,{type:"primary",link:""},{default:$(()=>j[29]||(j[29]=[re(" 重命名 ")])),_:1})]),_:2},1032,["onConfirm","value"])]),V("div",En,[W(J,{type:"primary",link:"",onClick:et(de=>G(H.url),["stop"])},{default:$(()=>j[30]||(j[30]=[re(" 查看 ")])),_:2},1032,["onClick"])]),V("div",Tn,[W(J,{type:"primary",link:"",onClick:et(de=>B(L)([H.id]),["stop"])},{default:$(()=>j[31]||(j[31]=[re(" 删除 ")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data","onRowClick"]),[[Te,B(n)=="table"]]),!B(p).loading&&!B(p).lists.length?(z(),Z("div",Sn," 暂无数据~ ")):ee("",!0)]),V("div",bn,[V("div",Ln,[M.mode=="page"?(z(),Z(Ae,{key:0},[V("span",An,[W(fe,{disabled:!B(p).lists.length,modelValue:B(r),"onUpdate:modelValue":j[7]||(j[7]=H=>Ke(r)?r.value=H:null),onChange:B(F),indeterminate:B(i)},{default:$(()=>j[32]||(j[32]=[re(" 当页全选 ")])),_:1},8,["disabled","modelValue","onChange","indeterminate"])]),W(J,{disabled:!B(v).length,onClick:j[8]||(j[8]=H=>B(L)())},{default:$(()=>j[33]||(j[33]=[re(" 删除 ")])),_:1},8,["disabled"]),W(pe,{class:"ml-3 inline",onConfirm:B(_),disabled:!B(v).length,title:"移动文件"},{trigger:$(()=>[W(J,{disabled:!B(v).length},{default:$(()=>j[34]||(j[34]=[re("移动")])),_:1},8,["disabled"])]),default:$(()=>[V("div",null,[j[35]||(j[35]=V("span",{class:"mr-5"},"移动文件至",-1)),W(se,{modelValue:B(l),"onUpdate:modelValue":j[9]||(j[9]=H=>Ke(l)?l.value=H:null),placeholder:"请选择"},{default:$(()=>[(z(!0),Z(Ae,null,Fe(B(T),H=>(z(),Z(Ae,{key:H.id},[H.id!==""?(z(),ve(le,{key:0,label:H.name,value:H.id},null,8,["label","value"])):ee("",!0)],64))),128))]),_:1},8,["modelValue"])])]),_:1},8,["onConfirm","disabled"])],64)):ee("",!0)]),W(Ne,{modelValue:B(p),"onUpdate:modelValue":j[10]||(j[10]=H=>Ke(p)?p.value=H:null),onChange:B(c),layout:"total, prev, pager, next, jumper"},null,8,["modelValue","onChange"])])]),M.mode=="picker"?(z(),Z("div",kn,[V("div",Dn,[V("div",_n,[re(" 已选择 "+ce(B(v).length)+" ",1),B(A)?(z(),Z("span",Rn,"/"+ce(B(A)),1)):ee("",!0)]),W(J,{type:"primary",link:"",onClick:B(O)},{default:$(()=>j[36]||(j[36]=[re("清空")])),_:1},8,["onClick"])]),V("div",Cn,[W(ue,{class:"ls-scrollbar"},{default:$(()=>[V("ul",In,[(z(!0),Z(Ae,null,Fe(B(v),H=>(z(),Z("li",{class:"mb-4",key:H.id},[V("div",wn,[W(ge,{onClose:de=>B(P)(H.id)},{default:$(()=>[W(at,{uri:H.url,"file-size":"100px",type:M.type},null,8,["uri","type"])]),_:2},1032,["onClose"])])]))),128))])]),_:1})])])):ee("",!0),W(Ji,{modelValue:B(b),"onUpdate:modelValue":j[11]||(j[11]=H=>Ke(b)?b.value=H:null),url:B(s),type:M.type},null,8,["modelValue","url","type"]),V("input",{ref_key:"textCopys",ref:K,id:"textCopys",value:"",style:{opacity:"0",position:"absolute"}},null,512)])),[[xe,B(p).loading]])}}}),Yn=Tt(xn,[["__scopeId","data-v-2dcfa5be"]]);export{at as F,Yn as _,Ji as a};
