import{d as B,i as T,c as D,O as N,o as c,a as r,m as t,w as a,e as m,D as i,F as $,r as j,C as F,b as u,p as S,I as p,bJ as U,q,v as I,a1 as J,a2 as L,eX as O}from"./index-B2xNDy79.js";import{E as P}from"./index-C6Cr8aHe.js";const R={class:"code-preview"},X={class:"flex",style:{height:"50vh"}},H=B({__name:"code-preview",props:{modelValue:{type:Boolean},code:{}},emits:["update:modelValue"],setup(_,{emit:f}){const b=_,V=f,{toClipboard:v}=O(),n=T("index0"),g=async l=>{try{await v(l),p.msgSuccess("复制成功")}catch{p.msgError("复制失败")}},s=D({get(){return b.modelValue},set(l){V("update:modelValue",l)}});return(l,e)=>{const h=N("highlightjs"),C=U,y=q,k=I,E=J,w=L,x=P;return c(),r("div",R,[t(x,{modelValue:m(s),"onUpdate:modelValue":e[1]||(e[1]=o=>i(s)?s.value=o:null),width:"900px",title:"代码预览"},{default:a(()=>[t(w,{modelValue:m(n),"onUpdate:modelValue":e[0]||(e[0]=o=>i(n)?n.value=o:null)},{default:a(()=>[(c(!0),r($,null,j(l.code,(o,d)=>(c(),F(E,{label:o.name,name:`index${d}`,key:d},{default:a(()=>[u("div",X,[t(C,{class:"flex-1"},{default:a(()=>[t(h,{autodetect:"",code:o.content},null,8,["code"])]),_:2},1024),u("div",null,[t(k,{onClick:z=>g(o.content),type:"primary",link:""},{icon:a(()=>[t(y,{name:"el-icon-CopyDocument"})]),default:a(()=>[e[2]||(e[2]=S(" 复制 "))]),_:2},1032,["onClick"])])])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])])}}});export{H as _};
