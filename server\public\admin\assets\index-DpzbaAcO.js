import{d as j,s as L,i as q,j as A,z as G,o as m,a as H,m as e,w as a,e as o,n as C,p as d,b as V,B as y,C as c,t as O,D as J,G as M,H as x,I as Q,E as W,v as X,q as Y,K as Z}from"./index-B2xNDy79.js";import{_ as ee}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as te,a as oe}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as ae}from"./el-card-DpH4mUSc.js";import{E as le,a as se}from"./el-form-item-DlU85AZK.js";import{_ as ne}from"./index.vue_vue_type_script_setup_true_lang-DpKD7KQ8.js";import{E as ie,a as re}from"./el-select-BRdnbwTl.js";import{c as $,d as me}from"./post-zozQIMRx.js";import{u as de}from"./usePaging-Dm2wALfy.js";import{_ as pe}from"./edit.vue_vue_type_script_setup_true_lang-DPtEqZXs.js";import{E as ue}from"./index-CcX0CyWL.js";import"./isEqual-CLGO95LP.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./_baseClone-CdezRMKA.js";/* empty css                       */import"./el-radio-CKcO4hVq.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./token-DI9FKtlJ.js";const ce={class:"post-lists"},_e={class:"flex justify-end mt-4"},fe=j({name:"post"}),Fe=j({...fe,setup(be){const _=L(),f=q(!1),s=A({code:"",name:"",status:""}),{pager:r,getLists:b,resetPage:w,resetParams:D}=de({fetchFun:$,params:s}),h=async()=>{var n;f.value=!0,await x(),(n=_.value)==null||n.open("add")},B=async n=>{var t,p;f.value=!0,await x(),(t=_.value)==null||t.open("edit"),(p=_.value)==null||p.getDetail(n)},K=async n=>{await Q.confirm("确定要删除？"),await me({id:n}),b()};return b(),(n,t)=>{const p=W,v=le,g=ie,z=re,u=X,P=ne,R=se,E=ae,T=Y,i=te,N=ue,S=oe,U=ee,k=G("perms"),F=Z;return m(),H("div",ce,[e(E,{class:"!border-none",shadow:"never"},{default:a(()=>[e(R,{ref:"formRef",class:"mb-[-16px]",model:o(s),inline:!0},{default:a(()=>[e(v,{class:"w-[280px]",label:"岗位编码"},{default:a(()=>[e(p,{modelValue:o(s).code,"onUpdate:modelValue":t[0]||(t[0]=l=>o(s).code=l),placeholder:"请输入岗位编码",clearable:"",onKeyup:C(o(w),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(v,{class:"w-[280px]",label:"岗位名称"},{default:a(()=>[e(p,{modelValue:o(s).name,"onUpdate:modelValue":t[1]||(t[1]=l=>o(s).name=l),clearable:"",onKeyup:C(o(w),["enter"])},null,8,["modelValue","onKeyup"])]),_:1}),e(v,{class:"w-[280px]",label:"岗位状态"},{default:a(()=>[e(z,{modelValue:o(s).status,"onUpdate:modelValue":t[2]||(t[2]=l=>o(s).status=l)},{default:a(()=>[e(g,{label:"全部",value:""}),e(g,{label:"正常",value:1}),e(g,{label:"停用",value:0})]),_:1},8,["modelValue"])]),_:1}),e(v,null,{default:a(()=>[e(u,{type:"primary",onClick:o(w)},{default:a(()=>t[6]||(t[6]=[d("查询")])),_:1},8,["onClick"]),e(u,{onClick:o(D)},{default:a(()=>t[7]||(t[7]=[d("重置")])),_:1},8,["onClick"]),e(P,{class:"ml-2.5","fetch-fun":o($),params:o(s),"page-size":o(r).size},null,8,["fetch-fun","params","page-size"])]),_:1})]),_:1},8,["model"])]),_:1}),e(E,{class:"!border-none mt-4",shadow:"never"},{default:a(()=>[V("div",null,[y((m(),c(u,{type:"primary",onClick:t[3]||(t[3]=l=>h())},{icon:a(()=>[e(T,{name:"el-icon-Plus"})]),default:a(()=>[t[8]||(t[8]=d(" 新增 "))]),_:1})),[[k,["dept.jobs/add"]]])]),y((m(),c(S,{class:"mt-4",size:"large",data:o(r).lists},{default:a(()=>[e(i,{label:"岗位编码",prop:"code","min-width":"100"}),e(i,{label:"岗位名称",prop:"name","min-width":"100"}),e(i,{label:"排序",prop:"sort","min-width":"100"}),e(i,{label:"备注",prop:"remark","min-width":"100","show-overflow-tooltip":""}),e(i,{label:"添加时间",prop:"create_time","min-width":"180"}),e(i,{label:"状态",prop:"status","min-width":"100"},{default:a(({row:l})=>[e(N,{class:"ml-2",type:l.status?"primary":"danger"},{default:a(()=>[d(O(l.status_desc),1)]),_:2},1032,["type"])]),_:1}),e(i,{label:"操作",width:"120",fixed:"right"},{default:a(({row:l})=>[y((m(),c(u,{type:"primary",link:"",onClick:I=>B(l)},{default:a(()=>t[9]||(t[9]=[d(" 编辑 ")])),_:2},1032,["onClick"])),[[k,["dept.jobs/edit"]]]),y((m(),c(u,{type:"danger",link:"",onClick:I=>K(l.id)},{default:a(()=>t[10]||(t[10]=[d(" 删除 ")])),_:2},1032,["onClick"])),[[k,["dept.jobs/delete"]]])]),_:1})]),_:1},8,["data"])),[[F,o(r).loading]]),V("div",_e,[e(U,{modelValue:o(r),"onUpdate:modelValue":t[4]||(t[4]=l=>J(r)?r.value=l:null),onChange:o(b)},null,8,["modelValue","onChange"])])]),_:1}),o(f)?(m(),c(pe,{key:0,ref_key:"editRef",ref:_,onSuccess:o(b),onClose:t[5]||(t[5]=l=>f.value=!1)},null,8,["onSuccess"])):M("",!0)])}}});export{Fe as default};
