import{d as B,i as g,H as k,av as C,x as b,o as r,a as S,b as p,U as f,m as T,b8 as $,w as n,p as s,t as i,C as m,G as v,Y as E,v as V}from"./index-B2xNDy79.js";import{E as w}from"./index-C6Cr8aHe.js";const h=B({props:{title:{type:String,default:""},content:{type:String,default:""},confirmButtonText:{type:[String,Boolean],default:"确定"},cancelButtonText:{type:[String,Boolean],default:"取消"},width:{type:String,default:"400px"},disabled:{type:Boolean,default:!1},async:{type:Boolean,default:!1},clickModalClose:{type:Boolean,default:!1},center:{type:Boolean,default:!1},customClass:{type:String,default:""}},emits:["confirm","cancel","close","open"],setup(e,{emit:o}){const t=g(!1),c=l=>{o(l),(!e.async||l==="cancel")&&d()},d=()=>{t.value=!1,k(()=>{o("close")})},u=()=>{e.disabled||(o("open"),t.value=!0)};return C("visible",t),{visible:t,handleEvent:c,close:d,open:u}}}),N={class:"dialog"},D={class:"dialog-footer"};function M(e,o,t,c,d,u){const l=V,y=w;return r(),S("div",N,[p("div",{class:"dialog__trigger",onClick:o[0]||(o[0]=(...a)=>e.open&&e.open(...a))},[f(e.$slots,"trigger",{},void 0,!0)]),T(y,{modelValue:e.visible,"onUpdate:modelValue":o[3]||(o[3]=a=>e.visible=a),class:E(e.customClass),center:e.center,"append-to-body":!0,width:e.width,"close-on-click-modal":e.clickModalClose,onClosed:e.close},$({footer:n(()=>[p("div",D,[e.cancelButtonText?(r(),m(l,{key:0,onClick:o[1]||(o[1]=a=>e.handleEvent("cancel"))},{default:n(()=>[s(i(e.cancelButtonText),1)]),_:1})):v("",!0),e.confirmButtonText?(r(),m(l,{key:1,type:"primary",onClick:o[2]||(o[2]=a=>e.handleEvent("confirm"))},{default:n(()=>[s(i(e.confirmButtonText),1)]),_:1})):v("",!0)])]),default:n(()=>[f(e.$slots,"default",{},()=>[s(i(e.content),1)],!0)]),_:2},[e.title?{name:"header",fn:n(()=>[s(i(e.title),1)]),key:"0"}:void 0]),1032,["modelValue","class","center","width","close-on-click-modal","onClosed"])])}const z=b(h,[["render",M],["__scopeId","data-v-a5e5144a"]]);export{z as P};
