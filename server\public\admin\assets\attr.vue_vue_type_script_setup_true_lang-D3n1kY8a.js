import{d as V,c as E,o as w,a as y,m as t,w as o,e as a,b as s,p,E as B}from"./index-B2xNDy79.js";import{E as C,a as N}from"./el-form-item-DlU85AZK.js";/* empty css                       */import{E as U,a as g}from"./el-radio-CKcO4hVq.js";import{E as j}from"./el-card-DpH4mUSc.js";import{_ as k}from"./add-nav.vue_vue_type_script_setup_true_lang-BlUx6EnP.js";const F={class:"flex-1"},$=V({__name:"attr",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},emits:["update:content"],setup(u,{emit:i}){const f=i,_=u,l=E({get:()=>_.content,set:m=>{f("update:content",m)}});return(m,e)=>{const x=B,c=C,d=j,r=U,b=g,v=N;return w(),y("div",null,[t(v,{"label-width":"70px"},{default:o(()=>[t(d,{shadow:"never",class:"!border-none flex mt-2"},{default:o(()=>[t(c,{label:"标题"},{default:o(()=>[t(x,{class:"w-[396px]",modelValue:a(l).title,"onUpdate:modelValue":e[0]||(e[0]=n=>a(l).title=n)},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{shadow:"never",class:"!border-none flex mt-2"},{default:o(()=>[e[5]||(e[5]=s("div",{class:"flex items-end mb-4"},[s("div",{class:"text-base text-[#101010] font-medium"},"展示样式")],-1)),t(b,{modelValue:a(l).style,"onUpdate:modelValue":e[1]||(e[1]=n=>a(l).style=n)},{default:o(()=>[t(r,{value:1},{default:o(()=>e[3]||(e[3]=[p("横排")])),_:1}),t(r,{value:2},{default:o(()=>e[4]||(e[4]=[p("竖排")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(d,{shadow:"never",class:"!border-none flex mt-2"},{default:o(()=>[e[6]||(e[6]=s("div",{class:"flex items-end mb-4"},[s("div",{class:"text-base text-[#101010] font-medium"},"菜单"),s("div",{class:"text-xs text-tx-secondary ml-2"},"建议图片尺寸：100px*100px")],-1)),s("div",F,[t(k,{modelValue:a(l).data,"onUpdate:modelValue":e[2]||(e[2]=n=>a(l).data=n)},null,8,["modelValue"])])]),_:1})]),_:1})])}}});export{$ as _};
