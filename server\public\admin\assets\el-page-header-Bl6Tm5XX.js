import{at as g,au as C,d as u,an as k,c as $,o,a as l,Y as s,e as a,U as i,G as n,as as E,ar as h,ay as S,bp as H,e9 as D,am as N,bb as V,b as d,C as m,w as _,aq as z,S as B,p as b,t as y,m as w}from"./index-B2xNDy79.js";const T=g({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:C(String),default:"solid"}}),I=u({name:"ElDivider"}),q=u({...I,props:T,setup(v){const p=v,r=k("divider"),c=$(()=>r.cssVar({"border-style":p.borderStyle}));return(e,f)=>(o(),l("div",{class:s([a(r).b(),a(r).m(e.direction)]),style:E(a(c)),role:"separator"},[e.$slots.default&&e.direction!=="vertical"?(o(),l("div",{key:0,class:s([a(r).e("text"),a(r).is(e.contentPosition)])},[i(e.$slots,"default")],2)):n("v-if",!0)],6))}});var G=h(q,[["__file","divider.vue"]]);const L=S(G),U=g({icon:{type:H,default:()=>D},title:String,content:{type:String,default:""}}),Y={back:()=>!0},j=u({name:"ElPageHeader"}),A=u({...j,props:U,emits:Y,setup(v,{emit:p}){const r=N(),{t:c}=V(),e=k("page-header"),f=$(()=>[e.b(),{[e.m("has-breadcrumb")]:!!r.breadcrumb,[e.m("has-extra")]:!!r.extra,[e.is("contentful")]:!!r.default}]);function P(){p("back")}return(t,J)=>(o(),l("div",{class:s(a(f))},[t.$slots.breadcrumb?(o(),l("div",{key:0,class:s(a(e).e("breadcrumb"))},[i(t.$slots,"breadcrumb")],2)):n("v-if",!0),d("div",{class:s(a(e).e("header"))},[d("div",{class:s(a(e).e("left"))},[d("div",{class:s(a(e).e("back")),role:"button",tabindex:"0",onClick:P},[t.icon||t.$slots.icon?(o(),l("div",{key:0,"aria-label":t.title||a(c)("el.pageHeader.title"),class:s(a(e).e("icon"))},[i(t.$slots,"icon",{},()=>[t.icon?(o(),m(a(B),{key:0},{default:_(()=>[(o(),m(z(t.icon)))]),_:1})):n("v-if",!0)])],10,["aria-label"])):n("v-if",!0),d("div",{class:s(a(e).e("title"))},[i(t.$slots,"title",{},()=>[b(y(t.title||a(c)("el.pageHeader.title")),1)])],2)],2),w(a(L),{direction:"vertical"}),d("div",{class:s(a(e).e("content"))},[i(t.$slots,"content",{},()=>[b(y(t.content),1)])],2)],2),t.$slots.extra?(o(),l("div",{key:0,class:s(a(e).e("extra"))},[i(t.$slots,"extra")],2)):n("v-if",!0)],2),t.$slots.default?(o(),l("div",{key:1,class:s(a(e).e("main"))},[i(t.$slots,"default")],2)):n("v-if",!0)],2))}});var F=h(A,[["__file","page-header.vue"]]);const M=S(F);export{M as E};
