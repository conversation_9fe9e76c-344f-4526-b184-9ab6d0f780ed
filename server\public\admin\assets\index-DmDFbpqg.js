import{d as k,s as R,i as T,z as L,o as i,a as U,m as t,w as l,B as p,e as s,C as u,b,p as w,D as z,G as A,H as h,I as P,q as j,v as q,J as F,K as G}from"./index-B2xNDy79.js";import{_ as H}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as I,a as J}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as K}from"./el-card-DpH4mUSc.js";import{E as M}from"./el-alert-BUxHh72o.js";import{a as O,b as Q,c as W}from"./article-Dwgm3r-g.js";import{u as X}from"./usePaging-Dm2wALfy.js";import{_ as Y}from"./edit.vue_vue_type_script_setup_true_name_articleColumnEdit_lang-DoP8eRlh.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./isEqual-CLGO95LP.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./el-form-item-DlU85AZK.js";import"./_baseClone-CdezRMKA.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";const Z={class:"flex justify-end mt-4"},ee=k({name:"articleColumn"}),ke=k({...ee,setup(te){const _=R(),f=T(!1),{pager:r,getLists:n}=X({fetchFun:W}),y=async()=>{var o;f.value=!0,await h(),(o=_.value)==null||o.open("add")},E=async o=>{var e,c;f.value=!0,await h(),(e=_.value)==null||e.open("edit"),(c=_.value)==null||c.getDetail(o)},V=async o=>{await P.confirm("确定要删除？"),await O({id:o}),n()},x=async(o,e)=>{try{await Q({id:e,is_show:o}),n()}catch{n()}};return n(),(o,e)=>{const c=M,g=K,$=j,C=q,d=I,B=F,D=J,S=H,v=L("perms"),N=G;return i(),U("div",null,[t(g,{class:"!border-none",shadow:"never"},{default:l(()=>[t(c,{type:"warning",title:"温馨提示：用于管理网站的分类，只可添加到一级",closable:!1,"show-icon":""})]),_:1}),p((i(),u(g,{class:"!border-none mt-4",shadow:"never"},{default:l(()=>[b("div",null,[p((i(),u(C,{class:"mb-4",type:"primary",onClick:e[0]||(e[0]=a=>y())},{icon:l(()=>[t($,{name:"el-icon-Plus"})]),default:l(()=>[e[3]||(e[3]=w(" 新增 "))]),_:1})),[[v,["article.articleCate/add"]]])]),t(D,{size:"large",data:s(r).lists},{default:l(()=>[t(d,{label:"栏目名称",prop:"name","min-width":"120"}),t(d,{label:"文章数",prop:"article_count","min-width":"120"}),t(d,{label:"状态","min-width":"120"},{default:l(({row:a})=>[p(t(B,{modelValue:a.is_show,"onUpdate:modelValue":m=>a.is_show=m,"active-value":1,"inactive-value":0,onChange:m=>x(m,a.id)},null,8,["modelValue","onUpdate:modelValue","onChange"]),[[v,["article.articleCate/updateStatus"]]])]),_:1}),t(d,{label:"排序",prop:"sort","min-width":"120"}),t(d,{label:"操作",width:"120",fixed:"right"},{default:l(({row:a})=>[p((i(),u(C,{type:"primary",link:"",onClick:m=>E(a)},{default:l(()=>e[4]||(e[4]=[w(" 编辑 ")])),_:2},1032,["onClick"])),[[v,["article.articleCate/edit"]]]),p((i(),u(C,{type:"danger",link:"",onClick:m=>V(a.id)},{default:l(()=>e[5]||(e[5]=[w(" 删除 ")])),_:2},1032,["onClick"])),[[v,["article.articleCate/delete"]]])]),_:1})]),_:1},8,["data"]),b("div",Z,[t(S,{modelValue:s(r),"onUpdate:modelValue":e[1]||(e[1]=a=>z(r)?r.value=a:null),onChange:s(n)},null,8,["modelValue","onChange"])])]),_:1})),[[N,s(r).loading]]),s(f)?(i(),u(Y,{key:0,ref_key:"editRef",ref:_,onSuccess:s(n),onClose:e[2]||(e[2]=a=>f.value=!1)},null,8,["onSuccess"])):A("",!0)])}}});export{ke as default};
