import{at as B,a3 as F,a5 as S,a6 as N,a7 as $,a8 as z,b6 as O,i as E,a9 as W,c as f,ag as w,aj as Y,ab as Q,al as X,d as _,an as k,o as R,a as I,b as y,B as P,b7 as D,e,D as U,Y as m,ao as h,U as C,p as x,t as A,ar as G,H as K,as as Z,a4 as ee,aJ as ae,ac as oe,ak as le,k as se,av as ne,j as te,ax as re,V as ie,ad as de,ay as ue,az as L}from"./index-B2xNDy79.js";const T=B({modelValue:{type:[String,Number,Boolean],default:void 0},size:F,disabled:Boolean,label:{type:[String,Number,Boolean],default:void 0},value:{type:[String,Number,Boolean],default:void 0},name:{type:String,default:void 0}}),ce=B({...T,border:Boolean}),M={[S]:o=>N(o)||$(o)||z(o),[O]:o=>N(o)||$(o)||z(o)},j=Symbol("radioGroupKey"),H=(o,u)=>{const s=E(),a=W(j,void 0),i=f(()=>!!a),c=f(()=>w(o.value)?o.label:o.value),r=f({get(){return i.value?a.modelValue:o.modelValue},set(n){i.value?a.changeEvent(n):u&&u(S,n),s.value.checked=o.modelValue===c.value}}),d=Y(f(()=>a==null?void 0:a.size)),l=Q(f(()=>a==null?void 0:a.disabled)),t=E(!1),p=f(()=>l.value||i.value&&r.value!==c.value?-1:0);return X({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-radio",ref:"https://element-plus.org/en-US/component/radio.html"},f(()=>i.value&&w(o.value))),{radioRef:s,isGroup:i,radioGroup:a,focus:t,size:d,disabled:l,tabIndex:p,modelValue:r,actualValue:c}},pe=_({name:"ElRadio"}),ve=_({...pe,props:ce,emits:M,setup(o,{emit:u}){const s=o,a=k("radio"),{radioRef:i,radioGroup:c,focus:r,size:d,disabled:l,modelValue:t,actualValue:p}=H(s,u);function n(){K(()=>u("change",t.value))}return(v,g)=>{var b;return R(),I("label",{class:m([e(a).b(),e(a).is("disabled",e(l)),e(a).is("focus",e(r)),e(a).is("bordered",v.border),e(a).is("checked",e(t)===e(p)),e(a).m(e(d))])},[y("span",{class:m([e(a).e("input"),e(a).is("disabled",e(l)),e(a).is("checked",e(t)===e(p))])},[P(y("input",{ref_key:"radioRef",ref:i,"onUpdate:modelValue":V=>U(t)?t.value=V:null,class:m(e(a).e("original")),value:e(p),name:v.name||((b=e(c))==null?void 0:b.name),disabled:e(l),checked:e(t)===e(p),type:"radio",onFocus:V=>r.value=!0,onBlur:V=>r.value=!1,onChange:n,onClick:h(()=>{},["stop"])},null,42,["onUpdate:modelValue","value","name","disabled","checked","onFocus","onBlur","onClick"]),[[D,e(t)]]),y("span",{class:m(e(a).e("inner"))},null,2)],2),y("span",{class:m(e(a).e("label")),onKeydown:h(()=>{},["stop"])},[C(v.$slots,"default",{},()=>[x(A(v.label),1)])],42,["onKeydown"])],2)}}});var fe=G(ve,[["__file","radio.vue"]]);const me=B({...T}),be=_({name:"ElRadioButton"}),ye=_({...be,props:me,setup(o){const u=o,s=k("radio"),{radioRef:a,focus:i,size:c,disabled:r,modelValue:d,radioGroup:l,actualValue:t}=H(u),p=f(()=>({backgroundColor:(l==null?void 0:l.fill)||"",borderColor:(l==null?void 0:l.fill)||"",boxShadow:l!=null&&l.fill?`-1px 0 0 0 ${l.fill}`:"",color:(l==null?void 0:l.textColor)||""}));return(n,v)=>{var g;return R(),I("label",{class:m([e(s).b("button"),e(s).is("active",e(d)===e(t)),e(s).is("disabled",e(r)),e(s).is("focus",e(i)),e(s).bm("button",e(c))])},[P(y("input",{ref_key:"radioRef",ref:a,"onUpdate:modelValue":b=>U(d)?d.value=b:null,class:m(e(s).be("button","original-radio")),value:e(t),type:"radio",name:n.name||((g=e(l))==null?void 0:g.name),disabled:e(r),onFocus:b=>i.value=!0,onBlur:b=>i.value=!1,onClick:h(()=>{},["stop"])},null,42,["onUpdate:modelValue","value","name","disabled","onFocus","onBlur","onClick"]),[[D,e(d)]]),y("span",{class:m(e(s).be("button","inner")),style:Z(e(d)===e(t)?e(p):{}),onKeydown:h(()=>{},["stop"])},[C(n.$slots,"default",{},()=>[x(A(n.label),1)])],46,["onKeydown"])],2)}}});var q=G(ye,[["__file","radio-button.vue"]]);const _e=B({id:{type:String,default:void 0},size:F,disabled:Boolean,modelValue:{type:[String,Number,Boolean],default:void 0},fill:{type:String,default:""},textColor:{type:String,default:""},name:{type:String,default:void 0},validateEvent:{type:Boolean,default:!0},...ee(["ariaLabel"])}),ge=M,he=_({name:"ElRadioGroup"}),Be=_({...he,props:_e,emits:ge,setup(o,{emit:u}){const s=o,a=k("radio"),i=ae(),c=E(),{formItem:r}=oe(),{inputId:d,isLabeledByFormItem:l}=le(s,{formItemContext:r}),t=n=>{u(S,n),K(()=>u("change",n))};se(()=>{const n=c.value.querySelectorAll("[type=radio]"),v=n[0];!Array.from(n).some(g=>g.checked)&&v&&(v.tabIndex=0)});const p=f(()=>s.name||i.value);return ne(j,te({...re(s),changeEvent:t,name:p})),ie(()=>s.modelValue,()=>{s.validateEvent&&(r==null||r.validate("change").catch(n=>de()))}),(n,v)=>(R(),I("div",{id:e(d),ref_key:"radioGroupRef",ref:c,class:m(e(a).b("group")),role:"radiogroup","aria-label":e(l)?void 0:n.ariaLabel||"radio-group","aria-labelledby":e(l)?e(r).labelId:void 0},[C(n.$slots,"default")],10,["id","aria-label","aria-labelledby"]))}});var J=G(Be,[["__file","radio-group.vue"]]);const Ee=ue(fe,{RadioButton:q,RadioGroup:J}),Se=L(J);L(q);export{Ee as E,Se as a};
