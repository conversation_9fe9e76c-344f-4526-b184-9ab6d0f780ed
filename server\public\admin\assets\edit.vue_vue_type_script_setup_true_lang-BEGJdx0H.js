import{d as y,s as c,i as R,c as h,j as k,o as C,a as g,m as l,w as n,e as a,E as F,L as D}from"./index-B2xNDy79.js";import{E as I,a as U}from"./el-form-item-DlU85AZK.js";import{a as B,b as N}from"./role-BYPWLZ0d.js";import{P}from"./index-DFOp_83R.js";const j={class:"edit-popup"},H=y({__name:"edit",emits:["success","close"],setup(q,{expose:f,emit:_}){const u=_,d=c(),m=c(),r=R("add"),w=h(()=>r.value=="edit"?"编辑角色":"新增角色"),o=k({id:"",name:"",desc:"",sort:0,menu_id:[]}),b={name:[{required:!0,message:"请输入名称",trigger:["blur"]}]},v=async()=>{var t,e;await((t=d.value)==null?void 0:t.validate()),r.value=="edit"?await B(o):await N(o),(e=m.value)==null||e.close(),u("success")},x=()=>{u("close")};return f({open:(t="add")=>{var e;r.value=t,(e=m.value)==null||e.open()},setFormData:async t=>{for(const e in o)t[e]!=null&&t[e]!=null&&(o[e]=t[e])}}),(t,e)=>{const i=F,p=I,V=D,E=U;return C(),g("div",j,[l(P,{ref_key:"popupRef",ref:m,title:a(w),async:!0,width:"550px",onConfirm:v,onClose:x},{default:n(()=>[l(E,{class:"ls-form",ref_key:"formRef",ref:d,rules:b,model:a(o),"label-width":"60px"},{default:n(()=>[l(p,{label:"名称",prop:"name"},{default:n(()=>[l(i,{class:"ls-input",modelValue:a(o).name,"onUpdate:modelValue":e[0]||(e[0]=s=>a(o).name=s),placeholder:"请输入名称",clearable:""},null,8,["modelValue"])]),_:1}),l(p,{label:"备注",prop:"desc"},{default:n(()=>[l(i,{modelValue:a(o).desc,"onUpdate:modelValue":e[1]||(e[1]=s=>a(o).desc=s),type:"textarea",autosize:{minRows:4,maxRows:6},placeholder:"请输入备注",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(p,{label:"排序",prop:"sort"},{default:n(()=>[l(V,{modelValue:a(o).sort,"onUpdate:modelValue":e[2]||(e[2]=s=>a(o).sort=s),min:0,max:9999},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{H as _};
