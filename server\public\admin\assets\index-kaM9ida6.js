import{d as x,s as g,i as E,z as L,o as a,a as $,m as t,w as n,b as C,B as u,C as m,p as h,e as l,D as z,G as D,H as y,I,q as P,v as j,K as q}from"./index-B2xNDy79.js";import{E as G}from"./el-card-DpH4mUSc.js";import{_ as H}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as K,a as U}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{c as J,d as M}from"./role-BYPWLZ0d.js";import{u as O}from"./usePaging-Dm2wALfy.js";import{_ as Q}from"./auth.vue_vue_type_script_setup_true_lang-Cyk1UTrj.js";import{_ as W}from"./edit.vue_vue_type_script_setup_true_lang-BEGJdx0H.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./isEqual-CLGO95LP.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./el-form-item-DlU85AZK.js";import"./_baseClone-CdezRMKA.js";import"./el-tree-8o9N7gsQ.js";import"./menu-CoEQjKPG.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";const X={class:"role-lists"},Y={class:"mt-4"},Z={class:"flex justify-end mt-4"},ee=x({name:"role"}),Ee=x({...ee,setup(te){const c=g(),k=g(),f=E(!1),w=E(!1),{pager:d,getLists:p}=O({fetchFun:M}),V=async()=>{var o;f.value=!0,await y(),(o=c.value)==null||o.open("add")},B=async o=>{var e,s;f.value=!0,await y(),(e=c.value)==null||e.open("edit"),(s=c.value)==null||s.setFormData(o)},R=async o=>{var e,s;w.value=!0,await y(),(e=k.value)==null||e.open(),(s=k.value)==null||s.setFormData(o)},N=async o=>{await I.confirm("确定要删除？"),await J({id:o}),p()};return p(),(o,e)=>{const s=P,_=j,i=K,S=U,T=H,A=G,v=L("perms"),F=q;return a(),$("div",X,[t(A,{class:"!border-none",shadow:"never"},{default:n(()=>[C("div",null,[u((a(),m(_,{type:"primary",onClick:V},{icon:n(()=>[t(s,{name:"el-icon-Plus"})]),default:n(()=>[e[3]||(e[3]=h(" 新增 "))]),_:1})),[[v,["auth.role/add"]]])]),u((a(),$("div",Y,[C("div",null,[t(S,{data:l(d).lists,size:"large"},{default:n(()=>[t(i,{prop:"id",label:"ID","min-width":"100"}),t(i,{prop:"name",label:"名称","min-width":"150"}),t(i,{prop:"desc",label:"备注","min-width":"150","show-overflow-tooltip":""}),t(i,{prop:"sort",label:"排序","min-width":"100"}),t(i,{prop:"num",label:"管理员人数","min-width":"100"}),t(i,{prop:"create_time",label:"创建时间","min-width":"180"}),t(i,{label:"操作",width:"200",fixed:"right"},{default:n(({row:r})=>[u((a(),m(_,{link:"",type:"primary",onClick:b=>B(r)},{default:n(()=>e[4]||(e[4]=[h(" 编辑 ")])),_:2},1032,["onClick"])),[[v,["auth.role/edit"]]]),u((a(),m(_,{link:"",type:"primary",onClick:b=>R(r)},{default:n(()=>e[5]||(e[5]=[h(" 分配权限 ")])),_:2},1032,["onClick"])),[[v,["auth.role/edit"]]]),u((a(),m(_,{link:"",type:"danger",onClick:b=>N(r.id)},{default:n(()=>e[6]||(e[6]=[h(" 删除 ")])),_:2},1032,["onClick"])),[[v,["auth.role/delete"]]])]),_:1})]),_:1},8,["data"])]),C("div",Z,[t(T,{modelValue:l(d),"onUpdate:modelValue":e[0]||(e[0]=r=>z(d)?d.value=r:null),onChange:l(p)},null,8,["modelValue","onChange"])])])),[[F,l(d).loading]])]),_:1}),l(f)?(a(),m(W,{key:0,ref_key:"editRef",ref:c,onSuccess:l(p),onClose:e[1]||(e[1]=r=>f.value=!1)},null,8,["onSuccess"])):D("",!0),l(w)?(a(),m(Q,{key:1,ref_key:"authRef",ref:k,onSuccess:l(p),onClose:e[2]||(e[2]=r=>w.value=!1)},null,8,["onSuccess"])):D("",!0)])}}});export{Ee as default};
