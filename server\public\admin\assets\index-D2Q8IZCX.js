import{d as y,i as B,j as F,N as P,O as V,z,o as n,a as g,m as e,w as t,e as l,D,F as M,r as O,B as v,C as p,p as d,Q as U,a1 as S,a2 as j,v as q,K}from"./index-B2xNDy79.js";import{E as N,a as Q}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as G}from"./el-card-DpH4mUSc.js";import{E as H}from"./el-alert-BUxHh72o.js";import{a as I}from"./message-BcHkSWHo.js";import{u as J}from"./usePaging-Dm2wALfy.js";import{E as W}from"./index-CcX0CyWL.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./_initCloneObject-C-h6JGU9.js";const X=y({name:"notice"}),pe=y({...X,setup(Y){let c;(s=>{s[s.USER=1]="USER",s[s.PLATFORM=2]="PLATFORM"})(c||(c={}));const r=B(1),E=[{name:"通知用户",type:1},{name:"通知平台",type:2}],w=F({recipient:r}),{pager:u,getLists:_}=J({fetchFun:I,params:w});return P(()=>{_()}),_(),(s,a)=>{const T=H,f=G,k=S,h=j,i=N,b=W,C=V("router-link"),L=q,R=Q,x=z("perms"),A=K;return n(),g("div",null,[e(f,{class:"!border-none",shadow:"never"},{default:t(()=>[e(T,{type:"warning",title:"温馨提示：平台配置在各个场景下的通知发送方式和内容模板",closable:!1,"show-icon":""})]),_:1}),e(f,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[e(h,{modelValue:l(r),"onUpdate:modelValue":a[0]||(a[0]=o=>D(r)?r.value=o:null),onTabChange:l(_)},{default:t(()=>[(n(),g(M,null,O(E,(o,m)=>e(k,{key:m,label:o.name,name:o.type,lazy:""},null,8,["label","name"])),64))]),_:1},8,["modelValue","onTabChange"]),v((n(),p(R,{size:"large",data:l(u).lists},{default:t(()=>[e(i,{label:"通知场景",prop:"scene_name","min-width":"120"}),e(i,{label:"通知类型",prop:"type_desc","min-width":"160"}),e(i,{label:"短信通知","min-width":"80"},{default:t(({row:o})=>{var m;return[((m=o.sms_notice)==null?void 0:m.status)==1?(n(),p(b,{key:0},{default:t(()=>a[1]||(a[1]=[d("开启")])),_:1})):(n(),p(b,{key:1,type:"danger"},{default:t(()=>a[2]||(a[2]=[d("关闭")])),_:1}))]}),_:1}),e(i,{label:"操作","min-width":"80",fixed:"right"},{default:t(({row:o})=>[v((n(),p(L,{type:"primary",link:""},{default:t(()=>[e(C,{to:{path:l(U)("notice.notice/set"),query:{id:o.id}}},{default:t(()=>a[3]||(a[3]=[d(" 设置 ")])),_:2},1032,["to"])]),_:2},1024)),[[x,["notice.notice/set"]]])]),_:1})]),_:1},8,["data"])),[[A,l(u).loading]])]),_:1})])}}});export{pe as default};
