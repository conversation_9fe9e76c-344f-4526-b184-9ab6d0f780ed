import{d as k,i as B,eO as E,o as m,C as c,w as n,b as o,m as t,e,p as C,G as i,a as d}from"./index-B2xNDy79.js";import{E as y,a as D}from"./el-form-item-DlU85AZK.js";import{_ as I}from"./index.vue_vue_type_script_setup_true_lang-C-xtr9xT.js";/* empty css                       */import{E as N,a as T}from"./el-radio-CKcO4hVq.js";import z from"./theme-picker-fL8txkcV.js";const U="/admin/assets/blue1-gLOo1H0w.png",q="/admin/assets/blue2-CRQPdLZd.png",P="/admin/assets/blue3-D3K9OqGO.png",O="/admin/assets/green1-h1zqes95.png",G="/admin/assets/green2-C-VRKLSN.png",L="/admin/assets/green3-CPorIQiC.png",R="/admin/assets/purple1-BpMq9FWz.png",W="/admin/assets/purple2-C0tQkldV.png",K="/admin/assets/purple3-BGd0LxTa.png",Q="/admin/assets/yellow1-Ebw0T5sw.png",Z="/admin/assets/yellow2-B-WtqITJ.png",A="/admin/assets/yellow3-C_qd9cqN.png",M="/admin/assets/red1-C6Y3UuNB.png",S="/admin/assets/red2-Dw8p71sP.png",Y="/admin/assets/red3-NOuWP8DK.png",H="/admin/assets/pink1-BWNZrP7C.png",J="/admin/assets/pink2-BpEp33zy.png",$="/admin/assets/pink3-BxZ4Y6CS.png",j={key:0,class:"flex"},X={key:1,class:"flex"},h={key:2,class:"flex"},ll={key:3,class:"flex"},ol={key:4,class:"flex"},sl={key:5,class:"flex"},il=k({__name:"mobile-style",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{emit:f}){const w=_,v=B([{id:1,name:"科技蓝",color1:"#2F80ED",color2:"#56CCF2",btnColor:"white"},{id:2,name:"偏绿蓝",color1:"#2EC840",color2:"#3DE650",btnColor:"white"},{id:3,name:"商务紫",color1:"#A74BFD",color2:"#CB60FF",btnColor:"white"},{id:4,name:"活力橙",color1:"#F7971E",color2:"#FFD200",btnColor:"black"},{id:5,name:"经典红",color1:"#FF2C3C",color2:"#EF1D2D",btnColor:"white"},{id:6,name:"美妆色",color1:"#FD498F",color2:"#FA444D",btnColor:"white"},{id:7,name:"自定义",color1:"#F8F8F8",color2:"#F5F5F5",btnColor:"white"}]),b=f,F=a=>{a.id!=7&&(s.value.themeColor1=a.color1,s.value.themeColor2=a.color2,s.value.navigationBarColor=a.color1,s.value.buttonColor=a.btnColor,s.value.topTextColor=a.btnColor)},s=E(w,"modelValue",b);return(a,l)=>{const p=y,u=N,g=T,x=I,V=D;return m(),c(V,{"label-width":"140px"},{default:n(()=>[o("div",null,[l[6]||(l[6]=o("div",{class:"text-xl font-medium mb-[20px]"},"系统主题色",-1)),t(p,{"label-width":"50"},{default:n(()=>[t(z,{modelValue:e(s).themeColorId,"onUpdate:modelValue":l[0]||(l[0]=r=>e(s).themeColorId=r),"theme-colors":e(v),onChange:F},null,8,["modelValue","theme-colors"])]),_:1})]),o("div",null,[l[14]||(l[14]=o("div",{class:"text-xl font-medium mt-[40px] mb-[20px]"},"样式设置",-1)),t(p,{label:"导航顶部文字颜色"},{default:n(()=>[o("div",null,[t(g,{modelValue:e(s).topTextColor,"onUpdate:modelValue":l[1]||(l[1]=r=>e(s).topTextColor=r)},{default:n(()=>[t(u,{value:"white",size:"large"},{default:n(()=>l[7]||(l[7]=[C("白色")])),_:1}),t(u,{value:"black",size:"large"},{default:n(()=>l[8]||(l[8]=[C("黑色")])),_:1})]),_:1},8,["modelValue"]),l[9]||(l[9]=o("div",null,[o("span",{class:"form-tips"},"页面导航栏文字的颜色")],-1))])]),_:1}),t(p,{label:"导航顶部背景颜色"},{default:n(()=>[o("div",null,[t(x,{resetColor:e(s).themeColor1,modelValue:e(s).navigationBarColor,"onUpdate:modelValue":l[2]||(l[2]=r=>e(s).navigationBarColor=r)},null,8,["resetColor","modelValue"]),l[10]||(l[10]=o("div",null,[o("span",{class:"form-tips"}," 页面顶部导航栏背景颜色，不设置则跟随主题色 ")],-1))])]),_:1}),e(s).themeColorId==7?(m(),c(p,{key:0,label:"自定义主题颜色"},{default:n(()=>[o("div",null,[t(x,{modelValue:e(s).themeColor1,"onUpdate:modelValue":l[3]||(l[3]=r=>e(s).themeColor1=r),"default-color":"#F8F8F8"},null,8,["modelValue"]),t(x,{modelValue:e(s).themeColor2,"onUpdate:modelValue":l[4]||(l[4]=r=>e(s).themeColor2=r),"default-color":"#F8F8F8",class:"mt-2"},null,8,["modelValue"]),l[11]||(l[11]=o("div",null,[o("span",{class:"form-tips"},"自定义主题渐变色，用于按钮类和主要文字")],-1))])]),_:1})):i("",!0),t(p,{label:"按钮文字颜色"},{default:n(()=>[o("div",null,[t(g,{modelValue:e(s).buttonColor,"onUpdate:modelValue":l[5]||(l[5]=r=>e(s).buttonColor=r)},{default:n(()=>[t(u,{value:"white",size:"large"},{default:n(()=>l[12]||(l[12]=[C("白色")])),_:1}),t(u,{value:"black",size:"large"},{default:n(()=>l[13]||(l[13]=[C("黑色")])),_:1})]),_:1},8,["modelValue"])])]),_:1})]),o("div",null,[e(s).themeColorId==1?(m(),d("div",j,l[15]||(l[15]=[o("img",{class:"w-[200px]",src:U},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:q},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:P},null,-1)]))):i("",!0),e(s).themeColorId==2?(m(),d("div",X,l[16]||(l[16]=[o("img",{class:"w-[200px]",src:O},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:G},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:L},null,-1)]))):i("",!0),e(s).themeColorId==3?(m(),d("div",h,l[17]||(l[17]=[o("img",{class:"w-[200px]",src:R},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:W},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:K},null,-1)]))):i("",!0),e(s).themeColorId==4?(m(),d("div",ll,l[18]||(l[18]=[o("img",{class:"w-[200px]",src:Q},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:Z},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:A},null,-1)]))):i("",!0),e(s).themeColorId==5?(m(),d("div",ol,l[19]||(l[19]=[o("img",{class:"w-[200px]",src:M},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:S},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:Y},null,-1)]))):i("",!0),e(s).themeColorId==6?(m(),d("div",sl,l[20]||(l[20]=[o("img",{class:"w-[200px]",src:H},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:J},null,-1),o("img",{class:"w-[200px] ml-[30px]",src:$},null,-1)]))):i("",!0)])]),_:1})}}});export{il as _};
