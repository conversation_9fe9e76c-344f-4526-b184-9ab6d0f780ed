import{d as _,s as c,i as D,c as B,j as F,o as I,a as N,m as a,w as n,e as l,b as f,E as U,L as P,J as S}from"./index-B2xNDy79.js";import{E as j,a as q}from"./el-form-item-DlU85AZK.js";import{d as A,e as J,f as L}from"./article-Dwgm3r-g.js";import{P as T}from"./index-DFOp_83R.js";const z={class:"edit-popup"},G=_({name:"articleColumnEdit"}),W=_({...G,emits:["success","close"],setup(H,{expose:v,emit:w}){const u=w,d=c(),i=c(),r=D("add"),V=B(()=>r.value=="edit"?"编辑栏目":"新增栏目"),o=F({id:"",name:"",sort:0,is_show:1}),b={name:[{required:!0,message:"请输入栏目名称",trigger:["blur"]}]},E=async()=>{var t,e;await((t=d.value)==null?void 0:t.validate()),r.value=="edit"?await A(o):await J(o),(e=i.value)==null||e.close(),u("success")},C=(t="add")=>{var e;r.value=t,(e=i.value)==null||e.open()},p=t=>{for(const e in o)t[e]!=null&&t[e]!=null&&(o[e]=t[e])},h=async t=>{const e=await L({id:t.id});p(e)},x=()=>{u("close")};return v({open:C,setFormData:p,getDetail:h}),(t,e)=>{const y=U,m=j,R=P,k=S,g=q;return I(),N("div",z,[a(T,{ref_key:"popupRef",ref:i,title:l(V),async:!0,width:"550px",onConfirm:E,onClose:x},{default:n(()=>[a(g,{ref_key:"formRef",ref:d,model:l(o),"label-width":"84px",rules:b},{default:n(()=>[a(m,{label:"栏目名称",prop:"name"},{default:n(()=>[a(y,{modelValue:l(o).name,"onUpdate:modelValue":e[0]||(e[0]=s=>l(o).name=s),placeholder:"请输入栏目名称",clearable:""},null,8,["modelValue"])]),_:1}),a(m,{label:"排序",prop:"sort"},{default:n(()=>[f("div",null,[a(R,{modelValue:l(o).sort,"onUpdate:modelValue":e[1]||(e[1]=s=>l(o).sort=s),min:0,max:9999},null,8,["modelValue"]),e[3]||(e[3]=f("div",{class:"form-tips"},"默认为0， 数值越大越排前",-1))])]),_:1}),a(m,{label:"状态",prop:"is_show"},{default:n(()=>[a(k,{modelValue:l(o).is_show,"onUpdate:modelValue":e[2]||(e[2]=s=>l(o).is_show=s),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{W as _};
