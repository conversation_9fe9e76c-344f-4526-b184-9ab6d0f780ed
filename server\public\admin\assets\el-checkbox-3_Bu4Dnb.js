import{a3 as Q,a4 as X,a5 as w,a6 as q,a7 as H,a8 as U,a9 as _,c as f,aa as P,ab as fe,ac as M,V as Z,ad as ee,H as ae,ae as le,i as T,af as F,ag as O,ah as ke,ai as W,aj as Y,ak as ne,al as D,d as L,am as te,an as R,o as C,C as oe,w as ue,b as J,Y as g,e as n,B as N,a as V,D as $,ao as z,ap as G,U as j,F as pe,p as se,t as ie,G as A,aq as re,ar as K,as as ge,at as Ce,au as xe,av as Ve,aw as ye,ax as Se,ay as Le,az as ce}from"./index-B2xNDy79.js";import{i as Be}from"./isEqual-CLGO95LP.js";const de={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},value:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueValue:{type:[String,Number],default:void 0},falseValue:{type:[String,Number],default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},border:Boolean,size:Q,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0},...X(["ariaControls"])},be={[w]:e=>q(e)||H(e)||U(e),change:e=>q(e)||H(e)||U(e)},B=Symbol("checkboxGroupContextKey"),Ee=({model:e,isChecked:c})=>{const i=_(B,void 0),l=f(()=>{var r,d;const t=(r=i==null?void 0:i.max)==null?void 0:r.value,v=(d=i==null?void 0:i.min)==null?void 0:d.value;return!P(t)&&e.value.length>=t&&!c.value||!P(v)&&e.value.length<=v&&c.value});return{isDisabled:fe(f(()=>(i==null?void 0:i.disabled.value)||l.value)),isLimitDisabled:l}},Ie=(e,{model:c,isLimitExceeded:i,hasOwnLabel:l,isDisabled:b,isLabeledByFormItem:r})=>{const d=_(B,void 0),{formItem:t}=M(),{emit:v}=le();function s(a){var k,p,u,h;return[!0,e.trueValue,e.trueLabel].includes(a)?(p=(k=e.trueValue)!=null?k:e.trueLabel)!=null?p:!0:(h=(u=e.falseValue)!=null?u:e.falseLabel)!=null?h:!1}function o(a,k){v("change",s(a),k)}function m(a){if(i.value)return;const k=a.target;v("change",s(k.checked),a)}async function x(a){i.value||!l.value&&!b.value&&r.value&&(a.composedPath().some(u=>u.tagName==="LABEL")||(c.value=s([!1,e.falseValue,e.falseLabel].includes(c.value)),await ae(),o(c.value,a)))}const y=f(()=>(d==null?void 0:d.validateEvent)||e.validateEvent);return Z(()=>e.modelValue,()=>{y.value&&(t==null||t.validate("change").catch(a=>ee()))}),{handleChange:m,onClickRoot:x}},Fe=e=>{const c=T(!1),{emit:i}=le(),l=_(B,void 0),b=f(()=>P(l)===!1),r=T(!1),d=f({get(){var t,v;return b.value?(t=l==null?void 0:l.modelValue)==null?void 0:t.value:(v=e.modelValue)!=null?v:c.value},set(t){var v,s;b.value&&F(t)?(r.value=((v=l==null?void 0:l.max)==null?void 0:v.value)!==void 0&&t.length>(l==null?void 0:l.max.value)&&t.length>d.value.length,r.value===!1&&((s=l==null?void 0:l.changeEvent)==null||s.call(l,t))):(i(w,t),c.value=t)}});return{model:d,isGroup:b,isLimitExceeded:r}},_e=(e,c,{model:i})=>{const l=_(B,void 0),b=T(!1),r=f(()=>O(e.value)?e.label:e.value),d=f(()=>{const o=i.value;return U(o)?o:F(o)?ke(r.value)?o.map(W).some(m=>Be(m,r.value)):o.map(W).includes(r.value):o!=null?o===e.trueValue||o===e.trueLabel:!!o}),t=Y(f(()=>{var o;return(o=l==null?void 0:l.size)==null?void 0:o.value}),{prop:!0}),v=Y(f(()=>{var o;return(o=l==null?void 0:l.size)==null?void 0:o.value})),s=f(()=>!!c.default||!O(r.value));return{checkboxButtonSize:t,isChecked:d,isFocused:b,checkboxSize:v,hasOwnLabel:s,actualValue:r}},ve=(e,c)=>{const{formItem:i}=M(),{model:l,isGroup:b,isLimitExceeded:r}=Fe(e),{isFocused:d,isChecked:t,checkboxButtonSize:v,checkboxSize:s,hasOwnLabel:o,actualValue:m}=_e(e,c,{model:l}),{isDisabled:x}=Ee({model:l,isChecked:t}),{inputId:y,isLabeledByFormItem:a}=ne(e,{formItemContext:i,disableIdGeneration:o,disableIdManagement:b}),{handleChange:k,onClickRoot:p}=Ie(e,{model:l,isLimitExceeded:r,hasOwnLabel:o,isDisabled:x,isLabeledByFormItem:a});return(()=>{function h(){var E,I;F(l.value)&&!l.value.includes(m.value)?l.value.push(m.value):l.value=(I=(E=e.trueValue)!=null?E:e.trueLabel)!=null?I:!0}e.checked&&h()})(),D({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},f(()=>b.value&&O(e.value))),D({from:"true-label",replacement:"true-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},f(()=>!!e.trueLabel)),D({from:"false-label",replacement:"false-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},f(()=>!!e.falseLabel)),{inputId:y,isLabeledByFormItem:a,isChecked:t,isDisabled:x,isFocused:d,checkboxButtonSize:v,checkboxSize:s,hasOwnLabel:o,model:l,actualValue:m,handleChange:k,onClickRoot:p}},Ne=L({name:"ElCheckbox"}),$e=L({...Ne,props:de,emits:be,setup(e){const c=e,i=te(),{inputId:l,isLabeledByFormItem:b,isChecked:r,isDisabled:d,isFocused:t,checkboxSize:v,hasOwnLabel:s,model:o,actualValue:m,handleChange:x,onClickRoot:y}=ve(c,i),a=R("checkbox"),k=f(()=>[a.b(),a.m(v.value),a.is("disabled",d.value),a.is("bordered",c.border),a.is("checked",r.value)]),p=f(()=>[a.e("input"),a.is("disabled",d.value),a.is("checked",r.value),a.is("indeterminate",c.indeterminate),a.is("focus",t.value)]);return(u,h)=>(C(),oe(re(!n(s)&&n(b)?"span":"label"),{class:g(n(k)),"aria-controls":u.indeterminate?u.ariaControls:null,onClick:n(y)},{default:ue(()=>{var E,I;return[J("span",{class:g(n(p))},[u.trueValue||u.falseValue||u.trueLabel||u.falseLabel?N((C(),V("input",{key:0,id:n(l),"onUpdate:modelValue":S=>$(o)?o.value=S:null,class:g(n(a).e("original")),type:"checkbox",indeterminate:u.indeterminate,name:u.name,tabindex:u.tabindex,disabled:n(d),"true-value":(E=u.trueValue)!=null?E:u.trueLabel,"false-value":(I=u.falseValue)!=null?I:u.falseLabel,onChange:n(x),onFocus:S=>t.value=!0,onBlur:S=>t.value=!1,onClick:z(()=>{},["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[G,n(o)]]):N((C(),V("input",{key:1,id:n(l),"onUpdate:modelValue":S=>$(o)?o.value=S:null,class:g(n(a).e("original")),type:"checkbox",indeterminate:u.indeterminate,disabled:n(d),value:n(m),name:u.name,tabindex:u.tabindex,onChange:n(x),onFocus:S=>t.value=!0,onBlur:S=>t.value=!1,onClick:z(()=>{},["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","disabled","value","name","tabindex","onChange","onFocus","onBlur","onClick"])),[[G,n(o)]]),J("span",{class:g(n(a).e("inner"))},null,2)],2),n(s)?(C(),V("span",{key:0,class:g(n(a).e("label"))},[j(u.$slots,"default"),u.$slots.default?A("v-if",!0):(C(),V(pe,{key:0},[se(ie(u.label),1)],64))],2)):A("v-if",!0)]}),_:3},8,["class","aria-controls","onClick"]))}});var ze=K($e,[["__file","checkbox.vue"]]);const Ge=L({name:"ElCheckboxButton"}),we=L({...Ge,props:de,emits:be,setup(e){const c=e,i=te(),{isFocused:l,isChecked:b,isDisabled:r,checkboxButtonSize:d,model:t,actualValue:v,handleChange:s}=ve(c,i),o=_(B,void 0),m=R("checkbox"),x=f(()=>{var a,k,p,u;const h=(k=(a=o==null?void 0:o.fill)==null?void 0:a.value)!=null?k:"";return{backgroundColor:h,borderColor:h,color:(u=(p=o==null?void 0:o.textColor)==null?void 0:p.value)!=null?u:"",boxShadow:h?`-1px 0 0 0 ${h}`:void 0}}),y=f(()=>[m.b("button"),m.bm("button",d.value),m.is("disabled",r.value),m.is("checked",b.value),m.is("focus",l.value)]);return(a,k)=>{var p,u;return C(),V("label",{class:g(n(y))},[a.trueValue||a.falseValue||a.trueLabel||a.falseLabel?N((C(),V("input",{key:0,"onUpdate:modelValue":h=>$(t)?t.value=h:null,class:g(n(m).be("button","original")),type:"checkbox",name:a.name,tabindex:a.tabindex,disabled:n(r),"true-value":(p=a.trueValue)!=null?p:a.trueLabel,"false-value":(u=a.falseValue)!=null?u:a.falseLabel,onChange:n(s),onFocus:h=>l.value=!0,onBlur:h=>l.value=!1,onClick:z(()=>{},["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[G,n(t)]]):N((C(),V("input",{key:1,"onUpdate:modelValue":h=>$(t)?t.value=h:null,class:g(n(m).be("button","original")),type:"checkbox",name:a.name,tabindex:a.tabindex,disabled:n(r),value:n(v),onChange:n(s),onFocus:h=>l.value=!0,onBlur:h=>l.value=!1,onClick:z(()=>{},["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","value","onChange","onFocus","onBlur","onClick"])),[[G,n(t)]]),a.$slots.default||a.label?(C(),V("span",{key:2,class:g(n(m).be("button","inner")),style:ge(n(b)?n(x):void 0)},[j(a.$slots,"default",{},()=>[se(ie(a.label),1)])],6)):A("v-if",!0)],2)}}});var me=K(we,[["__file","checkbox-button.vue"]]);const De=Ce({modelValue:{type:xe(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:Q,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0},...X(["ariaLabel"])}),Ue={[w]:e=>F(e),change:e=>F(e)},Pe=L({name:"ElCheckboxGroup"}),Te=L({...Pe,props:De,emits:Ue,setup(e,{emit:c}){const i=e,l=R("checkbox"),{formItem:b}=M(),{inputId:r,isLabeledByFormItem:d}=ne(i,{formItemContext:b}),t=async s=>{c(w,s),await ae(),c("change",s)},v=f({get(){return i.modelValue},set(s){t(s)}});return Ve(B,{...ye(Se(i),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:v,changeEvent:t}),Z(()=>i.modelValue,()=>{i.validateEvent&&(b==null||b.validate("change").catch(s=>ee()))}),(s,o)=>{var m;return C(),oe(re(s.tag),{id:n(r),class:g(n(l).b("group")),role:"group","aria-label":n(d)?void 0:s.ariaLabel||"checkbox-group","aria-labelledby":n(d)?(m=n(b))==null?void 0:m.labelId:void 0},{default:ue(()=>[j(s.$slots,"default")]),_:3},8,["id","class","aria-label","aria-labelledby"])}}});var he=K(Te,[["__file","checkbox-group.vue"]]);const Me=Le(ze,{CheckboxButton:me,CheckboxGroup:he});ce(me);const Re=ce(he);export{Me as E,Re as a};
