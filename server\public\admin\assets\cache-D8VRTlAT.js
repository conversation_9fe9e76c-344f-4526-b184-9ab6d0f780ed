import{d as l,i as m,o as _,a as p,m as e,w as t,e as f,p as u,I as h,f6 as w,v as b}from"./index-B2xNDy79.js";import{E,a as C}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as k}from"./el-card-DpH4mUSc.js";import{E as x}from"./el-alert-BUxHh72o.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./_initCloneObject-C-h6JGU9.js";const y={class:"cache"},v=l({name:"cache"}),F=l({...v,setup(g){const s=m([{content:"系统缓存",desc:"系统运行过程中产生的各类缓存数据"}]),r=async()=>{await h.confirm("确认清除系统缓存？"),await w(),window.location.reload()};return(B,o)=>{const c=x,n=k,a=E,i=b,d=C;return _(),p("div",y,[e(n,{class:"!border-none",shadow:"never"},{default:t(()=>[e(c,{type:"warning",title:"温馨提示：管理系统运行过程中产生的缓存",closable:!1,"show-icon":""})]),_:1}),e(n,{class:"!border-none mt-4",shadow:"never"},{default:t(()=>[e(d,{data:f(s),size:"large"},{default:t(()=>[e(a,{label:"管理内容",prop:"content","min-width":"130"}),e(a,{label:"内容说明",prop:"desc","min-width":"180"}),e(a,{label:"操作",width:"130",fixed:"right"},{default:t(()=>[e(i,{type:"primary",link:"",onClick:r},{default:t(()=>o[0]||(o[0]=[u("清除系统缓存")])),_:1})]),_:1})]),_:1},8,["data"])]),_:1})])}}});export{F as default};
