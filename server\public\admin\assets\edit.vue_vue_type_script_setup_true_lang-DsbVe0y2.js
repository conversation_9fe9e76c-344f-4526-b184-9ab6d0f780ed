import{d as k,s as c,i as R,c as C,j as F,o as T,a as U,m as t,w as s,e as a,p as _,E as q}from"./index-B2xNDy79.js";import{E as D,a as B}from"./el-form-item-DlU85AZK.js";/* empty css                       */import{E as I,a as N}from"./el-radio-CKcO4hVq.js";import{g as P,h}from"./dict-DX85lXc6.js";import{P as j}from"./index-DFOp_83R.js";const z={class:"edit-popup"},Q=k({__name:"edit",emits:["success","close"],setup(A,{expose:V,emit:y}){const d=y,i=c(),n=c(),p=R("add"),v=C(()=>p.value=="edit"?"编辑字典类型":"新增字典类型"),o=F({id:"",name:"",type:"",status:1,remark:""}),w={name:[{required:!0,message:"请输入字典名称",trigger:["blur"]}],type:[{required:!0,message:"请输入字典类型",trigger:["blur"]}]},b=async()=>{var l,e;await((l=i.value)==null?void 0:l.validate()),p.value=="edit"?await P(o):await h(o),(e=n.value)==null||e.close(),d("success")},x=()=>{d("close")};return V({open:(l="add")=>{var e;p.value=l,(e=n.value)==null||e.open()},setFormData:l=>{for(const e in o)l[e]!=null&&l[e]!=null&&(o[e]=l[e])}}),(l,e)=>{const u=q,m=D,f=I,E=N,g=B;return T(),U("div",z,[t(j,{ref_key:"popupRef",ref:n,title:a(v),async:!0,width:"550px",onConfirm:b,onClose:x},{default:s(()=>[t(g,{class:"ls-form",ref_key:"formRef",ref:i,rules:w,model:a(o),"label-width":"84px"},{default:s(()=>[t(m,{label:"字典名称",prop:"name"},{default:s(()=>[t(u,{modelValue:a(o).name,"onUpdate:modelValue":e[0]||(e[0]=r=>a(o).name=r),placeholder:"请输入字典名称",clearable:""},null,8,["modelValue"])]),_:1}),t(m,{label:"字典类型",prop:"type"},{default:s(()=>[t(u,{modelValue:a(o).type,"onUpdate:modelValue":e[1]||(e[1]=r=>a(o).type=r),placeholder:"请输入字典类型",clearable:""},null,8,["modelValue"])]),_:1}),t(m,{label:"字典状态",required:"",prop:"status"},{default:s(()=>[t(E,{modelValue:a(o).status,"onUpdate:modelValue":e[2]||(e[2]=r=>a(o).status=r)},{default:s(()=>[t(f,{value:1},{default:s(()=>e[4]||(e[4]=[_("正常")])),_:1}),t(f,{value:0},{default:s(()=>e[5]||(e[5]=[_("停用")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(m,{label:"备注",prop:"remark"},{default:s(()=>[t(u,{modelValue:a(o).remark,"onUpdate:modelValue":e[3]||(e[3]=r=>a(o).remark=r),type:"textarea",autosize:{minRows:4,maxRows:6},clearable:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title"])])}}});export{Q as _};
