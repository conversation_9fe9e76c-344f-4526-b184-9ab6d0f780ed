import{d as F,c as U,o as d,a as m,m as l,w as o,b as s,e as n,p as x,F as c,r as b}from"./index-B2xNDy79.js";import{E as g,a as B}from"./el-form-item-DlU85AZK.js";import{E as C}from"./el-card-DpH4mUSc.js";import"./el-tag-CuODyGk4.js";import{a as N,E as O}from"./el-select-BRdnbwTl.js";/* empty css                       */import{E as j,a as R}from"./el-radio-CKcO4hVq.js";import{_ as D}from"./add-nav.vue_vue_type_script_setup_true_lang-BlUx6EnP.js";const G={class:"flex-1 mt-4"},H=F({__name:"attr",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},emits:["update:content"],setup(v,{emit:V}){const E=V,y=v,a=U({get:()=>y.content,set:r=>{E("update:content",r)}});return(r,e)=>{const u=j,w=R,p=O,i=N,_=g,f=C,k=B;return d(),m("div",null,[l(k,{"label-width":"70px"},{default:o(()=>[l(f,{shadow:"never",class:"!border-none flex mt-2"},{default:o(()=>[e[6]||(e[6]=s("div",{class:"flex items-end mb-4"},[s("div",{class:"text-base text-[#101010] font-medium"},"展示样式")],-1)),l(w,{modelValue:n(a).style,"onUpdate:modelValue":e[0]||(e[0]=t=>n(a).style=t)},{default:o(()=>[l(u,{value:1},{default:o(()=>e[4]||(e[4]=[x("固定显示")])),_:1}),l(u,{value:2},{default:o(()=>e[5]||(e[5]=[x("分页滑动")])),_:1})]),_:1},8,["modelValue"]),l(_,{label:"每行数量",class:"mt-4"},{default:o(()=>[l(i,{modelValue:n(a).per_line,"onUpdate:modelValue":e[1]||(e[1]=t=>n(a).per_line=t),style:{width:"300px"}},{default:o(()=>[(d(),m(c,null,b(5,t=>l(p,{key:t,label:t+"个",value:t},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(_,{label:"显示行数"},{default:o(()=>[l(i,{modelValue:n(a).show_line,"onUpdate:modelValue":e[2]||(e[2]=t=>n(a).show_line=t),style:{width:"300px"}},{default:o(()=>[(d(),m(c,null,b(2,t=>l(p,{key:t,label:t+"行",value:t},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(f,{shadow:"never",class:"!border-none flex mt-2"},{default:o(()=>[e[7]||(e[7]=s("div",{class:"flex items-end"},[s("div",{class:"text-base text-[#101010] font-medium"},"菜单设置"),s("div",{class:"text-xs text-tx-secondary ml-2"},"建议图片尺寸：100px*100px")],-1)),s("div",G,[l(D,{modelValue:n(a).data,"onUpdate:modelValue":e[3]||(e[3]=t=>n(a).data=t)},null,8,["modelValue"])])]),_:1})]),_:1})])}}});export{H as _};
