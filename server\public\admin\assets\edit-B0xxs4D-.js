import{d as E,g as N,h as S,i as T,j as U,M as j,s as L,o as d,a as p,m as t,w as o,B as M,e as a,C as $,b as r,p as m,t as _,F as z,r as G,eW as H,I as K,E as O,v as P,K as W}from"./index-B2xNDy79.js";import{_ as h}from"./index-onOHNH0j.js";import{E as A,a as J}from"./el-form-item-DlU85AZK.js";/* empty css                       */import{E as Q,a as X}from"./el-radio-CKcO4hVq.js";import{E as Y}from"./el-card-DpH4mUSc.js";import{E as Z}from"./el-page-header-Bl6Tm5XX.js";import{n as ee,s as te}from"./message-BcHkSWHo.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";const oe={class:"w-80"},se={class:"flex-1"},ae={class:"w-full max-w-[320px]"},ne={class:"form-tips"},le=E({name:"noticeEdit"}),Ee=E({...le,setup(ie){const f=N(),w=S(),u=T(!1),s=U({id:"",scene_name:"",type:"",scene_desc:"",sms_notice:{status:0,template_id:"",content:"",tips:[]},oa_notice:{},mnp_notice:{},system_notice:{}}),y={"sms_notice.template_id":[{required:!0,message:"请输入模板ID",trigger:"blur"}],"sms_notice.content":[{required:!0,message:"请输入短信内容",trigger:"blur"}]},{removeTab:V}=j(),v=L(),k=async()=>{u.value=!0;const l=await ee({id:f.query.id});Object.keys(l).forEach(e=>{s[e]=l[e]}),u.value=!1},x=async()=>{var e;await((e=v.value)==null?void 0:e.validate());const l={id:s.id,template:H.pick(s,["sms_notice","oa_notice","mnp_notice","system_notice"])};await te(l),K.msgSuccess("操作成功"),V(),w.back()};return f.query.id&&k(),(l,e)=>{const R=Z,c=Y,i=A,b=Q,D=X,g=O,B=J,C=P,I=h,q=W;return d(),p("div",null,[t(c,{class:"!border-none",shadow:"never"},{default:o(()=>[t(R,{content:"编辑通知设置",onBack:e[0]||(e[0]=n=>l.$router.back())})]),_:1}),M((d(),$(B,{ref_key:"formRef",ref:v,model:a(s),"label-width":"120px",rules:y},{default:o(()=>[t(c,{class:"!border-none mt-4",shadow:"never"},{default:o(()=>[e[4]||(e[4]=r("div",{class:"font-medium mb-7"},"通知名称",-1)),t(i,{label:"通知名称"},{default:o(()=>[m(_(a(s).scene_name),1)]),_:1}),t(i,{label:"通知类型"},{default:o(()=>[m(_(a(s).type),1)]),_:1}),t(i,{label:"通知业务"},{default:o(()=>[m(_(a(s).scene_desc),1)]),_:1})]),_:1}),t(c,{class:"!border-none mt-4",shadow:"never"},{default:o(()=>[e[7]||(e[7]=r("div",{class:"font-medium mb-7"},"短信通知",-1)),t(i,{label:"开启状态",prop:"sms_notice.status",required:""},{default:o(()=>[t(D,{modelValue:a(s).sms_notice.status,"onUpdate:modelValue":e[1]||(e[1]=n=>a(s).sms_notice.status=n)},{default:o(()=>[t(b,{value:"0"},{default:o(()=>e[5]||(e[5]=[m("关闭")])),_:1}),t(b,{value:"1"},{default:o(()=>e[6]||(e[6]=[m("开启")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"模板ID",prop:"sms_notice.template_id"},{default:o(()=>[r("div",oe,[t(g,{modelValue:a(s).sms_notice.template_id,"onUpdate:modelValue":e[2]||(e[2]=n=>a(s).sms_notice.template_id=n),placeholder:"请输入模板ID"},null,8,["modelValue"])])]),_:1}),t(i,{label:"短信内容",prop:"sms_notice.content"},{default:o(()=>[r("div",se,[r("div",ae,[t(g,{type:"textarea",autosize:{minRows:6,maxRows:6},modelValue:a(s).sms_notice.content,"onUpdate:modelValue":e[3]||(e[3]=n=>a(s).sms_notice.content=n)},null,8,["modelValue"])]),r("div",ne,[(d(!0),p(z,null,G(a(s).sms_notice.tips,(n,F)=>(d(),p("div",{key:F},_(n),1))),128))])])]),_:1})]),_:1})]),_:1},8,["model"])),[[q,a(u)]]),t(I,null,{default:o(()=>[t(C,{type:"primary",onClick:x},{default:o(()=>e[8]||(e[8]=[m("保存")])),_:1})]),_:1})])}}});export{Ee as default};
