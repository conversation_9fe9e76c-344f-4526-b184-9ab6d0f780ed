import{d as r,x as d,o as s,a as n,U as c,ao as p,m as i,G as _,q as m}from"./index-B2xNDy79.js";const u=r({props:{showClose:{type:Boolean,default:!0}},emits:["close"],setup(e,{emit:o}){return{handleClose:()=>{o("close")}}}}),f={class:"del-wrap"};function C(e,o,a,h,$,k){const t=m;return s(),n("div",f,[c(e.$slots,"default",{},void 0,!0),e.showClose?(s(),n("div",{key:0,class:"icon-close",onClick:o[0]||(o[0]=p((...l)=>e.handleClose&&e.handleClose(...l),["stop"]))},[i(t,{size:12,name:"el-icon-CloseBold"})])):_("",!0)])}const w=d(u,[["render",C],["__scopeId","data-v-2a98aa67"]]);export{w as _};
