import{d as i,j as n,o as l,a as s,m as e,w as _,e as r}from"./index-B2xNDy79.js";import{E as u}from"./el-card-DpH4mUSc.js";import{_ as c}from"./picker-qQ9YEtJl.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./_initCloneObject-C-h6JGU9.js";import"./el-form-item-DlU85AZK.js";import"./_baseClone-CdezRMKA.js";import"./article-Dwgm3r-g.js";import"./usePaging-Dm2wALfy.js";/* empty css                       */import"./el-radio-CKcO4hVq.js";const H=i({__name:"link",setup(d){const o=n({value1:{}});return(f,t)=>{const m=c,p=u;return l(),s("div",null,[e(p,{header:"基础使用",shadow:"never",class:"!border-none"},{default:_(()=>[e(m,{modelValue:r(o).value1,"onUpdate:modelValue":t[0]||(t[0]=a=>r(o).value1=a)},null,8,["modelValue"])]),_:1})])}}});export{H as default};
