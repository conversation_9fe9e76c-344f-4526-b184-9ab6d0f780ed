import i from"./attr-Bqhk7AF3.js";import{_}from"./content.vue_vue_type_script_setup_true_lang-DchKkk1p.js";import{_ as s}from"./attr.vue_vue_type_script_setup_true_lang-BUnp-W91.js";import a from"./content-CA3eC6JD.js";import{_ as r}from"./attr.vue_vue_type_script_setup_true_lang-_e1mwglx.js";import{_ as l}from"./content.vue_vue_type_script_setup_true_lang-BnJOM-Yt.js";import{_ as c}from"./attr.vue_vue_type_script_setup_true_lang-D3n1kY8a.js";import m from"./content-D3wpKzNH.js";import{_ as d}from"./attr.vue_vue_type_script_setup_true_lang-Cv1wbPt-.js";import{_ as p}from"./content.vue_vue_type_script_setup_true_lang-CwpJZnDJ.js";import{_ as f}from"./attr.vue_vue_type_script_setup_true_lang-DyaVW45d.js";import b from"./content-CVqMLU2z.js";import{_ as g}from"./attr.vue_vue_type_script_setup_true_lang-bDu15G_R.js";import u from"./content-DRWnLeOw.js";import{_ as y}from"./content.vue_vue_type_script_setup_true_lang-B_7xmKd0.js";import{_ as $}from"./attr.vue_vue_type_script_setup_true_lang-DkUoKAk4.js";import v from"./content-CvSg00F8.js";import{_ as x}from"./attr.vue_vue_type_script_setup_true_lang-DD7J54WS.js";import{_ as j}from"./content.vue_vue_type_script_setup_true_lang-DXwxhIWi.js";import{_ as O}from"./attr.vue_vue_type_script_setup_true_lang-BfAYpuFH.js";import S from"./content-DdyiS-ri.js";const z=()=>({title:"首页轮播图",name:"banner",content:{enabled:1,style:1,bg_style:0,data:[{is_show:"1",image:"",bg:"",name:"",link:{}}]},styles:{}}),M={attr:i,content:_,options:z},P=Object.freeze(Object.defineProperty({__proto__:null,default:M},Symbol.toStringTag,{value:"Module"})),T=()=>({title:"客服设置",name:"customer-service",content:{title:"添加客服二维码",time:"",mobile:"",qrcode:"",remark:""},styles:{}}),w={attr:s,content:a,options:T},h=Object.freeze(Object.defineProperty({__proto__:null,default:w},Symbol.toStringTag,{value:"Module"})),k=()=>({title:"首页中部轮播图",name:"middle-banner",content:{enabled:1,data:[{is_show:"1",image:"",name:"",link:{}}]},styles:{}}),q={attr:r,content:l,options:k},E=Object.freeze(Object.defineProperty({__proto__:null,default:q},Symbol.toStringTag,{value:"Module"})),N=()=>({title:"我的服务",name:"my-service",content:{style:1,title:"我的服务",data:[{image:"",name:"导航名称",link:{}}]},styles:{}}),W={attr:c,content:m,options:N},A=Object.freeze(Object.defineProperty({__proto__:null,default:W},Symbol.toStringTag,{value:"Module"})),B=()=>({title:"导航菜单",name:"nav",content:{enabled:1,style:1,per_line:5,show_line:2,data:[{image:"",name:"导航名称",link:{}}]},styles:{}}),C={attr:d,content:p,options:B},D=Object.freeze(Object.defineProperty({__proto__:null,default:C},Symbol.toStringTag,{value:"Module"})),F=()=>({title:"资讯",name:"news",disabled:1,content:{},styles:{}}),G={attr:f,content:b,options:F},H=Object.freeze(Object.defineProperty({__proto__:null,default:G},Symbol.toStringTag,{value:"Module"})),I=()=>({title:"页面设置",name:"page-meta",content:{title_type:1,title:"",title_img:"",bg_type:1,bg_color:"",bg_image:"",text_color:""},styles:{}}),J={attr:g,content:u,options:I},K=Object.freeze(Object.defineProperty({__proto__:null,default:J},Symbol.toStringTag,{value:"Module"})),L=()=>({title:"首页轮播图",name:"pc-banner",content:{enabled:1,data:[{image:"",name:"",link:{}}]},styles:{}}),Q={content:y,options:L},R=Object.freeze(Object.defineProperty({__proto__:null,default:Q},Symbol.toStringTag,{value:"Module"})),U=()=>({title:"搜索",name:"search",disabled:1,content:{},styles:{}}),V={attr:$,content:v,options:U},X=Object.freeze(Object.defineProperty({__proto__:null,default:V},Symbol.toStringTag,{value:"Module"})),Y=()=>({title:"个人中心广告图",name:"user-banner",content:{enabled:1,data:[{is_show:"1",image:"",name:"",link:{}}]},styles:{}}),Z={attr:x,content:j,options:Y},tt=Object.freeze(Object.defineProperty({__proto__:null,default:Z},Symbol.toStringTag,{value:"Module"})),et=()=>({title:"用户信息",name:"user-info",disabled:1,content:{},styles:{}}),ot={attr:O,content:S,options:et},nt=Object.freeze(Object.defineProperty({__proto__:null,default:ot},Symbol.toStringTag,{value:"Module"})),o=Object.assign({"./banner/index.ts":P,"./customer-service/index.ts":h,"./middle-banner/index.ts":E,"./my-service/index.ts":A,"./nav/index.ts":D,"./news/index.ts":H,"./page-meta/index.ts":K,"./pc-banner/index.ts":R,"./search/index.ts":X,"./user-banner/index.ts":tt,"./user-info/index.ts":nt}),it={};Object.keys(o).forEach(t=>{var e;const n=t.replace(/^\.\/([\w-]+).*/gi,"$1");it[n]=(e=o[t])==null?void 0:e.default});export{it as e};
