import{_ as x}from"./index-onOHNH0j.js";import{d as k,i as c,dg as B,c as D,k as E,o as s,a as p,m as l,w as t,b as T,e as u,D as h,F as i,r as N,C as _,aq as P,p as U,a1 as F,a2 as I,v as J}from"./index-B2xNDy79.js";import{E as L}from"./el-card-DpH4mUSc.js";import{a as O,s as R}from"./decoration-C6Bzwzfj.js";import{_ as S}from"./mobile-style.vue_vue_type_script_setup_true_lang-DmH5UwEK.js";import"./el-form-item-DlU85AZK.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./index.vue_vue_type_script_setup_true_lang-C-xtr9xT.js";/* empty css                       */import"./el-radio-CKcO4hVq.js";import"./theme-picker-fL8txkcV.js";const q={class:"mt-[-10px]"},ea=k({__name:"style",setup(M){const o=c(0),r=c([{name:"移动端",id:5,component:B(S),data:{themeColorId:1,topTextColor:"white",navigationBarColor:"",themeColor1:"",themeColor2:"",buttonColor:"white"}}]),e=D(()=>r.value[o.value]||{}),m=async()=>{const d=await O({id:e.value.id});e.value.data=JSON.parse(d.data)},f=async()=>{await R({id:e.value.id,type:e.value.id,data:JSON.stringify(e.value.data)}),m()};return E(async()=>{await m()}),(d,n)=>{const b=F,v=I,C=L,y=J,V=x;return s(),p(i,null,[l(C,{shadow:"never",class:"!border-none"},{default:t(()=>[T("div",q,[l(v,{modelValue:u(o),"onUpdate:modelValue":n[0]||(n[0]=a=>h(o)?o.value=a:null)},{default:t(()=>[(s(!0),p(i,null,N(u(r),(a,g)=>(s(),_(b,{key:a.id,label:a.name,name:g},{default:t(()=>[(s(),_(P(a.component),{modelValue:a.data,"onUpdate:modelValue":w=>a.data=w},null,8,["modelValue","onUpdate:modelValue"]))]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])]),_:1}),l(V,{class:"mt-4",fixed:!0},{default:t(()=>[l(y,{type:"primary",onClick:f},{default:t(()=>n[1]||(n[1]=[U("保存")])),_:1})]),_:1})],64)}}});export{ea as default};
