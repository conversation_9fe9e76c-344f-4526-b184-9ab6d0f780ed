import{d as u,o as n,a as g,m as c,w as r,b as y,t as x,C as i,cp as _,aq as w,e as h,bJ as C}from"./index-B2xNDy79.js";import{E as v}from"./el-card-DpH4mUSc.js";import{e as k}from"./index-JqFibg1v.js";const B={class:"pages-setting"},E={class:"title flex items-center before:w-[3px] before:h-[14px] before:block before:bg-primary before:mr-2 text-xl font-medium"},V=u({__name:"attr-setting",props:{widget:{type:Object,default:()=>({})},type:{type:String,default:"mobile"}},emits:["update:content"],setup(e,{emit:d}){const m=d,p=o=>{m("update:content",o)};return(o,S)=>{const f=v,b=C;return n(),g("div",B,[c(f,{shadow:"never",class:"!border-none flex"},{default:r(()=>{var t;return[y("div",E,x((t=e.widget)==null?void 0:t.title),1)]}),_:1}),c(b,{class:"w-full",style:{height:"calc(100% - 60px)"}},{default:r(()=>{var t,a,s,l;return[(n(),i(_,null,[(n(),i(w((a=h(k)[(t=e.widget)==null?void 0:t.name])==null?void 0:a.attr),{content:(s=e.widget)==null?void 0:s.content,styles:(l=e.widget)==null?void 0:l.styles,type:e.type,"onUpdate:content":p},null,40,["content","styles","type"]))],1024))]}),_:1})])}}});export{V as _};
