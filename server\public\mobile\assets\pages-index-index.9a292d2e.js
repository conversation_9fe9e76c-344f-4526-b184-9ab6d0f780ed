import{d as t,c as e,r as a,a as n,o as r,b as o,w as l,e as i,n as s,f as c,i as u,g as d,F as p,h as m,j as f,k as g,l as y,t as h,m as b,p as v,S as _,q as x,u as k,s as C,v as S,x as w,y as j,z as P,A as I,B as O,C as L,D as N,E as R,G as B,H as T,I as $,J as M,K as z}from"./index-561dd99e.js";import{_ as F}from"./page-meta.438f2c32.js";import{_ as A}from"./u-search.1f3b26b2.js";import{_ as U}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as Z}from"./u-image.e9ed38ca.js";import{_ as J}from"./news-card.1749442f.js";import{_ as q}from"./router-navigate.3c22c13e.js";import{_ as Y}from"./u-icon.f1b72599.js";import{_ as D}from"./tabbar.vue_vue_type_script_setup_true_lang.b3a4c4a3.js";import"./icon_visit.713e13e8.js";import"./u-badge.45c73cfd.js";const E=U(t({__name:"search",props:{pageMeta:{type:Object,default:()=>[]},content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})},isLargeScreen:{type:Boolean},percent:{type:Number}},setup(t){const d=t;return e((()=>d.pageMeta[0].content)),(e,d)=>{const p=a(n("u-search"),A),m=u;return t.isLargeScreen?c("v-if",!0):(r(),o(m,{key:0,url:"/pages/search/search",class:"px-[24rpx] mt-[24rpx] mb-[30rpx]",style:s({opacity:1-t.percent}),"hover-class":"none"},{default:l((()=>[i(p,{placeholder:"请输入关键词搜索",height:72,disabled:!0,"show-action":!1,bgColor:"#ffffff"})])),_:1},8,["style"]))}}}),[["__scopeId","data-v-1932de42"]]);const G=U({name:"u-swiper",emits:["click","change"],props:{list:{type:Array,default:()=>[]},title:{type:Boolean,default:!1},indicator:{type:Object,default:()=>({})},borderRadius:{type:[Number,String],default:8},interval:{type:[String,Number],default:3e3},mode:{type:String,default:"round"},height:{type:[Number,String],default:250},indicatorPos:{type:String,default:"bottomCenter"},effect3d:{type:Boolean,default:!1},effect3dPreviousMargin:{type:[Number,String],default:50},autoplay:{type:Boolean,default:!0},duration:{type:[Number,String],default:500},circular:{type:Boolean,default:!0},imgMode:{type:String,default:"aspectFill"},name:{type:String,default:"image"},bgColor:{type:String,default:"#f3f4f6"},current:{type:[Number,String],default:0},titleStyle:{type:Object,default:()=>({})}},watch:{list(t,e){t.length!==e.length&&(this.uCurrent=0)},current(t){this.uCurrent=t}},data(){return{uCurrent:this.current}},computed:{justifyContent(){return"topLeft"==this.indicatorPos||"bottomLeft"==this.indicatorPos?"flex-start":"topCenter"==this.indicatorPos||"bottomCenter"==this.indicatorPos?"center":"topRight"==this.indicatorPos||"bottomRight"==this.indicatorPos?"flex-end":void 0},titlePaddingBottom(){let t=0;return"none"==this.mode?"12rpx":(t=["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"==this.mode?"60rpx":["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"!=this.mode?"40rpx":"12rpx",t)},elCurrent(){return Number(this.current)}},methods:{listClick(t){this.$emit("click",t)},change(t){let e=t.detail.current;this.uCurrent=e,this.$emit("change",e)},animationfinish(t){}}},[["render",function(t,e,a,n,u,k){const C=b,S=v,w=_,j=x;return r(),o(S,{class:"u-swiper-wrap",style:s({borderRadius:`${a.borderRadius}rpx`})},{default:l((()=>[i(j,{current:k.elCurrent,onChange:k.change,onAnimationfinish:k.animationfinish,interval:a.interval,circular:a.circular,duration:a.duration,autoplay:a.autoplay,"previous-margin":a.effect3d?a.effect3dPreviousMargin+"rpx":"0","next-margin":a.effect3d?a.effect3dPreviousMargin+"rpx":"0",style:s({height:a.height+"rpx",backgroundColor:a.bgColor})},{default:l((()=>[(r(!0),d(p,null,m(a.list,((t,e)=>(r(),o(w,{class:"u-swiper-item",key:e},{default:l((()=>[i(S,{class:f(["u-list-image-wrap",[u.uCurrent!=e?"u-list-scale":""]]),onClick:g((t=>k.listClick(e)),["stop","prevent"]),style:s({borderRadius:`${a.borderRadius}rpx`,transform:a.effect3d&&u.uCurrent!=e?"scaleY(0.9)":"scaleY(1)",margin:a.effect3d&&u.uCurrent!=e?"0 20rpx":0})},{default:l((()=>[i(C,{class:"u-swiper-image",src:t[a.name]||t,mode:a.imgMode},null,8,["src","mode"]),a.title&&t.title?(r(),o(S,{key:0,class:"u-swiper-title u-line-1",style:s([{"padding-bottom":k.titlePaddingBottom},a.titleStyle])},{default:l((()=>[y(h(t.title),1)])),_:2},1032,["style"])):c("v-if",!0)])),_:2},1032,["onClick","class","style"])])),_:2},1024)))),128))])),_:1},8,["current","onChange","onAnimationfinish","interval","circular","duration","autoplay","previous-margin","next-margin","style"]),i(S,{class:"u-swiper-indicator",style:s({top:"topLeft"==a.indicatorPos||"topCenter"==a.indicatorPos||"topRight"==a.indicatorPos?"12rpx":"auto",bottom:"bottomLeft"==a.indicatorPos||"bottomCenter"==a.indicatorPos||"bottomRight"==a.indicatorPos?"12rpx":"auto",justifyContent:k.justifyContent,padding:"0 "+(a.effect3d?"74rpx":"24rpx")})},{default:l((()=>["rect"==a.mode?(r(!0),d(p,{key:0},m(a.list,((t,e)=>(r(),o(S,{class:f(["u-indicator-item-rect",{"u-indicator-item-rect-active":e==u.uCurrent}]),key:e},null,8,["class"])))),128)):c("v-if",!0),"dot"==a.mode?(r(!0),d(p,{key:1},m(a.list,((t,e)=>(r(),o(S,{class:f(["u-indicator-item-dot",{"u-indicator-item-dot-active":e==u.uCurrent}]),key:e},null,8,["class"])))),128)):c("v-if",!0),"round"==a.mode?(r(!0),d(p,{key:2},m(a.list,((t,e)=>(r(),o(S,{class:f(["u-indicator-item-round",{"u-indicator-item-round-active":e==u.uCurrent}]),key:e},null,8,["class"])))),128)):c("v-if",!0),"number"==a.mode?(r(),o(S,{key:3,class:"u-indicator-item-number"},{default:l((()=>[y(h(u.uCurrent+1)+"/"+h(a.list.length),1)])),_:1})):c("v-if",!0)])),_:1},8,["style"])])),_:1},8,["style"])}],["__scopeId","data-v-90956b03"]]),H=t({__name:"l-swiper",props:{content:{default:{data:[]}},mode:{default:"round"},height:{default:"340"},indicatorPos:{default:"bottomCenter"},effect3d:{type:Boolean,default:!1},autoplay:{type:Boolean,default:!0},interval:{default:"2500"},duration:{default:300},circular:{type:Boolean,default:!0},current:{default:0},name:{default:"image"},borderRadius:{default:"0"},bgColor:{default:"#f3f4f6"}},emits:["change"],setup(t,{emit:l}){const i=t,{getImageUrl:s}=k();C((()=>{var t;try{const e=null==i?void 0:i.content,a=null==(t=null==e?void 0:e.data)?void 0:t.length;if(!a)return;for(let t=0;t<a;t++){const a=e.data[t];a.image=s(a.image)}l("change",0)}catch(e){console.log("轮播图数据错误",e)}}));const u=e((()=>i.content.data||[]));S();const d=t=>{var e;const a=null==(e=i.content.data[t])?void 0:e.link;a&&j(a)},p=t=>{l("change",t)};return(e,l)=>{const i=a(n("u-swiper"),G);return w(u).length?(r(),o(i,{key:0,list:w(u),mode:t.mode,height:t.height,effect3d:t.effect3d,"indicator-pos":t.indicatorPos,autoplay:t.autoplay,interval:t.interval,duration:t.duration,circular:t.circular,borderRadius:t.borderRadius,current:t.current,name:t.name,"bg-color":t.bgColor,onClick:d,onChange:p},null,8,["list","mode","height","effect3d","indicator-pos","autoplay","interval","duration","circular","borderRadius","current","name","bg-color"])):c("v-if",!0)}}}),K=t({__name:"banner",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})},isLargeScreen:{type:Boolean}},emits:["change"],setup(t,{emit:e}){const a=t,{getImageUrl:n}=k(),s=t=>{e("change",n(a.content.data[t].bg))};return(e,a)=>{const n=v;return t.content.data.length&&t.content.enabled?(r(),o(n,{key:0,class:f(["banner translate-y-0",{"px-[20rpx]":!t.isLargeScreen}])},{default:l((()=>[i(H,{content:t.content,height:t.isLargeScreen?"1100":"321",circular:!0,effect3d:!1,"border-radius":t.isLargeScreen?"0":"14",interval:"7000",bgColor:"transparent",onChange:s},null,8,["content","height","border-radius"])])),_:1},8,["class"])):c("v-if",!0)}}}),Q=t({__name:"nav",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(t){const u=t,{getImageUrl:f}=k(),g=P(0),b=P([]),C=e((()=>u.content.per_line*u.content.show_line));I((()=>u.content.data),(t=>{const e=1===u.content.style?t.length:C.value;b.value=O(t,e),console.log(b.value)}),{deep:!0,immediate:!0});const S=t=>{g.value=t.detail.current};return(e,u)=>{const g=a(n("u-image"),Z),k=v,C=_,P=x;return r(),d("div",{class:"relative mx-[20rpx] mt-[20rpx]"},[i(P,{class:"py-[20rpx] bg-white rounded-lg",style:s({height:b.value[0].length>t.content.per_line?"288rpx":"132rpx"}),autoplay:!1,"indicator-dots":!1,onChange:S},{default:l((()=>[(r(!0),d(p,null,m(b.value,((e,a)=>(r(),o(C,{key:a},{default:l((()=>[b.value.length&&t.content.enabled?(r(),o(k,{key:0,class:"nav"},{default:l((()=>[i(k,{class:"grid grid-rows-auto gap-y-3 w-full",style:s({"grid-template-columns":`repeat(${t.content.per_line}, 1fr)`})},{default:l((()=>[(r(!0),d(p,null,m(e,((t,e)=>(r(),o(k,{key:e,class:"flex flex-col items-center",onClick:e=>{return a=t.link,void j(a);var a}},{default:l((()=>[i(g,{width:"82",height:"82",src:w(f)(t.image),alt:""},null,8,["src"]),i(k,{class:"mt-[14rpx] text-xs"},{default:l((()=>[y(h(t.name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:2},1032,["style"])])),_:2},1024)):c("v-if",!0)])),_:2},1024)))),128))])),_:1},8,["style"])])}}}),V=t({__name:"middle-banner",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(t){const s=t,{getImageUrl:u}=k(),f=e((()=>{var t;return(null==(t=s.content.data)?void 0:t.filter((t=>"1"==t.is_show)))||[]}));return(e,s)=>{const g=a(n("u-image"),Z),y=_,h=x,b=v;return w(f).length&&t.content.enabled?(r(),o(b,{key:0,class:"banner h-[200rpx] mx-[20rpx] mt-[20rpx] translate-y-0"},{default:l((()=>[i(h,{class:"swiper h-full","indicator-dots":w(f).length>1,"indicator-active-color":"#4173ff",autoplay:!0},{default:l((()=>[(r(!0),d(p,null,m(w(f),((t,e)=>(r(),o(y,{key:e,onClick:e=>{return a=t.link,void j(a);var a}},{default:l((()=>[i(g,{mode:"widthFix",width:"100%",height:"100%",src:w(u)(t.image),"border-radius":14},null,8,["src"])])),_:2},1032,["onClick"])))),128))])),_:1},8,["indicator-dots"])])),_:1})):c("v-if",!0)}}});const W=U({name:"u-back-top",props:{mode:{type:String,default:"circle"},icon:{type:String,default:"arrow-upward"},tips:{type:String,default:""},duration:{type:[Number,String],default:100},scrollTop:{type:[Number,String],default:0},top:{type:[Number,String],default:400},bottom:{type:[Number,String],default:200},right:{type:[Number,String],default:40},zIndex:{type:[Number,String],default:"9"},iconStyle:{type:Object,default:()=>({color:"#909399",fontSize:"38rpx"})},customStyle:{type:Object,default:()=>({})}},watch:{showBackTop(t,e){t?(this.uZIndex=this.zIndex,this.opacity=1):(this.uZIndex=-1,this.opacity=0)}},computed:{showBackTop(){return this.scrollTop>L(this.top)}},data:()=>({opacity:0,uZIndex:-1}),methods:{backToTop(){N({scrollTop:0,duration:this.duration})}}},[["render",function(t,e,c,u,d,p){const m=a(n("u-icon"),Y),g=v;return r(),o(g,{onClick:p.backToTop,class:f(["u-back-top",["u-back-top--mode--"+c.mode]]),style:s([{bottom:c.bottom+"rpx",right:c.right+"rpx",borderRadius:"circle"==c.mode?"10000rpx":"8rpx",zIndex:d.uZIndex,opacity:d.opacity},c.customStyle])},{default:l((()=>[t.$slots.default||t.$slots.$default?R(t.$slots,"default",{key:1},void 0,!0):(r(),o(g,{key:0,class:"u-back-top__content"},{default:l((()=>[i(m,{onClick:p.backToTop,name:c.icon,"custom-style":c.iconStyle},null,8,["onClick","name","custom-style"]),i(g,{class:"u-back-top__content__tips"},{default:l((()=>[y(h(c.tips),1)])),_:1})])),_:1}))])),_:3},8,["onClick","class","style"])}],["__scopeId","data-v-ec93567e"]]),X=U(t({__name:"index",setup(t){const u=k(),f=B({pages:[],meta:[],article:[],bannerImage:""}),g=P(0),b=P(0),_=e((()=>{var t;return 1===(null==(t=f.pages.find((t=>"banner"===t.name)))?void 0:t.content.bg_style)})),x=e((()=>{var t;return 2===(null==(t=f.pages.find((t=>"banner"===t.name)))?void 0:t.content.style)})),C=e((()=>{var t;const{bg_type:e,bg_color:a,bg_image:n}=(null==(t=f.meta[0])?void 0:t.content)??{};return _.value?{"background-image":`url(${f.bannerImage})`}:1==e?{"background-color":a}:{"background-image":`url(${n})`}})),S=t=>{f.bannerImage=t};return T((t=>{g.value=t.scrollTop;const e=L(100);b.value=t.scrollTop/e>1?1:t.scrollTop/e})),$((()=>{(async()=>{var t,e;const a=await M();f.pages=JSON.parse(null==(t=null==a?void 0:a.page)?void 0:t.data),f.meta=JSON.parse(null==(e=null==a?void 0:a.page)?void 0:e.meta),f.article=a.article,z({title:f.meta[0].content.title})})()})),(t,e)=>{const _=a(n("page-meta"),F),k=a(n("w-search"),E),j=a(n("w-banner"),K),P=a(n("w-nav"),Q),I=a(n("w-middle-banner"),V),O=v,L=a(n("news-card"),J),N=a(n("router-navigate"),q),R=a(n("u-back-top"),W),B=a(n("tabbar"),D);return r(),d(p,null,[i(_,{"page-style":t.$theme.pageStyle},null,8,["page-style"]),i(O,{class:"index",style:s(w(C))},{default:l((()=>[c(" 组件 "),(r(!0),d(p,null,m(f.pages,((t,e)=>(r(),d(p,{key:e},["search"==t.name?(r(),o(k,{key:0,pageMeta:f.meta,content:t.content,styles:t.styles,percent:b.value,isLargeScreen:w(x)},null,8,["pageMeta","content","styles","percent","isLargeScreen"])):c("v-if",!0),"banner"==t.name?(r(),o(j,{key:1,content:t.content,styles:t.styles,isLargeScreen:w(x),onChange:S},null,8,["content","styles","isLargeScreen"])):c("v-if",!0),"nav"==t.name?(r(),o(P,{key:2,content:t.content,styles:t.styles},null,8,["content","styles"])):c("v-if",!0),"middle-banner"==t.name?(r(),o(I,{key:3,content:t.content,styles:t.styles},null,8,["content","styles"])):c("v-if",!0)],64)))),128)),f.article.length?(r(),o(O,{key:0,class:"article"},{default:l((()=>[i(O,{class:"flex items-center article-title mx-[20rpx] my-[30rpx] text-lg font-medium"},{default:l((()=>[y(" 最新资讯 ")])),_:1}),(r(!0),d(p,null,m(f.article,(t=>(r(),o(L,{key:t.id,"news-id":t.id,item:t},null,8,["news-id","item"])))),128))])),_:1})):c("v-if",!0),i(O,{class:"text-center py-4 mb-12"},{default:l((()=>[(r(!0),d(p,null,m(w(u).getCopyrightConfig,(t=>(r(),o(N,{class:"mx-1 text-xs text-[#495770]",to:{path:"/pages/webview/webview",query:{url:t.value}},key:t.key},{default:l((()=>[y(h(t.key),1)])),_:2},1032,["to"])))),128))])),_:1}),c(" 返回顶部按钮 "),i(R,{"scroll-top":g.value,top:100,customStyle:{backgroundColor:"#FFF",color:"#000",boxShadow:"0px 3px 6px rgba(0, 0, 0, 0.1)"}},null,8,["scroll-top","customStyle"]),i(B)])),_:1},8,["style"])],64)}}}),[["__scopeId","data-v-c69df4d9"]]);export{X as default};
