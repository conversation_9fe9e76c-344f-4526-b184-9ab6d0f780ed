import{_ as j}from"./index-onOHNH0j.js";import{d as v,g as z,h as I,j as V,M as L,s as O,o as u,a as w,m as t,w as r,e as a,b as s,F as T,r as A,C as M,p,E as S,L as G,v as H}from"./index-B2xNDy79.js";import{E as P,a as J}from"./el-form-item-DlU85AZK.js";import{_ as K}from"./index.vue_vue_type_style_index_0_lang-Dw4fui56.js";/* empty css                       */import{E as Q,a as W}from"./el-radio-CKcO4hVq.js";import{_ as X}from"./picker-Cd5l2hZ5.js";import"./el-tag-CuODyGk4.js";import{E as Y,a as Z}from"./el-select-BRdnbwTl.js";import{E as ee}from"./el-card-DpH4mUSc.js";import{E as te}from"./el-page-header-Bl6Tm5XX.js";import{g as le,h as oe,i as ae,j as re}from"./article-Dwgm3r-g.js";import{u as se}from"./useDictOptions-D0QsC3Dl.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./isEqual-CLGO95LP.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./index-BuNto3DN.js";import"./index-DSiy6YVt.js";import"./el-tree-8o9N7gsQ.js";import"./token-DI9FKtlJ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";import"./index-CcX0CyWL.js";const ie={class:"article-edit"},ne={class:"xl:flex"},de={class:"w-80"},me={class:"w-80"},ue={class:"w-80"},pe={class:"w-80"},_e={class:"xl:ml-20"},ce=v({name:"articleListsEdit"}),Je=v({...ce,setup(fe){const m=z(),g=I(),l=V({id:"",title:"",image:"",cid:"",desc:"",author:"",content:"",click_virtual:0,sort:0,is_show:1,abstract:""}),{removeTab:E}=L(),_=O(),x=V({title:[{required:!0,message:"请输入文章标题",trigger:"blur"}],cid:[{required:!0,message:"请选择文章栏目",trigger:"blur"}]}),k=async()=>{const n=await le({id:m.query.id});Object.keys(l).forEach(e=>{l[e]=n[e]})},{optionsData:R}=se({article_cate:{api:oe}}),y=async()=>{var n;await((n=_.value)==null?void 0:n.validate()),m.query.id?await ae(l):await re(l),E(),g.back()};return m.query.id&&k(),(n,e)=>{const U=te,c=ee,d=S,i=P,h=Y,q=Z,B=X,f=G,b=Q,C=W,D=K,F=J,N=H,$=j;return u(),w("div",ie,[t(c,{class:"!border-none",shadow:"never"},{default:r(()=>[t(U,{content:n.$route.meta.title,onBack:e[0]||(e[0]=o=>n.$router.back())},null,8,["content"])]),_:1}),t(c,{class:"mt-4 !border-none",shadow:"never"},{default:r(()=>[t(F,{ref_key:"formRef",ref:_,class:"ls-form",model:a(l),"label-width":"85px",rules:a(x)},{default:r(()=>[s("div",ne,[s("div",null,[t(i,{label:"文章标题",prop:"title"},{default:r(()=>[s("div",de,[t(d,{modelValue:a(l).title,"onUpdate:modelValue":e[1]||(e[1]=o=>a(l).title=o),placeholder:"请输入文章标题",type:"textarea",autosize:{minRows:3,maxRows:3},maxlength:"64","show-word-limit":"",clearable:""},null,8,["modelValue"])])]),_:1}),t(i,{label:"文章栏目",prop:"cid"},{default:r(()=>[t(q,{class:"w-80",modelValue:a(l).cid,"onUpdate:modelValue":e[2]||(e[2]=o=>a(l).cid=o),placeholder:"请选择文章栏目",clearable:""},{default:r(()=>[(u(!0),w(T,null,A(a(R).article_cate,o=>(u(),M(h,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"文章简介",prop:"desc"},{default:r(()=>[s("div",me,[t(d,{modelValue:a(l).desc,"onUpdate:modelValue":e[3]||(e[3]=o=>a(l).desc=o),placeholder:"请输入文章简介",type:"textarea",autosize:{minRows:3,maxRows:6},maxlength:200,"show-word-limit":"",clearable:""},null,8,["modelValue"])])]),_:1}),t(i,{label:"摘要",prop:"abstract"},{default:r(()=>[s("div",ue,[t(d,{type:"textarea",autosize:{minRows:6,maxRows:6},modelValue:a(l).abstract,"onUpdate:modelValue":e[4]||(e[4]=o=>a(l).abstract=o),maxlength:"200","show-word-limit":"",clearable:""},null,8,["modelValue"])])]),_:1}),t(i,{label:"文章封面",prop:"image"},{default:r(()=>[s("div",null,[s("div",null,[t(B,{modelValue:a(l).image,"onUpdate:modelValue":e[5]||(e[5]=o=>a(l).image=o),limit:1},null,8,["modelValue"])]),e[11]||(e[11]=s("div",{class:"form-tips"},"建议尺寸：240*180px",-1))])]),_:1}),t(i,{label:"作者",prop:"author"},{default:r(()=>[s("div",pe,[t(d,{modelValue:a(l).author,"onUpdate:modelValue":e[6]||(e[6]=o=>a(l).author=o),placeholder:"请输入作者名称"},null,8,["modelValue"])])]),_:1}),t(i,{label:"排序",prop:"sort"},{default:r(()=>[s("div",null,[t(f,{modelValue:a(l).sort,"onUpdate:modelValue":e[7]||(e[7]=o=>a(l).sort=o),min:0,max:9999},null,8,["modelValue"]),e[12]||(e[12]=s("div",{class:"form-tips"},"默认为0， 数值越大越排前",-1))])]),_:1}),t(i,{label:"初始浏览量",prop:"click_virtual"},{default:r(()=>[s("div",null,[t(f,{modelValue:a(l).click_virtual,"onUpdate:modelValue":e[8]||(e[8]=o=>a(l).click_virtual=o),min:0},null,8,["modelValue"])])]),_:1}),t(i,{label:"文章状态",required:"",prop:"is_show"},{default:r(()=>[t(C,{modelValue:a(l).is_show,"onUpdate:modelValue":e[9]||(e[9]=o=>a(l).is_show=o)},{default:r(()=>[t(b,{value:1},{default:r(()=>e[13]||(e[13]=[p("显示")])),_:1}),t(b,{value:0},{default:r(()=>e[14]||(e[14]=[p("隐藏")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),s("div",_e,[t(i,{label:"文章内容",prop:"content"},{default:r(()=>[t(D,{modelValue:a(l).content,"onUpdate:modelValue":e[10]||(e[10]=o=>a(l).content=o),height:667,width:375},null,8,["modelValue"])]),_:1})])])]),_:1},8,["model","rules"])]),_:1}),t($,null,{default:r(()=>[t(N,{type:"primary",onClick:y},{default:r(()=>e[15]||(e[15]=[p("保存")])),_:1})]),_:1})])}}});export{Je as default};
