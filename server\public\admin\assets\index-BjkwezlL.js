import{d as w,O as B,z as T,o as l,a as D,m as t,w as a,B as m,C as s,e as i,Q as g,p as r,G as f,b as k,D as N,I as L,fd as P,q as $,v as q,K as R,fe as j}from"./index-B2xNDy79.js";import{E as z}from"./el-card-DpH4mUSc.js";import{_ as F}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as G,a as I}from"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{u as K}from"./usePaging-Dm2wALfy.js";import{E as O}from"./index-CcX0CyWL.js";import"./el-select-BRdnbwTl.js";import"./token-DI9FKtlJ.js";import"./isEqual-CLGO95LP.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";const Q={class:"flex"},U={class:"flex justify-end mt-4"},A=w({name:"scheduledTask"}),it=w({...A,setup(H){const{pager:d,getLists:c}=K({fetchFun:j,params:{}}),v=async b=>{await L.confirm("确定要删除？"),await P({id:b}),c()};return c(),(b,e)=>{const h=$,p=q,y=B("router-link"),o=G,u=O,C=I,x=F,E=z,_=T("perms"),V=R;return l(),D("div",null,[t(E,{shadow:"never",class:"!border-none"},{default:a(()=>[m((l(),s(y,{to:i(g)("crontab.crontab/add:edit")},{default:a(()=>[t(p,{type:"primary",class:"mb-[16px]"},{icon:a(()=>[t(h,{name:"el-icon-Plus"})]),default:a(()=>[e[1]||(e[1]=r(" 新增 "))]),_:1})]),_:1},8,["to"])),[[_,["crontab.crontab/add","crontab.crontab/add:edit"]]]),m((l(),s(C,{ref:"paneTable",class:"m-t-24",data:i(d).lists,style:{width:"100%"}},{default:a(()=>[t(o,{prop:"name",label:"名称","min-width":"120"}),t(o,{prop:"type_desc",label:"类型","min-width":"100"}),t(o,{prop:"command",label:"命令","min-width":"100"}),t(o,{prop:"params",label:"参数","min-width":"80"}),t(o,{prop:"expression",label:"规则","min-width":"100"}),t(o,{prop:"status",label:"状态","min-width":"100"},{default:a(({row:n})=>[n.status==1?(l(),s(u,{key:0,type:"success"},{default:a(()=>e[2]||(e[2]=[r("运行中")])),_:1})):f("",!0),n.status==2?(l(),s(u,{key:1,type:"info"},{default:a(()=>e[3]||(e[3]=[r("已停止")])),_:1})):f("",!0),n.status==3?(l(),s(u,{key:2,type:"danger"},{default:a(()=>e[4]||(e[4]=[r("错误")])),_:1})):f("",!0)]),_:1}),t(o,{prop:"error",label:"错误原因","min-width":"120"}),t(o,{prop:"last_time",label:"最后执行时间",width:"180"}),t(o,{prop:"time",label:"时长","min-width":"100"}),t(o,{prop:"max_time",label:"最大时长","min-width":"100"}),t(o,{label:"操作",width:"120",fixed:"right"},{default:a(({row:n})=>[k("div",Q,[t(p,{type:"primary",link:""},{default:a(()=>[m((l(),s(y,{to:{path:i(g)("crontab.crontab/add:edit"),query:{id:n.id}}},{default:a(()=>[t(p,{type:"primary",link:""},{default:a(()=>e[5]||(e[5]=[r(" 编辑 ")])),_:1})]),_:2},1032,["to"])),[[_,["crontab.crontab/edit","crontab.crontab/add:edit"]]])]),_:2},1024),m((l(),s(p,{type:"danger",link:"",onClick:J=>v(n.id)},{default:a(()=>e[6]||(e[6]=[r(" 删除 ")])),_:2},1032,["onClick"])),[[_,["crontab.crontab/delete"]]])])]),_:1})]),_:1},8,["data"])),[[V,i(d).loading]]),k("div",U,[t(x,{modelValue:i(d),"onUpdate:modelValue":e[0]||(e[0]=n=>N(d)?d.value=n:null),onChange:i(c)},null,8,["modelValue","onChange"])])]),_:1})])}}});export{it as default};
