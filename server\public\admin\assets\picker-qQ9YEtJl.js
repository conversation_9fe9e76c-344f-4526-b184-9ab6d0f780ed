import{d as I,i as O,j as F,V as k,o as r,a as P,m,w as x,e as o,n as z,eJ as J,B as Y,C as g,b as l,t as C,D as E,E as L,v as Q,ea as W,bF as X,K as Z,x as q,p as T,F as H,r as $,Y as ee,c as M,G as A,eK as te,eL as le,eM as ae,s as ne,ao as se,q as oe}from"./index-B2xNDy79.js";import{P as ie}from"./index-DFOp_83R.js";import{_ as de}from"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import{E as me,a as ue}from"./el-table-column-DG3vRCd5.js";import{E as pe}from"./el-checkbox-3_Bu4Dnb.js";import"./el-tag-CuODyGk4.js";import{E as re,a as ce}from"./el-form-item-DlU85AZK.js";import{m as _e}from"./article-Dwgm3r-g.js";import{u as fe}from"./usePaging-Dm2wALfy.js";/* empty css                       */import{E as xe,a as ve}from"./el-radio-CKcO4hVq.js";var w=(n=>(n.SHOP_PAGES="shop",n.APPTOOL="application_tool",n.OTHER_LINK="other_link",n))(w||{}),a=(n=>(n.SHOP_PAGES="shop",n.ARTICLE_LIST="article",n.CUSTOM_LINK="custom",n.MINI_PROGRAM="mini_program",n))(a||{});const ye={class:"article-list"},he={class:"flex row-center"},Se={class:"flex items-center"},Ve={class:"ml-4 overflow-hidden"},Pe={class:"text-base line-clamp-2"},ge={class:"flex justify-end mt-4"},we=I({__name:"article-list",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(n,{emit:h}){const c=n,y=h,d=O({path:"/pages/news_detail/news_detail",name:"",query:{},type:a.ARTICLE_LIST}),t=F({name:"",is_show:1}),{pager:e,getLists:_,resetPage:s}=fe({fetchFun:_e,params:t}),i=f=>f==Number(d.value.id),p=f=>{d.value={id:f.id,name:f.title,path:"/pages/news_detail/news_detail",query:{id:f.id},type:a.ARTICLE_LIST},y("update:modelValue",d.value)};return k(()=>c.modelValue,f=>{if(f.type!=a.ARTICLE_LIST)return d.value={id:"",name:"",path:"/pages/news_detail/news_detail",type:a.SHOP_PAGES};d.value=f},{immediate:!0}),_(),(f,v)=>{const u=L,S=Q,b=re,G=ce,N=pe,R=me,K=W,U=X,j=ue,B=de,D=Z;return r(),P("div",ye,[m(G,{ref:"formRef",model:o(t),inline:!0},{default:x(()=>[m(b,{class:"w-[280px]",label:"文章名称"},{default:x(()=>[m(u,{modelValue:o(t).name,"onUpdate:modelValue":v[0]||(v[0]=V=>o(t).name=V),placeholder:"请输入",clearable:"",onKeyup:z(o(s),["enter"])},null,8,["modelValue","onKeyup"]),m(S,{type:"primary",class:"ml-4",icon:o(J),onClick:o(s)},null,8,["icon","onClick"])]),_:1})]),_:1},8,["model"]),Y((r(),g(j,{size:"large",data:o(e).lists,height:"432px",onRowClick:p},{default:x(()=>[m(R,{label:"选择","min-width":"50"},{default:x(({row:V})=>[l("div",he,[m(N,{"model-value":i(V.id),size:"large",onChange:Ye=>p(V)},null,8,["model-value","onChange"])])]),_:1}),m(R,{label:"文章名称","min-width":"180"},{default:x(({row:V})=>[l("div",Se,[m(K,{fit:"cover",src:V.image,class:"flex-none w-[58px] h-[58px]"},null,8,["src"]),l("div",Ve,[m(U,{effect:"dark",content:V.title,placement:"top"},{default:x(()=>[l("div",Pe,C(V.title),1)]),_:2},1032,["content"])])])]),_:1}),m(R,{label:"创建时间",prop:"create_time","min-width":"140"})]),_:1},8,["data"])),[[D,o(e).loading]]),l("div",ge,[m(B,{modelValue:o(e),"onUpdate:modelValue":v[1]||(v[1]=V=>E(e)?e.value=V:null),onChange:o(_)},null,8,["modelValue","onChange"])])])}}}),Ee=q(we,[["__scopeId","data-v-9e297bd2"]]),Ie={class:"custom-link h-[530px]"},Oe={class:"flex flex-wrap items-center mt-4"},ke={class:"ml-4 flex-1 min-w-[100px]"},be=I({__name:"custom-link",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(n,{emit:h}){const c=h,y=d=>{c("update:modelValue",{path:"/pages/webview/webview",query:{url:d},type:a.CUSTOM_LINK})};return(d,t)=>{var _;const e=L;return r(),P("div",Ie,[t[1]||(t[1]=l("div",{class:"text-xl font-medium"},"自定义链接",-1)),l("div",Oe,[t[0]||(t[0]=l("div",{class:"w-[86px] text-right"},"自定义链接",-1)),l("div",ke,[m(e,{class:"max-w-[320px]","model-value":(_=n.modelValue.query)==null?void 0:_.url,placeholder:"请输入链接地址",onInput:y},null,8,["model-value"])])]),t[2]||(t[2]=l("div",{class:"form-tips ml-[101px] max-w-[320px]"}," 请填写完整的带有“https://”或“http://”的链接地址，链接的域名必须在微信公众平台设置业务域名 ",-1))])}}}),Ae={class:"mini-program h-[530px]"},Ce={class:"flex flex-wrap items-center mt-4"},Le={class:"ml-4 flex-1 min-w-[100px]"},Ge={class:"flex flex-wrap items-center mt-4"},Re={class:"ml-4 flex-1 min-w-[100px]"},Te={class:"flex flex-wrap items-center mt-4"},He={class:"ml-4 flex-1 min-w-[100px]"},$e={class:"flex flex-wrap items-center mt-4"},qe={class:"ml-4 flex-1 min-w-[100px]"},Me=I({__name:"mini-program",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(n,{emit:h}){const c=n,y=h,d=(t,e)=>{y("update:modelValue",{...c.modelValue,name:"小程序跳转",query:{...c.modelValue.query,[t]:e},type:a.MINI_PROGRAM})};return k(()=>c.modelValue,t=>{var e;(e=t.query)!=null&&e.env_version||d("env_version","release")},{immediate:!0}),(t,e)=>{var p,f,v,u;const _=L,s=xe,i=ve;return r(),P("div",Ae,[e[11]||(e[11]=l("div",{class:"text-xl font-medium"},"跳转小程序",-1)),l("div",Ce,[e[4]||(e[4]=l("div",{class:"w-[86px] text-right"},"小程序APPID",-1)),l("div",Le,[m(_,{class:"max-w-[320px]","model-value":(p=n.modelValue.query)==null?void 0:p.appId,placeholder:"请输入小程序appId",onInput:e[0]||(e[0]=S=>d("appId",S))},null,8,["model-value"])])]),l("div",Ge,[e[5]||(e[5]=l("div",{class:"w-[86px] text-right"},"小程序路径",-1)),l("div",Re,[m(_,{class:"max-w-[320px]","model-value":(f=n.modelValue.query)==null?void 0:f.path,placeholder:"请输入小程序路径链接地址",onInput:e[1]||(e[1]=S=>d("path",S))},null,8,["model-value"])])]),l("div",Te,[e[6]||(e[6]=l("div",{class:"w-[86px] text-right"},"传递参数",-1)),l("div",He,[m(_,{class:"max-w-[320px]","model-value":(v=n.modelValue.query)==null?void 0:v.query,placeholder:"请输入小程序跳转参数(选填)",onInput:e[2]||(e[2]=S=>d("query",S))},null,8,["model-value"])])]),e[12]||(e[12]=l("div",{class:"form-tips ml-[100px] max-w-[320px]"},[l("div",null,"示例：id=2&ustm=jiny&name=234"),l("div",{class:"text-error"}," 注意：不允许输入中文、特殊字符等。如果出现对不起，当前页面无法访问，大概率是跳转参数的问题！！ ")],-1)),l("div",$e,[e[10]||(e[10]=l("div",{class:"w-[86px] text-right"},"小程序版本",-1)),l("div",qe,[m(i,{"model-value":(u=n.modelValue.query)==null?void 0:u.env_version,onChange:e[3]||(e[3]=S=>d("env_version",S))},{default:x(()=>[m(s,{value:"release"},{default:x(()=>e[7]||(e[7]=[T("正式版")])),_:1}),m(s,{value:"trial"},{default:x(()=>e[8]||(e[8]=[T("体验版")])),_:1}),m(s,{value:"develop"},{default:x(()=>e[9]||(e[9]=[T("开发版")])),_:1})]),_:1},8,["model-value"])])]),e[13]||(e[13]=l("div",null,[l("div",{class:"form-tips ml-[100px] max-w-[320px]"},[l("div",{class:"mt-4"}," 1. 小程序APPID和小程序路径链接地址，小程序路径链接地址请填写小程序的页面路径，如：pages/index/index "),l("div",{class:"mt-2"},[l("span",null,"2. 如果是H5(浏览器)中需要跳转到小程序，则需要以下配置--->"),l("a",{href:"https://mp.weixin.qq.com/",class:"text-primary",target:"_blank",rel:"nofollow"}," 小程序管理后台 -> 设置 -> 隐私与安全 -> 明文 scheme 拉起此小程序 （点击跳转去配置） ")])])],-1))])}}}),Ne={class:"shop-pages h-[530px]"},Ke={class:"link-list flex flex-wrap"},Ue=["onClick"],je=I({__name:"shop-pages",props:{modelValue:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(n,{emit:h}){const c=h,y=O([{path:"/pages/index/index",name:"商城首页",type:a.SHOP_PAGES,canTab:!0},{path:"/pages/news/news",name:"文章资讯",type:a.SHOP_PAGES,canTab:!0},{path:"/pages/user/user",name:"个人中心",type:a.SHOP_PAGES,canTab:!0},{path:"/pages/collection/collection",name:"我的收藏",type:a.SHOP_PAGES},{path:"/pages/customer_service/customer_service",name:"联系客服",type:a.SHOP_PAGES},{path:"/pages/user_set/user_set",name:"个人设置",type:a.SHOP_PAGES},{path:"/pages/as_us/as_us",name:"关于我们",type:a.SHOP_PAGES},{path:"/pages/user_data/user_data",name:"个人资料",type:a.SHOP_PAGES},{path:"/pages/agreement/agreement",name:"隐私政策",query:{type:"privacy"},type:a.SHOP_PAGES},{path:"/pages/agreement/agreement",name:"服务协议",query:{type:"service"},type:a.SHOP_PAGES},{path:"/pages/search/search",name:"搜索",type:a.SHOP_PAGES},{path:"/packages/pages/user_wallet/user_wallet",name:"我的钱包",type:a.SHOP_PAGES}]),d=t=>{c("update:modelValue",t)};return(t,e)=>(r(),P("div",Ne,[l("div",Ke,[(r(!0),P(H,null,$(o(y),(_,s)=>(r(),P("div",{class:ee(["link-item border border-br px-5 py-[5px] rounded-[3px] cursor-pointer mr-[10px] mb-[10px]",{"border-primary text-primary":n.modelValue.path==_.path&&n.modelValue.name==_.name}]),key:s,onClick:i=>d(_)},C(_.name),11,Ue))),128))])]))}}),Be={class:"link flex"},De={class:"flex-1 ml-4 link-content"},Fe=I({__name:"index",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(n,{emit:h}){const c=n,y=h,d=O([{name:"商城页面",type:w.SHOP_PAGES,children:[{name:"基础页面",type:a.SHOP_PAGES,link:{}}]},{name:"应用工具",type:w.APPTOOL,children:[{name:"文章资讯",type:a.ARTICLE_LIST,link:{}}]},{name:"其他",type:w.OTHER_LINK,children:[{name:"自定义链接",type:a.CUSTOM_LINK,link:{}},{name:"跳转小程序",type:a.MINI_PROGRAM,link:{}}]}]),t=M({get(){let s={};return d.value.forEach(i=>{const p=i.children.find(f=>f.type==e.value);p&&(s=p)}),s.link},set(s){d.value.forEach(i=>{i.children.forEach(p=>{p.type==e.value&&(p.link=s)})})}}),e=O(a.SHOP_PAGES),_=s=>{e.value=s};return k(t,s=>{s.type&&y("update:modelValue",s)},{deep:!0}),k(()=>c.modelValue,s=>{e.value=s.type,t.value=s},{immediate:!0}),(s,i)=>{const p=te,f=le,v=ae;return r(),P("div",Be,[m(v,{"default-active":o(e),class:"flex-none w-[180px] min-h-[350px] link-menu","default-openeds":[o(w).SHOP_PAGES,o(w).APPTOOL,o(w).OTHER_LINK],onSelect:_},{default:x(()=>[(r(!0),P(H,null,$(o(d),(u,S)=>(r(),g(f,{index:u.type,key:S},{title:x(()=>[l("span",null,C(u.name),1)]),default:x(()=>[(r(!0),P(H,null,$(u.children,(b,G)=>(r(),g(p,{index:b.type,key:G,style:{"min-width":"160px"}},{default:x(()=>[l("span",null,C(b.name),1)]),_:2},1032,["index"]))),128))]),_:2},1032,["index"]))),128))]),_:1},8,["default-active","default-openeds"]),l("div",De,[o(a).SHOP_PAGES==o(e)?(r(),g(je,{key:0,modelValue:o(t),"onUpdate:modelValue":i[0]||(i[0]=u=>E(t)?t.value=u:null)},null,8,["modelValue"])):A("",!0),o(a).ARTICLE_LIST==o(e)?(r(),g(Ee,{key:1,modelValue:o(t),"onUpdate:modelValue":i[1]||(i[1]=u=>E(t)?t.value=u:null)},null,8,["modelValue"])):A("",!0),o(a).CUSTOM_LINK==o(e)?(r(),g(be,{key:2,modelValue:o(t),"onUpdate:modelValue":i[2]||(i[2]=u=>E(t)?t.value=u:null)},null,8,["modelValue"])):A("",!0),o(a).MINI_PROGRAM==o(e)?(r(),g(Me,{key:3,modelValue:o(t),"onUpdate:modelValue":i[3]||(i[3]=u=>E(t)?t.value=u:null)},null,8,["modelValue"])):A("",!0)])])}}}),ze=q(Fe,[["__scopeId","data-v-c8ad0dfa"]]),Je=I({__name:"picker",props:{modelValue:{type:Object},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(n,{emit:h}){const c=n,y=h,d=ne(),t=O({path:"",type:a.SHOP_PAGES}),e=()=>{y("update:modelValue",t.value)},_=M(()=>{var s,i,p;switch((s=c.modelValue)==null?void 0:s.type){case a.SHOP_PAGES:return c.modelValue.name;case a.ARTICLE_LIST:return c.modelValue.name;case a.CUSTOM_LINK:return(i=c.modelValue.query)==null?void 0:i.url;default:return(p=c.modelValue)==null?void 0:p.name}});return k(()=>c.modelValue,s=>{s!=null&&s.type&&(t.value=s)},{immediate:!0}),(s,i)=>{const p=oe,f=L;return r(),P("div",{class:"link-picker flex-1",onClick:i[2]||(i[2]=v=>{var u;return!n.disabled&&((u=o(d))==null?void 0:u.open())})},[m(f,{"model-value":o(_),placeholder:"请选择链接",readonly:"",disabled:n.disabled},{suffix:x(()=>{var v;return[(v=n.modelValue)!=null&&v.path?(r(),g(p,{key:1,name:"el-icon-Close",onClick:i[0]||(i[0]=se(u=>!n.disabled&&y("update:modelValue",{}),["stop"]))})):(r(),g(p,{key:0,name:"el-icon-ArrowRight"}))]}),_:1},8,["model-value","disabled"]),m(ie,{ref_key:"popupRef",ref:d,width:"1050px",title:"链接选择",onConfirm:e},{default:x(()=>[m(ze,{modelValue:o(t),"onUpdate:modelValue":i[1]||(i[1]=v=>E(t)?t.value=v:null)},null,8,["modelValue"])]),_:1},512)])}}}),it=q(Je,[["__scopeId","data-v-c281815e"]]);export{it as _};
