import{d as _,o as m,a as p,m as t,w as o,b as s,p as d,v as u}from"./index-B2xNDy79.js";import{E as f}from"./el-card-DpH4mUSc.js";import{_ as c}from"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./el-tag-CuODyGk4.js";import"./_Uint8Array-0jgVjd-W.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";const v={class:"flex flex-wrap"},x={class:"m-4"},w={class:"m-4"},b={class:"m-4"},B={class:"m-4"},C={class:"m-4"},q=_({__name:"popover_input",setup(E){const l=i=>{console.log(i)};return(i,e)=>{const n=u,a=c,r=f;return m(),p("div",null,[t(r,{header:"基础使用",shadow:"never",class:"!border-none"},{default:o(()=>[s("div",v,[s("div",x,[t(a,{onConfirm:l},{default:o(()=>[t(n,null,{default:o(()=>e[0]||(e[0]=[d(" 点击输入 ")])),_:1})]),_:1})]),s("div",w,[t(a,{type:"number",onConfirm:l},{default:o(()=>[t(n,null,{default:o(()=>e[1]||(e[1]=[d(" 输入数字 ")])),_:1})]),_:1})]),s("div",b,[t(a,{size:"small",onConfirm:l},{default:o(()=>[t(n,null,{default:o(()=>e[2]||(e[2]=[d(" 调整大小 ")])),_:1})]),_:1})]),s("div",B,[t(a,{limit:20,"show-limit":!0,onConfirm:l},{default:o(()=>[t(n,null,{default:o(()=>e[3]||(e[3]=[d(" 限制输入长度 ")])),_:1})]),_:1})]),s("div",C,[t(a,{value:"默认值",onConfirm:l},{default:o(()=>[t(n,null,{default:o(()=>e[4]||(e[4]=[d(" 默认值 ")])),_:1})]),_:1})])])]),_:1})])}}});export{q as default};
