import{a as i,b as c,c as g}from"./wx_oa-3-DCeMZg.js";import{s as h,i as l,j as o,I as d}from"./index-B2xNDy79.js";const r=h(),u=l([]),n=l(0),q=o({name:[{required:!0,message:"必填项不能为空",trigger:["blur","change"]},{min:1,max:12,message:"长度限制12个字符",trigger:["blur","change"]}],menuType:[{required:!0,message:"必填项不能为空",trigger:["blur","change"]}],visitType:[{required:!0,message:"必填项不能为空",trigger:["blur","change"]}],url:[{required:!0,message:"必填项不能为空",trigger:["blur","change"]},{pattern:/(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/,message:"请输入合法的网址链接",trigger:["blur","change"]}],appId:[{required:!0,message:"必填项不能为空",trigger:["blur","change"]}],pagePath:[{required:!0,message:"必填项不能为空",trigger:["blur","change"]}]}),O=s=>(s&&(r.value=s),{menuList:u,menuIndex:n,handleAddMenu:()=>{u.value.push({name:"菜单名称",has_menu:!1,type:"view",url:"",appid:"",pagepath:"",sub_button:[]})},handleAddSubMenu:e=>{const a=n.value;if(u.value[a].sub_button.length>=5){d.msgError("已添加上限～");return}u.value[a].sub_button.push(e)},handleEditSubMenu:(e,a)=>{const t=n.value;u.value[t].sub_button[a]=e},handleDelMenu:e=>{e!=0&&n.value--,u.value.splice(e,1)},handleDelSubMenu:(e,a)=>{u.value[e].sub_button.splice(a,1)},getOaMenuFunc:async()=>{try{u.value=await i()}catch(e){console.log("获取菜单=>",e)}},handleSave:async()=>{const e=r.value.value;for(let a=0;a<e.length;a++)try{await e[a].menuFormRef.validate()}catch{n.value=a;return}await c(u.value)},handlePublish:async()=>{const e=r.value.value;for(let a=0;a<e.length;a++)try{await e[a].menuFormRef.validate()}catch{n.value=a;return}await g(u.value)}});export{q as r,O as u};
