import{d as I,c as j,o as i,a as g,m as t,w as a,b as s,e as p,C as F,p as O,G as q,I as v,E as z,J as A,q as G,v as J}from"./index-B2xNDy79.js";import{E as R,a as S}from"./el-form-item-DlU85AZK.js";import{E as T}from"./el-card-DpH4mUSc.js";import{_ as H}from"./index-BuNto3DN.js";import{_ as K}from"./picker-qQ9YEtJl.js";import{D as L,_ as M}from"./picker-Cd5l2hZ5.js";import{c as b}from"./index-DSiy6YVt.js";const P={class:"bg-fill-light flex items-center w-full p-4 mt-4"},Q={class:"ml-3 flex-1"},W={class:"flex-1 flex items-center"},X={class:"drag-move cursor-move ml-auto"},Y={key:0,class:"mt-4"},u=5,se=I({__name:"attr",props:{content:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},emits:["update:content"],setup(r,{emit:h}){const m=h,c=r,f=j({get:()=>c.content,set:l=>{m("update:content",l)}}),k=()=>{var l;if(((l=c.content.data)==null?void 0:l.length)<u){const e=b(c.content);e.data.push({is_show:"1",image:"",name:"",link:{}}),m("update:content",e)}else v.msgError(`最多添加${u}张图片`)},w=l=>{var d;if(((d=c.content.data)==null?void 0:d.length)<=1)return v.msgError("最少保留一张图片");const e=b(c.content);e.data.splice(l,1),m("update:content",e)};return(l,e)=>{const d=M,E=z,_=R,y=K,U=A,C=G,B=H,D=J,N=T,$=S;return i(),g("div",null,[t($,{"label-width":"70px"},{default:a(()=>[t(N,{shadow:"never",class:"!border-none flex mt-2"},{default:a(()=>{var x;return[e[2]||(e[2]=s("div",{class:"flex items-end mb-4"},[s("div",{class:"text-base text-[#101010] font-medium"},"菜单"),s("div",{class:"text-xs text-tx-secondary ml-2"}," 最多添加5张，建议图片尺寸：750px*200px ")],-1)),t(p(L),{class:"draggable",modelValue:p(f).data,"onUpdate:modelValue":e[0]||(e[0]=o=>p(f).data=o),animation:"300",handle:".drag-move","item-key":"index"},{item:a(({element:o,index:V})=>[(i(),F(B,{key:V,onClose:n=>w(V),class:"w-[467px]"},{default:a(()=>[s("div",P,[t(d,{modelValue:o.image,"onUpdate:modelValue":n=>o.image=n,"upload-class":"bg-body","exclude-domain":""},null,8,["modelValue","onUpdate:modelValue"]),s("div",Q,[t(_,{label:"图片名称"},{default:a(()=>[t(E,{modelValue:o.name,"onUpdate:modelValue":n=>o.name=n,placeholder:"请输入名称"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),t(_,{class:"mt-[18px]",label:"图片链接"},{default:a(()=>[t(y,{modelValue:o.link,"onUpdate:modelValue":n=>o.link=n},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),t(_,{label:"是否显示",class:"mt-[18px]"},{default:a(()=>[s("div",W,[t(U,{modelValue:o.is_show,"onUpdate:modelValue":n=>o.is_show=n,"active-value":"1","inactive-value":"0"},null,8,["modelValue","onUpdate:modelValue"]),s("div",X,[t(C,{name:"el-icon-Rank",size:"18"})])])]),_:2},1024)])])]),_:2},1032,["onClose"]))]),_:1},8,["modelValue"]),((x=r.content.data)==null?void 0:x.length)<u?(i(),g("div",Y,[t(D,{class:"w-full",type:"primary",onClick:k},{default:a(()=>e[1]||(e[1]=[O("添加图片")])),_:1})])):q("",!0)]}),_:1})]),_:1})])}}});export{se as _};
