import{_ as R}from"./index-onOHNH0j.js";import{d as x,u as j,j as q,s as B,z as c,o as r,a as T,m as o,w as d,b as l,e as t,B as f,C as v,p as m,E as N,v as O}from"./index-B2xNDy79.js";import{E as z,a as F}from"./el-form-item-DlU85AZK.js";/* empty css                       */import{E as J,a as K}from"./el-radio-CKcO4hVq.js";import{_ as L}from"./picker-Cd5l2hZ5.js";import{E as G}from"./el-card-DpH4mUSc.js";import{E as M}from"./el-alert-BUxHh72o.js";import{g as Z,s as H}from"./wx_oa-3-DCeMZg.js";import"./_baseClone-CdezRMKA.js";import"./_Uint8Array-0jgVjd-W.js";import"./_initCloneObject-C-h6JGU9.js";import"./index-DFOp_83R.js";import"./index-C6Cr8aHe.js";import"./index-BhVAe0P7.js";import"./index.vue_vue_type_script_setup_true_lang-DUdeBZfj.js";import"./el-tag-CuODyGk4.js";import"./isEqual-CLGO95LP.js";import"./el-select-BRdnbwTl.js";import"./index-CcX0CyWL.js";import"./token-DI9FKtlJ.js";import"./el-table-column-DG3vRCd5.js";import"./el-checkbox-3_Bu4Dnb.js";import"./index-BuNto3DN.js";import"./index-DSiy6YVt.js";import"./el-tree-8o9N7gsQ.js";import"./index.vue_vue_type_script_setup_true_lang-B8J7_re8.js";import"./el-popover-Bpu4paqp.js";import"./usePaging-Dm2wALfy.js";const P={class:"w-80"},Q={class:"w-80"},W={class:"w-80"},X={class:"w-80"},Y={class:"flex-1 min-w-0"},$={class:"sm:flex"},h={class:"mr-4 sm:w-80 flex"},ee={class:"flex-1 min-w-0"},oe={class:"w-80"},le={class:"flex-1 min-w-0"},se={class:"w-80"},te={class:"flex-1 min-w-0"},de={class:"flex-1 min-w-0"},ne={class:"sm:flex"},ae={class:"mr-4 sm:w-80 flex"},ie={class:"flex-1 min-w-0"},me={class:"sm:flex"},re={class:"mr-4 sm:w-80 flex"},pe={class:"flex-1 min-w-0"},ue={class:"sm:flex"},_e={class:"mr-4 sm:w-80 flex"},fe=x({name:"wxOaConfig"}),Ze=x({...fe,setup(ve){const y=j(),s=q({name:"",original_id:" ",qr_code:"",app_id:"",app_secret:"",url:"",token:"",encoding_aes_key:"",encryption_type:1,business_domain:"",js_secure_domain:"",web_auth_domain:""}),V=B(),k={app_id:[{required:!0,message:"请输入AppID",trigger:["blur","change"]}],app_secret:[{required:!0,message:"请输入AppSecret",trigger:["blur","change"]}]},g=async()=>{const p=await Z();for(const e in s)s[e]=p[e]},E=async()=>{var p;await((p=V.value)==null?void 0:p.validate()),await H(s),g()};return g(),(p,e)=>{const U=M,u=G,i=N,a=z,A=L,_=O,w=J,D=K,S=F,C=R,b=c("copy"),I=c("perms");return r(),T("div",null,[o(u,{class:"!border-none",shadow:"never"},{default:d(()=>[o(U,{type:"warning",title:"温馨提示：填写微信公众号开发配置，请前往微信公众平台申请服务号并完成认证",closable:!1,"show-icon":""})]),_:1}),o(S,{ref_key:"formRef",ref:V,model:t(s),rules:k,"label-width":t(y).isMobile?"80px":"160px"},{default:d(()=>[o(u,{class:"!border-none mt-4",shadow:"never"},{default:d(()=>[e[13]||(e[13]=l("div",{class:"font-medium mb-7"},"微信公众号",-1)),o(a,{label:"公众号名称",prop:"name"},{default:d(()=>[l("div",P,[o(i,{modelValue:t(s).name,"onUpdate:modelValue":e[0]||(e[0]=n=>t(s).name=n),placeholder:"请输入公众号名称"},null,8,["modelValue"])])]),_:1}),o(a,{label:"原始ID",prop:"original_id"},{default:d(()=>[l("div",Q,[o(i,{modelValue:t(s).original_id,"onUpdate:modelValue":e[1]||(e[1]=n=>t(s).original_id=n),placeholder:"请输入原始ID"},null,8,["modelValue"])])]),_:1}),o(a,{label:"公众号二维码",prop:"qr_code"},{default:d(()=>[l("div",null,[l("div",null,[o(A,{modelValue:t(s).qr_code,"onUpdate:modelValue":e[2]||(e[2]=n=>t(s).qr_code=n),limit:1},null,8,["modelValue"])]),e[12]||(e[12]=l("div",{class:"form-tips"},"建议尺寸：宽400px*高400px。jpg，jpeg，png格式",-1))])]),_:1})]),_:1}),o(u,{class:"!border-none mt-4",shadow:"never"},{default:d(()=>[e[15]||(e[15]=l("div",{class:"font-medium mb-7"},"公众号开发者信息",-1)),o(a,{label:"AppID",prop:"app_id"},{default:d(()=>[l("div",W,[o(i,{modelValue:t(s).app_id,"onUpdate:modelValue":e[3]||(e[3]=n=>t(s).app_id=n),placeholder:"请输入AppID"},null,8,["modelValue"])])]),_:1}),o(a,{label:"AppSecret",prop:"app_secret"},{default:d(()=>[l("div",X,[o(i,{modelValue:t(s).app_secret,"onUpdate:modelValue":e[4]||(e[4]=n=>t(s).app_secret=n),placeholder:"请输入AppSecret"},null,8,["modelValue"])])]),_:1}),o(a,null,{default:d(()=>e[14]||(e[14]=[l("div",{class:"form-tips"}," 小程序账号登录微信公众平台，点击开发>开发设置->开发者ID，设置AppID和AppSecret ",-1)])),_:1})]),_:1}),o(u,{class:"!border-none mt-4",shadow:"never"},{default:d(()=>[e[23]||(e[23]=l("div",{class:"font-medium mb-7"},"服务器配置",-1)),o(a,{label:"URL"},{default:d(()=>[l("div",Y,[l("div",$,[l("div",h,[o(i,{modelValue:t(s).url,"onUpdate:modelValue":e[5]||(e[5]=n=>t(s).url=n),disabled:""},null,8,["modelValue"])]),f((r(),v(_,null,{default:d(()=>e[16]||(e[16]=[m("复制")])),_:1})),[[b,t(s).url]])]),e[17]||(e[17]=l("div",{class:"form-tips"}," 登录微信公众平台，点击开发>基本配置>服务器配置，填写服务器地址（URL） ",-1))])]),_:1}),o(a,{label:"Token",prop:"Token"},{default:d(()=>[l("div",ee,[l("div",oe,[o(i,{modelValue:t(s).token,"onUpdate:modelValue":e[6]||(e[6]=n=>t(s).token=n),placeholder:"请输入Token"},null,8,["modelValue"])]),e[18]||(e[18]=l("div",{class:"form-tips"}," 登录微信公众平台，点击开发>基本配置>服务器配置，设置令牌Token。不填默认为“likeshop” ",-1))])]),_:1}),o(a,{label:"EncodingAESKey",prop:"encoding_aes_key"},{default:d(()=>[l("div",le,[l("div",se,[o(i,{modelValue:t(s).encoding_aes_key,"onUpdate:modelValue":e[7]||(e[7]=n=>t(s).encoding_aes_key=n),placeholder:"请输入EncodingAESKey"},null,8,["modelValue"])]),e[19]||(e[19]=l("div",{class:"form-tips"}," 消息加密密钥由43位字符组成，字符范围为A-Z,a-z,0-9 ",-1))])]),_:1}),o(a,{label:"消息加密方式",required:"",prop:"encryption_type"},{default:d(()=>[l("div",te,[o(D,{class:"flex-col !items-start min-w-0",modelValue:t(s).encryption_type,"onUpdate:modelValue":e[8]||(e[8]=n=>t(s).encryption_type=n)},{default:d(()=>[o(w,{value:1},{default:d(()=>e[20]||(e[20]=[m(" 明文模式 (不使用消息体加解密功能，安全系数较低) ")])),_:1}),o(w,{value:2},{default:d(()=>e[21]||(e[21]=[m(" 兼容模式 (明文、密文将共存，方便开发者调试和维护) ")])),_:1}),o(w,{value:3},{default:d(()=>e[22]||(e[22]=[m(" 安全模式（推荐） (消息包为纯密文，需要开发者加密和解密，安全系数高) ")])),_:1})]),_:1},8,["modelValue"])])]),_:1})]),_:1}),o(u,{class:"!border-none mt-4",shadow:"never"},{default:d(()=>[e[30]||(e[30]=l("div",{class:"font-medium mb-7"},"功能设置",-1)),o(a,{label:"业务域名"},{default:d(()=>[l("div",de,[l("div",ne,[l("div",ae,[o(i,{modelValue:t(s).business_domain,"onUpdate:modelValue":e[9]||(e[9]=n=>t(s).business_domain=n),disabled:""},null,8,["modelValue"])]),f((r(),v(_,null,{default:d(()=>e[24]||(e[24]=[m("复制")])),_:1})),[[b,t(s).business_domain]])]),e[25]||(e[25]=l("div",{class:"form-tips"}," 登录微信公众平台，点击设置>公众号设置>功能设置，填写业务域名 ",-1))])]),_:1}),o(a,{label:"JS接口安全域名"},{default:d(()=>[l("div",ie,[l("div",me,[l("div",re,[o(i,{modelValue:t(s).js_secure_domain,"onUpdate:modelValue":e[10]||(e[10]=n=>t(s).js_secure_domain=n),disabled:""},null,8,["modelValue"])]),f((r(),v(_,null,{default:d(()=>e[26]||(e[26]=[m("复制")])),_:1})),[[b,t(s).js_secure_domain]])]),e[27]||(e[27]=l("div",{class:"form-tips"}," 登录微信公众平台，点击设置>公众号设置>功能设置，填写JS接口安全域名 ",-1))])]),_:1}),o(a,{label:"网页授权域名"},{default:d(()=>[l("div",pe,[l("div",ue,[l("div",_e,[o(i,{modelValue:t(s).web_auth_domain,"onUpdate:modelValue":e[11]||(e[11]=n=>t(s).web_auth_domain=n),disabled:""},null,8,["modelValue"])]),f((r(),v(_,null,{default:d(()=>e[28]||(e[28]=[m("复制")])),_:1})),[[b,t(s).web_auth_domain]])]),e[29]||(e[29]=l("div",{class:"form-tips"}," 登录微信公众平台，点击设置>公众号设置>功能设置，填写网页授权域名 ",-1))])]),_:1})]),_:1})]),_:1},8,["model","label-width"]),f((r(),v(C,null,{default:d(()=>[o(_,{type:"primary",onClick:E},{default:d(()=>e[31]||(e[31]=[m("保存")])),_:1})]),_:1})),[[I,["channel.official_account_setting/setConfig"]]])])}}});export{Ze as default};
